(window.webpackJsonp=window.webpackJsonp||[]).push([[722],{maCF:function(e,t,n){"use strict";n.r(t),n.d(t,"HelpModule",(function(){return el}));var l=n("ofXK"),o=n("wiVK"),r=n("tyNb"),i=n("mrSG"),a=n("3Pt+");function s(){return e=>{try{let n=JSON.parse(e.value);if(Array.isArray(n)){var t=!1;return n.forEach((function(e){"string"!=typeof e&&(t=!0)})),!t&&n.length>0?null:"invalid"}}catch(n){return"invalid"}}}var c=n("bEYa"),d=n("0IaG"),m=n("fXoL"),u=n("XNiG"),p=n("tk/3"),x=n("XXEo"),h=n("5eHb"),v=n("dNgK");let f=(()=>{class e{constructor(e,t,n,l){this.http=e,this._auth=t,this.toastr=n,this._snackBar=l,this.sub=[],this.reloadSubject=new u.b,this.requestOptions=this.getHeaders(this._auth.getToken())}getHeaders(e){return this.requestOptions={headers:new p.f({"Content-Type":"application/json",Authorization:"Bearer "+e})},this.requestOptions}unSubscribeEvent(){this.sub.forEach(e=>e.unsubscribe()),this.sub=[]}getValueSearch(e){try{return new Promise((t,n)=>{this.http.post("/api/kebshelp/searchQues",{searchWord:e},this.requestOptions).subscribe({next:e=>("E"==e.messType&&this.showMessage("Your Search didn't match any questions!","dismiss",2e3),t(e)),error:e=>(this.showMessage("Error! Your Search didn't match any questions!","dismiss",2e3),n(e))})})}catch(t){return Promise.reject()}}getSearchElements(e){try{return new Promise((t,n)=>{this.http.post("/api/kebshelp/searchQuesAns",{question_id:e},this.requestOptions).subscribe({next:e=>("E"==e.messType&&this.showMessage("Couldn't fetch Search Results!","dismiss",2e3),t(e)),error:e=>(this.showMessage("Error! Couldn't fetch Search Results!","dismiss",2e3),n(e))})})}catch(t){return Promise.reject()}}getAllParentTopic(){try{return new Promise((e,t)=>{this.http.post("/api/kebshelp/getParentTopic",{},this.requestOptions).subscribe({next:t=>("E"==t.messType&&this.showMessage("No Data Found!","dismiss",2e3),e(t)),error:e=>(this.showMessage("Error! Couldn't fetch Parent Topic!","dismiss",2e3),t(e))})})}catch(e){return Promise.reject()}}getParentTopic(){try{return new Promise((e,t)=>{this.http.post("/api/kebshelp/getAllTopic",{},this.requestOptions).subscribe({next:t=>("E"==t.messType&&this.showMessage("No Data Found!","dismiss",2e3),e(t)),error:e=>(this.showMessage("Error! Couldn't fetch Topic!","dismiss",2e3),t(e))})})}catch(e){return Promise.reject()}}getChildTopic(e){try{return new Promise((t,n)=>{this.http.post("/api/kebshelp/getChildTopic",{parentTopic:e},this.requestOptions).subscribe({next:e=>t(e),error:e=>(this.showMessage("Error! Couldn't fetch Child Topic!","dismiss",2e3),n(e))})})}catch(t){return Promise.reject()}}getGeneralDetails(){try{return new Promise((e,t)=>{this.http.post("/api/kebshelp/readGeneralDetails",{},this.requestOptions).subscribe({next:t=>("E"==t.messType&&this.showMessage("Couldn't fetch general details!","dismiss",2e3),e(t)),error:e=>(this.showMessage("Error! Couldn't fetch General Details!","dismiss",2e3),t(e))})})}catch(e){return Promise.reject()}}getTopicDetails(e){try{return new Promise((t,n)=>{this.http.post("/api/kebshelp/getQuesByTopic",{topic_id:e},this.requestOptions).subscribe({next:e=>t(e),error:e=>(this.showMessage("Error! Couldn't fetch Questions!","dismiss",2e3),n(e))})})}catch(t){return Promise.reject()}}addViewCount(e){e=String(e);try{return new Promise((t,n)=>{this.http.put("/api/kebshelp/updateViews",{ques_id:e},this.requestOptions).subscribe({next:e=>t(e),error:e=>(this.showMessage("Error! Couldn't update Views!","dismiss",2e3),n(e))})})}catch(t){return Promise.reject()}}getIcon(){try{return new Promise((e,t)=>{this.http.post("/api/kebshelp/getIconNames",{},this.requestOptions).subscribe({next:t=>("E"==t.messType&&this.showMessage("Couldn't fetch Icons!","dismiss",2e3),e(t)),error:e=>(this.showMessage("Error! Couldn't fetch Icons!","dismiss",2e3),t(e))})})}catch(e){return Promise.reject()}}getElementType(){try{return new Promise((e,t)=>{this.http.post("/api/kebshelp/getElementTypes",{},this.requestOptions).subscribe({next:t=>("E"==t.messType&&this.showMessage("Couldn't fetch Element Types!","dismiss",2e3),e(t)),error:e=>(this.showMessage("Error! Couldn't fetch Element Types!","dismiss",2e3),t(e))})})}catch(e){return Promise.reject()}}createHelpDetail(e){try{return new Promise((t,n)=>{this.http.post("/api/kebshelp/createRecord",{answerForm:e},this.requestOptions).subscribe({next:e=>("S"==e.messType?this.toastr.success("Insertion Successful!","Success",{timeOut:3e3}):this.toastr.error("Sorry! couldn't update","Error!",{timeOut:3e3}),t(e)),error:e=>(this.toastr.error("Error! Couldn't update","Error!",{timeOut:3e3}),n(e))})})}catch(t){return Promise.reject()}}showMessage(e,t,n=3e3){return this._snackBar.open(e,t,{duration:n})}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275inject"](p.c),m["\u0275\u0275inject"](x.a),m["\u0275\u0275inject"](h.c),m["\u0275\u0275inject"](v.a))},e.\u0275prov=m["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var g=n("NFeN"),S=n("d3UM"),w=n("FKr1"),C=n("qFsG");let E=(()=>{class e{constructor(){}transform(e){return JSON.parse(e)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=m["\u0275\u0275definePipe"]({name:"stringParser",type:e,pure:!0}),e})();var y=n("jhN1");let I=(()=>{class e{constructor(e){this.dom=e}transform(e){return this.dom.bypassSecurityTrustResourceUrl(e)}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](y.c))},e.\u0275pipe=m["\u0275\u0275definePipe"]({name:"kebsHelpYoutubeSanitize",type:e,pure:!0}),e})();function F(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",27),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null;m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[t]||null==(o=l.controls[t].get("elements"))||null==o.controls[e].controls||null==o.controls[e].controls.value?null:o.controls[e].controls.value.value," ")}}function O(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",26),m["\u0275\u0275elementStart"](1,"div",24),m["\u0275\u0275template"](2,F,3,1,"div",7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2);let o=null,r=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","title"==(null==l.answerForm||null==(o=l.answerForm.get("answerSet"))||null==o.controls[n]||null==(r=o.controls[n].get("elements"))||null==r.controls[e]||null==r.controls[e].controls||null==r.controls[e].controls.type?null:r.controls[e].controls.type.value))}}function b(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",21),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[t]||null==(o=l.controls[t].get("elements"))||null==o.controls[e]||null==o.controls[e].controls||null==o.controls[e].controls.value?null:o.controls[e].controls.value.value,m["\u0275\u0275sanitizeHtml"])}}function _(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",26),m["\u0275\u0275elementStart"](1,"div",24),m["\u0275\u0275template"](2,b,2,1,"div",7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2);let o=null,r=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","description"==(null==l.answerForm||null==(o=l.answerForm.get("answerSet"))||null==o.controls[n]||null==(r=o.controls[n].get("elements"))||null==r.controls[e]||null==r.controls[e].controls||null==r.controls[e].controls.type?null:r.controls[e].controls.type.value))}}function P(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"img",36),m["\u0275\u0275elementStart"](2,"div",37),m["\u0275\u0275elementStart"](3,"span"),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",38),m["\u0275\u0275elementStart"](6,"span"),m["\u0275\u0275text"](7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2);let o=null,r=null,i=null,a=null;m["\u0275\u0275advance"](1),m["\u0275\u0275propertyInterpolate"]("src",e,m["\u0275\u0275sanitizeUrl"]),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==l.answerForm||null==(o=l.answerForm.get("answerSet"))||null==o.controls[n]||null==(r=o.controls[n].get("elements"))||null==r.controls[t]||null==r.controls[t].controls||null==r.controls[t].controls.carouselTitle?null:r.controls[t].controls.carouselTitle.value),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==l.answerForm||null==(i=l.answerForm.get("answerSet"))||null==i.controls[n]||null==(a=i.controls[n].get("elements"))||null==a.controls[t]||null==a.controls[t].controls||null==a.controls[t].controls.carouselDescription?null:a.controls[t].controls.carouselDescription.value)}}function T(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,P,8,3,"div",7),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e===(null==o.answerForm||null==o.answerForm.controls||null==o.answerForm.controls.answerSet||null==o.answerForm.controls.answerSet.controls[l]||null==o.answerForm.controls.answerSet.controls[l].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n]||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount?null:o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount.value))}}function M(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",41),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).changeImageIndicator(l,n,t)})),m["\u0275\u0275elementEnd"]()}}function D(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",42),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).changeImageIndicator(l,n,t)})),m["\u0275\u0275elementEnd"]()}}function q(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,M,1,0,"span",39),m["\u0275\u0275template"](2,D,1,0,"span",40),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==o.answerForm||null==o.answerForm.controls||null==o.answerForm.controls.answerSet||null==o.answerForm.controls.answerSet.controls[l]||null==o.answerForm.controls.answerSet.controls[l].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n]||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount?null:o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount.value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==o.answerForm||null==o.answerForm.controls||null==o.answerForm.controls.answerSet||null==o.answerForm.controls.answerSet.controls[l]||null==o.answerForm.controls.answerSet.controls[l].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n]||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount?null:o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount.value))}}function k(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",28),m["\u0275\u0275elementStart"](2,"div",5),m["\u0275\u0275elementStart"](3,"div",29),m["\u0275\u0275elementStart"](4,"div",30),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).displayPreviousImage(n,t)})),m["\u0275\u0275elementStart"](5,"mat-icon"),m["\u0275\u0275text"](6,"chevron_left"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",31),m["\u0275\u0275elementStart"](8,"div",32),m["\u0275\u0275elementStart"](9,"div",31),m["\u0275\u0275template"](10,T,2,1,"div",33),m["\u0275\u0275pipe"](11,"stringParser"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](12,"div",14),m["\u0275\u0275elementStart"](13,"div",31),m["\u0275\u0275template"](14,q,3,2,"span",33),m["\u0275\u0275pipe"](15,"stringParser"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](16,"div",5),m["\u0275\u0275elementStart"](17,"div",29),m["\u0275\u0275elementStart"](18,"div",34),m["\u0275\u0275elementStart"](19,"div",35),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).displayNextImage(n,t)})),m["\u0275\u0275elementStart"](20,"mat-icon"),m["\u0275\u0275text"](21,"chevron_right"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](10),m["\u0275\u0275property"]("ngForOf",m["\u0275\u0275pipeBind1"](11,2,null==n.answerForm||null==n.answerForm.controls||null==n.answerForm.controls.answerSet||null==n.answerForm.controls.answerSet.controls[t]||null==n.answerForm.controls.answerSet.controls[t].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e]||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value?null:n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value.value)),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",m["\u0275\u0275pipeBind1"](15,4,null==n.answerForm||null==n.answerForm.controls||null==n.answerForm.controls.answerSet||null==n.answerForm.controls.answerSet.controls[t]||null==n.answerForm.controls.answerSet.controls[t].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e]||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value?null:n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value.value))}}function V(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",26),m["\u0275\u0275elementStart"](1,"div",24),m["\u0275\u0275template"](2,k,22,6,"div",7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2);let o=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","imageCarousel"==(null==l.answerForm||null==(o=l.answerForm.get("answerSet"))||null==o.controls[n]||null==o.controls[n].get("elements").controls[e]||null==o.controls[n].get("elements").controls[e].controls||null==o.controls[n].get("elements").controls[e].controls.type?null:o.controls[n].get("elements").controls[e].controls.type.value))}}function Q(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"iframe",43),m["\u0275\u0275pipe"](2,"kebsHelpYoutubeSanitize"),m["\u0275\u0275elementStart"](3,"div",37),m["\u0275\u0275elementStart"](4,"span"),m["\u0275\u0275text"](5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",38),m["\u0275\u0275elementStart"](7,"span"),m["\u0275\u0275text"](8),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2);let o=null,r=null,i=null,a=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("src",m["\u0275\u0275pipeBind1"](2,3,e),m["\u0275\u0275sanitizeResourceUrl"]),m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate"](null==l.answerForm||null==(o=l.answerForm.get("answerSet"))||null==o.controls[n]||null==(r=o.controls[n].get("elements"))||null==r.controls[t]||null==r.controls[t].controls||null==r.controls[t].controls.carouselTitle?null:r.controls[t].controls.carouselTitle.value),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==l.answerForm||null==(i=l.answerForm.get("answerSet"))||null==i.controls[n]||null==(a=i.controls[n].get("elements"))||null==a.controls[t]||null==a.controls[t].controls||null==a.controls[t].controls.carouselDescription?null:a.controls[t].controls.carouselDescription.value)}}function $(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Q,9,5,"div",7),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e===(null==o.answerForm||null==o.answerForm.controls||null==o.answerForm.controls.answerSet||null==o.answerForm.controls.answerSet.controls[l]||null==o.answerForm.controls.answerSet.controls[l].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount?null:o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount.value))}}function z(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",41),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).changeImageIndicator(l,n,t)})),m["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",42),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).changeImageIndicator(l,n,t)})),m["\u0275\u0275elementEnd"]()}}function H(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,z,1,0,"span",39),m["\u0275\u0275template"](2,j,1,0,"span",40),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==o.answerForm||null==o.answerForm.controls||null==o.answerForm.controls.answerSet||null==o.answerForm.controls.answerSet.controls[l]||null==o.answerForm.controls.answerSet.controls[l].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n]||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount?null:o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount.value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==o.answerForm||null==o.answerForm.controls||null==o.answerForm.controls.answerSet||null==o.answerForm.controls.answerSet.controls[l]||null==o.answerForm.controls.answerSet.controls[l].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n]||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls||null==o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount?null:o.answerForm.controls.answerSet.controls[l].controls.elements.controls[n].controls.carouselCount.value))}}function N(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",28),m["\u0275\u0275elementStart"](2,"div",5),m["\u0275\u0275elementStart"](3,"div",29),m["\u0275\u0275elementStart"](4,"div"),m["\u0275\u0275elementStart"](5,"div",30),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).displayPreviousImage(n,t)})),m["\u0275\u0275elementStart"](6,"mat-icon"),m["\u0275\u0275text"](7,"chevron_left"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",31),m["\u0275\u0275elementStart"](9,"div",32),m["\u0275\u0275elementStart"](10,"div",31),m["\u0275\u0275template"](11,$,2,1,"div",33),m["\u0275\u0275pipe"](12,"stringParser"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](13,"div",14),m["\u0275\u0275elementStart"](14,"div",31),m["\u0275\u0275template"](15,H,3,2,"span",33),m["\u0275\u0275pipe"](16,"stringParser"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](17,"div",5),m["\u0275\u0275elementStart"](18,"div",29),m["\u0275\u0275elementStart"](19,"div",34),m["\u0275\u0275elementStart"](20,"div",35),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).displayNextImage(n,t)})),m["\u0275\u0275elementStart"](21,"mat-icon"),m["\u0275\u0275text"](22,"chevron_right"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](11),m["\u0275\u0275property"]("ngForOf",m["\u0275\u0275pipeBind1"](12,2,null==n.answerForm||null==n.answerForm.controls||null==n.answerForm.controls.answerSet||null==n.answerForm.controls.answerSet.controls[t]||null==n.answerForm.controls.answerSet.controls[t].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e]||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value?null:n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value.value)),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",m["\u0275\u0275pipeBind1"](16,4,null==n.answerForm||null==n.answerForm.controls||null==n.answerForm.controls.answerSet||null==n.answerForm.controls.answerSet.controls[t]||null==n.answerForm.controls.answerSet.controls[t].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e]||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls||null==n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value?null:n.answerForm.controls.answerSet.controls[t].controls.elements.controls[e].controls.value.value))}}function R(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",26),m["\u0275\u0275elementStart"](1,"div",24),m["\u0275\u0275template"](2,N,23,6,"div",7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2);let o=null,r=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","videoCarousel"==(null==l.answerForm||null==(o=l.answerForm.get("answerSet"))||null==o.controls[n]||null==(r=o.controls[n].get("elements"))||null==r.controls[e]||null==r.controls[e].controls||null==r.controls[e].controls.type?null:r.controls[e].controls.type.value))}}function A(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",23),m["\u0275\u0275elementStart"](1,"div",24),m["\u0275\u0275template"](2,O,3,2,"div",25),m["\u0275\u0275template"](3,_,3,2,"div",25),m["\u0275\u0275template"](4,V,3,2,"div",25),m["\u0275\u0275template"](5,R,3,2,"div",25),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null,r=null,i=null,a=null,s=null,c=null,d=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[e]||null==(o=l.controls[e].get("elements"))?null:o.controls),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==n.answerForm||null==(r=n.answerForm.get("answerSet"))||null==r.controls[e]||null==(i=r.controls[e].get("elements"))?null:i.controls),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==n.answerForm||null==(a=n.answerForm.get("answerSet"))||null==a.controls[e]||null==(s=a.controls[e].get("elements"))?null:s.controls),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==n.answerForm||null==(c=n.answerForm.get("answerSet"))||null==c.controls[e]||null==(d=c.controls[e].get("elements"))?null:d.controls)}}function G(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275element"](2,"div",15),m["\u0275\u0275elementStart"](3,"div",3),m["\u0275\u0275elementStart"](4,"mat-icon",16),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().changeEditMode()})),m["\u0275\u0275text"](5,"edit"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",17),m["\u0275\u0275text"](7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](8,"br"),m["\u0275\u0275elementStart"](9,"div",18),m["\u0275\u0275elementStart"](10,"span",19),m["\u0275\u0275elementStart"](11,"mat-icon",20),m["\u0275\u0275text"](12),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](13,"span"),m["\u0275\u0275text"](14),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](15,"br"),m["\u0275\u0275element"](16,"div",21),m["\u0275\u0275template"](17,A,6,5,"div",22),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();let t=null;m["\u0275\u0275advance"](7),m["\u0275\u0275textInterpolate"](null==e.answerForm||null==e.answerForm.controls||null==e.answerForm.controls.parentTopic?null:e.answerForm.controls.parentTopic.value),m["\u0275\u0275advance"](5),m["\u0275\u0275textInterpolate"](null==e.answerForm||null==e.answerForm.controls||null==e.answerForm.controls.icon?null:e.answerForm.controls.icon.value),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e.answerForm||null==e.answerForm.controls||null==e.answerForm.controls.questionTopic?null:e.answerForm.controls.questionTopic.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("innerHtml",null==e.answerForm||null==e.answerForm.controls||null==e.answerForm.controls.question?null:e.answerForm.controls.question.value,m["\u0275\u0275sanitizeHtml"]),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.answerForm||null==(t=e.answerForm.get("answerSet"))?null:t.controls)}}function L(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"mat-option",59),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](2).getChildTopicDetails(n.id)})),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.name),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate"](null==e?null:e.name)}}function U(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1," Please select one of above field"),m["\u0275\u0275elementEnd"]())}function Y(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"mat-option",63),m["\u0275\u0275listener"]("valueChange",(function(e){return t.$implicit.name=e}))("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](4).getDefaultIcon(n)})),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.name),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate"](null==e?null:e.name)}}function B(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Y,2,2,"mat-option",62),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.childTopic?null:e.childTopic.data)}}function X(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",46),m["\u0275\u0275elementStart"](1,"mat-select",61),m["\u0275\u0275template"](2,B,2,1,"div",7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",e.childTopic)}}function K(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",46),m["\u0275\u0275element"](1,"input",64),m["\u0275\u0275elementEnd"]())}function J(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1,"Please enter question topic"),m["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",65),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).addChildTopic()})),m["\u0275\u0275text"](1," Go back to select "),m["\u0275\u0275elementEnd"]()}}function Z(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",65),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).addChildTopic()})),m["\u0275\u0275text"](1,"Add new topic"),m["\u0275\u0275elementEnd"]()}}function ee(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",67),m["\u0275\u0275elementStart"](1,"mat-icon",68),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](e)}}function te(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,ee,3,2,"mat-option",66),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.iconList)}}function ne(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1," Please select one of above field"),m["\u0275\u0275elementEnd"]())}function le(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1," Please enter question"),m["\u0275\u0275elementEnd"]())}function oe(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",67),m["\u0275\u0275elementStart"](1,"span"),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",null==e?null:e.name),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e?null:e.name)}}function re(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1," Please enter title"),m["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",2),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",46),m["\u0275\u0275element"](4,"input",76),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,re,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null;m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(l=n.answerForm.get("answerSet"))&&null!=l.controls[t]&&null!=(o=l.controls[t].get("elements"))&&null!=o.controls[e]&&null!=o.controls[e].controls.value&&o.controls[e].controls.value.valid)&&(null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[t]||null==(o=l.controls[t].get("elements"))||null==o.controls[e]||null==o.controls[e].controls.value?null:o.controls[e].controls.value.touched))}}function ae(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1," Please enter description"),m["\u0275\u0275elementEnd"]())}function se(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",77),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275element"](3,"ngx-editor-menu",55),m["\u0275\u0275element"](4,"ngx-editor",78),m["\u0275\u0275template"](5,ae,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null;m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("editor",n.newEditor)("toolbar",n.toolbar),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("editor",n.newEditor)("placeholder","Type question here..."),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(l=n.answerForm.get("answerSet"))&&null!=l.controls[t]&&null!=(o=l.controls[t].get("elements"))&&null!=o.controls[e]&&null!=o.controls[e].controls.value&&o.controls[e].controls.value.valid)&&(null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[t]||null==(o=l.controls[t].get("elements"))||null==o.controls[e]||null==o.controls[e].controls.value?null:o.controls[e].controls.value.touched))}}function ce(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1," Please enter in array format"),m["\u0275\u0275elementEnd"]())}function de(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1,"Please enter image title"),m["\u0275\u0275elementEnd"]())}function me(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1,"Please enter image description"),m["\u0275\u0275elementEnd"]())}function ue(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",2),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",46),m["\u0275\u0275element"](4,"textarea",79),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,ce,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](6,"br"),m["\u0275\u0275elementStart"](7,"div",2),m["\u0275\u0275elementStart"](8,"div",46),m["\u0275\u0275element"](9,"input",80),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](10,de,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](11,"br"),m["\u0275\u0275elementStart"](12,"div",2),m["\u0275\u0275elementStart"](13,"div",46),m["\u0275\u0275element"](14,"input",81),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](15,me,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null,r=null,i=null,a=null,s=null;m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(l=n.answerForm.get("answerSet"))&&null!=l.controls[t]&&null!=(o=l.controls[t].get("elements"))&&null!=o.controls[e]&&null!=o.controls[e].controls.value&&o.controls[e].controls.value.valid)&&(null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[t]||null==(o=l.controls[t].get("elements"))||null==o.controls[e]||null==o.controls[e].controls.value?null:o.controls[e].controls.value.touched)),m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(r=n.answerForm.get("answerSet"))&&null!=r.controls[t]&&null!=(i=r.controls[t].get("elements"))&&null!=i.controls[e]&&null!=i.controls[e].controls.carouselTitle&&i.controls[e].controls.carouselTitle.valid)&&(null==n.answerForm||null==(r=n.answerForm.get("answerSet"))||null==r.controls[t]||null==(i=r.controls[t].get("elements"))||null==i.controls[e]||null==i.controls[e].controls.carouselTitle?null:i.controls[e].controls.carouselTitle.touched)),m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(a=n.answerForm.get("answerSet"))&&null!=a.controls[t]&&null!=(s=a.controls[t].get("elements"))&&null!=s.controls[e]&&null!=s.controls[e].controls.carouselDescription&&s.controls[e].controls.carouselDescription.valid)&&(null==n.answerForm||null==(a=n.answerForm.get("answerSet"))||null==a.controls[t]||null==(s=a.controls[t].get("elements"))||null==s.controls[e]||null==s.controls[e].controls.carouselDescription?null:s.controls[e].controls.carouselDescription.touched))}}function pe(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1," Please enter in array format"),m["\u0275\u0275elementEnd"]())}function xe(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1,"Please enter video title"),m["\u0275\u0275elementEnd"]())}function he(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",60),m["\u0275\u0275text"](1,"Please enter video description"),m["\u0275\u0275elementEnd"]())}function ve(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",2),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",46),m["\u0275\u0275element"](4,"textarea",79),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,pe,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](6,"br"),m["\u0275\u0275elementStart"](7,"div",2),m["\u0275\u0275elementStart"](8,"div",46),m["\u0275\u0275element"](9,"input",82),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](10,xe,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](11,"br"),m["\u0275\u0275elementStart"](12,"div",2),m["\u0275\u0275elementStart"](13,"div",46),m["\u0275\u0275element"](14,"input",83),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](15,he,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().index,t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null,r=null,i=null,a=null,s=null;m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(l=n.answerForm.get("answerSet"))&&null!=l.controls[t]&&null!=(o=l.controls[t].get("elements"))&&null!=o.controls[e]&&null!=o.controls[e].controls.value&&o.controls[e].controls.value.valid)&&(null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[t]||null==(o=l.controls[t].get("elements"))||null==o.controls[e]||null==o.controls[e].controls.value?null:o.controls[e].controls.value.touched)),m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(r=n.answerForm.get("answerSet"))&&null!=r.controls[t]&&null!=(i=r.controls[t].get("elements"))&&null!=i.controls[e]&&null!=i.controls[e].controls.carouselTitle&&i.controls[e].controls.carouselTitle.valid)&&(null==n.answerForm||null==(r=n.answerForm.get("answerSet"))||null==r.controls[t]||null==(i=r.controls[t].get("elements"))||null==i.controls[e]||null==i.controls[e].controls.carouselTitle?null:i.controls[e].controls.carouselTitle.touched)),m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!(null!=n.answerForm&&null!=(a=n.answerForm.get("answerSet"))&&null!=a.controls[t]&&null!=(s=a.controls[t].get("elements"))&&null!=s.controls[e]&&null!=s.controls[e].controls.carouselDescription&&s.controls[e].controls.carouselDescription.valid)&&(null==n.answerForm||null==(a=n.answerForm.get("answerSet"))||null==a.controls[t]||null==(s=a.controls[t].get("elements"))||null==s.controls[e]||null==s.controls[e].controls.carouselDescription?null:s.controls[e].controls.carouselDescription.touched))}}function fe(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",26),m["\u0275\u0275elementStart"](1,"div",24),m["\u0275\u0275elementStart"](2,"div"),m["\u0275\u0275elementStart"](3,"div",28),m["\u0275\u0275elementStart"](4,"div",5),m["\u0275\u0275elementStart"](5,"div",46),m["\u0275\u0275elementStart"](6,"mat-select",70),m["\u0275\u0275listener"]("selectionChange",(function(){m["\u0275\u0275restoreView"](e);const n=t.index,l=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).changeElementType(l,n)})),m["\u0275\u0275template"](7,oe,3,2,"mat-option",66),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](8,ie,6,1,"div",71),m["\u0275\u0275template"](9,se,6,5,"div",72),m["\u0275\u0275template"](10,ue,16,3,"div",71),m["\u0275\u0275template"](11,ve,16,3,"div",71),m["\u0275\u0275elementStart"](12,"div",73),m["\u0275\u0275elementStart"](13,"span",74),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.index,l=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](2).deleteElement(l,n)})),m["\u0275\u0275elementStart"](14,"mat-icon",75),m["\u0275\u0275text"](15,"delete"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2);let o=null,r=null,i=null,a=null,s=null,c=null,d=null,u=null,p=null,x=null,h=null,v=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngForOf",null==l.types?null:l.types.data),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","title"==(null==l.answerForm||null==(o=l.answerForm.get("answerSet"))||null==o.controls[n]||null==(r=o.controls[n].get("elements"))||null==r.controls[e]||null==(i=r.controls[e].get("type"))?null:i.value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","description"==(null==l.answerForm||null==(a=l.answerForm.get("answerSet"))||null==a.controls[n]||null==(s=a.controls[n].get("elements"))||null==s.controls[e]||null==(c=s.controls[e].get("type"))?null:c.value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","imageCarousel"==(null==l.answerForm||null==(d=l.answerForm.get("answerSet"))||null==d.controls[n]||null==(u=d.controls[n].get("elements"))||null==u.controls[e]||null==(p=u.controls[e].get("type"))?null:p.value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","videoCarousel"==(null==l.answerForm||null==(x=l.answerForm.get("answerSet"))||null==x.controls[n]||null==(h=x.controls[n].get("elements"))||null==h.controls[e]||null==(v=h.controls[e].get("type"))?null:v.value))}}function ge(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",23),m["\u0275\u0275elementStart"](1,"div",24),m["\u0275\u0275element"](2,"hr"),m["\u0275\u0275template"](3,fe,16,6,"div",25),m["\u0275\u0275elementStart"](4,"div",28),m["\u0275\u0275element"](5,"div",2),m["\u0275\u0275elementStart"](6,"div",10),m["\u0275\u0275elementStart"](7,"span",57),m["\u0275\u0275text"](8,"Add Type"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](9,"mat-icon",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](2).createElementSet(n)})),m["\u0275\u0275text"](10,"add_circle"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",10),m["\u0275\u0275elementStart"](12,"span",57),m["\u0275\u0275text"](13,"Delete All"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](14,"mat-icon",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](2).deleteAnswerElement(n)})),m["\u0275\u0275text"](15,"delete"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](16,"div",2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2);let l=null,o=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==n.answerForm||null==(l=n.answerForm.get("answerSet"))||null==l.controls[e]||null==(o=l.controls[e].get("elements"))?null:o.controls)}}function Se(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",44),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",45),m["\u0275\u0275text"](4,"Main Topic"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",46),m["\u0275\u0275elementStart"](6,"mat-select",47),m["\u0275\u0275template"](7,L,2,2,"mat-option",48),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](8,U,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](9,"div",3),m["\u0275\u0275elementStart"](10,"div",2),m["\u0275\u0275elementStart"](11,"div",45),m["\u0275\u0275text"](12,"Question Topic"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](13,"div"),m["\u0275\u0275template"](14,X,3,1,"div",50),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](15,K,2,0,"div",51),m["\u0275\u0275template"](16,J,2,0,"span",49),m["\u0275\u0275template"](17,W,2,0,"span",52),m["\u0275\u0275template"](18,Z,2,0,"span",52),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](19,"div",44),m["\u0275\u0275elementStart"](20,"div",2),m["\u0275\u0275elementStart"](21,"div",45),m["\u0275\u0275text"](22,"Select Icon"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](23,"div",46),m["\u0275\u0275elementStart"](24,"mat-select",53),m["\u0275\u0275template"](25,te,2,1,"span",7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](26,ne,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](27,"div",3),m["\u0275\u0275element"](28,"div",2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](29,"div",9),m["\u0275\u0275elementStart"](30,"div",2),m["\u0275\u0275elementStart"](31,"div",54),m["\u0275\u0275text"](32,"Question"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](33,"ngx-editor-menu",55),m["\u0275\u0275element"](34,"ngx-editor",56),m["\u0275\u0275template"](35,le,2,0,"span",49),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](36,"div",28),m["\u0275\u0275elementStart"](37,"div",31),m["\u0275\u0275elementStart"](38,"span",57),m["\u0275\u0275text"](39,"Add Answer"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](40,"mat-icon",58),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().addAnswer()})),m["\u0275\u0275text"](41,"add_circle"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](42,ge,17,2,"div",22),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();let t=null;m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngForOf",null==e.topic||null==e.topic.topic?null:e.topic.topic.data),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!(null!=e.answerForm&&null!=e.answerForm.controls.parentTopic&&e.answerForm.controls.parentTopic.valid)&&(null==e.answerForm||null==e.answerForm.controls.parentTopic?null:e.answerForm.controls.parentTopic.touched)),m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngIf",1==e.childSelect),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",0==e.childSelect),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!(null!=e.answerForm&&null!=e.answerForm.controls.questionTopic&&e.answerForm.controls.questionTopic.valid)&&(null==e.answerForm||null==e.answerForm.controls.questionTopic?null:e.answerForm.controls.questionTopic.touched)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",0==e.childSelect&&0==e.noSelect),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",1==e.childSelect),m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngIf",e.matIcons),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!(null!=e.answerForm&&null!=e.answerForm.controls.icon&&e.answerForm.controls.icon.valid)&&(null==e.answerForm||null==e.answerForm.controls.icon?null:e.answerForm.controls.icon.touched)),m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("editor",e.questionEditor)("toolbar",e.toolbar),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("editor",e.questionEditor)("placeholder","Type question here..."),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!(null!=e.answerForm&&null!=e.answerForm.controls.question&&e.answerForm.controls.question.valid)&&(null==e.answerForm||null==e.answerForm.controls.question?null:e.answerForm.controls.question.touched)),m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngForOf",null==e.answerForm||null==(t=e.answerForm.get("answerSet"))?null:t.controls)}}function we(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",9),m["\u0275\u0275element"](1,"div",84),m["\u0275\u0275elementStart"](2,"div",3),m["\u0275\u0275elementStart"](3,"mat-icon",85),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().pathValue()})),m["\u0275\u0275text"](4,"remove_red_eye"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",77),m["\u0275\u0275elementStart"](6,"span",86),m["\u0275\u0275text"](7,"preview"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function Ce(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"button",87),m["\u0275\u0275element"](1,"span",88),m["\u0275\u0275text"](2," Submit "),m["\u0275\u0275elementEnd"]())}function Ee(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"button",89),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().submitHelpDialog()})),m["\u0275\u0275text"](1,"Submit"),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275property"]("disabled",!(null!=e.answerForm&&e.answerForm.valid))}}let ye=(()=>{class e{constructor(e,t,n,l,o){this.fb=e,this.topic=t,this.helpDialogRef=n,this._helpService=l,this.dialog=o,this.spinner=!1,this.childSelect=!0,this.selected=null,this.noSelect=!1,this.toolbar=[["bold","italic"],["underline"],["ordered_list","bullet_list"],["text_color","background_color"],["align_left","align_center","align_right","align_justify"]],this.answerForm=new a.m({parentTopic:new a.j("",a.H.required),questionTopic:new a.j("",a.H.required),icon:new a.j("",a.H.required),question:new a.j("",a.H.required),answerSet:new a.g([]),mode:new a.j("edit")})}ngOnInit(){this.questionEditor=new c.a,this.getInitialData()}getInitialData(){return Object(i.c)(this,void 0,void 0,(function*(){this.childTopic=yield this._helpService.getChildTopic(1),this.matIcons=yield this._helpService.getIcon(),this.iconList=this.matIcons.icon_name,this.types=yield this._helpService.getElementType()}))}getChildTopicDetails(e,t){return Object(i.c)(this,void 0,void 0,(function*(){this.answerForm.controls.questionTopic.patchValue(""),this.childSelect=!0,this.noSelect=!1,this.childTopic=" ",this.childTopic=yield this._helpService.getChildTopic(e),Array.isArray(this.childTopic.data)||(this.childSelect=!1,this.noSelect=!0)}))}ngOnDestroy(){this.questionEditor.destroy()}createAnswer(){return this.fb.group({elements:new a.g([this.fb.group({type:["",a.H.required],value:["",a.H.required]})])})}addAnswer(){this.elements=this.answerForm.get("answerSet"),this.elements.push(this.createAnswer())}createElementGroup(){return this.fb.group({type:["",a.H.required],value:["",a.H.required]})}createElementSet(e){this.answerForm.get("answerSet").controls[e].get("elements").push(this.createElementGroup())}deleteAnswerElement(e){this.answerForm.get("answerSet").removeAt(e)}changeElementType(e,t){let n=this.answerForm.get("answerSet").controls[e].get("elements").controls[t].get("type").value;if("imageCarousel"==n){this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselTitle"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselDescription");let n=this.answerForm.get("answerSet").controls[e].controls.elements.controls[t];n.addControl("carouselTitle",new a.j("",a.H.required)),n.addControl("carouselDescription",new a.j("",a.H.required)),n.addControl("carouselCount",new a.j(0)),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].controls.value.setValidators([a.H.required,s()])}else if("videoCarousel"==n){this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselTitle"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselDescription");let n=this.answerForm.get("answerSet").controls[e].controls.elements.controls[t];n.addControl("carouselTitle",new a.j("",a.H.required)),n.addControl("carouselDescription",new a.j("",a.H.required)),n.addControl("carouselCount",new a.j(0)),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].controls.value.setValidators([a.H.required,s()])}else"description"==n?(this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselTitle"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselDescription"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselCount"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].controls.value.setValidators([a.H.required]),this.newEditor=new c.a):"title"==n&&(this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].controls.type.patchValue("title"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselTitle"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselDescription"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].removeControl("carouselCount"),this.answerForm.get("answerSet").controls[e].controls.elements.controls[t].controls.value.setValidators([a.H.required]))}deleteElement(e,t){this.answerForm.get("answerSet").controls[e].controls.elements.removeAt(t),0==this.answerForm.get("answerSet").controls[e].controls.elements.length&&this.answerForm.get("answerSet").removeAt(e)}changeEditMode(){this.answerForm.controls.mode.patchValue("edit")}pathValue(){this.answerForm.controls.mode.patchValue("read")}onCancelHelpDialog(){this.helpDialogRef.close()}submitHelpDialog(){return Object(i.c)(this,void 0,void 0,(function*(){this.spinner=!0,yield this._helpService.createHelpDetail(this.answerForm.value),this.spinner=!1,this.helpDialogRef.close(),this._helpService.reloadSubject.next()}))}displayPreviousImage(e,t){let n=this.answerForm.controls.answerSet.controls[e].controls.elements.controls[t].controls.carouselCount.value,l=JSON.parse(this.answerForm.controls.answerSet.controls[e].controls.elements.controls[t].controls.value.value);this.answerForm.controls.answerSet.controls[e].controls.elements.controls[t].controls.carouselCount.patchValue(n<=0?l.length-1:n-1)}displayNextImage(e,t){let n=this.answerForm.controls.answerSet.controls[e].controls.elements.controls[t].controls.carouselCount.value,l=JSON.parse(this.answerForm.controls.answerSet.controls[e].controls.elements.controls[t].controls.value.value);this.answerForm.controls.answerSet.controls[e].controls.elements.controls[t].controls.carouselCount.patchValue(n>=l.length-1?0:n+1)}changeImageIndicator(e,t,n){this.answerForm.controls.answerSet.controls[e].controls.elements.controls[t].controls.carouselCount.patchValue(n)}addChildTopic(){this.childSelect=!this.childSelect,this.answerForm.controls.questionTopic.patchValue("")}getDefaultIcon(e){this.selected=e.icon_name,this.answerForm.controls.icon.patchValue(this.selected)}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](a.i),m["\u0275\u0275directiveInject"](d.a),m["\u0275\u0275directiveInject"](d.h),m["\u0275\u0275directiveInject"](f),m["\u0275\u0275directiveInject"](d.b))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-update-info"]],decls:22,vars:6,consts:[[1,"container-fluid","update-info","p-5",3,"formGroup"],[1,"row","header","pt-4","pb-3"],[1,"col"],[1,"col-1"],[1,"icon-display"],[1,"col-3"],[1,"heading"],[4,"ngIf"],["class","row mt-5",4,"ngIf"],[1,"row","mt-5"],[1,"col-2","p-0"],[1,"btn","cancel-btn",3,"click"],["class","btn save-btn","type","button","disabled","",4,"ngIf"],["class","btn save-btn","type","submit",3,"disabled","click",4,"ngIf"],[1,"row"],[1,"col-11"],[1,"icon-display-edit",3,"click"],[1,"Heading"],[1,"question-topic"],[1,"pr-1"],[1,"icon-title"],[3,"innerHtml"],["formArrayName","answerSet",4,"ngFor","ngForOf"],["formArrayName","answerSet"],[3,"formGroupName"],["formArrayName","elements",4,"ngFor","ngForOf"],["formArrayName","elements"],[1,"answer-section"],[1,"row","pt-3"],[1,"pt-5"],[1,"pointer","text-center",3,"click"],[1,"col","text-center"],[1,"row","text-center",2,"width","100%"],[4,"ngFor","ngForOf"],[1,"rightalign"],[1,"text-center","pointer",3,"click"],[1,"move-in-right",2,"height","180px","width","100%",3,"src"],[1,"pt-1","question-topic","text-center"],[1,"answer-section","text-center"],["class","indicator m-1",3,"click",4,"ngIf"],["class","indicator-notclick m-1",3,"click",4,"ngIf"],[1,"indicator","m-1",3,"click"],[1,"indicator-notclick","m-1",3,"click"],["allowfullscreen","",1,"move-in-right",2,"height","100%","width","100%",3,"src"],[1,"row","mt-3"],[1,"pb-3","input-heading"],[1,"form-field","p-1"],["formControlName","parentTopic","placeholder","Select topic",1,"p-2","full-width"],[3,"value","click",4,"ngFor","ngForOf"],["class","in-valid",4,"ngIf"],["class"," form-field p-1",4,"ngIf"],["class","form-field p-1",4,"ngIf"],["class","pl-2 pt-1 child-topic",3,"click",4,"ngIf"],["placeholder","Select Icon","formControlName","icon",1,"p-2","full-width"],[1,"pb-2","input-heading"],[3,"editor","toolbar"],["formControlName","question",3,"editor","placeholder"],[1,"input-heading"],[1,"pl-2","icon-display",3,"click"],[3,"value","click"],[1,"in-valid"],["placeholder","Select topic","formControlName","questionTopic",1,"p-2","full-width"],[3,"value","valueChange","click",4,"ngFor","ngForOf"],[3,"value","valueChange","click"],["matInput","","formControlName","questionTopic","placeholder","Enter short topic for question",1,"p-2","full-width"],[1,"pl-2","pt-1","child-topic",3,"click"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[1,"icon-dropdown"],[1,"pl-2","icon-display-edit",3,"click"],["formControlName","type","placeholder","Select type",1,"p-2",3,"selectionChange"],["class","col",4,"ngIf"],["class","col p-0",4,"ngIf"],[1,"col-1","p-0"],[3,"click"],[1,"pt-2","icon-display"],["matInput","","formControlName","value","placeholder","Enter here",1,"p-2","full-width"],[1,"col","p-0"],["formControlName","value",3,"editor","placeholder"],["matInput","","rows","3","formControlName","value","placeholder",'Enter URLs in the form of array. eg:["url1","url2",...] ',1,"full-width","p-2"],["formControlName","carouselTitle","placeholder","Image Title",1,"full-width",2,"width","100%"],["formControlName","carouselDescription","placeholder","Image Description",1,"full-width"],["formControlName","carouselTitle","placeholder","Video Title",1,"full-width"],["formControlName","carouselDescription","placeholder","Video Description",1,"full-width"],[1,"col-9"],[1,"icon-display",3,"click"],[1,"preview"],["type","button","disabled","",1,"btn","save-btn"],["role","status","aria-hidden","true",1,"spinner-border","spinner-border-sm"],["type","submit",1,"btn","save-btn",3,"disabled","click"]],template:function(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275element"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",3),m["\u0275\u0275elementStart"](4,"span"),m["\u0275\u0275elementStart"](5,"mat-icon",4),m["\u0275\u0275text"](6,"playlist_add_check"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",5),m["\u0275\u0275elementStart"](8,"span",6),m["\u0275\u0275text"](9,"Add Question"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](10,"div",2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](11,G,18,5,"div",7),m["\u0275\u0275template"](12,Se,43,15,"div",7),m["\u0275\u0275template"](13,we,8,0,"div",8),m["\u0275\u0275elementStart"](14,"div",9),m["\u0275\u0275element"](15,"div",2),m["\u0275\u0275elementStart"](16,"div",10),m["\u0275\u0275elementStart"](17,"button",11),m["\u0275\u0275listener"]("click",(function(){return t.onCancelHelpDialog()})),m["\u0275\u0275text"](18,"Cancel"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](19,"div",10),m["\u0275\u0275template"](20,Ce,3,0,"button",12),m["\u0275\u0275template"](21,Ee,2,1,"button",13),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){let e=null,n=null;m["\u0275\u0275property"]("formGroup",t.answerForm),m["\u0275\u0275advance"](11),m["\u0275\u0275property"]("ngIf","read"==(null==t.answerForm||null==(e=t.answerForm.get("mode"))?null:e.value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","edit"==(null==t.answerForm||null==t.answerForm.value?null:t.answerForm.value.mode)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","edit"==(null==t.answerForm||null==(n=t.answerForm.get("mode"))?null:n.value)),m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngIf",1==t.spinner),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",0==t.spinner)}},directives:[a.w,a.n,g.a,l.NgIf,l.NgForOf,a.h,a.o,S.c,a.v,a.l,c.b,c.c,w.p,C.b,a.e],pipes:[E,I],styles:[".update-info[_ngcontent-%COMP%]{font-family:Roboto}.update-info[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{background-color:#f6f8fb;border-radius:8px}.update-info[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%}.update-info[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-focused .mat-form-field-outline-thick, .update-info[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline{color:#e5e5e5!important}.update-info[_ngcontent-%COMP%]   .font-color-red[_ngcontent-%COMP%]{color:#cf0001}.update-info[_ngcontent-%COMP%]     .mat-select-value{font-style:normal;font-weight:400;font-size:14px;letter-spacing:.02em;color:#526179}.update-info[_ngcontent-%COMP%]     .mat-dialog-container{padding:0}.update-info[_ngcontent-%COMP%]   .mat-input-element[_ngcontent-%COMP%]{color:#526179}.update-info[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{outline:none!important;border:none}.update-info[_ngcontent-%COMP%]   .pointer[_ngcontent-%COMP%]{cursor:pointer}.update-info[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{width:100%}.update-info[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%], .update-info[_ngcontent-%COMP%]   .form-field-input[_ngcontent-%COMP%]{border:2px solid #b9c0ca;border-radius:7px}.update-info[_ngcontent-%COMP%]   .answerindex-label[_ngcontent-%COMP%]{background-color:#526179;font-style:normal;font-weight:500;font-size:14px}.update-info[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-style:normal;font-weight:500;font-size:18px;color:#526179}.update-info[_ngcontent-%COMP%]   .question-topic[_ngcontent-%COMP%]{font-style:normal;font-weight:700;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:uppercase;color:#45546e}.update-info[_ngcontent-%COMP%]   .answer-section[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:14px;color:#526179}.update-info[_ngcontent-%COMP%]   .icon-display[_ngcontent-%COMP%]{cursor:pointer;font-size:30px;color:#cf0001;vertical-align:middle}.update-info[_ngcontent-%COMP%]   .icon-dropdown[_ngcontent-%COMP%], .update-info[_ngcontent-%COMP%]   .icon-title[_ngcontent-%COMP%]{font-size:20px;color:#cf0001}.update-info[_ngcontent-%COMP%]   .icon-title[_ngcontent-%COMP%]{cursor:pointer;vertical-align:middle}.update-info[_ngcontent-%COMP%]   .icon-display-edit[_ngcontent-%COMP%]{cursor:pointer;font-size:30px;color:#526179;vertical-align:middle}.update-info[_ngcontent-%COMP%]   .icon-display-add[_ngcontent-%COMP%]{vertical-align:middle;color:#438f61;cursor:pointer;font-size:30px}.update-info[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%]{color:#cf0001}.update-info[_ngcontent-%COMP%]   .icon-action[_ngcontent-%COMP%]{cursor:pointer}.update-info[_ngcontent-%COMP%]   .in-valid[_ngcontent-%COMP%]{color:#cf0001;font-size:12px}.update-info[_ngcontent-%COMP%]   .add-answer[_ngcontent-%COMP%]{color:#fff;font-weight:500;background-color:#526179}.update-info[_ngcontent-%COMP%]   .input-heading[_ngcontent-%COMP%]{font-style:normal;font-weight:500;font-size:14px;color:#6e7b8f}.update-info[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{outline:none!important;border:none}.update-info[_ngcontent-%COMP%]   .video-title[_ngcontent-%COMP%]{font-style:normal;font-weight:700;font-size:12px;letter-spacing:.02em;color:#45546e}.update-info[_ngcontent-%COMP%]   .video-description[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:11px;letter-spacing:.02em;color:#526179}.update-info[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%]{width:20px;border-radius:8px}.update-info[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%], .update-info[_ngcontent-%COMP%]   .indicator-notclick[_ngcontent-%COMP%]{height:8px;background-color:#bbb;display:inline-block;cursor:pointer}.update-info[_ngcontent-%COMP%]   .indicator-notclick[_ngcontent-%COMP%]{width:8px;border-radius:50%}.update-info[_ngcontent-%COMP%]   .move-in-right[_ngcontent-%COMP%]{animation:moveInRight .5s ease-out forwards;border-radius:5%;background-color:#dadce2;height:15vh;width:15vw}@keyframes moveInRight{0%{opacity:0;transform:translateX(100px)}80%{transform:translateX(-10px)}to{opacity:1;transform:translate(0)}}.update-info[_ngcontent-%COMP%]   .capital[_ngcontent-%COMP%]{text-transform:capitalize}.update-info[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:linear-gradient(90deg,#d84343 10%,#cf0001 105.29%);color:#fff}.update-info[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background-color:#dadce2}.update-info[_ngcontent-%COMP%]   .child-topic[_ngcontent-%COMP%]{cursor:pointer;color:#45546e;text-decoration:underline}"]}),e})();var Ie=n("Kj3r"),Fe=n("/uUt"),Oe=n("STbY"),be=n("kmnG"),_e=n("bTqV"),Pe=n("wZkO"),Te=n("7EHt"),Me=n("Wp6s");function De(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"button",35),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().clearSearch()})),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function qe(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",42),m["\u0275\u0275elementStart"](1,"div",43),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](3).displaySearchResult(n.id)})),m["\u0275\u0275element"](2,"div",44),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("innerHtml",null==e?null:e.title,m["\u0275\u0275sanitizeHtml"])}}function ke(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",38),m["\u0275\u0275element"](1,"div",3),m["\u0275\u0275elementStart"](2,"div",39),m["\u0275\u0275template"](3,qe,3,1,"div",40),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](4,"div",41),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngForOf",e.searchResult)}}function Ve(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",36),m["\u0275\u0275template"](1,ke,5,1,"div",37),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e.searchResult?null:e.searchResult.length)>0)}}function Qe(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",28),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",46),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e.generalDetails||null==e.generalDetails.data[0]?null:e.generalDetails.data[0].details_for),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e.generalDetails||null==e.generalDetails.data[0]?null:e.generalDetails.data[0].value)}}function $e(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",27),m["\u0275\u0275template"](1,Qe,5,2,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e.generalDetails||null==e.generalDetails.data?null:e.generalDetails.data.length)>0)}}function ze(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",28),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",29),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e.generalDetails||null==e.generalDetails.data[1]?null:e.generalDetails.data[1].details_for),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e.generalDetails||null==e.generalDetails.data[1]?null:e.generalDetails.data[1].value)}}function je(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",27),m["\u0275\u0275template"](1,ze,5,2,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e.generalDetails||null==e.generalDetails.data?null:e.generalDetails.data.length)>0)}}function He(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",53),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.value,"")}}function Ne(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,He,2,1,"div",52),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","title"==e.type)}}function Re(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Ne,2,1,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements)}}function Ae(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",54),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.value,m["\u0275\u0275sanitizeHtml"])}}function Ge(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Ae,2,1,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","description"==(null==e?null:e.type))}}function Le(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",63),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"chevron_left"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function Ue(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"img",64),m["\u0275\u0275element"](2,"br"),m["\u0275\u0275elementStart"](3,"div",65),m["\u0275\u0275elementStart"](4,"span"),m["\u0275\u0275text"](5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",66),m["\u0275\u0275elementStart"](7,"span"),m["\u0275\u0275text"](8),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275propertyInterpolate"]("src",e,m["\u0275\u0275sanitizeUrl"]),m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselTitle),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselDescription)}}function Ye(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Ue,9,3,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.searchQuestion||null==r.searchQuestion.data||null==r.searchQuestion.data.questions[o]||null==r.searchQuestion.data.questions[o].answerSet[l]||null==r.searchQuestion.data.questions[o].answerSet[l].elements[n]?null:r.searchQuestion.data.questions[o].answerSet[l].elements[n].carouselCount))}}function Be(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).changeImageSearchIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function Xe(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",70),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).changeImageSearchIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function Ke(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,Be,1,0,"span",67),m["\u0275\u0275template"](2,Xe,1,0,"span",68),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.searchQuestion||null==r.searchQuestion.data||null==r.searchQuestion.data.questions[o]||null==r.searchQuestion.data.questions[o].answerSet[l]||null==r.searchQuestion.data.questions[o].answerSet[l].elements[n]?null:r.searchQuestion.data.questions[o].answerSet[l].elements[n].carouselCount)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==r.searchQuestion||null==r.searchQuestion.data||null==r.searchQuestion.data.questions[o]||null==r.searchQuestion.data.questions[o].answerSet[l]||null==r.searchQuestion.data.questions[o].answerSet[l].elements[n]?null:r.searchQuestion.data.questions[o].answerSet[l].elements[n].carouselCount))}}function Je(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",63),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"chevron_right"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function We(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",55),m["\u0275\u0275elementStart"](2,"div",56),m["\u0275\u0275elementStart"](3,"div",57),m["\u0275\u0275elementStart"](4,"div",58),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).displayPreviousSearchImage(l,n,t)})),m["\u0275\u0275template"](5,Le,3,0,"span",59),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",4),m["\u0275\u0275elementStart"](7,"div",60),m["\u0275\u0275template"](8,Ye,2,1,"div",51),m["\u0275\u0275elementStart"](9,"div",61),m["\u0275\u0275template"](10,Ke,3,2,"span",51),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",57),m["\u0275\u0275elementStart"](12,"div",62),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).displayNextSearchImage(l,n,t)})),m["\u0275\u0275template"](13,Je,3,0,"span",59),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1)}}function Ze(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,We,14,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","imageCarousel"==(null==e?null:e.type))}}function et(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",58),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).displayPreviousSearchImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"span",63),m["\u0275\u0275elementStart"](2,"mat-icon"),m["\u0275\u0275text"](3,"chevron_left"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function tt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"iframe",74),m["\u0275\u0275pipe"](2,"kebsHelpYoutubeSanitize"),m["\u0275\u0275element"](3,"br"),m["\u0275\u0275elementStart"](4,"div",65),m["\u0275\u0275elementStart"](5,"span"),m["\u0275\u0275text"](6),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",66),m["\u0275\u0275elementStart"](8,"span"),m["\u0275\u0275text"](9),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("src",m["\u0275\u0275pipeBind1"](2,3,e),m["\u0275\u0275sanitizeResourceUrl"]),m["\u0275\u0275advance"](5),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselTitle),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselDescription)}}function nt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,tt,10,5,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.searchQuestion||null==r.searchQuestion.data||null==r.searchQuestion.data.questions[o]||null==r.searchQuestion.data.questions[o].answerSet[l]||null==r.searchQuestion.data.questions[o].answerSet[l].elements[n]?null:r.searchQuestion.data.questions[o].answerSet[l].elements[n].carouselCount))}}function lt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).changeImageSearchIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function ot(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",70),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).changeImageSearchIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function rt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,lt,1,0,"span",67),m["\u0275\u0275template"](2,ot,1,0,"span",68),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.searchQuestion||null==r.searchQuestion.data||null==r.searchQuestion.data.questions[o]||null==r.searchQuestion.data.questions[o].answerSet[l]||null==r.searchQuestion.data.questions[o].answerSet[l].elements[n]?null:r.searchQuestion.data.questions[o].answerSet[l].elements[n].carouselCount)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==r.searchQuestion||null==r.searchQuestion.data||null==r.searchQuestion.data.questions[o]||null==r.searchQuestion.data.questions[o].answerSet[l]||null==r.searchQuestion.data.questions[o].answerSet[l].elements[n]?null:r.searchQuestion.data.questions[o].answerSet[l].elements[n].carouselCount))}}function it(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",62),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"]().index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).displayNextSearchImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"span",63),m["\u0275\u0275elementStart"](2,"mat-icon"),m["\u0275\u0275text"](3,"chevron_right"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function at(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",55),m["\u0275\u0275elementStart"](2,"div",56),m["\u0275\u0275elementStart"](3,"div",57),m["\u0275\u0275template"](4,et,4,0,"div",71),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275elementStart"](6,"div",72),m["\u0275\u0275elementStart"](7,"div",4),m["\u0275\u0275template"](8,nt,2,1,"div",51),m["\u0275\u0275elementStart"](9,"div",61),m["\u0275\u0275template"](10,rt,3,2,"span",51),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",57),m["\u0275\u0275template"](12,it,4,0,"div",73),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1)}}function st(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,at,13,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","videoCarousel"==(null==e?null:e.type))}}function ct(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Re,2,1,"div",45),m["\u0275\u0275template"](2,Ge,2,1,"div",51),m["\u0275\u0275template"](3,Ze,2,1,"div",51),m["\u0275\u0275template"](4,st,2,1,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==o.searchQuestion||null==o.searchQuestion.data||null==o.searchQuestion.data.questions[l]||null==o.searchQuestion.data.questions[l].answerSet[n]||null==o.searchQuestion.data.questions[l].answerSet[n].elements?null:o.searchQuestion.data.questions[l].answerSet[n].elements.length)>0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements)}}function dt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,ct,5,4,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.answerSet)}}function mt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",49),m["\u0275\u0275element"](1,"div",50),m["\u0275\u0275template"](2,dt,2,1,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,l=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.question,m["\u0275\u0275sanitizeHtml"]),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==l.searchQuestion||null==l.searchQuestion.data||null==l.searchQuestion.data.questions[n]||null==l.searchQuestion.data.questions[n].answerSet?null:l.searchQuestion.data.questions[n].answerSet.length)>0)}}function ut(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,mt,3,2,"div",48),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.searchQuestion||null==e.searchQuestion.data?null:e.searchQuestion.data.questions)}}function pt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",47),m["\u0275\u0275element"](1,"div",18),m["\u0275\u0275elementStart"](2,"div",4),m["\u0275\u0275template"](3,ut,2,1,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](4,"div",18),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngIf",(null==e.searchQuestion||null==e.searchQuestion.data||null==e.searchQuestion.data.questions?null:e.searchQuestion.data.questions.length)>0)}}function xt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",78),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().$implicit;return m["\u0275\u0275nextContext"](4).tabSwitch(t)})),m["\u0275\u0275elementStart"](1,"mat-icon",81),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"span",82),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e?null:e.icon_name),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null==e?null:e.name)}}function ht(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",84),m["\u0275\u0275elementStart"](2,"div",4),m["\u0275\u0275elementStart"](3,"span"),m["\u0275\u0275elementStart"](4,"mat-icon"),m["\u0275\u0275text"](5,"running_with_errors"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"span",82),m["\u0275\u0275text"](7,"No Data Found!"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function vt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",3),m["\u0275\u0275elementStart"](1,"mat-card",88),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](9).childQuestions(n)})),m["\u0275\u0275elementStart"](2,"div",42),m["\u0275\u0275elementStart"](3,"div",89),m["\u0275\u0275elementStart"](4,"span"),m["\u0275\u0275elementStart"](5,"mat-icon",90),m["\u0275\u0275text"](6),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",91),m["\u0275\u0275elementStart"](8,"div",92),m["\u0275\u0275text"](9),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",93),m["\u0275\u0275text"](11,"Click here to view."),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.icon_name,""),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",null==e?null:e.name," ")}}function ft(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",86),m["\u0275\u0275element"](1,"div",4),m["\u0275\u0275template"](2,vt,12,2,"div",87),m["\u0275\u0275element"](3,"div",4),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](8);m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==e.childTopic?null:e.childTopic.data)}}function gt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",41),m["\u0275\u0275elementStart"](1,"mat-card",88),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](9).childQuestions(n)})),m["\u0275\u0275elementStart"](2,"div",42),m["\u0275\u0275elementStart"](3,"div",89),m["\u0275\u0275elementStart"](4,"span"),m["\u0275\u0275elementStart"](5,"mat-icon",90),m["\u0275\u0275text"](6),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",91),m["\u0275\u0275elementStart"](8,"div",95),m["\u0275\u0275text"](9),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",93),m["\u0275\u0275text"](11,"Click here to view."),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.icon_name,""),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.name,"")}}function St(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",86),m["\u0275\u0275template"](1,gt,12,2,"div",94),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](8);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.childTopic?null:e.childTopic.data)}}function wt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"mat-card",101),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().$implicit;return m["\u0275\u0275nextContext"](11).childQuestions(t)})),m["\u0275\u0275elementStart"](2,"div",42),m["\u0275\u0275elementStart"](3,"div",102),m["\u0275\u0275elementStart"](4,"span",103),m["\u0275\u0275elementStart"](5,"mat-icon",90),m["\u0275\u0275text"](6),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",91),m["\u0275\u0275elementStart"](8,"div",95),m["\u0275\u0275text"](9),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",93),m["\u0275\u0275text"](11," Click here to view. "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.icon_name," "),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.name," ")}}function Ct(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,wt,12,2,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](11);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==n.firstIndex)}}function Et(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Ct,2,1,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.childTopic?null:e.childTopic.data)}}function yt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"mat-card",101),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().$implicit;return m["\u0275\u0275nextContext"](11).childQuestions(t)})),m["\u0275\u0275elementStart"](2,"div",42),m["\u0275\u0275elementStart"](3,"div",102),m["\u0275\u0275elementStart"](4,"span",103),m["\u0275\u0275elementStart"](5,"mat-icon",90),m["\u0275\u0275text"](6),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",91),m["\u0275\u0275elementStart"](8,"div",95),m["\u0275\u0275text"](9),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",93),m["\u0275\u0275text"](11," Click here to view. "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.icon_name," "),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.name," ")}}function It(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,yt,12,2,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](11);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==n.secondIndex)}}function Ft(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,It,2,1,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.childTopic?null:e.childTopic.data)}}function Ot(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"mat-card",101),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().$implicit;return m["\u0275\u0275nextContext"](11).childQuestions(t)})),m["\u0275\u0275elementStart"](2,"div",42),m["\u0275\u0275elementStart"](3,"div",102),m["\u0275\u0275elementStart"](4,"span",103),m["\u0275\u0275elementStart"](5,"mat-icon",90),m["\u0275\u0275text"](6),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",91),m["\u0275\u0275elementStart"](8,"div",95),m["\u0275\u0275text"](9),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",93),m["\u0275\u0275text"](11," Click here to view. "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.icon_name," "),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.name," ")}}function bt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Ot,12,2,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](11);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==n.thirdIndex)}}function _t(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,bt,2,1,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.childTopic?null:e.childTopic.data)}}function Pt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",42),m["\u0275\u0275elementStart"](1,"div",4),m["\u0275\u0275template"](2,Et,2,1,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",4),m["\u0275\u0275template"](4,Ft,2,1,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275template"](6,_t,2,1,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](9);m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e.childTopic||null==e.childTopic.data?null:e.childTopic.data.length)>0),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e.childTopic||null==e.childTopic.data?null:e.childTopic.data.length)>0),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e.childTopic||null==e.childTopic.data?null:e.childTopic.data.length)>0)}}function Tt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",86),m["\u0275\u0275elementStart"](1,"div",96),m["\u0275\u0275elementStart"](2,"div",97),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](8).displayPrevious()})),m["\u0275\u0275elementStart"](3,"mat-icon"),m["\u0275\u0275text"](4,"chevron_left"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",98),m["\u0275\u0275template"](6,Pt,7,3,"div",99),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",96),m["\u0275\u0275elementStart"](8,"div",100),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](8).displayNext()})),m["\u0275\u0275elementStart"](9,"mat-icon"),m["\u0275\u0275text"](10,"chevron_right"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](8);m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngIf",e.childTopic)}}function Mt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,ft,4,1,"div",85),m["\u0275\u0275template"](2,St,2,1,"div",85),m["\u0275\u0275template"](3,Tt,11,1,"div",85),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](7);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",1==(null==e.childTopic||null==e.childTopic.data?null:e.childTopic.data.length)||2==(null==e.childTopic||null==e.childTopic.data?null:e.childTopic.data.length)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",3==(null==e.childTopic||null==e.childTopic.data?null:e.childTopic.data.length)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e.childTopic||null==e.childTopic.data?null:e.childTopic.data.length)>3)}}function Dt(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",104),m["\u0275\u0275elementStart"](1,"div",105),m["\u0275\u0275elementStart"](2,"span",106),m["\u0275\u0275text"](3,"Loading..."),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function qt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",113),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.question,m["\u0275\u0275sanitizeHtml"])}}function kt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",114),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.question,m["\u0275\u0275sanitizeHtml"])}}function Vt(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"remove"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function Qt(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"add"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function $t(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",53),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.value," ")}}function zt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,$t,2,1,"div",52),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","title"==(null==e?null:e.type))}}function jt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",54),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.value,m["\u0275\u0275sanitizeHtml"])}}function Ht(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,jt,2,1,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","description"==e.type)}}function Nt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",58),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).displayPreviousChildImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2," chevron_left "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function Rt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"img",64),m["\u0275\u0275elementStart"](2,"div",65),m["\u0275\u0275elementStart"](3,"span"),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",66),m["\u0275\u0275elementStart"](6,"span"),m["\u0275\u0275text"](7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275propertyInterpolate"]("src",e,m["\u0275\u0275sanitizeUrl"]),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselTitle),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselDescription)}}function At(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Rt,8,3,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.childTopicDetails||null==r.childTopicDetails.data||null==r.childTopicDetails.data.questions[o]||null==r.childTopicDetails.data.questions[o].answerSet[l]||null==r.childTopicDetails.data.questions[o].answerSet[l].elements[n]?null:r.childTopicDetails.data.questions[o].answerSet[l].elements[n].carouselCount))}}function Gt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).changeImageChildIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function Lt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",70),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).changeImageChildIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function Ut(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,Gt,1,0,"span",67),m["\u0275\u0275template"](2,Lt,1,0,"span",68),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.childTopicDetails||null==r.childTopicDetails.data||null==r.childTopicDetails.data.questions[o]||null==r.childTopicDetails.data.questions[o].answerSet[l]||null==r.childTopicDetails.data.questions[o].answerSet[l].elements[n]?null:r.childTopicDetails.data.questions[o].answerSet[l].elements[n].carouselCount)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==r.childTopicDetails||null==r.childTopicDetails.data||null==r.childTopicDetails.data.questions[o]||null==r.childTopicDetails.data.questions[o].answerSet[l]||null==r.childTopicDetails.data.questions[o].answerSet[l].elements[n]?null:r.childTopicDetails.data.questions[o].answerSet[l].elements[n].carouselCount))}}function Yt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",62),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).displayNextChildImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2," chevron_right "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function Bt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",115),m["\u0275\u0275elementStart"](2,"div",42),m["\u0275\u0275elementStart"](3,"div",57),m["\u0275\u0275template"](4,Nt,3,0,"div",71),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275elementStart"](6,"div",72),m["\u0275\u0275elementStart"](7,"div",4),m["\u0275\u0275template"](8,At,2,1,"div",51),m["\u0275\u0275elementStart"](9,"div",61),m["\u0275\u0275template"](10,Ut,3,2,"span",51),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",57),m["\u0275\u0275template"](12,Yt,3,0,"div",73),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1)}}function Xt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Bt,13,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","imageCarousel"==e.type)}}function Kt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",58),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).displayPreviousChildImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"span",63),m["\u0275\u0275elementStart"](2,"mat-icon"),m["\u0275\u0275text"](3," chevron_left "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function Jt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"iframe",74),m["\u0275\u0275pipe"](2,"kebsHelpYoutubeSanitize"),m["\u0275\u0275elementStart"](3,"div",65),m["\u0275\u0275elementStart"](4,"span"),m["\u0275\u0275text"](5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",66),m["\u0275\u0275elementStart"](7,"span"),m["\u0275\u0275text"](8),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("src",m["\u0275\u0275pipeBind1"](2,3,e),m["\u0275\u0275sanitizeResourceUrl"]),m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselTitle),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselDescription)}}function Wt(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Jt,9,5,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.childTopicDetails||null==r.childTopicDetails.data||null==r.childTopicDetails.data.questions[o]||null==r.childTopicDetails.data.questions[o].answerSet[l]||null==r.childTopicDetails.data.questions[o].answerSet[l].elements[n]?null:r.childTopicDetails.data.questions[o].answerSet[l].elements[n].carouselCount))}}function Zt(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).changeImageChildIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function en(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",70),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).changeImageChildIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function tn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,Zt,1,0,"span",67),m["\u0275\u0275template"](2,en,1,0,"span",68),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.childTopicDetails||null==r.childTopicDetails.data||null==r.childTopicDetails.data.questions[o]||null==r.childTopicDetails.data.questions[o].answerSet[l]||null==r.childTopicDetails.data.questions[o].answerSet[l].elements[n]?null:r.childTopicDetails.data.questions[o].answerSet[l].elements[n].carouselCount)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==r.childTopicDetails||null==r.childTopicDetails.data||null==r.childTopicDetails.data.questions[o]||null==r.childTopicDetails.data.questions[o].answerSet[l]||null==r.childTopicDetails.data.questions[o].answerSet[l].elements[n]?null:r.childTopicDetails.data.questions[o].answerSet[l].elements[n].carouselCount))}}function nn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",62),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](10).displayNextChildImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"span",63),m["\u0275\u0275elementStart"](2,"mat-icon"),m["\u0275\u0275text"](3," chevron_right "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function ln(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",55),m["\u0275\u0275elementStart"](2,"div",56),m["\u0275\u0275elementStart"](3,"div",57),m["\u0275\u0275template"](4,Kt,4,0,"div",71),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275elementStart"](6,"div",72),m["\u0275\u0275elementStart"](7,"div",4),m["\u0275\u0275template"](8,Wt,2,1,"div",51),m["\u0275\u0275elementStart"](9,"div",61),m["\u0275\u0275template"](10,tn,3,2,"span",51),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",57),m["\u0275\u0275template"](12,nn,4,0,"div",73),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1)}}function on(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,ln,13,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","videoCarousel"==e.type)}}function rn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,zt,2,1,"div",51),m["\u0275\u0275template"](2,Ht,2,1,"div",51),m["\u0275\u0275template"](3,Xt,2,1,"div",51),m["\u0275\u0275template"](4,on,2,1,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements)}}function an(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",49),m["\u0275\u0275template"](1,rn,5,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==l.childTopicDetails||null==l.childTopicDetails.data||null==l.childTopicDetails.data.questions[n]||null==l.childTopicDetails.data.questions[n].answerSet[e]||null==l.childTopicDetails.data.questions[n].answerSet[e].elements?null:l.childTopicDetails.data.questions[n].answerSet[e].elements.length)>0)}}function sn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,an,2,1,"div",48),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.answerSet)}}function cn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"mat-expansion-panel",109),m["\u0275\u0275listener"]("opened",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](10).setOpened(n)}))("closed",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](10).setClosed(n)})),m["\u0275\u0275elementStart"](1,"mat-expansion-panel-header",110),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](10).updateViews(null==n?null:n.question_id)})),m["\u0275\u0275elementStart"](2,"div",111),m["\u0275\u0275elementStart"](3,"div",112),m["\u0275\u0275template"](4,qt,2,1,"div",45),m["\u0275\u0275template"](5,kt,2,1,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",96),m["\u0275\u0275template"](7,Vt,3,0,"div",45),m["\u0275\u0275template"](8,Qt,3,0,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](9,sn,2,1,"div",45),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=m["\u0275\u0275nextContext"](10);m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",n.currentlyOpenedItemIndex===e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",n.currentlyOpenedItemIndex!=e),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",n.currentlyOpenedItemIndex===e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",n.currentlyOpenedItemIndex!=e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==n.childTopicDetails||null==n.childTopicDetails.data||null==n.childTopicDetails.data.questions[e]||null==n.childTopicDetails.data.questions[e].answerSet?null:n.childTopicDetails.data.questions[e].answerSet.length)>0)}}function dn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,cn,10,5,"mat-expansion-panel",108),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](9);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.childTopicDetails||null==e.childTopicDetails.data?null:e.childTopicDetails.data.questions)}}function mn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",107),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](3,dn,2,1,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](8);m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",null==e.childTopicDetails||null==e.childTopicDetails.data?null:e.childTopicDetails.data.questionTopic,""),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e.childTopicDetails||null==e.childTopicDetails.data||null==e.childTopicDetails.data.questions?null:e.childTopicDetails.data.questions.length)>0)}}function un(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",27),m["\u0275\u0275template"](1,mn,4,2,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](7);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",null==e.childTopicDetails?null:e.childTopicDetails.data)}}function pn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",113),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.question,m["\u0275\u0275sanitizeHtml"])}}function xn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",114),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.question,m["\u0275\u0275sanitizeHtml"])}}function hn(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"remove"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function vn(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"add"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function fn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",53),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.value," ")}}function gn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,fn,2,1,"div",52),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","title"==(null==e?null:e.type))}}function Sn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"div",54),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("innerHtml",null==e?null:e.value,m["\u0275\u0275sanitizeHtml"])}}function wn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Sn,2,1,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","description"==(null==e?null:e.type))}}function Cn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",58),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).displayPreviousImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2," chevron_left "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function En(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"img",64),m["\u0275\u0275elementStart"](2,"div",65),m["\u0275\u0275elementStart"](3,"span"),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",66),m["\u0275\u0275elementStart"](6,"span"),m["\u0275\u0275text"](7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275propertyInterpolate"]("src",e,m["\u0275\u0275sanitizeUrl"]),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselTitle),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselDescription)}}function yn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,En,8,3,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](9);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.topicDetail||null==r.topicDetail.data||null==r.topicDetail.data.questions[o]||null==r.topicDetail.data.questions[o].answerSet[l]||null==r.topicDetail.data.questions[o].answerSet[l].elements[n]?null:r.topicDetail.data.questions[o].answerSet[l].elements[n].carouselCount))}}function In(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).changeImageIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function Fn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",70),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).changeImageIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function On(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,In,1,0,"span",67),m["\u0275\u0275template"](2,Fn,1,0,"span",68),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](9);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.topicDetail||null==r.topicDetail.data||null==r.topicDetail.data.questions[o]||null==r.topicDetail.data.questions[o].answerSet[l]||null==r.topicDetail.data.questions[o].answerSet[l].elements[n]?null:r.topicDetail.data.questions[o].answerSet[l].elements[n].carouselCount)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==r.topicDetail||null==r.topicDetail.data||null==r.topicDetail.data.questions[o]||null==r.topicDetail.data.questions[o].answerSet[l]||null==r.topicDetail.data.questions[o].answerSet[l].elements[n]?null:r.topicDetail.data.questions[o].answerSet[l].elements[n].carouselCount))}}function bn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",62),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).displayNextImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2," chevron_right "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function _n(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",115),m["\u0275\u0275elementStart"](2,"div",42),m["\u0275\u0275elementStart"](3,"div",57),m["\u0275\u0275template"](4,Cn,3,0,"div",71),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275elementStart"](6,"div",72),m["\u0275\u0275elementStart"](7,"div",4),m["\u0275\u0275template"](8,yn,2,1,"div",51),m["\u0275\u0275elementStart"](9,"div",61),m["\u0275\u0275template"](10,On,3,2,"span",51),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",57),m["\u0275\u0275template"](12,bn,3,0,"div",73),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1)}}function Pn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,_n,13,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","imageCarousel"==(null==e?null:e.type))}}function Tn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",58),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).displayPreviousImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"span",63),m["\u0275\u0275elementStart"](2,"mat-icon"),m["\u0275\u0275text"](3," chevron_left "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function Mn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275element"](1,"iframe",74),m["\u0275\u0275pipe"](2,"kebsHelpYoutubeSanitize"),m["\u0275\u0275elementStart"](3,"div",65),m["\u0275\u0275elementStart"](4,"span"),m["\u0275\u0275text"](5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",66),m["\u0275\u0275elementStart"](7,"span"),m["\u0275\u0275text"](8),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2).$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("src",m["\u0275\u0275pipeBind1"](2,3,e),m["\u0275\u0275sanitizeResourceUrl"]),m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselTitle),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](null==t?null:t.carouselDescription)}}function Dn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Mn,9,5,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](9);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.topicDetail||null==r.topicDetail.data||null==r.topicDetail.data.questions[o]||null==r.topicDetail.data.questions[o].answerSet[l]||null==r.topicDetail.data.questions[o].answerSet[l].elements[n]?null:r.topicDetail.data.questions[o].answerSet[l].elements[n].carouselCount))}}function qn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",69),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).changeImageIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function kn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"span",70),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).changeImageIndicator(o,l,n,t)})),m["\u0275\u0275elementEnd"]()}}function Vn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,qn,1,0,"span",67),m["\u0275\u0275template"](2,kn,1,0,"span",68),m["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index,o=m["\u0275\u0275nextContext"](2).index,r=m["\u0275\u0275nextContext"](9);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e==(null==r.topicDetail||null==r.topicDetail.data||null==r.topicDetail.data.questions[o]||null==r.topicDetail.data.questions[o].answerSet[l]||null==r.topicDetail.data.questions[o].answerSet[l].elements[n]?null:r.topicDetail.data.questions[o].answerSet[l].elements[n].carouselCount)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e!=(null==r.topicDetail||null==r.topicDetail.data||null==r.topicDetail.data.questions[o]||null==r.topicDetail.data.questions[o].answerSet[l]||null==r.topicDetail.data.questions[o].answerSet[l].elements[n]?null:r.topicDetail.data.questions[o].answerSet[l].elements[n].carouselCount))}}function Qn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",62),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"](2).index,n=m["\u0275\u0275nextContext"](2).index,l=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](9).displayNextImage(l,n,t)})),m["\u0275\u0275elementStart"](1,"span",63),m["\u0275\u0275elementStart"](2,"mat-icon"),m["\u0275\u0275text"](3," chevron_right "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function $n(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",55),m["\u0275\u0275elementStart"](2,"div",56),m["\u0275\u0275elementStart"](3,"div",57),m["\u0275\u0275template"](4,Tn,4,0,"div",71),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275elementStart"](6,"div",72),m["\u0275\u0275elementStart"](7,"div",4),m["\u0275\u0275template"](8,Dn,2,1,"div",51),m["\u0275\u0275elementStart"](9,"div",61),m["\u0275\u0275template"](10,Vn,3,2,"span",51),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",57),m["\u0275\u0275template"](12,Qn,4,0,"div",73),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==e?null:e.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",(null==e||null==e.value?null:e.value.length)>1)}}function zn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,$n,13,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","videoCarousel"==(null==e?null:e.type))}}function jn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,gn,2,1,"div",51),m["\u0275\u0275template"](2,wn,2,1,"div",51),m["\u0275\u0275template"](3,Pn,2,1,"div",51),m["\u0275\u0275template"](4,zn,2,1,"div",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.elements)}}function Hn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",49),m["\u0275\u0275template"](1,jn,5,4,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e||null==e.elements?null:e.elements.length)>0)}}function Nn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Hn,2,1,"div",48),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e?null:e.answerSet)}}function Rn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"mat-expansion-panel",109),m["\u0275\u0275listener"]("opened",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](9).setChildOpened(n)}))("closed",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](9).setChildClosed(n)})),m["\u0275\u0275elementStart"](1,"mat-expansion-panel-header",110),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](9).updateViews(null==n?null:n.question_id)})),m["\u0275\u0275elementStart"](2,"div",111),m["\u0275\u0275elementStart"](3,"div",112),m["\u0275\u0275template"](4,pn,2,1,"div",45),m["\u0275\u0275template"](5,xn,2,1,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",96),m["\u0275\u0275template"](7,hn,3,0,"div",45),m["\u0275\u0275template"](8,vn,3,0,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](9,Nn,2,1,"div",45),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,l=m["\u0275\u0275nextContext"](9);m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",l.currentlyOpenedChildIndex===n),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",l.currentlyOpenedChildIndex!=n),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",l.currentlyOpenedChildIndex===n),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",l.currentlyOpenedChildIndex!=n),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e||null==e.answerSet?null:e.answerSet.length)>0)}}function An(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275elementStart"](1,"div",107),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](3,Rn,10,5,"mat-expansion-panel",108),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](4).$implicit,t=m["\u0275\u0275nextContext"](4);m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"]("FAQs On ",e.name," "),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==t.topicDetail||null==t.topicDetail.data?null:t.topicDetail.data.questions)}}function Gn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",27),m["\u0275\u0275template"](1,An,4,2,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](7);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e.topicDetail||null==e.topicDetail.data||null==e.topicDetail.data.questions?null:e.topicDetail.data.questions.length)>=1)}}function Ln(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Mt,4,3,"div",45),m["\u0275\u0275elementStart"](2,"mat-accordion"),m["\u0275\u0275elementStart"](3,"div",33),m["\u0275\u0275template"](4,Dt,4,0,"div",34),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,un,2,1,"div",22),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"mat-accordion"),m["\u0275\u0275template"](7,Gn,2,1,"div",22),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](6);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.childTopic&&(null==e.childTopic?null:e.childTopic.data)),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngIf",1==e.childSpinner),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.childTopicDetails&&(null==e.childTopicDetails?null:e.childTopicDetails.data)),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",e.topicDetail&&(null==e.topicDetail?null:e.topicDetail.data))}}function Un(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",42),m["\u0275\u0275element"](1,"div",4),m["\u0275\u0275elementStart"](2,"div",83),m["\u0275\u0275template"](3,ht,8,0,"div",45),m["\u0275\u0275template"](4,Ln,8,4,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](5,"div",4),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](5);m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngIf",1==e.nullData),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",0==e.nullData)}}function Yn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"mat-tab"),m["\u0275\u0275elementStart"](1,"span",78),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](4).tabSwitch(n)})),m["\u0275\u0275template"](2,xt,5,2,"ng-template",79),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](4).tabSwitch(n)})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](3,Un,6,2,"ng-template",80),m["\u0275\u0275elementEnd"]()}}function Bn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Yn,4,0,"mat-tab",51),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",null==e.parentTopics?null:e.parentTopics.data)}}function Xn(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275template"](1,Bn,2,1,"div",45),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==e.parentTopics||null==e.parentTopics.data?null:e.parentTopics.data.length)>0)}}function Kn(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",75),m["\u0275\u0275elementStart"](1,"div",76),m["\u0275\u0275elementStart"](2,"div",4),m["\u0275\u0275elementStart"](3,"mat-tab-group",77),m["\u0275\u0275listener"]("selectedTabChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().tabClick(t)})),m["\u0275\u0275template"](4,Xn,2,1,"div",45),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",e.parentTopics&&(null==e.parentTopics?null:e.parentTopics.data))}}function Jn(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",104),m["\u0275\u0275elementStart"](1,"div",105),m["\u0275\u0275elementStart"](2,"span",106),m["\u0275\u0275text"](3,"Loading..."),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}const Wn=[{path:"",component:(()=>{class e{constructor(e,t,n,l){this._helpLanService=e,this._helpService=t,this._loginService=n,this.dialog=l,this.currentlyOpenedItemIndex=-1,this.firstIndex=0,this.secondIndex=1,this.thirdIndex=2,this.searchValue=null,this.showMain=!0,this.spinner=!0,this.questionId=null,this.nullData=!1,this.subjectKeyUp=new u.b,this.childSpinner=!1,this.currentlyOpenedChildIndex=-1,this.subjectKeyUp.pipe(Object(Ie.a)(100),Object(Fe.a)()).subscribe(e=>{this.getSearch(e)}),this._helpLanService.reloadSubject.subscribe(e=>{this.getInitialData(),this.getDataOnReload()})}ngOnInit(){this.getInitialData()}openUpdateDataDialog(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this._helpLanService.getParentTopic();this.dialog.open(ye,{width:"50%",height:"95%",data:{topic:e}})}))}getInitialData(){return Object(i.c)(this,void 0,void 0,(function*(){this.generalDetails=yield this._helpLanService.getGeneralDetails(),this.parentTopics=yield this._helpLanService.getAllParentTopic(),this.spinner=!1}))}onSearch(e){return Object(i.c)(this,void 0,void 0,(function*(){""==this.searchVal?(this.showMain=!0,this.searchResult=null,this.searchQuestion=null):this.subjectKeyUp.next(e.target.value)}))}getDataOnReload(){var e;return Object(i.c)(this,void 0,void 0,(function*(){if(this.spinner=!0,this.parentTopics=yield this._helpLanService.getAllParentTopic(),this.parentTopic)this.callToChild(this.parentTopic);else{let t=null===(e=this.parentTopics.data[0])||void 0===e?void 0:e.id;this.callToChild(t)}}))}getSearch(e){return Object(i.c)(this,void 0,void 0,(function*(){let t=yield this._helpLanService.getValueSearch(e);this.searchResult=t.data?t.data:null}))}displaySearchResult(e){return Object(i.c)(this,void 0,void 0,(function*(){this.spinner=!0,this.searchResult=null,this.showMain=!1,this.searchQuestion=yield this._helpLanService.getSearchElements(e),this.currentlyOpenedItemIndex=-1,this.currentlyOpenedChildIndex=-1,this.spinner=!1}))}tabSwitch(e){this.parentTopic=e.id,this._helpLanService.unSubscribeEvent()}tabClick(e){this.nullData=!1,this.spinner=!0,this.childTopic=null,this.topicDetail=null,this.childTopicDetails=null,this.currentlyOpenedItemIndex=-1,this.currentlyOpenedChildIndex=-1,this.topicId=this.parentTopics.data[e.index].id,this.callToChild(this.topicId)}onChangeParentTopic(e){this.childTopic=null,this.topicDetail=null,this.childTopicDetails=null,this.callToChild(e)}callToChild(e){return Object(i.c)(this,void 0,void 0,(function*(){if(this.childTopic=null,this.topicDetail=null,this.childTopicDetails=null,this.childTopics=yield this._helpLanService.getChildTopic(e),null!=this.childTopics.data&&(this.childTopic=this.childTopics),this.questions=yield this._helpLanService.getTopicDetails(e),null!=this.questions.data){let e=this.questions;this.spinner=!1,this.topicDetail=e}else this.spinner=!1,this.noData()}))}noData(){null==this.childTopics.data&&null==this.questions.data&&(this.nullData=!0)}childQuestions(e){return Object(i.c)(this,void 0,void 0,(function*(){let t=e.id;this.childSpinner=!0,this.childDetails=yield this._helpLanService.getTopicDetails(t),this.childTopicDetails=this.childDetails,this.childSpinner=!1,this.currentlyOpenedItemIndex=-1}))}displayPreviousImage(e,t,n){let l=this.topicDetail.data.questions[e].answerSet[t].elements[n].carouselCount;this.topicDetail.data.questions[e].answerSet[t].elements[n].carouselCount=l<=0?this.topicDetail.data.questions[e].answerSet[t].elements[n].value.length-1:l-1}displayNextImage(e,t,n){let l=this.topicDetail.data.questions[e].answerSet[t].elements[n].carouselCount;this.topicDetail.data.questions[e].answerSet[t].elements[n].carouselCount=l>=this.topicDetail.data.questions[e].answerSet[t].elements[n].value.length-1?0:l+1}changeImageIndicator(e,t,n,l){this.topicDetail.data.questions[e].answerSet[t].elements[n].carouselCount=l}displayPreviousChildImage(e,t,n){let l=this.childDetails.data.questions[e].answerSet[t].elements[n].carouselCount;this.childDetails.data.questions[e].answerSet[t].elements[n].carouselCount=l<=0?this.childDetails.data.questions[e].answerSet[t].elements[n].value.length-1:l-1}displayNextChildImage(e,t,n){let l=this.childDetails.data.questions[e].answerSet[t].elements[n].carouselCount;this.childDetails.data.questions[e].answerSet[t].elements[n].carouselCount=l>=this.childDetails.data.questions[e].answerSet[t].elements[n].value.length-1?0:l+1}changeImageChildIndicator(e,t,n,l){this.childDetails.data.questions[e].answerSet[t].elements[n].carouselCount=l}displayPreviousSearchImage(e,t,n){let l=this.searchQuestion.data.questions[e].answerSet[t].elements[n].carouselCount;this.searchQuestion.data.questions[e].answerSet[t].elements[n].carouselCount=l<=0?this.searchQuestion.data.questions[e].answerSet[t].elements[n].value.length-1:l-1}displayNextSearchImage(e,t,n){let l=this.searchQuestion.data.questions[e].answerSet[t].elements[n].carouselCount;this.searchQuestion.data.questions[e].answerSet[t].elements[n].carouselCount=l>=this.searchQuestion.data.questions[e].answerSet[t].elements[n].value.length-1?0:l+1}changeImageSearchIndicator(e,t,n,l){this.searchQuestion.data.questions[e].answerSet[t].elements[n].carouselCount=l}displayNext(){this.thirdIndex+1>this.childTopic.data.length-1?(this.firstIndex=this.secondIndex,this.secondIndex=this.thirdIndex,this.thirdIndex=0):(this.firstIndex=this.secondIndex,this.secondIndex=this.thirdIndex,this.thirdIndex=this.thirdIndex+1)}displayPrevious(){this.firstIndex-1<=1?(this.firstIndex=this.secondIndex,this.secondIndex=this.thirdIndex,this.thirdIndex=this.thirdIndex>=this.childTopic.data.length-1?0:this.thirdIndex+1):this.firstIndex-1>1&&this.thirdIndex>=this.childTopic.data.length-1?(this.firstIndex=this.secondIndex,this.secondIndex=this.thirdIndex,this.thirdIndex=0):(this.firstIndex=this.secondIndex,this.secondIndex=this.thirdIndex,this.thirdIndex=this.secondIndex+1)}setOpened(e){this.currentlyOpenedItemIndex=e,this.panelOpenState=!0}setClosed(e){this.panelOpenState=!1,this.currentlyOpenedItemIndex===e&&(this.currentlyOpenedItemIndex=-1)}setChildOpened(e){this.currentlyOpenedChildIndex=e,this.panelOpenState=!0}setChildClosed(e){this.panelOpenState=!1,this.currentlyOpenedChildIndex===e&&(this.currentlyOpenedChildIndex=-1)}checkCarouselIndex(e,t,n){this.searchQuestion.data.questions[e].answerSet[t].elements[n].carouselCount||(this.searchQuestion.data.questions[e].answerSet[t].elements[n].carouselCount=0)}onExitSearch(){this.searchResult=null,this.searchValue=null}clearSearch(){this.searchVal="",this.onSearch(null),this.parentTopics.data.filter(e=>{if("General"==e.name)return e}),this.callToChild(this.topicId)}updateViews(e){e!=this.questionId&&(this._helpLanService.addViewCount(e),this.questionId=e)}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](f),m["\u0275\u0275directiveInject"](o.c),m["\u0275\u0275directiveInject"](x.a),m["\u0275\u0275directiveInject"](d.b))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-help-landing-page"]],decls:52,vars:9,consts:[[1,"container-fluid","help-index",3,"click"],[1,"containerhead","pb-4","pt-2","mt-5","mb-3"],[1,"row","pt-3","text-center",2,"width","100%"],[1,"col-3"],[1,"col"],[1,"text-center","top-heading","pt-4"],[1,"col-3","text-right"],["mat-icon-button","",3,"matMenuTriggerFor"],[1,"pointer"],["moreMenu","matMenu"],["mat-menu-item","",3,"click"],[1,"row","pt-4","pb-1","text-center",2,"width","100%"],[1,"col","ml-4","mr-4"],["appearance","outline",1,"example-form-field",2,"width","100%"],["matInput","","type","text","placeholder","Search",3,"ngModel","keyup","ngModelChange"],["class","mb-2 ","mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",3,"click",4,"ngIf"],["style","width:100%",4,"ngIf"],[1,"row","text-center","pt-4","pb-2"],[1,"col-2"],[1,"col","text-right"],["src","https://assets.kebs.app/images/help1.png","alt","",2,"width","53px","height","68px"],[1,"col","text-left","p-0"],["class","mt-4",4,"ngIf"],[1,"col","text-right","p-0"],["src","https://assets.kebs.app/images/help2.png","alt","",2,"width","68px","height","68px"],[1,"col","text-left"],["src","https://assets.kebs.app/images/help3.png","alt","",2,"width","91px","height","68px"],[1,"mt-4"],[1,"title-property"],[1,"content-property"],[1,"col","pt-4"],["class","row pt-3 mb-3",4,"ngIf"],["class","main-section",4,"ngIf"],[1,"spinner-class"],["class","pt-4 pb-4 text-center",4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",1,"mb-2",3,"click"],[2,"width","100%"],["class","row search","style","width:100%;",4,"ngIf"],[1,"row","search",2,"width","100%"],[1,"ml-2","shadow-lg","rounded","col","search-result","p-0"],["class","row",4,"ngFor","ngForOf"],[1,"col-4"],[1,"row"],[2,"width","100%",3,"click"],[1,"search-hover","p-2",2,"width","100%","height","100%",3,"innerHtml"],[4,"ngIf"],[1,"content-property","inline"],[1,"row","pt-3","mb-3"],["class","pb-2",4,"ngFor","ngForOf"],[1,"pb-2"],[1,"pl-3","question-search",3,"innerHtml"],[4,"ngFor","ngForOf"],["class","pl-3 answer-title",4,"ngIf"],[1,"pl-3","answer-title"],[1,"pl-3","pt-2","answer-section",3,"innerHtml"],[1,"pl-3","answer-section"],[1,"row","pt-2"],[1,"col-3","mt-5"],[1,"pointer","text-right",3,"click"],["class","align-middle",4,"ngIf"],[2,"width","100%","height","100%"],[1,"text-center"],[1,"pointer",3,"click"],[1,"align-middle"],[1,"move-in-right",2,"width","100%","height","180px",3,"src"],[1,"pt-1","question-topic","text-center"],[1,"answer-section","text-center"],["class","indicator m-1",3,"click",4,"ngIf"],["class","indicator-notclick m-1",3,"click",4,"ngIf"],[1,"indicator","m-1",3,"click"],[1,"indicator-notclick","m-1",3,"click"],["class","pointer text-right",3,"click",4,"ngIf"],[1,"row",2,"width","100%","height","100%"],["class","pointer",3,"click",4,"ngIf"],["allowfullscreen","",1,"move-in-right","bg-color",2,"width","100%","height","180px",3,"src"],[1,"main-section"],[1,"row","ml-2","mr-2","mb-5"],["mat-align-tabs","center",1,"tab-section",2,"width","100%",3,"selectedTabChange"],[3,"click"],["mat-tab-label","",3,"click"],["matTabContent",""],[1,"icon-title"],[1,"pl-2"],[1,"col-8"],[1,"row","nodata","text-center","mt-5"],["class","row pt-3 pb-1",4,"ngIf"],[1,"row","pt-3","pb-1"],["class","col-3",4,"ngFor","ngForOf"],[1,"child-topics","pointer","shadow",3,"click"],[1,"col-1","p-0","mt-1"],[1,"pt-1","text-center","border-icon"],[1,"col","content"],[1,"content-title"],[1,"content-description"],["class","col-4",4,"ngFor","ngForOf"],[1,"content-title","content"],[1,"col-1"],[1,"pointer","pt-4","text-center",3,"click"],[1,"col-10","pt-3"],["class","row",4,"ngIf"],[1,"pt-4","text-center","pointer",3,"click"],[1,"child-topics","move-in-right","pointer","shadow",3,"click"],[1,"col-1","p-0"],[1,"mt-1"],[1,"pt-4","pb-4","text-center"],["role","status",1,"spinner-border"],[1,"sr-only"],[1,"mt-4","mb-2","pl-2","faq-heading"],["class","shadow-none","style","border-top: 1px solid #E8E9EE;","hideToggle","true",3,"opened","closed",4,"ngFor","ngForOf"],["hideToggle","true",1,"shadow-none",2,"border-top","1px solid #E8E9EE",3,"opened","closed"],[1,"pt-4","pb-3","pl-2",3,"click"],[1,"row",2,"width","100%"],[1,"col-11"],[1,"expanded-question",3,"innerHtml"],[1,"not-expanded-question",3,"innerHtml"],[1,"pl-3","pt-2","answer-section"]],template:function(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275listener"]("click",(function(){return t.onExitSearch()})),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div"),m["\u0275\u0275elementStart"](3,"div",2),m["\u0275\u0275element"](4,"div",3),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275elementStart"](6,"span",5),m["\u0275\u0275text"](7,"How Can We help You?"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",6),m["\u0275\u0275elementStart"](9,"span",7),m["\u0275\u0275elementStart"](10,"mat-icon",8),m["\u0275\u0275text"](11,"more_vert"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](12,"mat-menu",8,9),m["\u0275\u0275elementStart"](14,"span",10),m["\u0275\u0275listener"]("click",(function(){return t.openUpdateDataDialog()})),m["\u0275\u0275elementStart"](15,"mat-icon"),m["\u0275\u0275text"](16,"add_task"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](17,"span"),m["\u0275\u0275text"](18,"Update info"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](19,"div",11),m["\u0275\u0275element"](20,"div",3),m["\u0275\u0275elementStart"](21,"div",12),m["\u0275\u0275elementStart"](22,"mat-form-field",13),m["\u0275\u0275elementStart"](23,"input",14),m["\u0275\u0275listener"]("keyup",(function(e){return t.onSearch(e)}))("ngModelChange",(function(e){return t.searchVal=e})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](24,De,3,0,"button",15),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](25,"div",3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](26,Ve,2,1,"div",16),m["\u0275\u0275elementStart"](27,"div",17),m["\u0275\u0275element"](28,"div",18),m["\u0275\u0275elementStart"](29,"div",19),m["\u0275\u0275element"](30,"img",20),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](31,"div",21),m["\u0275\u0275template"](32,$e,2,1,"div",22),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](33,"div",23),m["\u0275\u0275element"](34,"img",24),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](35,"div",25),m["\u0275\u0275template"](36,je,2,1,"div",22),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](37,"div",19),m["\u0275\u0275element"](38,"img",26),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](39,"div",21),m["\u0275\u0275elementStart"](40,"div",27),m["\u0275\u0275elementStart"](41,"div",28),m["\u0275\u0275text"](42,"Missing Something?"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](43,"div",29),m["\u0275\u0275text"](44,"Let Us know"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](45,"div",30),m["\u0275\u0275element"](46,"div",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](47,"div"),m["\u0275\u0275template"](48,pt,5,1,"div",31),m["\u0275\u0275template"](49,Kn,5,1,"div",32),m["\u0275\u0275elementStart"](50,"div",33),m["\u0275\u0275template"](51,Jn,4,0,"div",34),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275reference"](13);m["\u0275\u0275advance"](9),m["\u0275\u0275property"]("matMenuTriggerFor",e),m["\u0275\u0275advance"](14),m["\u0275\u0275property"]("ngModel",t.searchVal),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.searchVal),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",t.searchResult),m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngIf",t.generalDetails&&(null==t.generalDetails?null:t.generalDetails.data)),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",t.generalDetails),m["\u0275\u0275advance"](12),m["\u0275\u0275property"]("ngIf",t.searchQuestion&&(null==t.searchQuestion?null:t.searchQuestion.data)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",1==t.showMain),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",1==t.spinner)}},directives:[Oe.f,g.a,Oe.g,Oe.d,be.c,C.b,a.e,a.v,a.y,l.NgIf,_e.a,be.i,l.NgForOf,Pe.c,Pe.a,Pe.d,Pe.b,Te.a,Me.a,Te.c,Te.g],pipes:[I],styles:[".help-index[_ngcontent-%COMP%]   .child-topics[_ngcontent-%COMP%]{background:#f6f8fb}.help-index[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:100%}.help-index[_ngcontent-%COMP%]   .inline[_ngcontent-%COMP%]{display:inline-block}.help-index[_ngcontent-%COMP%]   .answer-title[_ngcontent-%COMP%]{font-size:13px;letter-spacing:.02em;color:#526179;text-transform:capitalize}.help-index[_ngcontent-%COMP%]   .carouselPointer[_ngcontent-%COMP%]{display:inline-block;vertical-align:middle}.help-index[_ngcontent-%COMP%]   .faq-heading[_ngcontent-%COMP%]{font-size:14px;color:#45546e;font-weight:700}.help-index[_ngcontent-%COMP%]   .tab-section[_ngcontent-%COMP%]{z-index:-1}.help-index[_ngcontent-%COMP%]   .search[_ngcontent-%COMP%]{position:absolute;z-index:2}.help-index[_ngcontent-%COMP%]   .search-hover[_ngcontent-%COMP%]:hover{border-left:3px solid #cf0001;background-color:#f6f8fb}.help-index[_ngcontent-%COMP%]   .search-result[_ngcontent-%COMP%]{background-color:#fff;cursor:pointer}.help-index[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%]{font-size:medium;vertical-align:bottom}.help-index[_ngcontent-%COMP%]   input[type=text][_ngcontent-%COMP%]{width:100%;box-sizing:border-box;border:#fff;transition:.5s;outline:none}.help-index[_ngcontent-%COMP%]   .top-heading[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;font-style:normal;font-weight:500;font-size:24px;text-align:center;letter-spacing:.02em;text-transform:capitalize;color:#526179}.help-index[_ngcontent-%COMP%]   .containerhead[_ngcontent-%COMP%]{background:#f6f8fb}.help-index[_ngcontent-%COMP%]     .mat-ink-bar{display:none}.help-index[_ngcontent-%COMP%]     .mat-tab-label{background-color:initial;display:flex;justify-content:center;font-weight:500;color:#8b95a5}.help-index[_ngcontent-%COMP%]     .mat-tab-label, .help-index[_ngcontent-%COMP%]     .mat-tab-label.mat-tab-label-active{font-family:Roboto,sans-serif;font-style:normal;font-size:14px;letter-spacing:.02em;text-transform:capitalize}.help-index[_ngcontent-%COMP%]     .mat-tab-label.mat-tab-label-active{border-bottom:3px solid #cf0001;font-weight:700;line-height:16px;color:#cf0001}.help-index[_ngcontent-%COMP%]   .mat-tab-labels[_ngcontent-%COMP%]{display:flex;justify-content:center}.help-index[_ngcontent-%COMP%]     .mat-expansion-panel{border-radius:0;box-shadow:0}.help-index[_ngcontent-%COMP%]     .mat-dialog-container{direction:ltr;padding:0;margin:0;border:#fff;box-shadow:none}.help-index[_ngcontent-%COMP%]   .shadow-none[_ngcontent-%COMP%]{box-shadow:0}.help-index[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{border-radius:0}.help-index[_ngcontent-%COMP%]   .col-search[_ngcontent-%COMP%]{border:1px solid #e8e9ee;border-radius:6px}.help-index[_ngcontent-%COMP%]   .title-property[_ngcontent-%COMP%]{font-weight:400;font-size:12px}.help-index[_ngcontent-%COMP%]   .content-property[_ngcontent-%COMP%], .help-index[_ngcontent-%COMP%]   .title-property[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;font-style:normal;letter-spacing:.02em;text-transform:capitalize;color:#45546e}.help-index[_ngcontent-%COMP%]   .content-property[_ngcontent-%COMP%]{font-weight:700;font-size:15px}.help-index[_ngcontent-%COMP%]   .btn-property[_ngcontent-%COMP%]{background:linear-gradient(90deg,#d84343 10%,#cf0001 105.29%);border-radius:8px;font-family:Roboto,sans-serif;font-style:normal;font-weight:700;font-size:12px;line-height:16px;letter-spacing:-.02em;text-transform:capitalize;color:#fff}.help-index[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{margin-bottom:0}.help-index[_ngcontent-%COMP%]   .kebs-question[_ngcontent-%COMP%]{font-weight:700;font-size:14px;text-transform:capitalize;color:#45546e}.help-index[_ngcontent-%COMP%]   .kebs-answer[_ngcontent-%COMP%], .help-index[_ngcontent-%COMP%]   .kebs-question[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;font-style:normal;line-height:16px;letter-spacing:.02em}.help-index[_ngcontent-%COMP%]   .kebs-answer[_ngcontent-%COMP%]{font-weight:400;font-size:12px;color:#526179}.help-index[_ngcontent-%COMP%]   .li-kebs[_ngcontent-%COMP%]{color:#cf0001}.help-index[_ngcontent-%COMP%]   .quesAndAnsHeading[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;font-style:normal;font-weight:700;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#45546e}.help-index[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]{padding:0}.help-index[_ngcontent-%COMP%]   .expanded-question[_ngcontent-%COMP%]{font-weight:550;font-size:12px;color:#cf0001}.help-index[_ngcontent-%COMP%]   .answer-section[_ngcontent-%COMP%], .help-index[_ngcontent-%COMP%]   .expanded-question[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;font-style:normal;letter-spacing:.02em}.help-index[_ngcontent-%COMP%]   .answer-section[_ngcontent-%COMP%]{font-weight:400;font-size:13px;color:#45546e}.help-index[_ngcontent-%COMP%]   .question-search[_ngcontent-%COMP%]{font-size:14px;color:#cf0001}.help-index[_ngcontent-%COMP%]   .not-expanded-question[_ngcontent-%COMP%], .help-index[_ngcontent-%COMP%]   .question-search[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;font-style:normal;font-weight:550;letter-spacing:.02em}.help-index[_ngcontent-%COMP%]   .not-expanded-question[_ngcontent-%COMP%]{font-size:12px;color:#526179}.help-index[_ngcontent-%COMP%]   .important-content[_ngcontent-%COMP%]{background:#f6f8fb;border-radius:8px;cursor:pointer}.help-index[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-weight:700;font-size:14px;color:#526179}.help-index[_ngcontent-%COMP%]   .content-description[_ngcontent-%COMP%], .help-index[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-family:Roboto,sans-serif;font-style:normal;line-height:16px;letter-spacing:.02em;text-transform:capitalize}.help-index[_ngcontent-%COMP%]   .content-description[_ngcontent-%COMP%]{font-weight:400;font-size:11px;color:#5f6c81}.help-index[_ngcontent-%COMP%]   .border-icon[_ngcontent-%COMP%]{border:#cf0001;border-radius:40%;background-color:#cf0001;color:#fff;font-size:18px}.help-index[_ngcontent-%COMP%]   .icon-title[_ngcontent-%COMP%]{vertical-align:middle;font-size:20px}.help-index[_ngcontent-%COMP%]   .move-in-right[_ngcontent-%COMP%]{animation:moveInRight .5s ease-out forwards;border-radius:5%}.help-index[_ngcontent-%COMP%]   .bg-color[_ngcontent-%COMP%]{background-color:#dadce2}@keyframes moveInRight{0%{opacity:0;transform:translateX(150px)}80%{transform:translateX(-20px)}to{opacity:1;transform:translate(0)}}.help-index[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%]{width:20px;border-radius:8px}.help-index[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%], .help-index[_ngcontent-%COMP%]   .indicator-notclick[_ngcontent-%COMP%]{height:8px;background-color:#bbb;display:inline-block;cursor:pointer}.help-index[_ngcontent-%COMP%]   .indicator-notclick[_ngcontent-%COMP%]{width:8px;border-radius:50%}.help-index[_ngcontent-%COMP%]   .pointer[_ngcontent-%COMP%]{cursor:pointer}.help-index[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.help-index[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.help-index[_ngcontent-%COMP%]     .mat-tab-body-content{overflow:hidden!important}.help-index[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{color:#cf0001}.help-index[_ngcontent-%COMP%]   .nodata[_ngcontent-%COMP%]{color:#888a8f;font-size:18px}"]}),e})()},{path:"help-index",component:o.a}];let Zn=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(Wn)],r.k]}),e})(),el=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[],imports:[[l.CommonModule,o.b,a.p,a.E,c.d,d.g,Pe.g,g.b,_e.b,Zn,h.b.forRoot({timeOut:2e3,positionClass:"toast-bottom-right",preventDuplicates:!0}),Te.b,C.c,S.d,Me.d,Oe.e,be.e,v.b]]}),e})()}}]);