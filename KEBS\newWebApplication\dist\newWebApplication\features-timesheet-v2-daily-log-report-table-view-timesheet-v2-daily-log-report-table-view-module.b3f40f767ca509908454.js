(window.webpackJsonp=window.webpackJsonp||[]).push([[686,634,858],{NJ67:function(e,t,o){"use strict";o.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},w6wj:function(e,t,o){"use strict";o.r(t),o.d(t,"TimesheetV2DailyLogReportTableViewModule",(function(){return ee}));var i=o("ofXK"),r=o("tyNb"),s=o("mrSG"),a=o("3Pt+"),n=o("fXoL"),l=o("jtHE"),c=o("XNiG"),h=o("NJ67"),d=o("1G5W"),p=o("xG9w"),g=o("kmnG"),u=o("d3UM"),v=o("FKr1"),m=o("WJ5W"),f=o("bSwM");function y(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.placeholder)}}const S=function(){return{standalone:!0}};function D(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"div",6),n["\u0275\u0275elementStart"](1,"mat-checkbox",7),n["\u0275\u0275listener"]("ngModelChange",(function(t){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().allSelected=t}))("change",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().toggleAllSelection()})),n["\u0275\u0275text"](2,"Select All"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}if(2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngModel",e.allSelected)("ngModelOptions",n["\u0275\u0275pureFunction0"](2,S))}}function w(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",8),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,o=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("value",e[o.idKey]),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e[o.valueKey]," ")}}let b=(()=>{class e extends h.a{constructor(){super(),this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.filteredList=new l.a,this.showLabel=!1,this.list=[],this.required=!1,this.idKey="id",this.valueKey="name",this.disabled=!1,this.selectAllFlag=!0,this.valueChange=new n.EventEmitter,this._onDestroy=new c.b,this.allSelected=!1}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(e){e.list&&this.list&&this.filteredList.next(this.list.slice())}writeValue(e){void 0!==e&&this.fieldCtrl.setValue(e,{emitEvent:!1})}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>{var o,i;return(null===(i=null===(o=t[this.valueKey])||void 0===o?void 0:o.toLowerCase())||void 0===i?void 0:i.indexOf(e))>-1}))):this.filteredList.next(this.list.slice())}emptyAllSelection(){this.fieldCtrl.patchValue([]),this.allSelected=!1}toggleAllSelection(){this.fieldCtrl.patchValue(this.allSelected?p.pluck(this.list,"id"):[])}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search"]],inputs:{showLabel:"showLabel",list:"list",placeholder:"placeholder",required:"required",idKey:"idKey",valueKey:"valueKey",disabled:"disabled",selectAllFlag:"selectAllFlag"},outputs:{valueChange:"valueChange"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:12,consts:[["appearance","outline",2,"width","100%"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled","multiple"],["noEntriesFoundLabel","No Results Found",3,"formControl","placeholderLabel"],["class","select-all",4,"ngIf"],[3,"value",4,"ngFor","ngForOf"],[1,"select-all"],[3,"ngModel","ngModelOptions","ngModelChange","change"],[3,"value"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275template"](1,y,2,1,"mat-label",1),n["\u0275\u0275elementStart"](2,"mat-select",2),n["\u0275\u0275elementStart"](3,"mat-option"),n["\u0275\u0275element"](4,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](5,D,3,3,"div",4),n["\u0275\u0275template"](6,w,2,2,"mat-option",5),n["\u0275\u0275pipe"](7,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.showLabel),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled)("multiple",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.selectAllFlag),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](7,10,t.filteredList)))},directives:[g.c,i.NgIf,u.c,a.v,a.k,a.F,v.p,m.a,i.NgForOf,g.g,f.a,a.y],pipes:[i.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.select-all[_ngcontent-%COMP%]{margin:5px 17px}"]}),e})();var C=o("Kj3r"),_=o("wd/R"),O=o("1yaQ"),I=o("PSD3"),x=o.n(I),F=o("EUZL"),R=o("1A3m"),E=o("XXEo"),M=o("tk/3");let T=(()=>{class e{constructor(e){this._http=e}getErrorDetails(){return this._http.post("/api/timesheetv2/Master/getErrorDetails",{})}getTimesheetStatus(){return this._http.post("/api/timesheetv2/Master/getTimesheetStatus",{})}getEmployeeDetailsForDailyLogReport(e,t,o,i,r,s){return this._http.post("/api/timesheetv2/Master/getEmployeeDetailsForDailyLogReport",{startDate:e,endDate:t,aid:o,oid:i,projectIdForFilter:r,sowIdForFilter:s})}getProjectDetailsForDailyLogReport(e,t,o,i){return this._http.post("/api/timesheetv2/Master/getProjectDetailsForDailyLogReport",{startDate:e,endDate:t,aid:o,oid:i})}getItemDetailsForDailyLogReport(e,t,o,i,r){return this._http.post("/api/timesheetv2/Master/getItemDetailsForDailyLogReport",{startDate:e,endDate:t,aid:o,oid:i,projectIdForFilter:r})}getTsDailyLogReportItemData(e,t,o,i,r,s,a,n,l,c,h,d,p,g,u,v,m,f,y){return this._http.post("/api/tsNodeV2Primary/getTsDailyLogReportItemData",{startDate:e,endDate:t,aid:o,oid:i,projectIdForFilter:r,sowIdForFilter:s,aidForFilter:a,statusForFilter:n,division:l,subDivision:c,region:h,level:d,entity:p,salesRegionForFilter:g,workstream:u,approver:v,page:m,isForDownload:f,pageSize:y})}getDivision(){return this._http.post("/api/timesheetv2/Master/getDivision",{})}getSubDivision(e){return this._http.post("/api/timesheetv2/Master/getSubDivisionForReport",{division:e})}getRegion(){return this._http.post("/api/timesheetv2/Master/getRegion",{})}getEntity(){return this._http.post("/api/timesheetv2/Master/getEntity",{})}getLevel(){return this._http.post("/api/timesheetv2/Master/getLevel",{})}getTsDailyLogReportPageData(e,t,o,i,r,s,a,n,l,c,h,d,p,g,u,v){return this._http.post("/api/tsNodeV2Primary/getTsDailyLogReportPageData",{startDate:e,endDate:t,aid:o,oid:i,projectIdForFilter:r,sowIdForFilter:s,aidForFilter:a,statusForFilter:n,division:l,subDivision:c,region:h,level:d,entity:p,salesRegionForFilter:g,workstream:u,approver:v})}getSalesRegion(){return this._http.post("/api/timesheetv2/Master/getSalesRegion",{})}getWorkstream(e,t,o){return this._http.post("/api/timesheetv2/Master/getWorkstream",{sow:e,startDate:t,endDate:o})}getApproverDetailsForDailyLogReport(e,t,o,i,r,s){return this._http.post("/api/timesheetv2/Master/getApproverDetailsForDailyLogReport",{startDate:e,endDate:t,aid:o,oid:i,projectIdForFilter:r,sowIdForFilter:s})}getTimesheetReportAccess(e){return this._http.post("/api/timesheetv2/Reports/getTimesheetReportAccess",{applicationId:e})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275inject"](M.c))},e.\u0275prov=n["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var L=o("HmYF"),P=o("JqCM"),j=o("NFeN"),A=o("xHqg"),k=o("iadO"),Y=o("ZzPI"),N=o("6t9p");const B=["dataGrid"],J=["stepper"];function V(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",14),n["\u0275\u0275element"](1,"app-multi-select-search",71),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",e.portfolio)("list",e.portfolioList)("selectAllFlag",!1)}}function W(e,t){if(1&e&&n["\u0275\u0275element"](0,"dxi-column",72),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("caption",e.zifo_sow_refernece_caption)}}function H(e,t){1&e&&n["\u0275\u0275element"](0,"dxi-column",73)}function z(e,t){1&e&&n["\u0275\u0275element"](0,"dxi-column",74)}const q=function(e){return{"pointer-events":e}},G={parse:{dateInput:"DD-MMM-YYYY"},display:{dateInput:"DD-MMM-YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},K=[{path:"",component:(()=>{class e{constructor(e,t,o,i,r,s){this.router=e,this.toastService=t,this.authService=o,this.reportService=i,this.excelService=r,this.spinnerService=s,this.errorData=[],this._onDestroy=new c.b,this.statusList=[],this.employeeList=[],this.portfolioList=[],this.sowList=[],this.logData=[],this.divisionList=[],this.subDivisionList=[],this.regionList=[],this.levelList=[],this.entityList=[],this.salesRegionList=[],this.workstreamList=[],this.approverList=[],this.downloadData=[],this.disableDownloadButton=!1,this.isInitialLoading=!0,this.apiInProgress=!1,this.portfolioFilterApiInProgress=!1,this.sowFilterApiInProgress=!1,this.employeeFilterApiInProgress=!1,this.approverFilterApiInProgress=!1,this.subDivisionApiInProgress=!1,this.startDate=new a.j(null),this.endDate=new a.j(null),this.portfolio=new a.j([]),this.sow=new a.j([]),this.employee=new a.j([]),this.status=new a.j([]),this.division=new a.j([]),this.subDivision=new a.j([]),this.region=new a.j([]),this.level=new a.j([]),this.entity=new a.j([]),this.salesRegion=new a.j([]),this.workstream=new a.j([]),this.approver=new a.j([]),this.totalRecords=0,this.pages=0,this.defaultPageSize=1e6,this.showDownloadIcon=!1,this.sow_placeholder="Project Code - Project Name",this.entity_placeholder="Company",this.division_placeholder="Business Unit",this.sub_division_placeholder="Department",this.sow_ref_caption="Project Code",this.sow_description_caption="SOW Description",this.entity_caption="Zifo Legal Entity",this.activity_caption="Activity Name",this.zifo_sow_refernece_caption="Zifo SOW Reference",this.workstream_Caption="Workstream",this.report_data_version=1,this.color="#EE4961",this.showSOWReference=!0,this.showWorkstream=!0,this.showLocation=!1,this.showWeekOff=!1}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){yield this.getTimesheetReportAccess(),document.documentElement.style.setProperty("--color",this.color),this.currentMonthStartDate=_().startOf("month"),this.currentMonthEndDate=_().endOf("month"),this.startDate.setValue(this.currentMonthStartDate),this.endDate.setValue(this.currentMonthEndDate),this.calculateDynamicContentHeight(),this.currentUser=this.authService.getProfile().profile,this.oid=this.currentUser.oid,this.aid=this.currentUser.aid,this.token=this.authService.getJwtToken(),this.getFilterData(),yield this.getErrorDetails(),yield Promise.all([this.getEntity(),this.getSalesRegion(),this.getTimesheetStatus(),this.getDivision(),this.getSubDivision(),this.getRegion(),this.getLevel(),this.getProjectDetailsForDailyLogReport(),this.getItemDetailsForDailyLogReport(),this.getEmployeeDetailsForDailyLogReport(),this.getApproverDetailsForDailyLogReport(),this.getWorkstream()]).then(e=>{this.isInitialLoading=!1}),this.portfolio.valueChanges.pipe(Object(C.a)(1e3)).subscribe(()=>Object(s.c)(this,void 0,void 0,(function*(){null!=this.startDate.value&&null!=this.endDate.value&&this.getItemDetailsForDailyLogReport()}))),this.sow.valueChanges.pipe(Object(C.a)(1e3)).subscribe(()=>Object(s.c)(this,void 0,void 0,(function*(){null!=this.startDate.value&&null!=this.endDate.value&&(this.getWorkstream(),this.getEmployeeDetailsForDailyLogReport(),this.getApproverDetailsForDailyLogReport())}))),this.division.valueChanges.pipe(Object(C.a)(1e3)).subscribe(()=>Object(s.c)(this,void 0,void 0,(function*(){null!=this.startDate.value&&null!=this.endDate.value&&this.division.value&&this.division.value.length>0&&this.getSubDivision()})))}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-104+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicGridReportHeight=window.innerHeight-240+"px",document.documentElement.style.setProperty("--dynamicGridReportHeight",this.dynamicGridReportHeight),this.dynamicReportHeight=window.innerHeight-223+"px",document.documentElement.style.setProperty("--dynamicReportHeight",this.dynamicReportHeight)}goToReportsMainScreen(){this.disableDownloadButton=!1,this.router.navigate(["/main/reports"])}goToPreviousScreen(){this.disableDownloadButton=!1,this.onDeleteFilters(1),this.clearSortAndFilters(),this.getFilterData(),this.stepper.reset()}onKeyDownDateSearch(e){e.preventDefault()}onChangeInDate(e){null!=this.startDate.value&&null!=this.endDate.value&&(this.getProjectDetailsForDailyLogReport(),this.getItemDetailsForDailyLogReport())}onDeleteFilters(e){this.resetSpecificSearchComponent(0),this.resetSpecificSearchComponent(1),this.resetSpecificSearchComponent(2),this.resetSpecificSearchComponent(3),this.resetSpecificSearchComponent(4),this.resetSpecificSearchComponent(5),this.resetSpecificSearchComponent(6),this.resetSpecificSearchComponent(7),this.resetSpecificSearchComponent(8),this.logData=[],0==e&&(localStorage.removeItem("tsv2-daily-log-report-portfolio"),localStorage.removeItem("tsv2-daily-log-report-sow"),localStorage.removeItem("tsv2-daily-log-report-employee"),localStorage.removeItem("tsv2-daily-log-report-status"),localStorage.removeItem("tsv2-daily-log-report-division"),localStorage.removeItem("tsv2-daily-log-report-subDivision"),localStorage.removeItem("tsv2-daily-log-report-region"),localStorage.removeItem("tsv2-daily-log-report-level"),localStorage.removeItem("tsv2-daily-log-report-entity"),localStorage.removeItem("tsv2-daily-log-report-salesRegion"),localStorage.removeItem("tsv2-daily-log-report-workstream"),localStorage.removeItem("tsv2-daily-log-report-approver"))}clearSortAndFilters(){this.dataGrid.instance.clearSorting(),this.dataGrid.instance.clearFilter()}resetSpecificSearchComponent(e){if(!this.isInitialLoading&&this.multiSelectSearch){let t=this.multiSelectSearch.toArray()[e];t&&t.emptyAllSelection()}}onGenerateReport(e){return Object(s.c)(this,void 0,void 0,(function*(){null!=this.startDate.value&&null!=this.endDate.value?this.portfolioFilterApiInProgress||this.sowFilterApiInProgress||this.employeeFilterApiInProgress||this.approverFilterApiInProgress?this.toastService.showInfo("Refreshing report with your input. Please wait a moment before clicking Generate Report again!","",3e3):(this.disableDownloadButton=!0,yield this.getTsDailyLogReportPageData(),e?this.downloadReport():0!=this.division.value.length||0!=this.subDivision.value.length||0!=this.region.value.length||0!=this.level.value.length||0!=this.employee.value.length||0!=this.sow.value.length||0!=this.portfolio.value.length||0!=this.status.value.length||0!=this.entity.value.length||0!=this.workstream.value.length||0!=this.approver.value.length?this.sow.value.length>10||this.portfolio.value.length>10?x.a.fire({title:"Timesheet Daily Log Report",text:"To view the report, you have to select maximum 10 Project Code - Project Name Or else, you can click on download button to download the report",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1}))):this.division.value.length>1?x.a.fire({title:"Timesheet Daily Log Report",text:"Only one Business Unit can be selected or download button to download the report",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1}))):this.region.value.length>1?x.a.fire({title:"Timesheet Daily Log Report",text:"Only one Region can be selected or download button to download the report",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1}))):this.level.value.length>1?x.a.fire({title:"Timesheet Daily Log Report",text:"Only one level can be selected or download button to download the report",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1}))):this.entity.value.length>1?x.a.fire({title:"Timesheet Daily Log Report",text:"Only one Company can be selected or download button to download the report",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1}))):this.employee.value.length>10?x.a.fire({title:"Timesheet Daily Log Report",text:"Maximum 10 employees timesheet data can be view in report or click download button to download the report",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1}))):this.totalRecords>5e3?x.a.fire({title:"Timesheet Daily Log Report",text:"The Record Size is Huge, Kindly use the download option for better performance",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1}))):(yield this.getTsDailyLogReportItemData(1,!1,this.defaultPageSize),this.stepper.next()):x.a.fire({title:"Timesheet Daily Log Report",text:"To view the report, you have to select a combination of inputs such as date range with either Project Code - Project Name or date range with either with Business Unit or Department or Region or Employee Or else, you can click on download button to download the report",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Download Report",cancelButtonText:"Close"}).then(e=>Object(s.c)(this,void 0,void 0,(function*(){e.isConfirmed?this.downloadReport():this.disableDownloadButton=!1})))):this.toastService.showWarning("Date Range is Mandatory!","")}))}onExporting(e){e.fileName="Timesheet Daily Log Report - Table View"}getErrorDetails(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.reportService.getErrorDetails().pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType&&t.data.length>0&&(this.errorData=t.data),e(!0)},error:e=>{console.log(e),t()}}))}))}getTimesheetStatus(){return Object(s.c)(this,void 0,void 0,(function*(){return this.statusList=[],this.resetSpecificSearchComponent(9),new Promise((e,t)=>this.reportService.getTimesheetStatus().pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{if("S"==t.messType){if(t.data&&t.data.length>0){let e=t.data.filter(e=>4==e.id||2==e.id||1==e.id||3==e.id);this.statusList=e,this.color=t.colorCode,document.documentElement.style.setProperty("--color",this.color);let o=localStorage.getItem("tsv2-daily-log-report-status")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-status")):[];this.status.setValue(o)}2==t.config_val&&(this.report_data_version=t.config_val,this.entity_placeholder="Employee Entity",this.entity_caption="Employee Entity",this.division_placeholder="Employee Division",this.sub_division_placeholder="Employee Sub Division",this.activity_caption="Task Name",this.sow_placeholder="Charge Code",this.sow_ref_caption="Charge Code",this.sow_description_caption="Charge Code Description",this.zifo_sow_refernece_caption="Charge Code Reference",this.workstream_Caption="Phase",this.showWorkstream=!1,this.showLocation=!1,this.showWeekOff=!1,this.showSOWReference=t.showWorstream,this.color=t.colorCode,document.documentElement.style.setProperty("--color",this.color)),3==t.config_val&&(this.report_data_version=t.config_val,this.entity_placeholder="Employee Entity",this.entity_caption="Employee Entity",this.division_placeholder="Employee Division",this.sub_division_placeholder="Employee Sub Division",this.activity_caption="Task Name",this.sow_placeholder="Cost Centre",this.sow_ref_caption="Cost Centre",this.sow_description_caption="Project Description",this.zifo_sow_refernece_caption="Cost Centre Reference",this.workstream_Caption="Phase",this.showSOWReference=!1,this.showWorkstream=t.showWorstream,this.color=t.colorCode,this.showLocation=t.location,this.showWeekOff=!0,document.documentElement.style.setProperty("--color",this.color))}else this.report_data_version=1,this.color="#EE4961",this.toastService.showInfo("Timesheet App Message",t.messText,3e3);e(!0)},error:e=>{var o;this.report_data_version=1,this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),t()}}))}))}getEmployeeDetailsForDailyLogReport(){return Object(s.c)(this,void 0,void 0,(function*(){return this.employeeFilterApiInProgress=!0,this.employeeList=[],this.resetSpecificSearchComponent(8),new Promise((e,t)=>this.reportService.getEmployeeDetailsForDailyLogReport(_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value,this.sow.value).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{if("S"==t.messType){this.employeeList=t.data&&t.data.length>0?t.data:[];let e=localStorage.getItem("tsv2-daily-log-report-employee")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-employee")):[];this.employee.setValue(e)}else{let e=localStorage.getItem("tsv2-daily-log-report-employee")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-employee")):[];this.employee.setValue(e),this.toastService.showInfo("Timesheet App Message",t.messText,3e3)}this.employeeFilterApiInProgress=!1,e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),this.employeeFilterApiInProgress=!1,t()}}))}))}getProjectDetailsForDailyLogReport(){return Object(s.c)(this,void 0,void 0,(function*(){return this.portfolioFilterApiInProgress=!0,this.portfolioList=[],this.resetSpecificSearchComponent(1),new Promise((e,t)=>this.reportService.getProjectDetailsForDailyLogReport(_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.portfolioList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),this.portfolioFilterApiInProgress=!1,e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),this.portfolioFilterApiInProgress=!1,t()}}))}))}getItemDetailsForDailyLogReport(){return Object(s.c)(this,void 0,void 0,(function*(){return this.sowFilterApiInProgress=!0,this.sowList=[],this.resetSpecificSearchComponent(2),new Promise((e,t)=>this.reportService.getItemDetailsForDailyLogReport(_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{if("S"==t.messType){this.sowList=t.data&&t.data.length>0?t.data:[];let e=localStorage.getItem("tsv2-daily-log-report-sow")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-sow")):[];this.sow.setValue(e)}else{let e=localStorage.getItem("tsv2-daily-log-report-sow")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-sow")):[];this.sow.setValue(e),this.toastService.showInfo("Timesheet App Message",t.messText,3e3)}this.sowFilterApiInProgress=!1,e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),this.sowFilterApiInProgress=!1,t()}}))}))}getTsDailyLogReportItemData(e,t,o){return Object(s.c)(this,void 0,void 0,(function*(){return this.apiInProgress=!0,new Promise((i,r)=>this.reportService.getTsDailyLogReportItemData(_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value&&this.portfolio.value.length>0?this.portfolio.value:[],this.sow.value&&this.sow.value.length>0?this.sow.value:[],this.employee.value&&this.employee.value.length>0?this.employee.value:[],this.status.value&&this.status.value.length>0?this.status.value:[1,2,3,4],this.division.value&&this.division.value.length>0?this.division.value:[],this.subDivision.value&&this.subDivision.value.length>0?this.subDivision.value:[],this.region.value&&this.region.value.length>0?this.region.value:[],this.level.value&&this.level.value.length>0?this.level.value:[],this.entity.value&&this.entity.value.length>0?this.entity.value:[],this.salesRegion.value&&this.salesRegion.value.length>0?this.salesRegion.value:[],this.workstream.value&&this.workstream.value.length>0?this.workstream.value:[],this.approver.value&&this.approver.value.length>0?this.approver.value:[],e,t,o).pipe(Object(d.a)(this._onDestroy)).subscribe({next:e=>{"S"==e.messType?this.logData=e.data:this.toastService.showInfo("Timesheet App Message",e.messText,3e3),this.apiInProgress=!1,i(!0)},error:e=>{var t;this.toastService.showError("Timesheet Error "+e.error.code,null===(t=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===t?void 0:t.user_message,6e4),this.apiInProgress=!1,r()}}))}))}getDivision(){return Object(s.c)(this,void 0,void 0,(function*(){return this.divisionList=[],this.resetSpecificSearchComponent(6),new Promise((e,t)=>this.reportService.getDivision().pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.divisionList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),t()}}))}))}getSubDivision(){return Object(s.c)(this,void 0,void 0,(function*(){if(this.division.value&&0!=this.division.value.length)return this.subDivisionApiInProgress=!0,this.subDivisionList=[],this.resetSpecificSearchComponent(7),new Promise((e,t)=>this.reportService.getSubDivision(this.division.value).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{if("S"==t.messType){this.subDivisionList=t.data&&t.data.length>0?t.data:[];let e=localStorage.getItem("tsv2-daily-log-report-subDivision")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-subDivision")):[];this.subDivision.setValue(e)}else{let e=localStorage.getItem("tsv2-daily-log-report-subDivision")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-subDivision")):[];this.subDivision.setValue(e),this.toastService.showInfo("Timesheet App Message",t.messText,3e3)}this.subDivisionApiInProgress=!1,e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),this.subDivisionApiInProgress=!1,t()}}))}))}getRegion(){return Object(s.c)(this,void 0,void 0,(function*(){return this.regionList=[],this.resetSpecificSearchComponent(4),new Promise((e,t)=>this.reportService.getRegion().pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{if("S"==t.messType){this.regionList=t.data&&t.data.length>0?t.data:[];let e=localStorage.getItem("tsv2-daily-log-report-region")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-region")):[];this.region.setValue(e)}else this.toastService.showInfo("Timesheet App Message",t.messText,3e3);e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),t()}}))}))}getEntity(){return Object(s.c)(this,void 0,void 0,(function*(){return this.entityList=[],this.resetSpecificSearchComponent(5),new Promise((e,t)=>this.reportService.getEntity().pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.entityList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),t()}}))}))}getLevel(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.reportService.getLevel().pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.levelList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),t()}}))}))}getTsDailyLogReportPageData(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.reportService.getTsDailyLogReportPageData(_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value&&this.portfolio.value.length>0?this.portfolio.value:[],this.sow.value&&this.sow.value.length>0?this.sow.value:[],this.employee.value&&this.employee.value.length>0?this.employee.value:[],this.status.value&&this.status.value.length>0?this.status.value:[1,2,3,4],this.division.value&&this.division.value.length>0?this.division.value:[],this.subDivision.value&&this.subDivision.value.length>0?this.subDivision.value:[],this.region.value&&this.region.value.length>0?this.region.value:[],this.level.value&&this.level.value.length>0?this.level.value:[],this.entity.value&&this.entity.value.length>0?this.entity.value:[],this.salesRegion.value&&this.salesRegion.value.length>0?this.salesRegion.value:[],this.workstream.value&&this.workstream.value.length>0?this.workstream.value:[],this.approver.value&&this.approver.value.length>0?this.approver.value:[]).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?(this.totalRecords=t.totalRecords,this.pages=t.pages,0==this.totalRecords&&(this.disableDownloadButton=!1)):(this.disableDownloadButton=!1,this.toastService.showInfo("Timesheet App Message",t.messText,3e3)),e(!0)},error:e=>{var o;this.disableDownloadButton=!1,this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),t()}}))}))}downloadReport(){return Object(s.c)(this,void 0,void 0,(function*(){if(0==this.pages)return this.toastService.showInfo("Timesheet V2 Daily Log Report Table View","No Data Found",3e3);this.spinnerService.show();let e=0;for(let t=1;t<=this.pages;t++)yield this.getTsDailyLogReportItemDataNew(t,!0,this.defaultPageSize).then(()=>{if(e++,e===this.pages)if(this.spinnerService.hide(),this.downloadData&&this.downloadData.length>0){this.toastService.showSuccess("Timesheet Daily Log Report Message","Report Downloaded Successfully",3e3);let e=this.downloadData.map(e=>Object.assign(Object.assign({},e),{Date:e.Date?this.convertToDate(e.Date):"NA","Saved On":e["Saved On"]?this.convertToDate(e["Saved On"]):"NA","Submitted On":e["Submitted On"]?this.convertToDate(e["Submitted On"]):"NA","Approved On":e["Approved On"]?this.convertToDate(e["Approved On"]):"NA"}));const t=F.utils.json_to_sheet(e);["Date","Saved On","Submitted On","Approved On"].forEach(o=>{const i=this.getColumnLetter(t,o);if(i)for(let r=2;r<=e.length+1;r++){const e=`${i}${r}`;t[e]&&(t[e].z="DD-MMM-YYYY")}});const o=F.utils.book_new();F.utils.book_append_sheet(o,t,"data"),F.writeFile(o,"Timesheet Daily Log Report - Table View.xlsx"),this.downloadData=[],this.disableDownloadButton=!1}else this.disableDownloadButton=!1,this.toastService.showSuccess("Timesheet Daily Log Report Message","No Data Found",3e3)}).catch(()=>{this.disableDownloadButton=!1,this.toastService.showError("Timesheet Daily Log Report Message","Error: Server Unavailable, Kindly try after soemtime",3e3)})}))}getTsDailyLogReportItemDataNew(e,t,o){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((i,r)=>this.reportService.getTsDailyLogReportItemData(_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value&&this.portfolio.value.length>0?this.portfolio.value:[],this.sow.value&&this.sow.value.length>0?this.sow.value:[],this.employee.value&&this.employee.value.length>0?this.employee.value:[],this.status.value&&this.status.value.length>0?this.status.value:[1,2,3,4],this.division.value&&this.division.value.length>0?this.division.value:[],this.subDivision.value&&this.subDivision.value.length>0?this.subDivision.value:[],this.region.value&&this.region.value.length>0?this.region.value:[],this.level.value&&this.level.value.length>0?this.level.value:[],this.entity.value&&this.entity.value.length>0?this.entity.value:[],this.salesRegion.value&&this.salesRegion.value.length>0?this.salesRegion.value:[],this.workstream.value&&this.workstream.value.length>0?this.workstream.value:[],this.approver.value&&this.approver.value.length>0?this.approver.value:[],e,t,o).pipe(Object(d.a)(this._onDestroy)).subscribe({next:e=>{"S"==e.messType?(this.logData=e.data,t&&(this.downloadData=this.downloadData.concat(e.data))):this.toastService.showInfo("Timesheet App Message",e.messText,3e3),i(!0)},error:e=>{var t;this.toastService.showError("Timesheet Error "+e.error.code,null===(t=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===t?void 0:t.user_message,6e4),r()}}))}))}convertToDate(e){if(null!=e||""!=e||"NA"!=e||" "!=e){const[t,o,i]=e.split("-"),r=new Date(o+" 1, 2000").getMonth();return new Date(Number(i),r,Number(t))}}getColumnLetter(e,t){var o;const i=F.utils.decode_range(e["!ref"]);for(let r=i.s.c;r<=i.e.c;r++)if((null===(o=e[F.utils.encode_cell({c:r,r:0})])||void 0===o?void 0:o.v)===t)return F.utils.encode_col(r);return null}getSalesRegion(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.reportService.getSalesRegion().pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.salesRegionList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),t()}}))}))}saveFilterData(){let e=this.portfolio.value&&this.portfolio.value.length>0?JSON.stringify(this.portfolio.value):JSON.stringify([]),t=this.sow.value&&this.sow.value.length>0?JSON.stringify(this.sow.value):JSON.stringify([]),o=this.employee.value&&this.employee.value.length>0?JSON.stringify(this.employee.value):JSON.stringify([]),i=this.status.value&&this.status.value.length>0?JSON.stringify(this.status.value):JSON.stringify([1,2,3,4]),r=this.division.value&&this.division.value.length>0?JSON.stringify(this.division.value):JSON.stringify([]),s=this.subDivision.value&&this.subDivision.value.length>0?JSON.stringify(this.subDivision.value):JSON.stringify([]),a=this.region.value&&this.region.value.length>0?JSON.stringify(this.region.value):JSON.stringify([]),n=this.level.value&&this.level.value.length>0?JSON.stringify(this.level.value):JSON.stringify([]),l=this.entity.value&&this.entity.value.length>0?JSON.stringify(this.entity.value):JSON.stringify([]),c=this.salesRegion.value&&this.salesRegion.value.length>0?JSON.stringify(this.salesRegion.value):JSON.stringify([]),h=this.workstream.value&&this.workstream.value.length>0?JSON.stringify(this.workstream.value):JSON.stringify([]),d=this.approver.value&&this.approver.value.length>0?JSON.stringify(this.approver.value):JSON.stringify([]);return localStorage.setItem("tsv2-daily-log-report-portfolio",e),localStorage.setItem("tsv2-daily-log-report-sow",t),localStorage.setItem("tsv2-daily-log-report-employee",o),localStorage.setItem("tsv2-daily-log-report-status",i),localStorage.setItem("tsv2-daily-log-report-division",r),localStorage.setItem("tsv2-daily-log-report-subDivision",s),localStorage.setItem("tsv2-daily-log-report-region",a),localStorage.setItem("tsv2-daily-log-report-level",n),localStorage.setItem("tsv2-daily-log-report-entity",l),localStorage.setItem("tsv2-daily-log-report-salesRegion",c),localStorage.setItem("tsv2-daily-log-report-workstream",h),localStorage.setItem("tsv2-daily-log-report-approver",d),this.toastService.showInfo("Timesheet App Message","Success: Filter is Saved",7e3)}getFilterData(){return Object(s.c)(this,void 0,void 0,(function*(){let e=localStorage.getItem("tsv2-daily-log-report-portfolio")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-portfolio")):[];this.portfolio.setValue(e);let t=localStorage.getItem("tsv2-daily-log-report-sow")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-sow")):[];this.sow.setValue(t);let o=localStorage.getItem("tsv2-daily-log-report-employee")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-employee")):[];this.employee.setValue(o);let i=localStorage.getItem("tsv2-daily-log-report-status")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-status")):[];this.status.setValue(i);let r=localStorage.getItem("tsv2-daily-log-report-division")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-division")):[];this.division.setValue(r);let s=localStorage.getItem("tsv2-daily-log-report-subDivision")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-subDivision")):[];this.subDivision.setValue(s);let a=localStorage.getItem("tsv2-daily-log-report-region")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-region")):[];this.region.setValue(a);let n=localStorage.getItem("tsv2-daily-log-report-level")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-level")):[];this.level.setValue(n);let l=localStorage.getItem("tsv2-daily-log-report-entity")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-entity")):[];this.entity.setValue(l);let c=localStorage.getItem("tsv2-daily-log-report-salesRegion")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-salesRegion")):[];this.salesRegion.setValue(c);let h=localStorage.getItem("tsv2-daily-log-report-workstream")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-workstream")):[];this.workstream.setValue(h);let d=localStorage.getItem("tsv2-daily-log-report-approver")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-approver")):[];this.approver.setValue(d)}))}getWorkstream(){return Object(s.c)(this,void 0,void 0,(function*(){return this.workstreamList=[],this.resetSpecificSearchComponent(3),new Promise((e,t)=>this.reportService.getWorkstream(this.sow.value.length>0?this.sow.value:[],_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD")).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.workstreamList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,7e3),e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,7e3),t()}}))}))}getApproverDetailsForDailyLogReport(){return Object(s.c)(this,void 0,void 0,(function*(){return this.approverFilterApiInProgress=!0,this.approverList=[],this.resetSpecificSearchComponent(7),new Promise((e,t)=>this.reportService.getApproverDetailsForDailyLogReport(_(this.startDate.value).format("YYYY-MM-DD"),_(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value,this.sow.value).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{if("S"==t.messType){this.approverList=t.data&&t.data.length>0?t.data:[];let e=localStorage.getItem("tsv2-daily-log-report-approver")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-approver")):[];this.approver.setValue(e)}else{let e=localStorage.getItem("tsv2-daily-log-report-approver")?JSON.parse(localStorage.getItem("tsv2-daily-log-report-approver")):[];this.approver.setValue(e),this.toastService.showInfo("Timesheet App Message",t.messText,3e3)}this.approverFilterApiInProgress=!1,e(!0)},error:e=>{var o;this.toastService.showError("Timesheet Error "+e.error.code,null===(o=p.filter(this.errorData,{error_code:e.error.code})[0])||void 0===o?void 0:o.user_message,6e4),this.approverFilterApiInProgress=!1,t()}}))}))}getTimesheetReportAccess(){return Object(s.c)(this,void 0,void 0,(function*(){this.reportService.getTimesheetReportAccess(571).pipe(Object(d.a)(this._onDestroy)).subscribe({next:e=>{e.reportAccess||this.router.navigateByUrl("/main/reports")},error:e=>{this.toastService.showError("Timesheet App Message",e.messText,3e3)}})}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](r.g),n["\u0275\u0275directiveInject"](R.a),n["\u0275\u0275directiveInject"](E.a),n["\u0275\u0275directiveInject"](T),n["\u0275\u0275directiveInject"](L.a),n["\u0275\u0275directiveInject"](P.c))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-v2-daily-log-rp-table-view-landing-page"]],viewQuery:function(e,t){if(1&e&&(n["\u0275\u0275viewQuery"](B,!0),n["\u0275\u0275viewQuery"](J,!0),n["\u0275\u0275viewQuery"](b,!0)),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.dataGrid=e.first),n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.stepper=e.first),n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.multiSelectSearch=e)}},hostBindings:function(e,t){1&e&&n["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,n["\u0275\u0275resolveWindow"])},features:[n["\u0275\u0275ProvidersFeature"]([{provide:v.c,useClass:O.c,deps:[v.f,O.a]},{provide:v.e,useValue:G}])],decls:105,vars:85,consts:[[1,"bg-container"],[1,"report-screen"],[1,"align-items-center",2,"margin-bottom","16px"],[1,"align-items-center"],[1,"back-btn",3,"click"],[1,"back-btn-icon"],[1,"header-title"],["stepper",""],["label","Report Input","editable","false"],[1,"align-items-center-justify"],[1,"col-12","report-height","flex-direction-column"],[1,"align-items-center",2,"margin-bottom","16px","gap","10px"],[1,"col-12","p-2","row"],[1,"col-4"],[1,"col-6"],["appearance","outline",1,"form-field"],[3,"rangePicker"],["matStartDate","","placeholder","Start Date",3,"formControl","keydown","dateChange"],["matEndDate","","placeholder","End Date",3,"formControl","keydown","dateChange"],["matSuffix","",3,"for"],["picker",""],["placeholder","Sales Region",3,"formControl","list","selectAllFlag"],["class","col-6",4,"ngIf"],[3,"placeholder","formControl","list","selectAllFlag"],["placeholder","Workstream",3,"formControl","list","selectAllFlag","required"],["placeholder","Region",3,"formControl","list","selectAllFlag","required"],[3,"placeholder","formControl","list","selectAllFlag","required"],["placeholder","Employee Name",3,"formControl","list","selectAllFlag"],["placeholder","Status",3,"formControl","list","selectAllFlag"],["placeholder","Approver Name",3,"formControl","list","selectAllFlag"],[1,"btn-line-style"],[1,"btn-style",3,"ngStyle","click"],["bdColor","rgba(0, 0, 0, 0.8)","size","medium","color","#fff","type","ball-clip-rotate-pulse",3,"fullScreen"],[2,"color","white"],["label","Report Result","editable","false"],[1,"align-items-space-between"],[1,"btn-style",3,"click"],[1,"report-height"],[1,"data-grid",3,"height","dataSource","showBorders","columnAutoWidth","showColumnLines","showRowLines","onExporting"],["dataGrid",""],[3,"visible"],["mode","select",3,"allowSearch","enabled"],[3,"enabled"],["mode","infinite"],["caption","Timesheet Id","dataField","timesheet_id"],["caption","Customer","dataField","customer"],["caption","Portfolio","dataField","portfolio"],["caption","Sales Org","dataField","sales_org"],["dataField","sow_reference_name",3,"caption"],["dataField","sow",3,"caption"],["dataField","zifo_sow_reference",3,"caption",4,"ngIf"],["caption","Region","dataField","region"],["dataField","legal_entity",3,"caption"],["dataField","business_unit",3,"caption"],["dataField","department",3,"caption"],["caption","Employee Id","dataField","associate_id"],["caption","Employee Name","dataField","employee_name"],["caption","Date","dataField","date","dataType","date","format","dd-MMM-yyyy"],["caption","Location","dataField","location",4,"ngIf"],["dataField","workstream",3,"caption"],["dataField","activity",3,"caption"],["caption","Logged Hours","dataField","total_hours","dataType","number"],[3,"groupInterval"],["caption","Week Off","dataField","weekoff",4,"ngIf"],["caption","Billable/Non-Billable","dataField","billing_type"],["caption","Approver","dataField","approvers"],["caption","Timesheet Status","dataField","status_description"],["caption","Comments","dataField","comments"],["caption","Saved On","dataField","saved_on"],["caption","Submitted On","dataField","submitted_on"],["caption","Approved On","dataField","approved_on"],["placeholder","Portfolio",3,"formControl","list","selectAllFlag"],["dataField","zifo_sow_reference",3,"caption"],["caption","Location","dataField","location"],["caption","Week Off","dataField","weekoff"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",0),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275elementStart"](4,"div",4),n["\u0275\u0275listener"]("click",(function(){return t.goToReportsMainScreen()})),n["\u0275\u0275elementStart"](5,"mat-icon",5),n["\u0275\u0275text"](6,"chevron_left"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275text"](7," Back "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](8,"div",6),n["\u0275\u0275text"](9,"Timesheet Daily Log Report - Table View"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](10,"mat-horizontal-stepper",null,7),n["\u0275\u0275elementStart"](12,"mat-step",8),n["\u0275\u0275elementStart"](13,"div",9),n["\u0275\u0275elementStart"](14,"div",10),n["\u0275\u0275elementStart"](15,"div",11),n["\u0275\u0275elementStart"](16,"div",12),n["\u0275\u0275element"](17,"div",13),n["\u0275\u0275element"](18,"div",13),n["\u0275\u0275elementStart"](19,"div",14),n["\u0275\u0275elementStart"](20,"mat-form-field",15),n["\u0275\u0275elementStart"](21,"mat-label"),n["\u0275\u0275text"](22,"Duration *"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](23,"mat-date-range-input",16),n["\u0275\u0275elementStart"](24,"input",17),n["\u0275\u0275listener"]("keydown",(function(e){return t.onKeyDownDateSearch(e)}))("dateChange",(function(e){return t.onChangeInDate(e)})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](25,"input",18),n["\u0275\u0275listener"]("keydown",(function(e){return t.onKeyDownDateSearch(e)}))("dateChange",(function(e){return t.onChangeInDate(e)})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](26,"mat-datepicker-toggle",19),n["\u0275\u0275element"](27,"mat-date-range-picker",null,20),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](29,"div",14),n["\u0275\u0275element"](30,"app-multi-select-search",21),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](31,V,2,3,"div",22),n["\u0275\u0275elementStart"](32,"div",14),n["\u0275\u0275element"](33,"app-multi-select-search",23),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](34,"div",14),n["\u0275\u0275element"](35,"app-multi-select-search",24),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](36,"div",14),n["\u0275\u0275element"](37,"app-multi-select-search",25),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](38,"div",14),n["\u0275\u0275element"](39,"app-multi-select-search",26),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](40,"div",14),n["\u0275\u0275element"](41,"app-multi-select-search",26),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](42,"div",14),n["\u0275\u0275element"](43,"app-multi-select-search",26),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](44,"div",14),n["\u0275\u0275element"](45,"app-multi-select-search",27),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](46,"div",14),n["\u0275\u0275element"](47,"app-multi-select-search",28),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](48,"div",14),n["\u0275\u0275element"](49,"app-multi-select-search",29),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](50,"div",30),n["\u0275\u0275elementStart"](51,"div",31),n["\u0275\u0275listener"]("click",(function(){return t.onDeleteFilters(0)})),n["\u0275\u0275text"](52," Clear Inputs "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](53,"div",31),n["\u0275\u0275listener"]("click",(function(){return t.saveFilterData()})),n["\u0275\u0275text"](54," Save Filters "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](55,"div",31),n["\u0275\u0275listener"]("click",(function(){return t.onGenerateReport(!0)})),n["\u0275\u0275text"](56," Download Report "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](57,"div",31),n["\u0275\u0275listener"]("click",(function(){return t.onGenerateReport(!1)})),n["\u0275\u0275text"](58," Generate Report "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](59,"ngx-spinner",32),n["\u0275\u0275elementStart"](60,"p",33),n["\u0275\u0275text"](61," Downloading... "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](62,"mat-step",34),n["\u0275\u0275elementStart"](63,"div",35),n["\u0275\u0275elementStart"](64,"div",4),n["\u0275\u0275listener"]("click",(function(){return t.goToPreviousScreen()})),n["\u0275\u0275elementStart"](65,"mat-icon",5),n["\u0275\u0275text"](66,"chevron_left"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275text"](67," Back to Previous Screen "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](68,"div",36),n["\u0275\u0275listener"]("click",(function(){return t.clearSortAndFilters()})),n["\u0275\u0275text"](69," Clear Sort & Filters "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](70,"div",37),n["\u0275\u0275elementStart"](71,"dx-data-grid",38,39),n["\u0275\u0275listener"]("onExporting",(function(e){return t.onExporting(e)})),n["\u0275\u0275element"](73,"dxo-filter-row",40),n["\u0275\u0275element"](74,"dxo-header-filter",40),n["\u0275\u0275element"](75,"dxo-column-chooser",41),n["\u0275\u0275element"](76,"dxo-export",42),n["\u0275\u0275element"](77,"dxo-scrolling",43),n["\u0275\u0275element"](78,"dxi-column",44),n["\u0275\u0275element"](79,"dxi-column",45),n["\u0275\u0275element"](80,"dxi-column",46),n["\u0275\u0275element"](81,"dxi-column",47),n["\u0275\u0275element"](82,"dxi-column",48),n["\u0275\u0275element"](83,"dxi-column",49),n["\u0275\u0275template"](84,W,1,1,"dxi-column",50),n["\u0275\u0275element"](85,"dxi-column",51),n["\u0275\u0275element"](86,"dxi-column",52),n["\u0275\u0275element"](87,"dxi-column",53),n["\u0275\u0275element"](88,"dxi-column",54),n["\u0275\u0275element"](89,"dxi-column",55),n["\u0275\u0275element"](90,"dxi-column",56),n["\u0275\u0275element"](91,"dxi-column",57),n["\u0275\u0275template"](92,H,1,0,"dxi-column",58),n["\u0275\u0275element"](93,"dxi-column",59),n["\u0275\u0275element"](94,"dxi-column",60),n["\u0275\u0275elementStart"](95,"dxi-column",61),n["\u0275\u0275element"](96,"dxo-header-filter",62),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](97,z,1,0,"dxi-column",63),n["\u0275\u0275element"](98,"dxi-column",64),n["\u0275\u0275element"](99,"dxi-column",65),n["\u0275\u0275element"](100,"dxi-column",66),n["\u0275\u0275element"](101,"dxi-column",67),n["\u0275\u0275element"](102,"dxi-column",68),n["\u0275\u0275element"](103,"dxi-column",69),n["\u0275\u0275element"](104,"dxi-column",70),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](28);n["\u0275\u0275advance"](23),n["\u0275\u0275property"]("rangePicker",e),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.startDate),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.endDate),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",e),n["\u0275\u0275advance"](4),n["\u0275\u0275property"]("formControl",t.salesRegion)("list",t.salesRegionList)("selectAllFlag",!1),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",3==t.report_data_version),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("placeholder",t.sow_placeholder)("formControl",t.sow)("list",t.sowList)("selectAllFlag",!1),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("formControl",t.workstream)("list",t.workstreamList)("selectAllFlag",!0)("required",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("formControl",t.region)("list",t.regionList)("selectAllFlag",!0)("required",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("placeholder",t.entity_placeholder)("formControl",t.entity)("list",t.entityList)("selectAllFlag",!0)("required",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("placeholder",t.division_placeholder)("formControl",t.division)("list",t.divisionList)("selectAllFlag",!0)("required",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("placeholder",t.sub_division_placeholder)("formControl",t.subDivision)("list",t.subDivisionList)("selectAllFlag",!0)("required",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("formControl",t.employee)("list",t.employeeList)("selectAllFlag",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("formControl",t.status)("list",t.statusList)("selectAllFlag",!1),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("formControl",t.approver)("list",t.approverList)("selectAllFlag",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngStyle",n["\u0275\u0275pureFunction1"](77,q,t.apiInProgress?"none":"")),n["\u0275\u0275advance"](2),n["\u0275\u0275classProp"]("disabled",t.disableDownloadButton),n["\u0275\u0275property"]("ngStyle",n["\u0275\u0275pureFunction1"](79,q,t.apiInProgress?"none":"")),n["\u0275\u0275advance"](2),n["\u0275\u0275classProp"]("disabled",t.disableDownloadButton),n["\u0275\u0275property"]("ngStyle",n["\u0275\u0275pureFunction1"](81,q,t.apiInProgress?"none":"")),n["\u0275\u0275advance"](2),n["\u0275\u0275classProp"]("disabled",t.disableDownloadButton),n["\u0275\u0275property"]("ngStyle",n["\u0275\u0275pureFunction1"](83,q,t.apiInProgress?"none":"")),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("fullScreen",!0),n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("height",t.dynamicGridReportHeight)("dataSource",t.logData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("visible",!0),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("visible",!0),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("allowSearch",!0)("enabled",!0),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("enabled",!0),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("caption",t.sow_ref_caption),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("caption",t.sow_description_caption),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.showSOWReference),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("caption",t.entity_caption),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("caption",t.division_placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("caption",t.sub_division_placeholder),n["\u0275\u0275advance"](4),n["\u0275\u0275property"]("ngIf",t.showLocation),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("caption",t.workstream_Caption),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("caption",t.activity_caption),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("groupInterval",1),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.showWeekOff)}},directives:[j.a,A.a,A.b,g.c,g.g,k.d,k.l,a.e,a.v,a.k,k.k,k.i,g.i,k.e,b,i.NgIf,a.F,i.NgStyle,P.a,Y.a,N.dc,N.Cc,N.tb,N.Sb,N.Jd,N.g],styles:[".bg-container[_ngcontent-%COMP%]{background-color:#f1f3f8;overflow:hidden}.bg-container[_ngcontent-%COMP%]   .report-screen[_ngcontent-%COMP%]{margin:24px;height:var(--dynamicHeight)}.bg-container[_ngcontent-%COMP%]   .report-height[_ngcontent-%COMP%]{height:var(--dynamicReportHeight);overflow-y:auto}.bg-container[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%]{display:flex;align-items:center}.bg-container[_ngcontent-%COMP%]   .align-items-center-justify[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.bg-container[_ngcontent-%COMP%]   .align-items-space-between[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.bg-container[_ngcontent-%COMP%]   .flex-direction-column[_ngcontent-%COMP%]{display:flex;flex-direction:column}.bg-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;color:#111434;font-size:11px;font-weight:400;border:1px solid #8b95a5;border-radius:4px;padding:4px;cursor:pointer;width:-moz-fit-content;width:fit-content}.bg-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{width:0!important;height:15px!important}.bg-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .back-btn-icon[_ngcontent-%COMP%]{font-size:18px;color:#111434;margin-right:20px;margin-bottom:2px}.bg-container[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#111434;margin-left:20px}.bg-container[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .btn-line-style[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:20px}.bg-container[_ngcontent-%COMP%]   .btn-style[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background-color:var(--color)!important;border-radius:4px;padding:8px;cursor:pointer;color:#fff}.bg-container[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]     .dx-toolbar .dx-toolbar-items-container{height:50px!important}.bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-selected, .bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-done, .bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-edit, .bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-number{background-color:var(--color)!important;color:#fff}.bg-container[_ngcontent-%COMP%]     .mat-step-header{pointer-events:none!important}.bg-container[_ngcontent-%COMP%]   .disabled[_ngcontent-%COMP%]{opacity:.6;pointer-events:none}"]}),e})()}];let U=(()=>{class e{}return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(K)],r.k]}),e})();var Q=o("PVOt"),X=o("Xa2L"),Z=o("qFsG"),$=o("Qu3c");let ee=(()=>{class e{}return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,U,Y.b,Q.f,X.b,g.e,Z.c,k.h,v.n,a.p,a.E,j.b,$.b,u.d,m.b,A.f,f.b,P.b]]}),e})()}}]);