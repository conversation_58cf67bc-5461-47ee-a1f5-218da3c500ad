(window.webpackJsonp=window.webpackJsonp||[]).push([[897],{"1pIY":function(t,e,n){"use strict";var r=n("2tF/");e.asyncScheduler=new(n("NTcF").AsyncScheduler)(r.AsyncAction),e.async=e.asyncScheduler},"2tF/":function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();e.AsyncAction=function(t){function e(e,n){var r=t.call(this,e,n)||this;return r.scheduler=e,r.work=n,r.pending=!1,r}return r(e,t),e.prototype.schedule=function(t,e){if(void 0===e&&(e=0),this.closed)return this;this.state=t;var n=this.id,r=this.scheduler;return null!=n&&(this.id=this.recycleAsyncId(r,n,e)),this.pending=!0,this.delay=e,this.id=this.id||this.requestAsyncId(r,this.id,e),this},e.prototype.requestAsyncId=function(t,e,n){return void 0===n&&(n=0),setInterval(t.flush.bind(t,this),n)},e.prototype.recycleAsyncId=function(t,e,n){if(void 0===n&&(n=0),null!==n&&this.delay===n&&!1===this.pending)return e;clearInterval(e)},e.prototype.execute=function(t,e){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;var n=this._execute(t,e);if(n)return n;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},e.prototype._execute=function(t,e){var n=!1,r=void 0;try{this.work(t)}catch(i){n=!0,r=!!i&&i||new Error(i)}if(n)return this.unsubscribe(),r},e.prototype._unsubscribe=function(){var t=this.id,e=this.scheduler,n=e.actions,r=n.indexOf(this);this.work=null,this.state=null,this.pending=!1,this.scheduler=null,-1!==r&&n.splice(r,1),null!=t&&(this.id=this.recycleAsyncId(e,t,null)),this.delay=null},e}(n("Dz+M").Action)},"Dz+M":function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();e.Action=function(t){function e(e,n){return t.call(this)||this}return r(e,t),e.prototype.schedule=function(t,e){return void 0===e&&(e=0),this},e}(n("zB/H").Subscription)},FWf1:function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=n("pshJ"),o=n("GiSu"),s=n("zB/H"),c=n("p//D"),a=n("n3uD"),u=n("MkmW"),l=function(t){function e(n,r,i){var s=t.call(this)||this;switch(s.syncErrorValue=null,s.syncErrorThrown=!1,s.syncErrorThrowable=!1,s.isStopped=!1,arguments.length){case 0:s.destination=o.empty;break;case 1:if(!n){s.destination=o.empty;break}if("object"==typeof n){n instanceof e?(s.syncErrorThrowable=n.syncErrorThrowable,s.destination=n,n.add(s)):(s.syncErrorThrowable=!0,s.destination=new p(s,n));break}default:s.syncErrorThrowable=!0,s.destination=new p(s,n,r,i)}return s}return r(e,t),e.prototype[c.rxSubscriber]=function(){return this},e.create=function(t,n,r){var i=new e(t,n,r);return i.syncErrorThrowable=!1,i},e.prototype.next=function(t){this.isStopped||this._next(t)},e.prototype.error=function(t){this.isStopped||(this.isStopped=!0,this._error(t))},e.prototype.complete=function(){this.isStopped||(this.isStopped=!0,this._complete())},e.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,t.prototype.unsubscribe.call(this))},e.prototype._next=function(t){this.destination.next(t)},e.prototype._error=function(t){this.destination.error(t),this.unsubscribe()},e.prototype._complete=function(){this.destination.complete(),this.unsubscribe()},e.prototype._unsubscribeAndRecycle=function(){var t=this._parentOrParents;return this._parentOrParents=null,this.unsubscribe(),this.closed=!1,this.isStopped=!1,this._parentOrParents=t,this},e}(s.Subscription);e.Subscriber=l;var p=function(t){function e(e,n,r,s){var c,a=t.call(this)||this;a._parentSubscriber=e;var u=a;return i.isFunction(n)?c=n:n&&(c=n.next,r=n.error,s=n.complete,n!==o.empty&&(u=Object.create(n),i.isFunction(u.unsubscribe)&&a.add(u.unsubscribe.bind(u)),u.unsubscribe=a.unsubscribe.bind(a))),a._context=u,a._next=c,a._error=r,a._complete=s,a}return r(e,t),e.prototype.next=function(t){if(!this.isStopped&&this._next){var e=this._parentSubscriber;a.config.useDeprecatedSynchronousErrorHandling&&e.syncErrorThrowable?this.__tryOrSetError(e,this._next,t)&&this.unsubscribe():this.__tryOrUnsub(this._next,t)}},e.prototype.error=function(t){if(!this.isStopped){var e=this._parentSubscriber,n=a.config.useDeprecatedSynchronousErrorHandling;if(this._error)n&&e.syncErrorThrowable?(this.__tryOrSetError(e,this._error,t),this.unsubscribe()):(this.__tryOrUnsub(this._error,t),this.unsubscribe());else if(e.syncErrorThrowable)n?(e.syncErrorValue=t,e.syncErrorThrown=!0):u.hostReportError(t),this.unsubscribe();else{if(this.unsubscribe(),n)throw t;u.hostReportError(t)}}},e.prototype.complete=function(){var t=this;if(!this.isStopped){var e=this._parentSubscriber;if(this._complete){var n=function(){return t._complete.call(t._context)};a.config.useDeprecatedSynchronousErrorHandling&&e.syncErrorThrowable?(this.__tryOrSetError(e,n),this.unsubscribe()):(this.__tryOrUnsub(n),this.unsubscribe())}else this.unsubscribe()}},e.prototype.__tryOrUnsub=function(t,e){try{t.call(this._context,e)}catch(n){if(this.unsubscribe(),a.config.useDeprecatedSynchronousErrorHandling)throw n;u.hostReportError(n)}},e.prototype.__tryOrSetError=function(t,e,n){if(!a.config.useDeprecatedSynchronousErrorHandling)throw new Error("bad call");try{e.call(this._context,n)}catch(r){return a.config.useDeprecatedSynchronousErrorHandling?(t.syncErrorValue=r,t.syncErrorThrown=!0,!0):(u.hostReportError(r),!0)}return!1},e.prototype._unsubscribe=function(){var t=this._parentSubscriber;this._context=null,this._parentSubscriber=null,t.unsubscribe()},e}(l);e.SafeSubscriber=p},GMZp:function(t,e,n){"use strict";e.isObject=function(t){return null!==t&&"object"==typeof t}},GiSu:function(t,e,n){"use strict";var r=n("n3uD"),i=n("MkmW");e.empty={closed:!0,next:function(t){},error:function(t){if(r.config.useDeprecatedSynchronousErrorHandling)throw t;i.hostReportError(t)},complete:function(){}}},LBXl:function(t,e,n){"use strict";e.UnsubscriptionError=function(){function t(t){return Error.call(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map((function(t,e){return e+1+") "+t.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t,this}return t.prototype=Object.create(Error.prototype),t}()},MkmW:function(t,e,n){"use strict";e.hostReportError=function(t){setTimeout((function(){throw t}),0)}},NTcF:function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=n("ffpz");e.AsyncScheduler=function(t){function e(n,r){void 0===r&&(r=i.Scheduler.now);var o=t.call(this,n,(function(){return e.delegate&&e.delegate!==o?e.delegate.now():r()}))||this;return o.actions=[],o.active=!1,o.scheduled=void 0,o}return r(e,t),e.prototype.schedule=function(n,r,i){return void 0===r&&(r=0),e.delegate&&e.delegate!==this?e.delegate.schedule(n,r,i):t.prototype.schedule.call(this,n,r,i)},e.prototype.flush=function(t){var e=this.actions;if(this.active)e.push(t);else{var n;this.active=!0;do{if(n=t.execute(t.state,t.delay))break}while(t=e.shift());if(this.active=!1,n){for(;t=e.shift();)t.unsubscribe();throw n}}},e}(i.Scheduler)},d7mv:function(t,e,n){"use strict";n.r(e),n.d(e,"JumpToTicketHeaderComponent",(function(){return L})),n.d(e,"TicketDetailHeaderModule",(function(){return N}));var r=n("3Pt+"),i=n("1G5W"),o=n("XNiG"),s=n("dMDw"),c=n("ofXK"),a=n("bTqV"),u=n("NFeN"),l=n("Qu3c"),p=n("Xa2L"),h=n("kmnG"),d=n("qFsG"),f=n("v2UZ"),m=n("mrSG"),y=n("fXoL"),b=n("tyNb"),_=n("yu80"),g=n("mEBv"),v=n("BVzC"),S=n("N+wt"),k=n("Ag/a"),w=n("Vtn0");const x=function(t){return{background:t}};function E(t,e){if(1&t&&(y["\u0275\u0275element"](0,"span",15),y["\u0275\u0275pipe"](1,"priorityColor")),2&t){const t=y["\u0275\u0275nextContext"]();y["\u0275\u0275property"]("ngStyle",y["\u0275\u0275pureFunction1"](4,x,y["\u0275\u0275pipeBind2"](1,1,null==t.ticketItem?null:t.ticketItem.priority,t.priorityStatusColors)))}}function O(t,e){if(1&t&&(y["\u0275\u0275element"](0,"span",15),y["\u0275\u0275pipe"](1,"statusColor"),y["\u0275\u0275pipe"](2,"getObjectValue")),2&t){const t=y["\u0275\u0275nextContext"]();y["\u0275\u0275property"]("ngStyle",y["\u0275\u0275pureFunction1"](7,x,y["\u0275\u0275pipeBind2"](1,1,y["\u0275\u0275pipeBind2"](2,4,null==t.ticketItem?null:t.ticketItem.status,"status_name"),t.ticketStatusColors)))}}function I(t,e){if(1&t&&(y["\u0275\u0275elementStart"](0,"span",16),y["\u0275\u0275text"](1),y["\u0275\u0275pipe"](2,"date"),y["\u0275\u0275elementEnd"]()),2&t){const t=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](1),y["\u0275\u0275textInterpolate1"](" ",t.ticketItem.estimated_closure_date?y["\u0275\u0275pipeBind2"](2,1,null==t.ticketItem?null:t.ticketItem.estimated_closure_date,"dd-MMM-yy"):"-"," ")}}const T=function(t){return{"current-ticket":t}};let C=(()=>{class t{constructor(t,e,n,r){this.router=t,this._ticket=e,this.tooltipPopup=n,this._ErrorService=r,this._onDestroy=new o.b,this.ticketStatusColors=[],this.priorityStatusColors=[],this.navigateToSelectedTicket=()=>{this.router.navigateByUrl("/main/ams/ticketDetails/"+this.ticketId),this.tooltipPopup.close()}}ngOnInit(){this.loadTicketMetaData(),this.getStatusColor(),this.getActivePriority()}loadTicketMetaData(){this.ticketId&&this._ticket.getTicketMetaData(this.ticketId).pipe(Object(i.a)(this._onDestroy)).subscribe(t=>{"N"==t.error&&t.data?this.ticketItem=t.data:this._ticket.showMessage("No Data Found !")},t=>{console.log(t),this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}getStatusColor(){return Object(m.c)(this,void 0,void 0,(function*(){let t=yield this._ticket.getStatusColor();if("S"==t.messType&&t.data)for(let e=0;e<t.data.length;e++)this.ticketStatusColors.push({statusName:t.data[e].status_name,statusColor:t.data[e].status_color})}))}getActivePriority(){return Object(m.c)(this,void 0,void 0,(function*(){let t=yield this._ticket.getActivePriority();if("S"==t.messType&&t.data)for(let e=0;e<t.data.length;e++)this.priorityStatusColors.push({statusName:t.data[e].priority_name,statusColor:t.data[e].Color})}))}}return t.\u0275fac=function(e){return new(e||t)(y["\u0275\u0275directiveInject"](b.g),y["\u0275\u0275directiveInject"](_.a),y["\u0275\u0275directiveInject"](g.a),y["\u0275\u0275directiveInject"](v.a))},t.\u0275cmp=y["\u0275\u0275defineComponent"]({type:t,selectors:[["app-jump-to-ticket-item"]],inputs:{ticketId:"ticketId",isCurrentlySelectedTicket:"isCurrentlySelectedTicket"},decls:27,vars:20,consts:[[1,"container-fluid","jump-to-ticket-item-styles","pl-0","pr-0"],[1,"small-content",3,"ngClass","click"],[1,"row","pt-2","pb-2"],[1,"col-8","normalFont"],[2,"font-weight","600",3,"matTooltip"],[1,"col-4","d-flex"],["class","status-dot my-auto",3,"ngStyle",4,"ngIf"],[1,"normalFont","my-auto","pl-2"],[1,"row","pb-2"],[1,"col-8","headingBold",3,"matTooltip"],[1,"col-4","normalFont",2,"font-weight","600"],[1,"col-6","d-flex"],[1,"col-6"],[1,"header"],["class","normalFont pl-2",4,"ngIf"],[1,"status-dot","my-auto",3,"ngStyle"],[1,"normalFont","pl-2"]],template:function(t,e){1&t&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275elementStart"](1,"div",1),y["\u0275\u0275listener"]("click",(function(){return e.navigateToSelectedTicket()})),y["\u0275\u0275elementStart"](2,"div",2),y["\u0275\u0275elementStart"](3,"div",3),y["\u0275\u0275text"](4),y["\u0275\u0275elementStart"](5,"span",4),y["\u0275\u0275text"](6),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](7,"div",5),y["\u0275\u0275template"](8,E,2,6,"span",6),y["\u0275\u0275elementStart"](9,"span",7),y["\u0275\u0275text"](10),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](11,"div",8),y["\u0275\u0275elementStart"](12,"div",9),y["\u0275\u0275text"](13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](14,"div",10),y["\u0275\u0275text"](15),y["\u0275\u0275pipe"](16,"getObjectValue"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](17,"div",8),y["\u0275\u0275elementStart"](18,"div",11),y["\u0275\u0275template"](19,O,3,9,"span",6),y["\u0275\u0275elementStart"](20,"span",7),y["\u0275\u0275text"](21),y["\u0275\u0275pipe"](22,"getObjectValue"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](23,"div",12),y["\u0275\u0275elementStart"](24,"span",13),y["\u0275\u0275text"](25,"E.C. Date "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](26,I,3,4,"span",14),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&t&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngClass",y["\u0275\u0275pureFunction1"](18,T,e.isCurrentlySelectedTicket)),y["\u0275\u0275advance"](3),y["\u0275\u0275textInterpolate1"](" TK / ",null==e.ticketItem?null:e.ticketItem.ticket_number," - "),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("matTooltip",null==e.ticketItem?null:e.ticketItem.ticket_title),y["\u0275\u0275advance"](1),y["\u0275\u0275textInterpolate"](null==e.ticketItem?null:e.ticketItem.ticket_title),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("ngIf",e.priorityStatusColors.length),y["\u0275\u0275advance"](2),y["\u0275\u0275textInterpolate"](null==e.ticketItem?null:e.ticketItem.priority),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("matTooltip",null==e.ticketItem?null:e.ticketItem.project_item_name),y["\u0275\u0275advance"](1),y["\u0275\u0275textInterpolate1"](" ",null==e.ticketItem?null:e.ticketItem.project_item_name," "),y["\u0275\u0275advance"](2),y["\u0275\u0275textInterpolate1"](" ",y["\u0275\u0275pipeBind2"](16,12,null==e.ticketItem?null:e.ticketItem.ticket_type,"type_name")," "),y["\u0275\u0275advance"](4),y["\u0275\u0275property"]("ngIf",e.ticketStatusColors.length),y["\u0275\u0275advance"](2),y["\u0275\u0275textInterpolate"](y["\u0275\u0275pipeBind2"](22,15,null==e.ticketItem?null:e.ticketItem.status,"status_name")),y["\u0275\u0275advance"](5),y["\u0275\u0275property"]("ngIf",e.ticketItem))},directives:[c.NgClass,l.a,c.NgIf,c.NgStyle],pipes:[S.a,k.a,w.a,c.DatePipe],styles:[".jump-to-ticket-item-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.jump-to-ticket-item-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.jump-to-ticket-item-styles[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:13px;width:13px;border-radius:50%;display:inline-block;vertical-align:middle}.jump-to-ticket-item-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.jump-to-ticket-item-styles[_ngcontent-%COMP%]   .small-content[_ngcontent-%COMP%]{border-bottom:1px solid #cacaca;cursor:pointer}.jump-to-ticket-item-styles[_ngcontent-%COMP%]   .small-content[_ngcontent-%COMP%]:hover{cursor:pointer;background-color:hsla(0,0%,92.2%,.562)}.jump-to-ticket-item-styles[_ngcontent-%COMP%]   .current-ticket[_ngcontent-%COMP%]{border:1px solid #cf0001}"]}),t})();var j=n("xm0x");function P(t,e){1&t&&y["\u0275\u0275element"](0,"mat-spinner",12)}function M(t,e){1&t&&(y["\u0275\u0275elementStart"](0,"div",13),y["\u0275\u0275element"](1,"div",14),y["\u0275\u0275elementStart"](2,"div",15),y["\u0275\u0275element"](3,"mat-spinner",16),y["\u0275\u0275elementEnd"](),y["\u0275\u0275element"](4,"div",14),y["\u0275\u0275elementEnd"]())}function D(t,e){if(1&t&&y["\u0275\u0275element"](0,"app-jump-to-ticket-item",20),2&t){const t=e.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275property"]("ticketId",t.ticket_id)("readOnly",t.is_ticket_readonly)("isCurrentlySelectedTicket",t.ticket_id===n.currentTicketId)}}function A(t,e){if(1&t&&(y["\u0275\u0275elementStart"](0,"virtual-scroll",17,18),y["\u0275\u0275template"](2,D,1,3,"app-jump-to-ticket-item",19),y["\u0275\u0275elementEnd"]()),2&t){const t=y["\u0275\u0275reference"](1),e=y["\u0275\u0275nextContext"]();y["\u0275\u0275property"]("items",e.ticketList),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("ngForOf",t.viewPortItems)}}function F(t,e){1&t&&(y["\u0275\u0275elementStart"](0,"div",21),y["\u0275\u0275elementStart"](1,"div",22),y["\u0275\u0275elementStart"](2,"span",23),y["\u0275\u0275text"](3," No Tickets found ! "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",24),y["\u0275\u0275element"](5,"img",25),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]())}let L=(()=>{class t{constructor(t,e,n){this._ticket=t,this.route=e,this._ErrorService=n,this._onDestroy=new o.b,this.searchText=new r.j,this.ticketList=[],this.isTicketsLoading=!1,this.isSearching=!1,this.currentUser=this._ticket.currentUser,this.resolveSubscriptions=()=>{this.searchSubscription||(this.searchSubscription=this.searchText.valueChanges.pipe(Object(i.a)(this._onDestroy),Object(s.debounceTime)(700)).subscribe(t=>{t?this.getSearchResult(t):this.getTicketList()}))},this.getSearchResult=t=>{this.isTicketsLoading=!0,this.isSearching=!0,this._ticket.getTicketBasedOnSearch(t,this.getTicketListFromSession()).pipe(Object(i.a)(this._onDestroy)).subscribe(t=>{this.isTicketsLoading=!1,this.isSearching=!1,"N"==t.error&&t.data?this.ticketList=t.data:(this._ticket.showMessage("No Tickets Found !"),this.isTicketsLoading=!1)},t=>{this.isSearching=!1,this.isTicketsLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})},this.getTicketList=()=>{this.isTicketsLoading=!0,this._ticket.getTicketsForEmployee(this.currentUser.oid).pipe(Object(i.a)(this._onDestroy)).subscribe(t=>{"N"==t.error&&t.data.length>0?(this.isTicketsLoading=!1,this.ticketList=t.data,sessionStorage.removeItem("ticketList"),sessionStorage.setItem("ticketList",JSON.stringify(this.ticketList))):(this._ticket.showMessage("No Tickets Found !"),this.isTicketsLoading=!1)},t=>{this.isTicketsLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})},this.clearSearch=()=>{}}ngOnInit(){this.route.parent.params.subscribe(t=>{t.ticketId&&(this.currentTicketId=t.ticketId)}),this.resolveSubscriptions(),this.getTicketList()}getTicketListFromSession(){let t=sessionStorage.getItem("ticketList");if(t)return"string"==typeof t?JSON.parse(t):t}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.searchSubscription&&this.searchSubscription.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(y["\u0275\u0275directiveInject"](_.a),y["\u0275\u0275directiveInject"](b.a),y["\u0275\u0275directiveInject"](v.a))},t.\u0275cmp=y["\u0275\u0275defineComponent"]({type:t,selectors:[["app-jump-to-ticket-header"]],decls:13,vars:5,consts:[[1,"container-fluid","jump-to-ticket-header-styles","pl-0","pr-0"],[1,"row"],[1,"search-bar","d-flex","col-12","pt-1"],["appearance","outline",1,"ml-auto","mr-auto"],["matPrefix",""],[2,"font-size","18px !important","color","#66615b !important"],["matInput","","placeholder","Search Tickets",3,"formControl"],["matSuffix",""],["diameter","18","class","mt-2",4,"ngIf"],["class","container d-flex h-100 flex-column",4,"ngIf"],[3,"items",4,"ngIf"],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],["diameter","18",1,"mt-2"],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"],[3,"items"],["scroll",""],[3,"ticketId","readOnly","isCurrentlySelectedTicket",4,"ngFor","ngForOf"],[3,"ticketId","readOnly","isCurrentlySelectedTicket"],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","18px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","300","width","325",1,"mt-2","mb-2"]],template:function(t,e){1&t&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275elementStart"](1,"div",1),y["\u0275\u0275elementStart"](2,"div",2),y["\u0275\u0275elementStart"](3,"mat-form-field",3),y["\u0275\u0275elementStart"](4,"span",4),y["\u0275\u0275elementStart"](5,"mat-icon",5),y["\u0275\u0275text"](6,"search"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275element"](7,"input",6),y["\u0275\u0275elementStart"](8,"mat-icon",7),y["\u0275\u0275template"](9,P,1,0,"mat-spinner",8),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](10,M,5,0,"div",9),y["\u0275\u0275template"](11,A,3,2,"virtual-scroll",10),y["\u0275\u0275template"](12,F,6,0,"div",11),y["\u0275\u0275elementEnd"]()),2&t&&(y["\u0275\u0275advance"](7),y["\u0275\u0275property"]("formControl",e.searchText),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("ngIf",e.isSearching),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",e.isTicketsLoading),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",e.ticketList.length>0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",0==e.ticketList.length&&!e.isTicketsLoading))},directives:function(){return[h.c,h.h,u.a,d.b,r.e,r.v,r.k,h.i,c.NgIf,p.c,f.VirtualScrollComponent,c.NgForOf,C]},styles:[".jump-to-ticket-header-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:35vw}.jump-to-ticket-header-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.jump-to-ticket-header-styles[_ngcontent-%COMP%]   virtual-scroll[_ngcontent-%COMP%]{display:block;width:100%;height:20rem}.jump-to-ticket-header-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.jump-to-ticket-header-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})(),N=(()=>{class t{}return t.\u0275mod=y["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=y["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[c.CommonModule,a.b,u.b,l.b,p.b,h.e,d.c,r.E,f.VirtualScrollModule,j.a]]}),t})()},dMDw:function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=function(e,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(e,n)};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),i=n("FWf1"),o=n("1pIY");e.debounceTime=function(t,e){return void 0===e&&(e=o.async),function(n){return n.lift(new s(t,e))}};var s=function(){function t(t,e){this.dueTime=t,this.scheduler=e}return t.prototype.call=function(t,e){return e.subscribe(new c(t,this.dueTime,this.scheduler))},t}(),c=function(t){function e(e,n,r){var i=t.call(this,e)||this;return i.dueTime=n,i.scheduler=r,i.debouncedSubscription=null,i.lastValue=null,i.hasValue=!1,i}return r(e,t),e.prototype._next=function(t){this.clearDebounce(),this.lastValue=t,this.hasValue=!0,this.add(this.debouncedSubscription=this.scheduler.schedule(a,this.dueTime,this))},e.prototype._complete=function(){this.debouncedNext(),this.destination.complete()},e.prototype.debouncedNext=function(){if(this.clearDebounce(),this.hasValue){var t=this.lastValue;this.lastValue=null,this.hasValue=!1,this.destination.next(t)}},e.prototype.clearDebounce=function(){var t=this.debouncedSubscription;null!==t&&(this.remove(t),t.unsubscribe(),this.debouncedSubscription=null)},e}(i.Subscriber);function a(t){t.debouncedNext()}},ffpz:function(t,e,n){"use strict";e.Scheduler=function(){function t(e,n){void 0===n&&(n=t.now),this.SchedulerAction=e,this.now=n}return t.prototype.schedule=function(t,e,n){return void 0===e&&(e=0),new this.SchedulerAction(this,t).schedule(n,e)},t.now=function(){return Date.now()},t}()},mbIT:function(t,e,n){"use strict";e.isArray=function(){return Array.isArray||function(t){return t&&"number"==typeof t.length}}()},n3uD:function(t,e,n){"use strict";var r=!1;e.config={Promise:void 0,set useDeprecatedSynchronousErrorHandling(t){if(t){var e=new Error;console.warn("DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \n"+e.stack)}else r&&console.log("RxJS: Back to a better error behavior. Thank you. <3");r=t},get useDeprecatedSynchronousErrorHandling(){return r}}},"p//D":function(t,e,n){"use strict";e.rxSubscriber=function(){return"function"==typeof Symbol?Symbol("rxSubscriber"):"@@rxSubscriber_"+Math.random()}(),e.$$rxSubscriber=e.rxSubscriber},pshJ:function(t,e,n){"use strict";e.isFunction=function(t){return"function"==typeof t}},"zB/H":function(t,e,n){"use strict";var r=n("mbIT"),i=n("GMZp"),o=n("pshJ"),s=n("LBXl");function c(t){return t.reduce((function(t,e){return t.concat(e instanceof s.UnsubscriptionError?e.errors:e)}),[])}e.Subscription=function(){function t(t){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,t&&(this._ctorUnsubscribe=!0,this._unsubscribe=t)}var e;return t.prototype.unsubscribe=function(){var e;if(!this.closed){var n=this,a=n._parentOrParents,u=n._ctorUnsubscribe,l=n._unsubscribe,p=n._subscriptions;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,a instanceof t)a.remove(this);else if(null!==a)for(var h=0;h<a.length;++h)a[h].remove(this);if(o.isFunction(l)){u&&(this._unsubscribe=void 0);try{l.call(this)}catch(m){e=m instanceof s.UnsubscriptionError?c(m.errors):[m]}}if(r.isArray(p)){h=-1;for(var d=p.length;++h<d;){var f=p[h];if(i.isObject(f))try{f.unsubscribe()}catch(m){e=e||[],m instanceof s.UnsubscriptionError?e=e.concat(c(m.errors)):e.push(m)}}}if(e)throw new s.UnsubscriptionError(e)}},t.prototype.add=function(e){var n=e;if(!e)return t.EMPTY;switch(typeof e){case"function":n=new t(e);case"object":if(n===this||n.closed||"function"!=typeof n.unsubscribe)return n;if(this.closed)return n.unsubscribe(),n;if(!(n instanceof t)){var r=n;(n=new t)._subscriptions=[r]}break;default:throw new Error("unrecognized teardown "+e+" added to Subscription.")}var i=n._parentOrParents;if(null===i)n._parentOrParents=this;else if(i instanceof t){if(i===this)return n;n._parentOrParents=[i,this]}else{if(-1!==i.indexOf(this))return n;i.push(this)}var o=this._subscriptions;return null===o?this._subscriptions=[n]:o.push(n),n},t.prototype.remove=function(t){var e=this._subscriptions;if(e){var n=e.indexOf(t);-1!==n&&e.splice(n,1)}},t.EMPTY=((e=new t).closed=!0,e),t}()}}]);