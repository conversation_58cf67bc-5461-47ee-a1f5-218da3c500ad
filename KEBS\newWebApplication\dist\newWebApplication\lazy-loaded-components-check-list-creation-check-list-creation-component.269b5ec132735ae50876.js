(window.webpackJsonp=window.webpackJsonp||[]).push([[746],{AgLP:function(e,t,i){"use strict";i.r(t),i.d(t,"CheckListCreationComponent",(function(){return B}));var n=i("mrSG"),r=i("fXoL"),o=i("3Pt+"),a=i("wd/R"),s=i("0IaG"),l=i("ofXK"),c=i("jtHE"),d=i("XNiG"),h=i("NJ67"),m=i("1G5W"),p=i("kmnG"),u=i("d3UM"),g=i("FKr1"),f=i("WJ5W");const v=["singleSelect"];function b(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-option",6),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const i=t.$implicit;return r["\u0275\u0275nextContext"]().emitChanges(i)})),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275property"]("value",e.id),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends h.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new r.EventEmitter,this.disabled=!1,this.filteredList=new c.a,this.change=new r.EventEmitter,this._onDestroy=new d.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(m.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(m.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.Renderer2))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&r["\u0275\u0275viewQuery"](v,!0),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-form-field",0),r["\u0275\u0275elementStart"](1,"mat-label"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-select",1,2),r["\u0275\u0275elementStart"](5,"mat-option"),r["\u0275\u0275element"](6,"ngx-mat-select-search",3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"mat-option",4),r["\u0275\u0275text"](8,"None"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](9,b,2,2,"mat-option",5),r["\u0275\u0275pipe"](10,"async"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](t.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("value",null),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",r["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[p.c,p.g,u.c,o.v,o.k,o.F,g.p,f.a,l.NgForOf],pipes:[l.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var S=i("Kj3r"),C=i("F97M"),w=i("XVR1"),k=i("qFsG"),O=i("/1cH"),x=i("NFeN");function F(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-label"),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.label)}}function M(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275elementStart"](1,"small"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let E=(()=>{class e extends h.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new d.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new r.EventEmitter,this.selectedUser=new r.EventEmitter,this.label="",this.blur=new r.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new d.b}ngOnInit(){this.userSearchSubject.pipe(Object(S.a)(600)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(m.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](C.a),r["\u0275\u0275directiveInject"](w.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"mat-form-field",0),r["\u0275\u0275template"](2,F,2,1,"mat-label",1),r["\u0275\u0275elementStart"](3,"input",2),r["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"mat-icon",3),r["\u0275\u0275text"](5,"person_pin"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),r["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),r["\u0275\u0275template"](8,M,3,4,"mat-option",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](7);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.label),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[p.c,l.NgIf,k.b,O.d,o.e,o.F,o.v,o.k,x.a,p.i,O.b,l.NgForOf,p.g,g.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var D=i("dNgK"),L=i("bTqV"),_=i("iadO"),I=(i("we1Z"),i("9044")),P=i("tyNb"),j=i("Tzv/"),T=i("LcQX");function A(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",36),r["\u0275\u0275elementStart"](1,"div",37),r["\u0275\u0275elementStart"](2,"mat-form-field",12),r["\u0275\u0275elementStart"](3,"mat-label"),r["\u0275\u0275text"](4,"Activity Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](5,"input",38),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",39),r["\u0275\u0275elementStart"](7,"button",40),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const i=t.index,n=r["\u0275\u0275nextContext"](2);return n.addNewItemIncheckList(n.checkListCreationForm.controls.checkList,i)})),r["\u0275\u0275elementStart"](8,"mat-icon"),r["\u0275\u0275text"](9,"add_circle"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"button",40),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const i=t.index,n=r["\u0275\u0275nextContext"](2);return n.removeItemFromCheckList(n.checkListCreationForm.controls.checkList,i)})),r["\u0275\u0275elementStart"](11,"mat-icon"),r["\u0275\u0275text"](12,"remove_circle"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}2&e&&r["\u0275\u0275property"]("formGroupName",t.index)}function G(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",33),r["\u0275\u0275elementStart"](1,"div",34),r["\u0275\u0275template"](2,A,13,1,"div",35),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",e.checkListCreationForm.get("checkList").controls)}}function N(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",41),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().createCheckList()})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2," done_all"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function V(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",42),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().updateCheckList()})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2," done_all"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}const U=function(e,t){return{"btn-active":e,"btn-not-active":t}};let B=(()=>{class e{constructor(e,t,i,s,l,c,d,h,m){this.fb=e,this.dialogRef=t,this.dialogData=i,this.dialog=s,this.snackBar=l,this.opService=c,this._route=d,this.reloadService=h,this.utilityService=m,this.close=new r.EventEmitter,this.flag=!1,this.checkListCreationForm=this.fb.group({phase:["",o.H.required],title:["",o.H.required],dueOn:["",o.H.required],newDueOn:[""],predecessorActivityId:[""],description:[""],governanceType:[""],applicationId:36,applicationName:"Opportunity",applicationReferenceId:"",checkList:this.fb.array([]),overallOwner:["",o.H.required]}),this.checkWhetherDueOnTodaysDateOrTommorowsDate=e=>{let t=new Date(this.checkListCreationForm.value.dueOn).getFullYear(),i=new Date(this.checkListCreationForm.value.dueOn).getDate(),n=new Date(this.checkListCreationForm.value.dueOn).getMonth();if("today"==e){if(this.checkListCreationForm.value.dueOn){let e=(new Date).getFullYear();return i+"-"+n+"-"+t==(new Date).getDate()+"-"+(new Date).getMonth()+"-"+e}return!1}if("tommorrow"==e){if(this.checkListCreationForm.value.dueOn){const e=new Date,r=new Date(e);r.setDate(r.getDate()+1);let o=r.getFullYear();return i+"-"+n+"-"+t==r.getDate()+"-"+r.getMonth()+"-"+o}return!1}},this.fillUpDueOn=e=>{if("today"===e)this.checkListCreationForm.patchValue({dueOn:new Date});else if("tommorow"===e){const e=new Date,t=new Date(e);t.setDate(t.getDate()+1),this.checkListCreationForm.patchValue({dueOn:t})}},this.getAllActivityList=()=>new Promise((e,t)=>{console.log(this.opportunityId,this.flag,this.activityId),this.opService.getAllActivityList(this.opportunityId,this.flag,this.activityId).subscribe(t=>{console.log(this.activityList),this.activityList=t,e(t)},e=>{throw console.error(e),e})}),this.createCheckList=()=>{console.log(this.checkListCreationForm.value),console.log(this.checkListForm.value),this.checkListCreationForm.valid?this.opService.createCheckList(this.checkListCreationForm.value,36,this.opportunityId).subscribe(e=>{console.log(e),this.snackBar.open("Checklist created successfully!","Dismiss",{duration:2e3}),this.checkListForm.reset(),this.close.emit("close"),this.dialogRef.close("update required"),this.reloadService.sendNotification("reload")},e=>{console.error(e),this.snackBar.open("Checklist creation failed!","Dismiss",{duration:2e3})}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.updateCheckList=()=>{this.checkListCreationForm.valid?a(this.checkListCreationForm.get("dueOn").value).isSame(a(this.checkListCreationForm.get("newDueOn").value))?this.opService.editCheckList(this.activityId,this.checkListCreationForm.value,!1).subscribe(e=>{console.log(e),this.snackBar.open("Checklist updation successfully!","Dismiss",{duration:2e3}),this.checkListForm.reset(),this.close.emit("close"),this.dialogRef.close("update required"),this.reloadService.sendNotification("reload")},e=>{console.error(e),this.checkListForm.reset(),this.snackBar.open("Checklist updation failed!","Dismiss",{duration:2e3})}):this.utilityService.openConfirmationForUpdatingAllOpportunityActivities("Do you want to update dates for Dependant Activities?").then(e=>Object(n.c)(this,void 0,void 0,(function*(){"All"==e?(console.log(e),this.opService.editCheckList(this.activityId,this.checkListCreationForm.value,!0).subscribe(e=>{console.log(e),this.snackBar.open("Checklist updation successfully!","Dismiss",{duration:2e3}),this.checkListForm.reset(),this.close.emit("close"),this.dialogRef.close("update required"),this.reloadService.sendNotification("reload")},e=>{console.error(e),this.checkListForm.reset(),this.snackBar.open("Checklist updation failed!","Dismiss",{duration:2e3})})):"Single"==e&&(console.log(e),this.opService.editCheckList(this.activityId,this.checkListCreationForm.value,!1).subscribe(e=>{console.log(e),this.snackBar.open("Checklist updation successfully!","Dismiss",{duration:2e3}),this.checkListForm.reset(),this.close.emit("close"),this.dialogRef.close("update required"),this.reloadService.sendNotification("reload")},e=>{console.error(e),this.checkListForm.reset(),this.snackBar.open("Checklist updation failed!","Dismiss",{duration:2e3})}))}))):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.getPhase=()=>new Promise((e,t)=>{this.opService.getActivityPhase(this.opportunityId).subscribe(t=>{e(t)},e=>{throw console.error(e),e})}),this.getGovernanceTypes=()=>new Promise((e,t)=>{this.opService.getGovernanceTypes(66).then(t=>{e(t)},e=>{throw console.error(e),e})}),this.getSalesGovernanceTypes=()=>new Promise((e,t)=>{this.opService.getSalesGovernanceTypes(72).then(t=>{this.salesGovernanceTypes=t,e(t)},e=>{throw console.error(e),e})}),this.getMarketingGovernanceTypes=()=>new Promise((e,t)=>{this.opService.getSalesGovernanceTypes(74).then(t=>{this.marketingGovernanceTypes=t,e(t)},e=>{throw console.error(e),e})}),"Edit"!=this.mode?this.checkListCreationForm.controls.checkList.push(this.fb.group({activityName:[""],days:[""],owner:[""]})):this.flag=!1}ngOnChanges(){return Object(n.c)(this,void 0,void 0,(function*(){}))}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,this.opportunityId=parseInt(window.location.pathname.split("/")[5]),this.phase=yield this.getPhase(),this.governanceTypes=this.getGovernanceTypes(),this.salesGovernanceTypes=this.getSalesGovernanceTypes(),this.marketingGovernanceTypes=this.getMarketingGovernanceTypes(),this.activityList=yield this.getAllActivityList(),"Edit"==this.mode?(this.opportunityId=parseInt(this.data.application_reference_id),this.flag=!0,this.activityId=this.data.activity_id,this.activityList=yield this.getAllActivityList(),console.log(this.data),this.checkListCreationForm.patchValue({phase:parseInt(this.data.phase_id),title:this.data.title,dueOn:a(this.data.task_due_date),newDueOn:a(this.data.task_due_date),description:this.data.description?this.data.description:"",overallOwner:this.data.assigned_to?this.data.assigned_to:"",applicationReferenceId:this.opportunityId,predecessorActivityId:this.data.predecessor_id,governanceType:this.data.governance_activity_id}),this.checkListCreationForm.controls.checkList.removeAt(0)):this.checkListForm.reset()}))}addNewItemIncheckList(e,t){e.push(this.fb.group({activityName:[""],days:[""],owner:[""]}))}get checkListForm(){return this.checkListCreationForm.get("checkList")}closeDialog(){this.dialogRef.close()}removeItemFromCheckList(e,t){e.removeAt(t)}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.i),r["\u0275\u0275directiveInject"](s.h),r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](s.b),r["\u0275\u0275directiveInject"](D.a),r["\u0275\u0275directiveInject"](I.a),r["\u0275\u0275directiveInject"](P.a),r["\u0275\u0275directiveInject"](j.a),r["\u0275\u0275directiveInject"](T.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-check-list-creation"]],outputs:{close:"close"},features:[r["\u0275\u0275NgOnChangesFeature"]],decls:59,vars:20,consts:[[3,"formGroup"],[1,"container","createMailStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row",2,"margin-top","10px"],[1,"col-12"],[1,"row"],[1,"col-9","pl-0"],["required","true","placeholder","Phase","formControlName","phase",1,"create-account-field",3,"list"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","placeholder","Description","formControlName","description"],["placeholder","Presales Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["placeholder","Sales Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["placeholder","Marketing Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["class","row","formArrayName","checkList",4,"ngIf"],[1,"row","pt-2"],[1,"col-5","pl-0"],["matInput","","required","true","formControlName","dueOn",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-4","ml-2","pt-2"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-9","pl-0","organizer"],["label","Owner","formControlName","overallOwner","required","true",3,"isAutocomplete"],[1,"row","pt-5"],[1,"col-9","pr-3","quotes"],[1,"col-3","pl-0"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create checklist",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit checklist","type","submit",3,"click",4,"ngIf"],["formArrayName","checkList",1,"row"],[1,"col-12","p-0"],["class","row pt-1 border-bottom solid slide-down",3,"formGroupName",4,"ngFor","ngForOf"],[1,"row","pt-1","border-bottom","solid","slide-down",3,"formGroupName"],[1,"col-10","pl-0"],["matInput","","placeholder","Activity Name","formControlName","activityName"],[1,"col-2","d-flex","pt-2"],["color","primary","mat-icon-button","",1,"remove-icon",3,"click"],["mat-icon-button","","matTooltip","Create checklist",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit checklist","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"form",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",4),r["\u0275\u0275elementStart"](6,"button",5),r["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),r["\u0275\u0275elementStart"](7,"mat-icon",6),r["\u0275\u0275text"](8,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",7),r["\u0275\u0275elementStart"](10,"div",8),r["\u0275\u0275elementStart"](11,"div",9),r["\u0275\u0275elementStart"](12,"div",10),r["\u0275\u0275element"](13,"app-input-search",11),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",9),r["\u0275\u0275elementStart"](15,"div",10),r["\u0275\u0275elementStart"](16,"mat-form-field",12),r["\u0275\u0275elementStart"](17,"mat-label"),r["\u0275\u0275text"](18,"Title"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](19,"input",13),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](20,"div",9),r["\u0275\u0275elementStart"](21,"div",10),r["\u0275\u0275elementStart"](22,"mat-form-field",12),r["\u0275\u0275elementStart"](23,"mat-label"),r["\u0275\u0275text"](24,"Description"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](25,"input",14),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](26,"div",9),r["\u0275\u0275elementStart"](27,"div",10),r["\u0275\u0275element"](28,"app-input-search",15),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](29,"div",9),r["\u0275\u0275elementStart"](30,"div",10),r["\u0275\u0275element"](31,"app-input-search",16),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](32,"div",9),r["\u0275\u0275elementStart"](33,"div",10),r["\u0275\u0275element"](34,"app-input-search",17),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](35,G,3,1,"div",18),r["\u0275\u0275elementStart"](36,"div",19),r["\u0275\u0275elementStart"](37,"div",20),r["\u0275\u0275elementStart"](38,"mat-form-field",12),r["\u0275\u0275elementStart"](39,"mat-label"),r["\u0275\u0275text"](40,"Due on"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](41,"input",21),r["\u0275\u0275element"](42,"mat-datepicker-toggle",22),r["\u0275\u0275element"](43,"mat-datepicker",null,23),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](45,"div",24),r["\u0275\u0275elementStart"](46,"button",25),r["\u0275\u0275listener"]("click",(function(){return t.fillUpDueOn("today")})),r["\u0275\u0275text"](47," Today"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](48,"button",25),r["\u0275\u0275listener"]("click",(function(){return t.fillUpDueOn("tommorow")})),r["\u0275\u0275text"](49," Tommorow"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](50,"div",9),r["\u0275\u0275elementStart"](51,"div",26),r["\u0275\u0275element"](52,"app-search-user",27),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](53,"div",28),r["\u0275\u0275elementStart"](54,"div",29),r["\u0275\u0275text"](55,' "What we dwell is, what we become" '),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](56,"div",30),r["\u0275\u0275template"](57,N,3,0,"button",31),r["\u0275\u0275template"](58,V,3,0,"button",32),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](44);r["\u0275\u0275property"]("formGroup",t.checkListCreationForm),r["\u0275\u0275advance"](4),r["\u0275\u0275textInterpolate1"]("",t.mode," checklist "),r["\u0275\u0275advance"](9),r["\u0275\u0275property"]("list",t.phase),r["\u0275\u0275advance"](15),r["\u0275\u0275property"]("list",t.governanceTypes),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("list",t.salesGovernanceTypes),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("list",t.marketingGovernanceTypes),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","Edit"!=t.mode),r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("matDatepicker",e),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("for",e),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction2"](14,U,1==t.checkWhetherDueOnTodaysDateOrTommorowsDate("today"),0==t.checkWhetherDueOnTodaysDateOrTommorowsDate("today"))),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction2"](17,U,1==t.checkWhetherDueOnTodaysDateOrTommorowsDate("tommorrow"),0==t.checkWhetherDueOnTodaysDateOrTommorowsDate("tommorrow"))),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("isAutocomplete",!0),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngIf","Edit"!=t.mode),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","Edit"==t.mode)}},directives:function(){return[o.J,o.w,o.n,L.a,x.a,y,o.F,o.v,o.l,p.c,p.g,k.b,o.e,l.NgIf,_.g,_.i,p.i,_.f,l.NgClass,E,o.h,l.NgForOf,o.o]},styles:[".createMailStyles[_ngcontent-%COMP%]{background-image:url(change_date_bg.95645ca566cff500ae31.png);background-size:240px 170px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 104%;min-height:95vh}.createMailStyles[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.createMailStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.createMailStyles[_ngcontent-%COMP%]   .organizer[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:42px}.createMailStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{margin-right:6px!important;margin-bottom:3px;color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24);font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px}.createMailStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.createMailStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.createMailStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createMailStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.createMailStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createMailStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]{color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .remove-icon[_ngcontent-%COMP%]{height:32px!important;line-height:32px!important}.createMailStyles[_ngcontent-%COMP%]   .slide-down[_ngcontent-%COMP%]{animation:slide-down .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-down{0%{transform:translateY(-30px)}to{transform:translateY(0)}}"]}),e})()},"Tzv/":function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i("mrSG"),r=i("XNiG"),o=i("2Vo4"),a=i("fXoL");let s=(()=>{class e{constructor(){this.reloadSubject=new r.b,this.pipelineSubject=new r.b,this.searchRetainSubject=new o.a(""),this.searchData=this.searchRetainSubject.asObservable(),this.salesGovernanceTypeSubject=new o.a([]),this.salesReportGovernanceTypeSubject=new o.a([]),this.salesProposalTypeSubject=new r.b,this.bidManagerSearchSubject=new o.a(""),this.globalBidManagerSearchData=this.bidManagerSearchSubject.asObservable(),this.bidManagerSearchClear=new o.a(!1),this.bidManagerSearchClearFlag=this.bidManagerSearchSubject.asObservable(),this.salesGovernanceSearchSubject=new o.a(""),this.bidManagerUserVariants=new o.a([]),this.refreshVariant=new r.b}sendNotification(e){console.log(e),this.reloadSubject.next(e)}getNotification(){return this.reloadSubject.asObservable()}sendPiplelineContent(e){console.log(e),this.pipelineSubject.next(e)}getPiplelineContent(){return this.pipelineSubject.asObservable()}getRetainSearchContent(){return this.searchData}sendRetainSearchContent(e){console.log(e),this.searchRetainSubject.next(e)}sendGovTypes(e){console.log(e),this.salesGovernanceTypeSubject.next(e)}getGovTypes(){return this.salesGovernanceTypeSubject.asObservable()}getGovTypesValue(){return Object(n.c)(this,void 0,void 0,(function*(){return this.salesGovernanceTypeSubject.getValue()}))}sendSalesGovTypes(e){console.log(e),this.salesReportGovernanceTypeSubject.next(e)}getSalesGovTypes(){return this.salesReportGovernanceTypeSubject.asObservable()}getSalesGovTypesValue(){return Object(n.c)(this,void 0,void 0,(function*(){return this.salesReportGovernanceTypeSubject.getValue()}))}sendProposalFilter(e){console.log(e),this.pipelineSubject.next(e)}getProposalFilter(){return this.pipelineSubject.asObservable()}sendBidManagerSearch(e){console.log(e),this.bidManagerSearchSubject.next(e)}getBidManagerSearchValue(){return this.globalBidManagerSearchData}sendClearBidManagerSearch(e){this.bidManagerSearchClear.next(e)}getClearBidManagerSearch(){return this.bidManagerSearchClear.asObservable()}getClearBidManagerSearchValue(){return this.bidManagerSearchClear.getValue()}sendBidManagerUserVariants(e){this.bidManagerUserVariants.next(e)}getBidManagerUserVariants(){return this.bidManagerUserVariants.asObservable()}refreshBidManagerUserVariants(){return this.refreshVariant.asObservable()}sendRefreshFlag(e){this.refreshVariant.next(e)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);