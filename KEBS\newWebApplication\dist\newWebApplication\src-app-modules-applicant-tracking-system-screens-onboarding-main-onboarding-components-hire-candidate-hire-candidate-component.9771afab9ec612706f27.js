(window.webpackJsonp=window.webpackJsonp||[]).push([[923],{YEgr:function(e,t,n){"use strict";n.r(t),n.d(t,"MY_FORMATS",(function(){return z})),n.d(t,"HireCandidateComponent",(function(){return W}));var r=n("mrSG"),i=n("3Pt+"),o=n("0IaG"),a=n("1yaQ"),s=n("FKr1"),l=n("XNiG"),c=n("1G5W"),d=n("wd/R"),m=n("fXoL"),h=n("rQiX"),p=n("XNFG"),g=n("RThm"),u=n("NFeN"),f=n("ofXK"),v=n("Xa2L"),C=n("Qu3c"),y=n("kmnG"),M=n("qFsG"),_=n("iadO"),b=n("UVjm");function D(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",23),m["\u0275\u0275element"](1,"mat-spinner",24),m["\u0275\u0275elementEnd"]())}function O(e,t){}function E(e,t){if(1&e&&m["\u0275\u0275template"](0,O,0,0,"ng-template",32),2&e){m["\u0275\u0275nextContext"](4);const e=m["\u0275\u0275reference"](26);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function F(e,t){}function x(e,t){if(1&e&&m["\u0275\u0275template"](0,F,0,0,"ng-template",32),2&e){m["\u0275\u0275nextContext"](4);const e=m["\u0275\u0275reference"](28);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function w(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span",31),m["\u0275\u0275text"](1),m["\u0275\u0275template"](2,E,1,1,void 0,30),m["\u0275\u0275template"](3,x,1,1,void 0,30),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2);m["\u0275\u0275property"]("matTooltip",e.label),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.label," "),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.isMandatory),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.hireFormGroup.get(e.key).hasError("required")&&t.hireFormGroup.get(e.key).touched)}}function P(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",33),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function L(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"mat-form-field",34),m["\u0275\u0275element"](2,"input",35),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function k(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"mat-form-field",34),m["\u0275\u0275elementStart"](2,"input",36),m["\u0275\u0275listener"]("keydown",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).preventInvalidValues(t)}))("paste",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).preventPaste(t)})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function S(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"mat-form-field",34),m["\u0275\u0275elementStart"](2,"input",37),m["\u0275\u0275listener"]("keydown",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).preventInvalidValues(t)}))("paste",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).preventPaste(t)})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function j(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"mat-form-field",34),m["\u0275\u0275elementStart"](2,"div",38),m["\u0275\u0275element"](3,"input",39),m["\u0275\u0275elementStart"](4,"mat-datepicker-toggle",40),m["\u0275\u0275elementStart"](5,"mat-icon",41),m["\u0275\u0275text"](6," calendar_today "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"mat-datepicker",42,43),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275reference"](8),t=m["\u0275\u0275nextContext"]().$implicit,n=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("min","doj"==t.key?n.currentDate:null)("max",null)("placeholder",t.placeholder)("matDatepicker",e)("formControlName",t.key),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("for",e)}}const I=function(){return[]},V=function(e){return{field:e}};function A(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"app-single-select-chip",44),m["\u0275\u0275listener"]("onValueChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("placeholder",e.placeholder)("masterData",(null==e?null:e.masterData)||m["\u0275\u0275pureFunction0"](6,I))("selectedValue",t.hireFormGroup.get(e.key).value)("displayClose",!1)("data",m["\u0275\u0275pureFunction1"](7,V,e.key))("executeMasterOnChanges",!1)}}const T=function(){return["section-title","checkbox"]};function G(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div"),m["\u0275\u0275elementStart"](2,"div",28),m["\u0275\u0275template"](3,w,4,4,"span",29),m["\u0275\u0275template"](4,P,3,1,"ng-container",30),m["\u0275\u0275template"](5,L,3,2,"ng-container",30),m["\u0275\u0275template"](6,k,3,2,"ng-container",30),m["\u0275\u0275template"](7,S,3,2,"ng-container",30),m["\u0275\u0275template"](8,j,9,6,"ng-container",30),m["\u0275\u0275template"](9,A,2,9,"ng-container",30),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275classMapInterpolate1"]("col-",e.col," p-0"),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",!m["\u0275\u0275pureFunction0"](10,T).includes(e.fieldType)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","section-title"==e.fieldType),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","text"==e.fieldType),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","number"==e.fieldType),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","employee-id"==e.fieldType),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","date"==e.fieldType),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","single-select"==e.fieldType)}}function R(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",25),m["\u0275\u0275elementStart"](1,"form",26),m["\u0275\u0275template"](2,G,10,11,"ng-container",27),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroup",e.hireFormGroup),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.hireFormFields)}}function Y(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",45),m["\u0275\u0275text"](1,"*"),m["\u0275\u0275elementEnd"]())}function H(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",45),m["\u0275\u0275text"](1,"This field is required"),m["\u0275\u0275elementEnd"]())}const N=function(e){return{"pointer-events":e}},z={parse:{dateInput:"DD MMM YYYY"},display:{dateInput:"DD MMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let W=(()=>{class e{constructor(e,t,n,r,i,o){this.data=e,this._dialogRef=t,this._fb=n,this._atsMasterService=r,this._toaster=i,this._onboardingService=o,this._onDestroy=new l.b,this.hireFormFields=[],this.currentHireFormDetails={},this.employeeMasterData=[],this.currentDate=d(),this.isLoading=!0,this.hireFormGroup=this._fb.group({})}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.getAtsFormsConfig("onboardingInitiateHire"),yield Promise.all([this.getDefaultDetailsForAnOnboardingCandidate(),this.getEntity(),this.getDepartment(),this.getEmploymentType(),this.getWorkLocation(),this.getWorkSchedule(),this.getHolidayCalendar(),this.getRegion(),this.getDesignation(),this.getJobType(),this.getLevel(),this.getAssociateDetailsInSystem()]).then(e=>Object(r.c)(this,void 0,void 0,(function*(){yield this.createAndPatchHireForm(),yield this.triggerMasterDataApi(),this.isLoading=!1}))).catch(e=>{this.onClose()})}))}onClose(){this._dialogRef.close()}preventPaste(e){e.preventDefault()}preventInvalidValues(e){var t;["0","1","2","3","4","5","6","7","8","9","Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab"].includes(e.key)||e.preventDefault(),10!=(null===(t=e.target)||void 0===t?void 0:t.value).length||["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab"].includes(e.key)||e.preventDefault()}onCustomSelectValueChange(e){this.hireFormGroup.get(e.data.field).setValue(e.val)}syncMasterData(e,t){let n=this.hireFormFields.findIndex(t=>t.key==e);-1!=n&&(this.hireFormFields[n].masterData=t)}createAndPatchHireForm(){return Object(r.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.hireFormFields.length;e++){let t=this.currentHireFormDetails[this.hireFormFields[e].key];["date"].includes(this.hireFormFields[e].fieldType)&&(t=t?d(t):null);const n=this.hireFormFields[e].pattern||null;["section-title"].includes(this.hireFormFields[e].fieldType)||this.hireFormGroup.addControl(this.hireFormFields[e].key,this._fb.control({value:t,disabled:this.hireFormFields[e].isDisabled},[this.hireFormFields[e].isMandatory?i.H.required:null,n?i.H.pattern(n):null].filter(e=>null!=e)))}}))}triggerMasterDataApi(){var e,t,n,i,o,a;return Object(r.c)(this,void 0,void 0,(function*(){if(null===(e=this.hireFormGroup)||void 0===e?void 0:e.get("entity")){let e=null===(n=null===(t=this.hireFormGroup)||void 0===t?void 0:t.get("entity"))||void 0===n?void 0:n.value;if(e&&(yield this.getDivision(e),null===(i=this.hireFormGroup)||void 0===i?void 0:i.get("division"))){let t=null===(a=null===(o=this.hireFormGroup)||void 0===o?void 0:o.get("division"))||void 0===a?void 0:a.value;t&&(yield this.getSubDivision(e,t))}}this.createFormValueChanges()}))}createFormValueChanges(){var e,t,n,i;return Object(r.c)(this,void 0,void 0,(function*(){let r=this.hireFormFields.findIndex(e=>"entity"==e.key),o=this.hireFormFields.findIndex(e=>"division"==e.key);-1!=r&&(null===(t=null===(e=this.hireFormGroup)||void 0===e?void 0:e.get("entity"))||void 0===t||t.valueChanges.subscribe(e=>{this.getDivision(e),this.hireFormGroup.patchValue({division:null,subDivision:null}),this.syncMasterData("division",[]),this.syncMasterData("subDivision",[])}),-1!=o&&(null===(i=null===(n=this.hireFormGroup)||void 0===n?void 0:n.get("division"))||void 0===i||i.valueChanges.subscribe(e=>{var t;this.getSubDivision(null===(t=this.hireFormGroup.get("entity"))||void 0===t?void 0:t.value,e),this.hireFormGroup.patchValue({subDivision:null}),this.syncMasterData("subDivision",[])})))}))}hireCandidate(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.markFormGroupTouched(this.hireFormGroup),this.hireFormGroup.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Fill All Mandatory Fields!",7e3);this.isLoading=!0;let e=yield this.constructPayloadForCandidateForm(),t={candidateId:this.data.candidateId,jobId:this.data.jobId,formDetails:e,candidateDetails:this.currentHireFormDetails},n=yield this.hireCandidateFromOnboarding(t);this.isLoading=!1,n&&this.onClose()}))}markFormGroupTouched(e){Object.values(e.controls).forEach(e=>{e instanceof i.m?this.markFormGroupTouched(e):(e.markAsTouched(),e.markAllAsTouched(),e.markAsDirty())})}constructPayloadForCandidateForm(){return Object(r.c)(this,void 0,void 0,(function*(){let e=this.hireFormGroup.getRawValue();for(let t=0;t<this.hireFormFields.length;t++){const n=this.hireFormFields[t].key;"date"==this.hireFormFields[t].fieldType&&(e[n]=e[n]?d(e[n]).format("YYYY-MM-DD"):null)}return e}))}getAtsFormsConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(c.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.hireFormFields=e.data.form[0].formFields,t(!0)):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}getEntity(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getEntity().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("entity",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Entity Master Data Retrieval Failed!",7e3),t()}}))}))}getDivision(e){return Object(r.c)(this,void 0,void 0,(function*(){if(e)return new Promise((t,n)=>this._atsMasterService.getDivision(e).pipe(Object(c.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.syncMasterData("division",e.data):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Division Master Data Retrieval Failed!",7e3),n()}}))}))}getSubDivision(e,t){return Object(r.c)(this,void 0,void 0,(function*(){if(e&&t)return new Promise((n,r)=>this._atsMasterService.getSubDivision(e,t).pipe(Object(c.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.syncMasterData("subDivision",e.data):this._toaster.showError("Error",e.msg,7e3),n(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Sub Division Master Data Retrieval Failed!",7e3),r()}}))}))}getDepartment(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getDepartment().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("department",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Department Master Data Retrieval Failed!",7e3),t()}}))}))}getEmploymentType(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getEmploymentType().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("employmentType",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Employment Type Master Data Retrieval Failed!",7e3),t()}}))}))}getWorkLocation(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getWorkLocation().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("workLocation",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Work Location Master Data Retrieval Failed!",7e3),t()}}))}))}getAssociateDetailsInSystem(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAssociateDetailsInSystem().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this.employeeMasterData=t.data,this.syncMasterData("directManager",t.data)):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Team Master Data Retrieval Failed!",7e3),t()}}))}))}getWorkSchedule(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getWorkSchedule().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("workSchedule",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Work Schedule Master Data Retrieval Failed!",7e3),t()}}))}))}getHolidayCalendar(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getHolidayCalendar().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("holidayCalendar",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Holiday Calendar Master Data Retrieval Failed!",7e3),t()}}))}))}getRegion(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getRegion().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("region",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Region Master Data Retrieval Failed!",7e3),t()}}))}))}getDesignation(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getDesignation().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("position",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Region Master Data Retrieval Failed!",7e3),t()}}))}))}getJobType(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getJobType().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("jobOrDesignation",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Job/Designation Master Data Retrieval Failed!",7e3),t()}}))}))}getLevel(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getLevel().pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.syncMasterData("level",t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Level Master Data Retrieval Failed!",7e3),t()}}))}))}getDefaultDetailsForAnOnboardingCandidate(){return Object(r.c)(this,void 0,void 0,(function*(){let e={candidateId:this.data.candidateId,jobId:this.data.jobId};return new Promise((t,n)=>this._onboardingService.getDefaultDetailsForAnOnboardingCandidate(e).pipe(Object(c.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.currentHireFormDetails=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Default Form Data Retrieval Failed!",7e3),n()}}))}))}hireCandidateFromOnboarding(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._onboardingService.hireCandidateFromOnboarding(e).pipe(Object(c.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success \u2705",e.msg,7e3),t(!0)):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"Failed To Hire Candidate!",7e3),this.isLoading=!1,n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](o.a),m["\u0275\u0275directiveInject"](o.h),m["\u0275\u0275directiveInject"](i.i),m["\u0275\u0275directiveInject"](h.a),m["\u0275\u0275directiveInject"](p.a),m["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-hire-candidate"]],features:[m["\u0275\u0275ProvidersFeature"]([{provide:s.c,useClass:a.c,deps:[s.f,a.a]},{provide:s.e,useValue:z}])],decls:29,vars:7,consts:[[1,"main-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"d-flex","align-items-center"],["width","269","height","56","viewBox","0 0 269 56","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M133.603 -22.5168V4.76469C133.603 8.35397 132.087 11.811 129.39 14.4067C125.91 17.7302 120.857 19.1903 115.947 18.2782L115.946 18.2781L93.9778 14.2877L103.74 5.17581L116.468 7.51274L116.468 7.51278C119.364 8.04124 122.088 5.97651 122.088 3.19398V-11.8208L133.603 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.019 14.2879L157.079 18.2781L157.078 18.2782C152.174 19.1903 147.127 17.73 143.651 14.4063L143.649 14.4043C140.909 11.856 139.394 8.40031 139.394 4.81089V-22.5164L150.895 -11.8208V2.96299C150.895 5.88334 153.764 8.04193 156.758 7.46638C156.758 7.46632 156.758 7.46626 156.759 7.46621L169.221 5.17578L179.019 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.603 37.6429V64.9403L122.088 54.256V38.8889C122.088 36.2929 119.563 34.3707 116.817 34.8512L116.816 34.8515L103.74 37.2322L93.9781 28.1306L115.946 24.1447L115.947 24.1445C120.857 23.2335 125.91 24.6921 129.391 28.012C132.087 30.6048 133.603 34.0579 133.603 37.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.394 64.9399V37.6584C139.394 34.0692 140.91 30.6122 143.607 28.0166C147.087 24.693 152.141 23.2328 157.05 24.1448L157.051 24.145L179.018 28.1352L169.258 37.2012L155.933 34.7718C155.933 34.7718 155.933 34.7718 155.933 34.7718C153.335 34.2893 150.91 36.1234 150.91 38.6285V54.2438L139.394 64.9399Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.5926L63.5512 55.5828L63.5504 55.5829C58.6468 56.495 53.5996 55.0347 50.1236 51.711L50.1214 51.709C47.3813 49.1606 45.8669 45.705 45.8669 42.1156V14.7883L57.3676 25.4838V40.2677C57.3676 43.188 60.2364 45.3466 63.2304 44.7711C63.2307 44.771 63.231 44.771 63.2313 44.7709L75.6933 42.4805L85.4914 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.2879L63.5512 55.2781L63.5504 55.2782C58.6468 56.1903 53.5996 54.73 50.1236 51.4063L50.1214 51.4043C47.3813 48.856 45.8669 45.4003 45.8669 41.8109V14.4836L57.3676 25.1792V39.963C57.3676 42.8833 60.2364 45.0419 63.2304 44.4664C63.2307 44.4663 63.231 44.4663 63.2313 44.4662L75.6933 42.1758L85.4914 51.2879Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 14.7878V42.0694C227.13 45.6587 225.614 49.1157 222.918 51.7114C219.437 55.0348 214.384 56.495 209.475 55.5829L209.474 55.5828L187.505 51.5924L197.267 42.4805L209.995 44.8174L209.995 44.8175C212.892 45.3459 215.615 43.2812 215.615 40.4987V25.4839L227.13 14.7878Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.546 51.5926L250.606 55.5828L250.605 55.5829C245.702 56.495 240.654 55.0347 237.178 51.711L237.176 51.709C234.436 49.1606 232.922 45.705 232.922 42.1156V14.7883L244.422 25.4838V40.2677C244.422 43.188 247.291 45.3466 250.285 44.7711C250.285 44.771 250.286 44.771 250.286 44.7709L262.748 42.4805L272.546 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],[1,"icon",3,"click"],[1,"icon"],["class","d-flex align-items-center justify-content-center loading-screen",4,"ngIf"],["class","main-screen",4,"ngIf"],[1,"d-flex","align-items-center","justify-content-end","footer"],[1,"button",3,"ngStyle","click"],["mandatoryTemplate",""],["requiredTemplate",""],[1,"d-flex","align-items-center","justify-content-center","loading-screen"],["diameter","40",1,"green-spinner"],[1,"main-screen"],[1,"forms",3,"formGroup"],[4,"ngFor","ngForOf"],[1,"field-width"],["class","form-label",3,"matTooltip",4,"ngIf"],[4,"ngIf"],[1,"form-label",3,"matTooltip"],[3,"ngTemplateOutlet"],[1,"form-section-title"],["appearance","outline",1,"form-field-class"],["type","text","maxlength","255","matInput","",3,"placeholder","formControlName"],["type","number","min","0","matInput","",3,"placeholder","formControlName","keydown","paste"],["type","text","min","0","maxlength","9","matInput","",3,"placeholder","formControlName","keydown","paste"],[1,"date-picker"],["matInput","","disabled","",3,"min","max","placeholder","matDatepicker","formControlName"],[3,"for"],["matDatepickerToggleIcon","",1,"calendar-icon"],["disabled","false"],["dp",""],[3,"placeholder","masterData","selectedValue","displayClose","data","executeMasterOnChanges","onValueChange"],[1,"required-template"]],template:function(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",3),m["\u0275\u0275elementStart"](5,"div"),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](6,"svg",4),m["\u0275\u0275element"](7,"path",5),m["\u0275\u0275element"](8,"path",6),m["\u0275\u0275element"](9,"path",7),m["\u0275\u0275element"](10,"path",8),m["\u0275\u0275element"](11,"path",9),m["\u0275\u0275element"](12,"path",10),m["\u0275\u0275element"](13,"path",11),m["\u0275\u0275element"](14,"path",12),m["\u0275\u0275element"](15,"path",13),m["\u0275\u0275element"](16,"path",14),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275namespaceHTML"](),m["\u0275\u0275elementStart"](17,"div",15),m["\u0275\u0275listener"]("click",(function(){return t.onClose()})),m["\u0275\u0275elementStart"](18,"mat-icon",16),m["\u0275\u0275text"](19,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](20,D,2,0,"div",17),m["\u0275\u0275template"](21,R,3,2,"div",18),m["\u0275\u0275elementStart"](22,"div",19),m["\u0275\u0275elementStart"](23,"div",20),m["\u0275\u0275listener"]("click",(function(){return t.hireCandidate()})),m["\u0275\u0275text"](24),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](25,Y,2,0,"ng-template",null,21,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275template"](27,H,2,0,"ng-template",null,22,m["\u0275\u0275templateRefExtractor"])),2&e&&(m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"]((null==t.data?null:t.data.uiTextConfig["UI-HIRE-001"])||""),m["\u0275\u0275advance"](17),m["\u0275\u0275property"]("ngIf",t.isLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!t.isLoading),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](5,N,t.isLoading?"none":"")),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",(null==t.data?null:t.data.uiTextConfig["UI-HIRE-002"])||""," "))},directives:[u.a,f.NgIf,f.NgStyle,v.c,i.J,i.w,i.n,f.NgForOf,C.a,f.NgTemplateOutlet,y.c,M.b,i.e,i.q,i.v,i.l,i.A,_.g,_.i,_.j,_.f,b.a],styles:[".main-container[_ngcontent-%COMP%]{overflow:hidden}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#f4f4f6;position:absolute;z-index:1}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]{margin-top:20%}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]{padding:20px 24px;margin-top:56px;margin-bottom:56px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;width:100%;row-gap:16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-section-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .field-width[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:90%}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#5f6c81;font-size:12px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline:hover .mat-form-field-outline{color:#d63031!important;border:1px solid #d63031!important;border-radius:5px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#45546e!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:var(--atsfontFamily)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .date-picker[_ngcontent-%COMP%]{display:flex;align-items:center;height:17px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .calendar-icon[_ngcontent-%COMP%]{font-size:16px}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#fff;position:absolute;z-index:1;bottom:0}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%]{background-color:var(--atsprimaryColor);border-radius:8px;padding:8px 14px;cursor:pointer;font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff}.required-template[_ngcontent-%COMP%]{color:#cf0001;font-family:var(--atsfontFamily);font-size:9px}"]}),e})()}}]);