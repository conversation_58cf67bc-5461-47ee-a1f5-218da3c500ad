(window.webpackJsonp=window.webpackJsonp||[]).push([[999],{DiCY:function(t,e,n){"use strict";n.r(e),n.d(e,"UdrfNewSettingsModalComponent",(function(){return M}));var i=n("mrSG"),o=n("0IaG"),r=n("xG9w"),a=n("ofXK"),l=n("bTqV"),c=n("NFeN"),d=n("Qu3c"),p=n("5+WD"),m=n("QibW"),s=(n("Xi0T"),n("1jcm")),g=n("fXoL"),h=n("GnQ3"),x=n("LcQX");function f(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"p",17),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",t.description,"")}}function u(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"p",21),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275property"]("matTooltip",t.description),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](t.description)}}function b(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"mat-radio-button",18),g["\u0275\u0275elementStart"](1,"p",19),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](3,u,2,2,"p",20),g["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=e.index,i=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275classMapInterpolate1"]("",n==i.items.length-1?"":i.additionalItemClass," col-5-5 pl-2 pr-2 mt-2 radio-button"),g["\u0275\u0275property"]("value",t.value)("ngClass",t.value==i.value?"radio-selected":""),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("matTooltip",t.header),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](t.header),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(!t.description||""==t.description))}}function v(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",12),g["\u0275\u0275elementStart"](1,"p",13),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](3,f,2,1,"p",14),g["\u0275\u0275elementStart"](4,"mat-radio-group",15),g["\u0275\u0275listener"]("change",(function(e){g["\u0275\u0275restoreView"](t);const n=g["\u0275\u0275nextContext"]().$implicit;return g["\u0275\u0275nextContext"]().radioChange(e,n)})),g["\u0275\u0275template"](5,b,4,8,"mat-radio-button",16),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](t.header),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(!t.description||""==t.description)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("value",t.value),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",t.items)}}function C(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"p",17),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",t.description,"")}}function k(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"p",31),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275property"]("matTooltip",t.description),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](t.description)}}function w(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"div",24),g["\u0275\u0275elementStart"](1,"div",25),g["\u0275\u0275elementStart"](2,"mat-icon",26),g["\u0275\u0275text"](3,"drag_indicator "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",27),g["\u0275\u0275elementStart"](5,"p",28),g["\u0275\u0275text"](6),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](7,k,2,2,"p",29),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"mat-slide-toggle",30),g["\u0275\u0275listener"]("change",(function(){const t=e.$implicit;return t.isChecked=!t.isChecked})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("matTooltip",t.header)("ngClass",t.isChecked?"red-item":"black-item"),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",t.header,""),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(!t.description||""==t.description)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("checked",t.isChecked)}}function S(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",12),g["\u0275\u0275elementStart"](1,"p",13),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](3,C,2,1,"p",14),g["\u0275\u0275elementStart"](4,"div",22),g["\u0275\u0275listener"]("cdkDropListDropped",(function(e){g["\u0275\u0275restoreView"](t);const n=g["\u0275\u0275nextContext"]().$implicit;return g["\u0275\u0275nextContext"]().dragDropItem(e,n)})),g["\u0275\u0275template"](5,w,9,5,"div",23),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](t.header),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(!t.description||""==t.description)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("cdkDropListData",t.items),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",t.items)}}function y(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"p",17),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",t.description,"")}}function I(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"p",39),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275property"]("matTooltip",t.description),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](t.description)}}function O(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"div",34),g["\u0275\u0275elementStart"](1,"div",35),g["\u0275\u0275elementStart"](2,"div",36),g["\u0275\u0275elementStart"](3,"p",37),g["\u0275\u0275text"](4),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](5,I,2,2,"p",38),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"mat-slide-toggle",30),g["\u0275\u0275listener"]("change",(function(){const t=e.$implicit;return t.isChecked=!t.isChecked})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("matTooltip",t.header)("ngClass",t.isChecked?"red-item":"black-item"),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",t.header," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(!t.description||""==t.description)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("checked",t.isChecked)}}function P(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"div",12),g["\u0275\u0275elementStart"](1,"p",13),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](3,y,2,1,"p",14),g["\u0275\u0275elementStart"](4,"div",32),g["\u0275\u0275template"](5,O,7,5,"div",33),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](t.header),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(!t.description||""==t.description)),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",t.items)}}function E(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275template"](1,v,6,4,"div",11),g["\u0275\u0275template"](2,S,6,4,"div",11),g["\u0275\u0275template"](3,P,6,3,"div",11),g["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;g["\u0275\u0275classMapInterpolate1"]("col-6 ",t.additionalClass," mb-4"),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","radio"==t.type&&t.items.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","checkbox-with-sort"==t.type&&t.items.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","checkbox"==t.type&&t.items.length>0)}}let M=(()=>{class t{constructor(t,e,n,i){this.dialogRef=t,this.inData=e,this.udrfService=n,this.utilityService=i}ngOnInit(){this.modalParams=this.inData.modalParams,this.modalParams.udrfSettings=r.where(this.modalParams.udrfSettings,{isVisible:!0}),this.modalParams.udrfSettings=this.modalParams.udrfSettings.sort((t,e)=>t.order<e.order?-1:t.order>e.order?1:0)}radioChange(t,e){e.value=t.value}dragDropItem(t,e){Object(p.h)(t.container.data,t.previousIndex,t.currentIndex);for(let n=0;n<e.items.length;n++)e.items[n][e.orderField]=n+1}saveChanges(){let t=!0;for(let e of this.modalParams.udrfSettings)e.isVisible&&e.isCheckArray&&e.minCheckedCards&&e.items&&e.items.length>0&&r.where(e.items,{isChecked:!0}).length<e.minCheckedCards&&(this.utilityService.showToastMessage("A minimum of "+e.minCheckedCards+" "+e.header+" have to be checked!"),t=!1),e.isVisible&&e.isCheckArray&&e.maxCheckedCards&&e.items&&e.items.length>0&&r.where(e.items,{isChecked:!0}).length>e.maxCheckedCards&&(this.utilityService.showToastMessage("A maximum of "+e.maxCheckedCards+" "+e.header+" only can be checked!"),t=!1);t&&this.dialogRef.close({event:"Save",modalResult:this.modalParams})}clearSettings(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.udrfService.udrfFunctions.resetUserUdrfConfig(this.modalParams.udrfData)}))}closeModal(){this.dialogRef.close({event:"Close"})}}return t.\u0275fac=function(e){return new(e||t)(g["\u0275\u0275directiveInject"](o.h),g["\u0275\u0275directiveInject"](o.a),g["\u0275\u0275directiveInject"](h.a),g["\u0275\u0275directiveInject"](x.a))},t.\u0275cmp=g["\u0275\u0275defineComponent"]({type:t,selectors:[["udrf-new-settings-modal"]],decls:16,vars:1,consts:[[1,"ml-3","mr-3","mb-3","pt-3","pb-2","bor-bot-grey"],[1,"row"],[1,"col-2","m-0","p-0","header-text"],[1,"col-4-5","p-0"],["matTooltip","Save Changes","mat-raised-button","",1,"col-2","p-0","btn-red",2,"font-weight","normal","min-width","100px",3,"click"],[1,"col-0-5","p-0"],["matTooltip","Clear Settings","mat-raised-button","",1,"col-2","p-0","btn-white-bg-red",2,"font-weight","normal","min-width","100px",3,"click"],["matTooltip","Close","mat-icon-button","","matTooltip","Close",1,"col-1","p-0",3,"click"],[1,"close-icon"],[1,"pl-3","pr-3","row",2,"height","86vh","overflow-y","scroll"],[3,"class",4,"ngFor","ngForOf"],["class","pl-0 pr-0 row col-12",4,"ngIf"],[1,"pl-0","pr-0","row","col-12"],[1,"col-12","m-0","p-0","item-text"],["class","col-12 m-0 p-0 item-description",4,"ngIf"],[1,"p-0","col-12",2,"display","flex",3,"value","change"],[3,"class","value","ngClass",4,"ngFor","ngForOf"],[1,"col-12","m-0","p-0","item-description"],[3,"value","ngClass"],[1,"radio-header","ml-1","mr-0","mt-2","mb-1",3,"matTooltip"],["class","radio-item ml-1 mr-0 mt-0 mb-0",3,"matTooltip",4,"ngIf"],[1,"radio-item","ml-1","mr-0","mt-0","mb-0",3,"matTooltip"],["cdkDropList","",1,"mt-2","col-8","p-0","example-list","mh-200",3,"cdkDropListData","cdkDropListDropped"],["cdkDrag","","class","col-12 p-0 mb-2","style","cursor: pointer;",4,"ngFor","ngForOf"],["cdkDrag","",1,"col-12","p-0","mb-2",2,"cursor","pointer"],[1,"col-12","p-0","pb-2","drop-item","bor-bot-grey",2,"display","flex"],[1,"col-1","mt-2","p-0",2,"font-size","13px","margin","0px","width","15px"],[1,"col-9","p-0","pt-1"],[1,"m-0","p-0",3,"matTooltip","ngClass"],["class","grey-description m-0 pt-1",3,"matTooltip",4,"ngIf"],[1,"col-1","p-0","pt-1","ml-2",2,"margin","0px",3,"checked","change"],[1,"grey-description","m-0","pt-1",3,"matTooltip"],[1,"mt-2","col-8","p-0","example-list","mh-200"],["class","col-12 p-0 mb-2","style","cursor: pointer;",4,"ngFor","ngForOf"],[1,"col-12","p-0","mb-2",2,"cursor","pointer"],[1,"col-12","p-0","pb-2","bor-bot-grey",2,"display","flex"],[1,"col-10","p-0","pt-1"],[1,"m-0","p-0","pl-1",3,"matTooltip","ngClass"],["class","grey-description m-0 pt-1 pl-1",3,"matTooltip",4,"ngIf"],[1,"grey-description","m-0","pt-1","pl-1",3,"matTooltip"]],template:function(t,e){1&t&&(g["\u0275\u0275elementStart"](0,"div",0),g["\u0275\u0275elementStart"](1,"div",1),g["\u0275\u0275elementStart"](2,"p",2),g["\u0275\u0275text"](3,"Settings"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](4,"div",3),g["\u0275\u0275elementStart"](5,"button",4),g["\u0275\u0275listener"]("click",(function(){return e.saveChanges()})),g["\u0275\u0275text"](6," Save Changes "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](7,"div",5),g["\u0275\u0275elementStart"](8,"button",6),g["\u0275\u0275listener"]("click",(function(){return e.clearSettings()})),g["\u0275\u0275text"](9," Clear Settings "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](10,"div",5),g["\u0275\u0275elementStart"](11,"button",7),g["\u0275\u0275listener"]("click",(function(){return e.closeModal()})),g["\u0275\u0275elementStart"](12,"mat-icon",8),g["\u0275\u0275text"](13,"close"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](14,"div",9),g["\u0275\u0275template"](15,E,4,6,"div",10),g["\u0275\u0275elementEnd"]()),2&t&&(g["\u0275\u0275advance"](15),g["\u0275\u0275property"]("ngForOf",e.modalParams.udrfSettings))},directives:[l.a,d.a,c.a,a.NgForOf,a.NgIf,m.b,m.a,a.NgClass,p.e,p.a,s.a],styles:[".udrf-new-settings-modal[_ngcontent-%COMP%]{overflow-y:hidden}.btn-red[_ngcontent-%COMP%]{color:#fff;background-color:#f27a6c;border-radius:6px;font-size:13px;height:36px}.btn-white-bg-red[_ngcontent-%COMP%]{color:#f27a6c;background-color:#fff;border:1px solid #f27a6c;border-radius:6px;font-size:13px;height:36px}.header-text[_ngcontent-%COMP%]{font-weight:500;line-height:35px}.close-icon[_ngcontent-%COMP%], .header-text[_ngcontent-%COMP%]{font-size:17px}.item-text[_ngcontent-%COMP%]{font-weight:500;font-size:14px;word-break:break-word}.item-description[_ngcontent-%COMP%]{font-size:13px;color:rgba(0,0,0,.611764705882353);word-break:break-word}.radio-button[_ngcontent-%COMP%]{border:1px solid hsla(0,0%,50.2%,.5803921568627451);border-radius:6px;display:flex;align-items:center}.radio-header[_ngcontent-%COMP%]{font-weight:500}.radio-header[_ngcontent-%COMP%], .radio-item[_ngcontent-%COMP%]{font-size:13px;white-space:normal;word-break:break-word}.radio-item[_ngcontent-%COMP%]{color:grey}.radio-selected[_ngcontent-%COMP%]{background:rgba(242,122,108,.1607843137254902)}.cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.drop-item[_ngcontent-%COMP%]:hover{background:rgba(186,191,196,.27058823529411763)!important}.red-item[_ngcontent-%COMP%]{color:#f27a6c!important;font-weight:500}.black-item[_ngcontent-%COMP%], .red-item[_ngcontent-%COMP%]{font-size:13px;word-break:break-word}.grey-description[_ngcontent-%COMP%]{font-size:12px;color:rgba(0,0,0,.611764705882353);word-break:break-word}  .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb-container{transform:translate3d(18px,0,0)!important;height:20px!important;width:31px!important;top:1px!important}  .mat-slide-toggle-thumb-container{transform:translate3d(4px,0,0)!important;height:20px!important;width:31px!important;top:1px!important}  .mat-slide-toggle-thumb,   .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff!important;height:13px!important;width:13px!important;margin:1px 1px 1px -1px!important}  .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:#f27a6c!important;height:18px!important;width:34px!important}  .mat-slide-toggle-bar{height:18px!important;width:34px!important}  .mat-radio-container{margin-top:7px}  .mat-radio-button.mat-accent .mat-radio-outer-circle{height:14px!important;width:14px!important;border-color:#f27a6c!important}  .mat-radio-button.mat-accent .mat-radio-inner-circle{height:14px!important;width:14px!important;background-color:#f27a6c!important}.bor-bot-grey[_ngcontent-%COMP%]{border-bottom:1px solid hsla(0,0%,50.2%,.25098039215686274)}.col-5-5[_ngcontent-%COMP%]{flex:1;max-width:48.1%;width:100%}.col-4-5[_ngcontent-%COMP%]{flex:0 0 35%;max-width:35%}.col-0-5[_ngcontent-%COMP%]{flex:0 0 3.333333%;max-width:3.333333%}.bor-righ-grey[_ngcontent-%COMP%]{border-right:1px solid hsla(0,0%,50.2%,.25098039215686274)}.mh-200[_ngcontent-%COMP%]{max-height:200px;overflow-y:scroll}"]}),t})()}}]);