(window.webpackJsonp=window.webpackJsonp||[]).push([[842],{"fhX+":function(e,t,n){"use strict";n.r(t),n.d(t,"QuickbooksInboundFieldmappingModule",(function(){return Y}));var i=n("ofXK"),a=n("tyNb"),o=n("mrSG"),r=n("1G5W"),l=n("XNiG"),d=n("fXoL"),p=n("tk/3");let s=(()=>{class e{constructor(e){this.http=e,this.url="/api/integrationLayer/",this.getQBVoucherTypes=()=>this.http.post(this.url+"getQBVoucherTypes",{}),this.getQBVoucherFieldMappingList=e=>this.http.post(this.url+"getQBVoucherFieldMappingList",{voucher_type:e})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275inject"](p.c))},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var c=n("3Pt+"),g=n("Xa2L"),m=n("Wp6s"),f=n("bTqV"),u=n("STbY"),h=n("Qu3c"),M=n("kmnG"),v=n("qFsG"),y=n("NFeN");const b=["formArrayContainer"],F=["quickbooksField"],C=["kebsField"];function P(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",3),d["\u0275\u0275element"](1,"mat-spinner",4),d["\u0275\u0275elementEnd"]())}function w(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",3),d["\u0275\u0275element"](1,"mat-spinner",4),d["\u0275\u0275elementEnd"]())}function x(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"button",22),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](3).changeVoucherType(n)})),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function k(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",23),d["\u0275\u0275elementStart"](1,"div",24),d["\u0275\u0275elementStart"](2,"div",25),d["\u0275\u0275elementStart"](3,"div",26),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",27),d["\u0275\u0275elementStart"](6,"mat-form-field",28),d["\u0275\u0275element"](7,"input",29,30),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",11),d["\u0275\u0275elementStart"](10,"mat-icon"),d["\u0275\u0275text"](11,"link"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"div",27),d["\u0275\u0275elementStart"](13,"mat-form-field",28),d["\u0275\u0275element"](14,"input",31,32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.index;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroupName",e),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e+1," ")}}function S(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",8),d["\u0275\u0275elementStart"](1,"div",9),d["\u0275\u0275elementStart"](2,"div",10),d["\u0275\u0275element"](3,"div",11),d["\u0275\u0275elementStart"](4,"div",12),d["\u0275\u0275text"](5," QuickBooks Fields "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](6,"div",11),d["\u0275\u0275elementStart"](7,"div",12),d["\u0275\u0275text"](8," KEBS Fields "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",13),d["\u0275\u0275elementStart"](10,"button",14),d["\u0275\u0275elementStart"](11,"span",15),d["\u0275\u0275text"](12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"mat-menu",null,16),d["\u0275\u0275template"](15,x,2,1,"button",17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"form",18),d["\u0275\u0275elementStart"](17,"div",19,20),d["\u0275\u0275template"](19,k,16,2,"div",21),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275reference"](14),t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](10),d["\u0275\u0275property"]("matMenuTriggerFor",e),d["\u0275\u0275advance"](1),d["\u0275\u0275propertyInterpolate"]("matTooltip",t.selectedVoucherType),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("Voucher : ",t.selectedVoucherType,""),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",t.displaymenuMasterData),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",t.fieldMappingForm),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",t.fieldMappingForm.get("fieldMappingArray").controls)}}function _(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275text"](1,"Save"),d["\u0275\u0275elementEnd"]())}function O(e,t){1&e&&d["\u0275\u0275element"](0,"mat-spinner",42)}function L(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275element"](1,"div",34),d["\u0275\u0275elementStart"](2,"div",35),d["\u0275\u0275elementStart"](3,"div",36),d["\u0275\u0275elementStart"](4,"button",37),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).addNewFieldMapping()})),d["\u0275\u0275text"](5," New Field "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",38),d["\u0275\u0275elementStart"](7,"button",39),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).saveFieldMapping()})),d["\u0275\u0275template"](8,_,2,0,"span",40),d["\u0275\u0275template"](9,O,1,0,"mat-spinner",41),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](7),d["\u0275\u0275property"]("ngClass",e.isFieldMappingSaved?"save-fieldMapping-btn-loading":"save-btn"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isFieldMappingSaved),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isFieldMappingSaved)}}function Q(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275template"](1,w,2,0,"div",1),d["\u0275\u0275template"](2,S,20,6,"mat-card",6),d["\u0275\u0275template"](3,L,10,3,"div",7),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isFieldsMappingListRetrieved),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isFieldsMappingListRetrieved),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isFieldsMappingListRetrieved)}}const E=[{path:"",component:(()=>{class e{constructor(e,t){this.api=e,this.fb=t,this._onDestroy=new l.b,this.displaymenuMasterData=[],this.isQBFieldMappingPageGenerated=!1,this.selectedVoucherType="",this.selectedVoucherFieldMappingList=[],this.isFieldsMappingListRetrieved=!1,this.isFieldMappingSaved=!1,this.addFieldMapping=e=>{this.fieldMappingForm.get("fieldMappingArray").insert(e+1,this.addFieldMappingFormControl())},this.fieldMappingForm=this.fb.group({fieldMappingArray:this.fb.array([])})}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.api.getQBVoucherTypes().pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.displaymenuMasterData=e.data,yield this.changeVoucherType(this.displaymenuMasterData[0]),this.isQBFieldMappingPageGenerated=!0):(this.displaymenuMasterData=[],this.isQBFieldMappingPageGenerated=!0)})),e=>{this.isQBFieldMappingPageGenerated=!0,console.log("Error in getQBVoucherTypes"),console.log(e)})}))}changeVoucherType(e){return Object(o.c)(this,void 0,void 0,(function*(){this.isFieldsMappingListRetrieved=!1;const t=this.fieldMappingForm.get("fieldMappingArray");t.clear(),this.selectedVoucherType=e.name,console.log("Selected Voucher Type : ",this.selectedVoucherType),yield this.api.getQBVoucherFieldMappingList(e.qb_voucher_type).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>{"S"===e.messType?(t.clear(),this.selectedVoucherFieldMappingList=e.data.field_mapping,console.log(this.selectedVoucherType+" - Filed Mapping List : ",this.selectedVoucherFieldMappingList),this.selectedVoucherFieldMappingList.forEach(()=>{t.push(this.addFieldMappingFormControl())}),this.selectedVoucherFieldMappingList.forEach((e,n)=>{const i=t.at(n);i?(console.log("Patching control at index",n,"with value:",e),i.patchValue({quickbooks_field:e.external_field,kebs_field:e.kebs_field})):console.warn(`Form control at index ${n} is not defined.`)}),console.log("FormArray after patching:",this.fieldMappingForm.value),this.isFieldsMappingListRetrieved=!0):(this.selectedVoucherFieldMappingList=[],console.log("No Field Mapping List found for voucher type - ",this.selectedVoucherType),this.isFieldsMappingListRetrieved=!0)},e=>{this.isFieldsMappingListRetrieved=!0,console.log("Error in getQBVoucherFieldMappingList"),console.log(e)})}))}addFieldMappingFormControl(){return this.fb.group({quickbooks_field:[""],kebs_field:[""]})}get fieldMappingArray(){return this.fieldMappingForm.get("fieldMappingArray")}getFormArrayLength(){return this.fieldMappingArray.length}addNewFieldMapping(){console.log("Add New Field Mapping Clicked...!"),this.fieldMappingForm.get("fieldMappingArray").push(this.addFieldMappingFormControl());let e=this.getFormArrayLength()-1;setTimeout(()=>{const t=this.formArrayContainer.nativeElement;t.scrollTop=t.scrollHeight;const n=this.quickbooksFields.toArray();n[e]&&n[e].nativeElement.focus()},0)}saveFieldMapping(){console.log("Save Field Mapping Clicked...!")}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](s),d["\u0275\u0275directiveInject"](c.i))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-fieldmapping-landing-page"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](b,!0),d["\u0275\u0275viewQuery"](F,!0),d["\u0275\u0275viewQuery"](C,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.formArrayContainer=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.quickbooksFields=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.kebsFields=e)}},decls:3,vars:2,consts:[[1,"QBfieldMappingLandingPage"],["class","row justify-content-center","style","padding-top: 10vh;",4,"ngIf"],["class","col-12",4,"ngIf"],[1,"row","justify-content-center",2,"padding-top","10vh"],["diameter","30"],[1,"col-12"],["class","fieldMappingCard",4,"ngIf"],["class","row align-items-center addSaveRow",4,"ngIf"],[1,"fieldMappingCard"],[1,"fieldMappingListArray"],[1,"row","titleField","align-items-center"],[1,"col-1"],[1,"col-4","d-flex","justify-content-center"],[1,"col-2"],["mat-raised-button","",1,"vouchersMenuBtn","btn-active",2,"font-weight","normal","color","white","background-color","#cf0001",3,"matMenuTriggerFor"],[3,"matTooltip"],["menu","matMenu"],["mat-menu-item","",3,"click",4,"ngFor","ngForOf"],[1,"formListArray",2,"margin-top","15px",3,"formGroup"],[1,"formArrayContainer"],["formArrayContainer",""],["formArrayName","fieldMappingArray",4,"ngFor","ngForOf"],["mat-menu-item","",3,"click"],["formArrayName","fieldMappingArray"],[2,"margin-bottom","6px",3,"formGroupName"],[1,"row","align-items-center"],[1,"col-1","d-flex","justify-content-center"],[1,"col-5"],["appearance","outline",1,"col-12"],["matInput","","placeholder","QuickBooks Field","formControlName","quickbooks_field","required",""],["quickbooksField",""],["matInput","","placeholder","KEBS Field","formControlName","kebs_field","required",""],["kebsField",""],[1,"row","align-items-center","addSaveRow"],[1,"col-8"],[1,"col-4","d-flex"],[1,"col","addNewFieldMapping"],["mat-mini-fab","","matTooltip","Add New Field Mapping",1,"save-btn",3,"click"],[1,"col","saveFieldMapping"],["mat-mini-fab","","matTooltip","Save Field Mapping",1,"save-btn",3,"ngClass","click"],[4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","20",4,"ngIf"],["matTooltip","Please wait...","diameter","20",1,"spinner-align"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,P,2,0,"div",1),d["\u0275\u0275template"](2,Q,4,3,"div",2),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isQBFieldMappingPageGenerated),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isQBFieldMappingPageGenerated))},directives:[i.NgIf,g.c,m.a,f.a,u.f,h.a,u.g,i.NgForOf,c.J,c.w,c.n,u.d,c.h,c.o,M.c,v.b,c.e,c.v,c.l,c.F,y.a,i.NgClass],styles:[".QBfieldMappingLandingPage[_ngcontent-%COMP%]   .headerClass[_ngcontent-%COMP%]{font-size:medium;font-weight:500;color:#cf0001}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .matOptions[_ngcontent-%COMP%]:hover   .newTabIcon[_ngcontent-%COMP%]{font-size:21px!important;color:#545352!important}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .newTabIcon[_ngcontent-%COMP%]{color:#fff}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .example-month-picker[_ngcontent-%COMP%]   .mat-calendar-period-button[_ngcontent-%COMP%]{pointer-events:none}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .example-month-picker[_ngcontent-%COMP%]   .mat-calendar-arrow[_ngcontent-%COMP%]{display:none}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .dateRangeButton[_ngcontent-%COMP%]{border:none;background-color:initial}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .fieldMappingCard[_ngcontent-%COMP%]{border:1px solid #ccc;box-shadow:0 4px 8px rgba(0,0,0,.2);border-radius:4px;padding:16px}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .titleField[_ngcontent-%COMP%]{font-size:15px;font-weight:500;color:#cf0001;border-bottom:1px solid #000}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .vouchersMenuBtn[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;width:100%}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .add_btn[_ngcontent-%COMP%]{height:33px;width:33px;display:flex;cursor:pointer;border:1px solid #efa6a6;align-items:center;border-radius:4px;justify-content:center}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .addSaveRow[_ngcontent-%COMP%]{margin-top:2%;position:sticky;margin-bottom:1%}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background-color:#cf0001}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%], .QBfieldMappingLandingPage[_ngcontent-%COMP%]   .save-fieldMapping-btn-loading[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border-radius:4px;font-family:Roboto;font-size:14px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:-.28px;text-transform:capitalize;width:100%;height:35px;color:#fff}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .save-fieldMapping-btn-loading[_ngcontent-%COMP%]{background:#fff;background-color:#cf0001}.QBfieldMappingLandingPage[_ngcontent-%COMP%]   .formArrayContainer[_ngcontent-%COMP%]{max-height:60vh;overflow-y:auto}"]}),e})(),redirectTo:"",pathMatch:"full"}];let B=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(E)],a.k]}),e})();var V=n("/1cH"),I=n("bSwM"),T=n("FKr1"),A=n("iadO"),N=n("0IaG"),j=n("f0Cb"),q=n("M9IT"),G=n("bv9b"),R=n("d3UM"),D=n("XhcP"),z=n("1jcm"),X=n("Dh3D"),K=n("xHqg"),H=n("+0xr"),J=n("wZkO"),$=n("ZzPI"),W=n("lVl8"),Z=n("dlKe"),U=n("1yaQ");let Y=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,B,f.b,N.g,v.c,q.b,H.m,M.e,X.c,y.b,D.g,K.f,m.d,R.d,I.b,J.g,g.b,A.h,T.n,U.b,h.b,W.b,Z.b,u.e,$.b,z.b,G.b,V.c,j.b,c.p,c.E]]}),e})()}}]);