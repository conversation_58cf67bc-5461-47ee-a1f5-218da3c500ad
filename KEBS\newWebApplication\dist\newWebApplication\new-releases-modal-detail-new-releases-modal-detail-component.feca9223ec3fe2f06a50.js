(window.webpackJsonp=window.webpackJsonp||[]).push([[825],{gUlc:function(e,t,n){"use strict";n.r(t),n.d(t,"NewReleasesModalDetailComponent",(function(){return u})),n.d(t,"NewReleasesDetailModule",(function(){return w}));var i=n("wd/R"),o=n("ofXK"),a=n("bTqV"),l=n("NFeN"),s=n("Qu3c"),r=n("0IaG"),d=n("f0Cb"),c=n("fXoL"),m=n("LcQX");function p(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"span",15),c["\u0275\u0275text"](1,"Major Release! \u2728"),c["\u0275\u0275elementEnd"]())}function f(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",17),c["\u0275\u0275elementStart"](1,"span",9),c["\u0275\u0275text"](2,"Key Features: "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](3,"span",18),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("innerHtml",e.description,c["\u0275\u0275sanitizeHtml"])}}function g(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275template"](1,f,4,1,"div",16),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",e.updates)}}function v(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",8),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"div",9),c["\u0275\u0275elementStart"](3,"span"),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](5,p,2,0,"span",10),c["\u0275\u0275element"](6,"br"),c["\u0275\u0275elementStart"](7,"span",11),c["\u0275\u0275text"](8),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"mat-icon",12),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.index;return c["\u0275\u0275nextContext"]().toggleDetailMode(n)})),c["\u0275\u0275text"](10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](11,g,2,1,"div",13),c["\u0275\u0275element"](12,"mat-divider",14),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"]("Version - ",e.version," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.is_major_release),c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](e.date),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",e.detail_mode_activated?"keyboard_arrow_down":"keyboard_arrow_right"," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.detail_mode_activated)}}let u=(()=>{class e{constructor(e,t,n){this.dialogRef=e,this.inData=t,this.utilityService=n}ngOnInit(){this.modalParams=this.inData,console.log(this.inData),console.log(this.modalParams),this.displayVersionHistory()}displayVersionHistory(){for(let e of this.modalParams)e.date=i(e.date).format("DD-MMM-YYYY");this.versions=this.modalParams}toggleDetailMode(e){this.versions[e].detail_mode_activated=!this.versions[e].detail_mode_activated}closeModal(){this.dialogRef.close(!1)}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](r.h),c["\u0275\u0275directiveInject"](r.a),c["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-new-releases-modal-detail"]],decls:11,vars:1,consts:[[1,"container-fluid","new-releases-detail-styles","pt-4"],[1,"header","pl-2"],[1,"value14Bold"],[1,"close-icon"],["mat-icon-button","",1,"ml-auto","close-button","mt-0",3,"click"],["matTooltip","Close",1,"close-Icon",2,"font-size","16px"],[1,"pt-3"],["class","pl-2 pt-3",4,"ngFor","ngForOf"],[1,"pl-2","pt-3"],[1,"versionHeader"],["style","font-weight: 400;",4,"ngIf"],[2,"font-size","12px","font-weight","300"],[1,"icon-button",3,"click"],[4,"ngIf"],[2,"margin-top","40px"],[2,"font-weight","400"],["class","pt-5 detailView",4,"ngFor","ngForOf"],[1,"pt-5","detailView"],[3,"innerHtml"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"span",2),c["\u0275\u0275text"](3,"Version History"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",3),c["\u0275\u0275elementStart"](5,"button",4),c["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),c["\u0275\u0275elementStart"](6,"mat-icon",5),c["\u0275\u0275text"](7,"close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"div",6),c["\u0275\u0275element"](9,"mat-divider"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](10,v,13,5,"div",7),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](10),c["\u0275\u0275property"]("ngForOf",t.versions))},directives:[a.a,l.a,s.a,d.a,o.NgForOf,o.NgIf],styles:[".new-releases-detail-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:16px;width:120px}.new-releases-detail-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%], .new-releases-detail-styles[_ngcontent-%COMP%]   .versionHeader[_ngcontent-%COMP%]{color:#526179;position:static;height:24px;left:0;top:0;font-style:normal;font-weight:500;line-height:24px;letter-spacing:.02em;text-transform:capitalize}.new-releases-detail-styles[_ngcontent-%COMP%]   .versionHeader[_ngcontent-%COMP%]{font-size:14px}.new-releases-detail-styles[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]{font-size:18px;color:#6b7a99;cursor:pointer;position:relative;left:400px;vertical-align:sub}.new-releases-detail-styles[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{position:absolute;right:20px;top:15px}"]}),e})(),w=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,l.b,a.b,s.b,r.g,d.b]]}),e})()}}]);