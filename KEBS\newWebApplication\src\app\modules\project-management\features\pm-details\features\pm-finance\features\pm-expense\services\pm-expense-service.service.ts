import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ExpenseData,QuoteOption } from '../pages/pm-expense-landing-page/pm-expense-landing-page.component';

@Injectable({
  providedIn: 'root'
})
export class PmExpenseServiceService {

  constructor(private http: HttpClient) { }

  /**
   * Fetch expense data based on cost center using existing API
   * Uses getExpenseListBasedOnCostCenter API
   */
  getExpenseListBasedOnCostCenter(costCenter: string, startIndex: number = 0, noOfRecords: number = 50, searchParameter: string = ''): Observable<any> {
    return this.http.post('/api/exPrimary2/getExpenseListBasedOnCostCenter', {
      cost_center: costCenter,
      start_index: startIndex,
      no_of_records: noOfRecords,
      search_parameter: searchParameter,
      filter_params: {
        expense_category: [],
        t_approved_on_duration: {
          start_date: '',
          end_date: ''
        }
      }
    });
  }

  /**
   * Get available quotes for the project
   */
  getAvailableQuotes(projectId: number, itemId: number): Observable<QuoteOption[]> {
    return this.http.post<QuoteOption[]>('/api/pm/quote/getQuotesByProject', {
      project_id: projectId,
      item_id: itemId
    });
  }


  /**
   * Update expense status from Open to Billed
   */
  updateExpenseStatus(expenseIds: number[], fromStatus: string, toStatus: string): Observable<any> {
    return this.http.post('/api/exPrimary2/updateExpenseStatus', {
      expense_ids: expenseIds,
      from_status: fromStatus,
      to_status: toStatus
    });
  }

  /**
   * Get expense status display text
   */
  getStatusDisplay(status: number): string {
    switch (status) {
      case 1:
        return 'Open';
      case 2:
        return 'Billing';
      case 3:
        return 'SOW';
      default:
        return 'Unknown';
    }
  }

  /**
   * Get cost center project item details
   */
  getCostCenterProjectItemName(costCenter: string): Observable<any> {
    return this.http.post('/api/exPrimary2/getCostCenterProjectItemName', {
      cost_center: costCenter
    });
  }
}
