import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { ExpenseData, MilestoneCreationData, QuoteOption } from '../pages/pm-expense-landing-page/pm-expense-landing-page.component';
import { PmExpenseIntegrationService, MilestoneCardData } from './pm-expense-integration.service';

@Injectable({
  providedIn: 'root'
})
export class PmExpenseServiceService {

  constructor(
    private http: HttpClient,
    private integrationService: PmExpenseIntegrationService
  ) { }

  /**
   * Fetch expense data based on the SQL query provided
   * SELECT id,legal_entity_id,total_amount, cost_centre, cost_centre_description,amount_claimed,billable_milestone_id,DESCRIPTION,TEH.created_on,MEM.employee_name AS requested_by ,TEH.expense_status
   * FROM zifoqual.t_expense_header TEH
   * LEFT JOIN zifoqual.m_employee_master MEM ON TEH.init_assoc_oid = MEM.oid
   * WHERE STATUS='V' AND is_billable=1 AND TEH.is_active=1
   */
  getExpenseData(projectId?: number, itemId?: number): Observable<ExpenseData[]> {
    return this.http.post<ExpenseData[]>('/api/pm/expense/getExpenseData', {
      projectId,
      itemId
    });
  }

  /**
   * Get available quotes for milestone creation
   */
  getAvailableQuotes(projectId: number, itemId: number): Observable<QuoteOption[]> {
    return this.http.post<QuoteOption[]>('/api/pm/expense/getAvailableQuotes', {
      projectId,
      itemId
    });
  }

  /**
   * Create milestone with selected expenses and update related tabs
   */
  createMilestone(milestoneData: MilestoneCreationData, projectId: number, itemId: number): Observable<any> {
    return this.http.post('/api/pm/expense/createMilestone', {
      milestoneData,
      projectId,
      itemId
    }).pipe(
      switchMap((response: any) => {
        // Create milestone card data for integration
        const milestoneCardData: MilestoneCardData = {
          id: response.milestone_id,
          milestone_name: milestoneData.milestone_name,
          quote_id: milestoneData.quote_id,
          total_amount: milestoneData.total_amount,
          status: 'YTB',
          created_date: new Date(),
          expense_ids: milestoneData.selected_expenses.map(expense => expense.id)
        };

        // Create milestone cards in all related tabs
        const reimbursementCall = this.integrationService.createReimbursementMilestone(milestoneCardData);
        const billsCall = this.integrationService.createBillsMilestone(milestoneCardData);
        const invoiceYTBCall = this.integrationService.createInvoiceYTBMilestone(milestoneCardData);

        // Execute all calls in parallel
        return forkJoin({
          original: [response],
          reimbursement: reimbursementCall,
          bills: billsCall,
          invoiceYTB: invoiceYTBCall
        }).pipe(
          map((results) => {
            // Notify integration service about milestone creation
            this.integrationService.notifyMilestoneCreated(milestoneCardData);
            return response;
          })
        );
      })
    );
  }

  /**
   * Update expense status from 1 to 2 in t_expense_header table
   */
  updateExpenseStatus(expenseIds: number[], newStatus: number): Observable<any> {
    return this.integrationService.updateExpenseHeaderStatus(expenseIds).pipe(
      map((response) => {
        // Notify integration service about status update
        this.integrationService.notifyExpenseStatusUpdated(expenseIds, newStatus);
        return response;
      })
    );
  }

  /**
   * Get expense status display text
   */
  getStatusDisplay(status: number): string {
    switch (status) {
      case 1:
        return 'Open';
      case 2:
        return 'Billing';
      case 3:
        return 'SOW';
      default:
        return 'Unknown';
    }
  }
}
