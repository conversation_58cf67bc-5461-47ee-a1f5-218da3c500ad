(window.webpackJsonp=window.webpackJsonp||[]).push([[888],{"90Cu":function(e,t,n){"use strict";n.r(t),n.d(t,"EditUserConfigComponent",(function(){return v}));var o=n("mrSG"),a=n("0IaG"),i=n("XNiG"),l=n("1G5W"),r=n("fXoL"),m=n("ye8K"),d=n("1A3m"),s=n("3Pt+"),c=n("kmnG"),p=n("d3UM"),g=n("FKr1"),f=n("ofXK");function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",18),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275property"]("value",e.id),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.description," ")}}function u(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",18),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275property"]("value",e.dbname),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.dbname," ")}}function b(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",18),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275property"]("value",e.table_name_1),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.table_name_1," ")}}let v=(()=>{class e{constructor(e,t,n,o,a){this.dialogRef=e,this.data=t,this._apiservice=n,this._toaster=o,this.fb=a,this.adminRoles=[{id:1,description:"Admin"},{id:2,description:"User"},{id:3,description:"Global Admin"}],this.dbNameList=[],this.tableNameList=[],this.getadminRoles=0,this.updateButton=!1,this._onDestroy=new i.b}ngOnInit(){console.log(this.data),this.getadminRoles=this.data.gd_admin_id,this.dbNameList.push({dbname:this.data.db_name}),this.updateForm=this.fb.group({dbName:[""],tableName:[""]}),this.updateForm.setValue({dbName:this.data.db_name,tableName:JSON.parse(this.data.table_name_1)}),this.getTableList()}dialogClose(){this.dialogRef.close({event:"close"})}getTableList(){this._apiservice.getTableName(this.updateForm.value.dbName).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.tableNameList=e.data,console.log(this.tableNameList)):this.tableNameList=[]})),e=>{console.log(e)})}updateUserRole(){this._apiservice.updateUserRoles(this.getadminRoles,this.updateForm.value.dbName,this.data,this.updateForm.value.tableName).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._toaster.showSuccess(e.messText,"Success",3e3),this.dialogRef.close({event:"close"})):this._toaster.showError(e.messText,"error",3e3)})),e=>{this._toaster.showError(e,"error",3e3),console.log(e)})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](a.h),r["\u0275\u0275directiveInject"](a.a),r["\u0275\u0275directiveInject"](m.a),r["\u0275\u0275directiveInject"](d.a),r["\u0275\u0275directiveInject"](s.i))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-edit-user-config"]],decls:45,vars:6,consts:[[1,""],[1,"col-12"],[1,"row"],[1,"col-6","mt-3"],["appearance","outline"],["required","true",3,"ngModel","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],[1,"col-4","heading"],[1,"col-2","mt-3"],[1,"dialogCloseBtn",3,"click"],[3,"formGroup"],[1,"col-3","mt-3","displayname"],[1,"col-4","mt-3"],["formControlName","dbName","required","true"],["multiple","","formControlName","tableName","required","true"],[1,"col-2"],[1,"saveButton",3,"click"],[1,"closeButton",3,"click"],[3,"value"]],template:function(e,t){1&e&&(r["\u0275\u0275element"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275elementStart"](4,"mat-form-field",4),r["\u0275\u0275elementStart"](5,"mat-label"),r["\u0275\u0275text"](6,"Role"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"mat-select",5),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.getadminRoles=e})),r["\u0275\u0275elementStart"](8,"mat-option"),r["\u0275\u0275text"](9,"--"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](10,h,2,2,"mat-option",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](11,"div",7),r["\u0275\u0275text"](12," Edit Config "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](13,"div",8),r["\u0275\u0275elementStart"](14,"button",9),r["\u0275\u0275listener"]("click",(function(){return t.dialogClose()})),r["\u0275\u0275text"](15,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](16,"form",10),r["\u0275\u0275elementStart"](17,"div",1),r["\u0275\u0275elementStart"](18,"div",2),r["\u0275\u0275elementStart"](19,"div",11),r["\u0275\u0275text"](20),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](21,"div",12),r["\u0275\u0275elementStart"](22,"mat-form-field",4),r["\u0275\u0275elementStart"](23,"mat-label"),r["\u0275\u0275text"](24,"DB NAME"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](25,"mat-select",13),r["\u0275\u0275elementStart"](26,"mat-option"),r["\u0275\u0275text"](27,"--"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](28,u,2,2,"mat-option",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](29,"div",12),r["\u0275\u0275elementStart"](30,"mat-form-field",4),r["\u0275\u0275elementStart"](31,"mat-label"),r["\u0275\u0275text"](32,"TABLE"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](33,"mat-select",14),r["\u0275\u0275elementStart"](34,"mat-option"),r["\u0275\u0275text"](35,"--"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](36,b,2,2,"mat-option",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](37,"div",1),r["\u0275\u0275elementStart"](38,"div",2),r["\u0275\u0275elementStart"](39,"div",15),r["\u0275\u0275elementStart"](40,"button",16),r["\u0275\u0275listener"]("click",(function(){return t.updateUserRole()})),r["\u0275\u0275text"](41,"Update"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](42,"div",15),r["\u0275\u0275elementStart"](43,"button",17),r["\u0275\u0275listener"]("click",(function(){return t.dialogClose()})),r["\u0275\u0275text"](44,"Close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](7),r["\u0275\u0275property"]("ngModel",t.getadminRoles),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngForOf",t.adminRoles),r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("formGroup",t.updateForm),r["\u0275\u0275advance"](4),r["\u0275\u0275textInterpolate1"](" ",t.data.displayName," "),r["\u0275\u0275advance"](8),r["\u0275\u0275property"]("ngForOf",t.dbNameList),r["\u0275\u0275advance"](8),r["\u0275\u0275property"]("ngForOf",t.tableNameList))},directives:[c.c,c.g,p.c,s.F,s.v,s.y,g.p,f.NgForOf,s.J,s.w,s.n,s.l],styles:['@charset "UTF-8";.closeBtn[_ngcontent-%COMP%]{color:red;margin-left:384px}.btn-danger[_ngcontent-%COMP%]{margin-left:392px;--bs-btn-bg:#fff}.bi-x-circle[_ngcontent-%COMP%]:before{content:"\uf623";color:red}.heading[_ngcontent-%COMP%]{width:263px;height:24px;font-family:Roboto;font-style:normal;font-weight:700;font-size:16px;line-height:50px;display:flex;align-items:center;text-transform:capitalize;text-align:center;color:#26303e;margin-top:20px}.formEdit[_ngcontent-%COMP%]{margin-top:70px}.dialogCloseBtn[_ngcontent-%COMP%]{border:none;background-color:initial;width:30px;height:16px;font-size:12px;line-height:16px;letter-spacing:-.02em;color:#45546e;margin-top:10px}.dialogCloseBtn[_ngcontent-%COMP%], .displayname[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;text-transform:capitalize}.displayname[_ngcontent-%COMP%]{font-size:13px;line-height:40px;letter-spacing:.02em;color:#526179}.closeButton[_ngcontent-%COMP%]{box-sizing:border-box;flex-direction:row;width:74px;border:1px solid #45546e;border-radius:4px}.closeButton[_ngcontent-%COMP%], .saveButton[_ngcontent-%COMP%]{align-items:center;height:40px;text-align:center}.saveButton[_ngcontent-%COMP%]{width:78px;font-family:Roboto;font-style:normal;font-weight:700;font-size:14px;line-height:16px;background-color:#ee4961;letter-spacing:-.02em;text-transform:capitalize;border:none;border-radius:5px;color:#fff}']}),e})()}}]);