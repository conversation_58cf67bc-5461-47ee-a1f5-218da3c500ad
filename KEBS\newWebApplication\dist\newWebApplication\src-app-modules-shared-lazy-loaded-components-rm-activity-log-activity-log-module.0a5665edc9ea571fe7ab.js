(window.webpackJsonp=window.webpackJsonp||[]).push([[995,996],{nmAF:function(n,t,e){"use strict";e.d(t,"a",(function(){return i}));var o=e("wd/R"),r=e("fXoL");let i=(()=>{class n{transform(n,t,e=!0){return n&&o(n,o.ISO_8601,!0).isValid()?e?o(n,"YYYY-MM-DD HH:mm:ss").utc(n).local().format(t):o(n).format(t):"-"}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275pipe=r["\u0275\u0275definePipe"]({name:"localDateFormat",type:n,pure:!0}),n})()},o4ji:function(n,t,e){"use strict";e.r(t),e.d(t,"ActivityLogModule",(function(){return m}));var o=e("ofXK"),r=e("tyNb"),i=e("pgBy"),c=e("fXoL");const u=[{path:"",component:i.ActivityLogComponent}];let a=(()=>{class n{}return n.\u0275mod=c["\u0275\u0275defineNgModule"]({type:n}),n.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||n)},imports:[[r.k.forChild(u)],r.k]}),n})();var f=e("lr1n"),d=e("Xa2L"),s=e("d3UM"),p=e("kmnG"),l=e("3Pt+");let m=(()=>{class n{}return n.\u0275mod=c["\u0275\u0275defineNgModule"]({type:n}),n.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||n)},imports:[[o.CommonModule,a,d.b,l.p,p.e,s.d,f.a]]}),n})()}}]);