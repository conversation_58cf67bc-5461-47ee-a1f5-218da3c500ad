(window.webpackJsonp=window.webpackJsonp||[]).push([[889],{wGtE:function(e,i,t){"use strict";t.r(i),t.d(i,"InvoiceCourseOfActionPopupComponent",(function(){return _})),t.d(i,"InvoiceHelpPopupModule",(function(){return M}));var n=t("mrSG"),a=t("xG9w"),o=t("XNiG"),l=t("1G5W"),c=t("ofXK"),s=t("Qu3c"),r=t("bTqV"),d=t("NFeN"),p=t("9ejs"),v=t("Kb4U"),m=t("fXoL"),h=t("6xF0"),u=t("vFIH"),g=t("XXEo"),y=t("0IaG"),b=t("dNgK"),S=t("F97M"),f=t("YIj/"),D=t("idVn");let _=(()=>{class e{constructor(e,i,t,n,a,l,c){this._billedInvoiceService=e,this._mailUtilityService=i,this._loginService=t,this.dialog=n,this.snackBar=a,this._graphService=l,this._dunningService=c,this.$onDestroy=new o.b,this.authorizedMailIds=[],this.user=this._loginService.getProfile().profile,this.openMailLoading=!1}ngOnInit(){this.invoiceDetails(),this.initMailBox(),this._billedInvoiceService.getCourseOfActionPopupSubject().subscribe(e=>{this.overlayRef=e},e=>{console.log(e)})}invoiceDetails(){this._billedInvoiceService.getBilledInvoicePopupData().pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>{this.invoiceData=e},e=>{console.log(e)})}openMailBox(){return Object(n.c)(this,void 0,void 0,(function*(){this.initMailBox(),this.openMailLoading=!0;let e=this.invoiceData.customer_id,i=yield this._dunningService.dunningAgeingPayments(e),n=yield this._dunningService.getDunningTemplate(),a=this._dunningService.formatDunningDataForMailComponent(i,n);this._mailUtilityService.mUtilityData.authorizedMailSenders=a.authorizedMailSenders,this._mailUtilityService.mUtilityData.newMailTemplateData.push(a),this._mailUtilityService.mUtilityData.currentMailMode={mode:"create"},this._mailUtilityService.mUtilityData.o365Token={token:yield this._graphService.getO365Token()};const{ViewMailComponent:o}=yield Promise.resolve().then(t.bind(null,"vFIH"));this.dialog.open(o,{width:"96%",height:"97%",maxWidth:"100vw",maxHeight:"100vw",data:{},disableClose:!0}).afterClosed().subscribe(e=>{this._mailUtilityService.resetMailData()}),this.openMailLoading=!1}))}initMailBox(){this._mailUtilityService.mUtilityData.saveRecipientMailIds=this.saveMailIds.bind(this),this._mailUtilityService.mUtilityData.applicationId=235,this._mailUtilityService.mUtilityData.isCcRecipientFieldHasSaveButton=!0,this._mailUtilityService.mUtilityData.isBccRecipientFieldHasSaveButton=!0,this._mailUtilityService.mUtilityData.isToRecipientFieldHasSaveButton=!0,this._mailUtilityService.mUtilityData.currentUserMailId=this.user.email,this._mailUtilityService.mUtilityData.formatTableForTemplate=!0,this._mailUtilityService.mUtilityData.hasInitiateNewMailTemplate=!0}saveMailIds(){let e=this._mailUtilityService.mUtilityData.saveRecipientMailIdType.type,i=this._mailUtilityService.mUtilityData.saveRecipientMailIdType.mailFields,t=this._mailUtilityService.mUtilityData.saveRecipientMailIdType.uniqueId;console.log("customer",t);let n=[];"toMailId"==e?i.value.toRecipients.length>0?a.each(i.value.toRecipients,e=>{n.push(e.name)}):n=[]:"ccMailId"==e?i.value.ccRecipients.length>0?a.each(i.value.ccRecipients,e=>{n.push(e.name)}):n=[]:"bccMailIds"==e&&(i.value.bccRecipients.length>0?a.each(i.value.bccRecipients,e=>{n.push(e.name)}):n=[]),this._billedInvoiceService.saveDunningRecipientEmail(e,n,t).pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?this.snackBar.open(e.userMess,"Dismiss",{duration:2e3}):console.log(e)},e=>{console.log(e)})}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}}return e.\u0275fac=function(i){return new(i||e)(m["\u0275\u0275directiveInject"](h.a),m["\u0275\u0275directiveInject"](u.MailUtilityService),m["\u0275\u0275directiveInject"](g.a),m["\u0275\u0275directiveInject"](y.b),m["\u0275\u0275directiveInject"](b.a),m["\u0275\u0275directiveInject"](S.a),m["\u0275\u0275directiveInject"](f.a))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-invoice-course-of-action-popup"]],decls:23,vars:7,consts:[[1,"pop-up","d-flex","flex-column","p-2","invoice-help-popup"],[2,"width","280px","max-height","220px"],[1,"row","mb-2","dark-font"],[1,"d-flex","flex-row","mx-auto","mb-2",2,"background-color","white"],[1,"col-6"],[1,"row","light-font"],[1,"row","dark-font"],[1,"row","highlighted-font"],[1,"d-flex","justify-content-center"],["mat-stroked-button","",1,"send-btn",3,"disabled","click"]],template:function(e,i){1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",3),m["\u0275\u0275elementStart"](5,"div",4),m["\u0275\u0275elementStart"](6,"div",5),m["\u0275\u0275text"](7,"Billed on"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",5),m["\u0275\u0275text"](9,"payment terms"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",5),m["\u0275\u0275text"](11,"Age"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](12,"div",4),m["\u0275\u0275elementStart"](13,"div",6),m["\u0275\u0275text"](14),m["\u0275\u0275pipe"](15,"DDMMYY"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](16,"div",6),m["\u0275\u0275text"](17),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](18,"div",7),m["\u0275\u0275text"](19),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](20,"div",8),m["\u0275\u0275elementStart"](21,"button",9),m["\u0275\u0275listener"]("click",(function(){return i.openMailBox()})),m["\u0275\u0275text"](22,"Send mail"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e&&(m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",null!=i.invoiceData&&i.invoiceData.course_of_action?i.invoiceData.course_of_action:"-"," "),m["\u0275\u0275advance"](11),m["\u0275\u0275textInterpolate"](null!=i.invoiceData&&i.invoiceData.billed_on?m["\u0275\u0275pipeBind1"](15,5,i.invoiceData.billed_on):"-"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",null!=i.invoiceData&&i.invoiceData.CP?i.invoiceData.CP:"-"," days"),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](null!=i.invoiceData&&i.invoiceData.aging_days?i.invoiceData.aging_days:"-"),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("disabled",i.openMailLoading))},directives:[r.a],pipes:[D.a],styles:[".cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0!important}.invoice-help-popup[_ngcontent-%COMP%]{background-color:#fff2f2!important;width:auto;border:1px solid #ccc;border-radius:5px;margin:0;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(136,94,94,.19)!important}.invoice-help-popup[_ngcontent-%COMP%]   .dark-font[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#000}.invoice-help-popup[_ngcontent-%COMP%]   .light-font[_ngcontent-%COMP%]{font-size:14px;color:#727170;font-weight:200}.invoice-help-popup[_ngcontent-%COMP%]   .highlighted-font[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.invoice-help-popup[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]{color:#cf0001;border-color:#cf0001;width:100px;border-radius:0}"]}),e})(),M=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(i){return new(i||e)},providers:[],imports:[[c.CommonModule,r.b,s.b,d.b,p.a,v.b]]}),e})()}}]);