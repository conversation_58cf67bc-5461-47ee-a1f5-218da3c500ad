(window.webpackJsonp=window.webpackJsonp||[]).push([[854],{"8EZa":function(e,t,s){"use strict";s.d(t,"a",(function(){return h}));var i=s("mrSG"),c=s("xG9w"),o=s("fXoL"),r=s("tk/3"),n=s("flaP"),l=s("1A3m"),a=s("XXEo");let h=(()=>{class e{constructor(e,t,s,i){this.http=e,this.rolesService=t,this.toaster=s,this.loginService=i,this.projectRoleAccessList=[],this.roleList=[],this.mainAccess=[],this.projectEmployeeRole=[],this.projectListAccess=[],this.status_list=[],this.checkProjectApplicationAccess(),this.getProjectOverallAccess(),this.getUserRoleAccessProjectList(),this.getStatusList()}getProjectRoleAccess(){return new Promise((e,t)=>{if(this.projectRoleAccessList&&this.projectRoleAccessList.length>0)return e(this.projectRoleAccessList);this.http.post("/api/pm/auth/getProjectRoleAccessList",{}).subscribe(t=>(this.projectRoleAccessList=t,e(t)),e=>t(e))})}checkProjectApplicationAccess(){return new Promise((e,t)=>Object(i.c)(this,void 0,void 0,(function*(){if(this.rolesService.roles&&this.rolesService.roles.length>0){let t=c.where(this.rolesService.roles,{application_id:915});this.projectEmployeeRole=t;let s=c.where(this.projectEmployeeRole,{object_id:6});return e(s.length>0)}{const t=this.loginService.getProfile().profile;yield this.getAccessList(t.oid,"project").subscribe(t=>{let s=c.where(t,{application_id:915});this.projectEmployeeRole=s;let i=c.where(this.projectEmployeeRole,{object_id:6});return e(i.length>0)})}})))}checkProjectRoleAccess(e){let t=c.where(this.projectEmployeeRole,{application_id:915,object_id:6});if(t.length>0){if("*"==t[0].object_value)return{messType:"S",message:"Admin access enabled!",data:this.getAccessTopPriority([1],e)};if("True"==t[0].object_value){let s="string"==typeof t[0].object_entries?JSON.parse(t[0].object_entries):t[0].object_entries;return"null"==s||"*"==s||null==s||"null"==s?{messType:"S",message:"Team Member Access enabled",data:this.getAccessTopPriority([9],e)}:{messType:"S",message:"Project Role Access enabled",data:this.getAccessTopPriority(s,e)}}return{messType:"E",message:"Not authorized!",data:this.getAccessTopPriority([],e)}}return{messType:"E",message:"Not authorized!",data:this.getAccessTopPriority([],e)}}getAccessTopPriority(e,t){return c.filter(t,t=>{for(let s of e)if(t.id==s)return t})}getProjectRoleObjectAccess(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>Object(i.c)(this,void 0,void 0,(function*(){this.projectRoleAccess&&"S"==this.projectRoleAccess.messType?e(this.projectRoleAccess):yield this.getProjectRoleAccess().then(t=>{let s=this.checkProjectRoleAccess(this.projectRoleAccessList);if("S"==s.messType){let t=s.data;0==t.length?(this.projectRoleAccess={messType:"E",message:"No access to Projects",access:!1,data:[]},e(this.projectRoleAccess)):(this.projectRoleAccess={messType:"S",message:"Access to Project",access:!0,data:t},e(this.projectRoleAccess))}else this.projectRoleAccess={messType:"E",message:"No access to Projects",access:!1,data:[]},e(this.projectRoleAccess)},e=>{this.toaster.showError("This action is not allowed!","Dismiss",3e3),t(!1)})})))}))}getProjectOverallAccess(){return new Promise((e,t)=>Object(i.c)(this,void 0,void 0,(function*(){if(this.roleList&&this.roleList.length>0)return e(this.roleList);yield this.getProjectRoleObjectAccess().then(s=>{if("S"!=s.messType)return e([]);if(!(s.data.length>0))return e([]);{let i=c.pluck(s.data,"id");if(!(i.length>0))return e([]);this.http.post("/api/pm/auth/getProjectOverallAccess",{mainAccess:i}).subscribe(t=>(console.log(this.roleList),this.roleList=t,e(t)),e=>t(e))}})})))}getProjectObjectAccess(e){return c.where(this.roleList,{object_id:e}).length>0}getEmployeeRoleObjectAccess(e){return c.where(this.projectEmployeeRole,{object_id:e}).length>0}getUserRoleAccessProjectList(){return new Promise((e,t)=>Object(i.c)(this,void 0,void 0,(function*(){this.projectListAccess&&this.projectListAccess.length>0?e(this.projectListAccess):yield this.getProjectRoleObjectAccess().then(s=>"S"!=s.messType?e([]):s.data.length>0?void this.http.post("/api/pm/auth/getUserRoleAccessProjectList",{mainAccess:s.data}).subscribe(t=>(this.projectListAccess=t,e(t)),e=>t(e)):e([]))})))}getAccessList(e,t){return this.http.post("/api/pm/auth/getAccessFor",{oid:e,type:t})}getReadWriteAccess(e,t){return new Promise((s,o)=>Object(i.c)(this,void 0,void 0,(function*(){yield this.getUserRoleAccessProjectList().then(o=>Object(i.c)(this,void 0,void 0,(function*(){yield this.getStatusList().then(i=>{let r=c.where(o,{item_id:t,project_id:e,object_access:"Both"});if(r.length>0){let e=c.where(i,{id:r[0].item_status_id,object_access:"Both"});s(e.length>0)}else s(!1)})})),e=>{s(!0)})})))}getAdminAccess(){return new Promise((e,t)=>Object(i.c)(this,void 0,void 0,(function*(){this.http.post("/api/pm/auth/getAdminAccessList",{}).subscribe(t=>e(t),e=>t(e))})))}getStatusList(){return new Promise((e,t)=>{if(this.status_list&&this.status_list.length>0)return e(this.status_list);this.http.post("/api/pm/masterData/getStatusList",{}).subscribe(t=>(this.status_list=t,e(this.status_list)),e=>t(e))})}getProjectWiseObjectAccess(e,t,s){return Object(i.c)(this,void 0,void 0,(function*(){let i=c.pluck(c.where(this.projectListAccess,{project_id:e,item_id:t}),"role_access_id"),o=yield this.getSuperiorRole(i);return c.where(this.roleList,{role_id:o,object_id:s}).length>0}))}getSuperiorRole(e){let t=c.filter(this.projectRoleAccessList,t=>{if(c.contains(e,t.id))return t});return c.sortBy(t,"sequence_list")[0].id}updateProjectStatus(e,t,s){for(let i of this.projectListAccess)i.project_id==e&&i.item_id==t&&(i.item_status_id=s)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](r.c),o["\u0275\u0275inject"](n.a),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](a.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},qklm:function(e,t,s){"use strict";s.r(t),s.d(t,"ProjectManagementModule",(function(){return p}));var i=s("ofXK"),c=s("tyNb"),o=s("mrSG"),r=s("fXoL"),n=s("XXEo"),l=s("1A3m"),a=s("8EZa");let h=(()=>{class e{constructor(e,t,s,i){this.loginService=e,this.toaster=t,this.router=s,this.pmAuthService=i}canActivate(e){return Object(o.c)(this,void 0,void 0,(function*(){let e=yield this.canAccess();return 0==e&&(console.log("project Application access"),this.toaster.showError("You do not have access to Project Management Application!","Access Restricted",1e4),this.router.navigateByUrl("/main")),yield this.pmAuthService.getProjectOverallAccess(),this.pmAuthService.getUserRoleAccessProjectList(),e}))}canAccess(){return new Promise((e,t)=>Object(o.c)(this,void 0,void 0,(function*(){yield this.pmAuthService.checkProjectApplicationAccess().then(t=>Object(o.c)(this,void 0,void 0,(function*(){e(t)})),e=>{t(!1)})})))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](n.a),r["\u0275\u0275inject"](l.a),r["\u0275\u0275inject"](c.g),r["\u0275\u0275inject"](a.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const d=[{path:"",redirectTo:"landing",pathMatch:"full"},{path:"landing",canActivate:[h],loadChildren:()=>s.e(566).then(s.bind(null,"rQqh")).then(e=>e.PmLandingPageModule),data:{breadcrumb:"Project"}},{path:"setting",canActivate:[h],loadChildren:()=>Promise.all([s.e(1),s.e(7),s.e(8),s.e(22),s.e(23),s.e(156),s.e(570)]).then(s.bind(null,"zZTV")).then(e=>e.PmProjectSettingsModule),data:{breadcrumb:"Project"}},{path:"create",canActivate:[h],loadChildren:()=>s.e(558).then(s.bind(null,"/BLk")).then(e=>e.PmCreationModule),data:{breadcrumb:"Create"}},{path:":projectId/:itemId",canActivate:[h],children:[{path:":costingSheetID/costing_sheet",loadChildren:()=>Promise.all([s.e(23),s.e(557)]).then(s.bind(null,"avg0")).then(e=>e.PmCostingSheetModule)},{path:"baselineVsActual",loadChildren:()=>Promise.all([s.e(1),s.e(7),s.e(8),s.e(22),s.e(23),s.e(58),s.e(556)]).then(s.bind(null,"msZK")).then(e=>e.PmBaselineModule)},{path:":milestoneID/billing_advice",loadChildren:()=>Promise.all([s.e(1),s.e(4),s.e(7),s.e(8),s.e(20),s.e(22),s.e(23),s.e(63),s.e(75),s.e(83),s.e(110),s.e(109),s.e(154),s.e(562)]).then(s.bind(null,"c+f2")).then(e=>e.PmFullScreenBillingAdviceModule)},{path:":milestoneID/billing_advice/new",loadChildren:()=>Promise.all([s.e(1),s.e(4),s.e(7),s.e(8),s.e(20),s.e(22),s.e(23),s.e(63),s.e(75),s.e(83),s.e(110),s.e(109),s.e(154),s.e(562)]).then(s.bind(null,"c+f2")).then(e=>e.PmFullScreenBillingAdviceModule)},{path:":quoteID/resource_loading",loadChildren:()=>Promise.all([s.e(1),s.e(7),s.e(8),s.e(22),s.e(23),s.e(110),s.e(571)]).then(s.bind(null,"CB7g")).then(e=>e.PmResourceLoadingModule)},{path:":quoteID/fixed",loadChildren:()=>Promise.all([s.e(1),s.e(7),s.e(8),s.e(22),s.e(23),s.e(110),s.e(571)]).then(s.bind(null,"CB7g")).then(e=>e.PmResourceLoadingModule)}]},{path:":projectId/:projectName",canActivate:[h],loadChildren:()=>s.e(578).then(s.bind(null,"TWtS")).then(e=>e.PortfolioDetailsModule),children:[{path:":itemId/:itemName",loadChildren:()=>Promise.all([s.e(1),s.e(2),s.e(3),s.e(4),s.e(5),s.e(6),s.e(7),s.e(8),s.e(9),s.e(10),s.e(11),s.e(12),s.e(13),s.e(14),s.e(15),s.e(20),s.e(19),s.e(21),s.e(22),s.e(23),s.e(24),s.e(32),s.e(35),s.e(39),s.e(41),s.e(43),s.e(51),s.e(47),s.e(58),s.e(57),s.e(63),s.e(67),s.e(70),s.e(68),s.e(75),s.e(83),s.e(88),s.e(111),s.e(98),s.e(110),s.e(106),s.e(109),s.e(159),s.e(157),s.e(156),s.e(154),s.e(155),s.e(158),s.e(0),s.e(559)]).then(s.bind(null,"xX26")).then(e=>e.PmDetailsModule)}]}];let u=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[c.k.forChild(d)],c.k]}),e})(),p=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,u]]}),e})()}}]);