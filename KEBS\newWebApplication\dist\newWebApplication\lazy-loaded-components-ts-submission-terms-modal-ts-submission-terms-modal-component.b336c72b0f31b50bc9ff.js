(window.webpackJsonp=window.webpackJsonp||[]).push([[774],{gisV:function(t,e,n){"use strict";n.r(e),n.d(e,"TsSubmissionTermsModalComponent",(function(){return C}));var i=n("mrSG"),s=n("1G5W"),o=n("XNiG"),r=n("ofXK"),a=n("bTqV"),l=(n("3Pt+"),n("NFeN")),c=(n("kmnG"),n("7EHt")),m=n("fXoL"),p=n("0IaG"),d=n("zcNR"),g=n("BVzC"),h=n("XXEo");function f(t,e){if(1&t&&(m["\u0275\u0275elementStart"](0,"ul"),m["\u0275\u0275elementStart"](1,"li",20),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngClass",t.highlighted?"highlighted-text":""),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",t.text," ")}}function b(t,e){if(1&t&&(m["\u0275\u0275elementStart"](0,"span"),m["\u0275\u0275template"](1,f,3,2,"ul",19),m["\u0275\u0275elementEnd"]()),2&t){const t=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.description)}}function u(t,e){if(1&t&&(m["\u0275\u0275elementStart"](0,"span",21),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&t){const t=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275property"]("ngClass",t.description[0].highlighted?"highlighted-text":""),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",t.description[0].text," ")}}function x(t,e){if(1&t&&(m["\u0275\u0275elementStart"](0,"mat-expansion-panel",14),m["\u0275\u0275elementStart"](1,"mat-expansion-panel-header"),m["\u0275\u0275elementStart"](2,"mat-panel-title",15),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"mat-panel-description",16),m["\u0275\u0275text"](5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](6,b,2,1,"span",17),m["\u0275\u0275template"](7,u,2,2,"span",18),m["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",t.term_name?t.term_name:""," "),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",t.restriction?t.restriction:""," "),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.description.length>1),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",1==t.description.length)}}let C=(()=>{class t{constructor(t,e,n,i){this.dialogRef=t,this.tsSubmissionPrimaryService=e,this.errorService=n,this._auth=i,this.step=0,this.termsData=[],this._onDestroy=new o.b}ngOnInit(){this.currentUser=this._auth.getProfile().profile,this.associateId=this.currentUser.aid,this.getTermsData()}ngOnChanges(){this.getTermsData()}getTermsData(){this.tsSubmissionPrimaryService.getTermsForTS(this.associateId).pipe(Object(s.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){this.termsData=t.data;for(let t=0;t<this.termsData.length;t++)this.termsData[t].description="string"==typeof this.termsData[t].description?JSON.parse(this.termsData[t].description):this.termsData[t].description})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving terms in timesheet",t&&t.params?t.params:t&&t.error?t.error.params:{})})}setStep(t){this.step=t}nextStep(){this.step++}prevStep(){this.step--}closeModal(){this.dialogRef.close({event:"Close"})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return t.\u0275fac=function(e){return new(e||t)(m["\u0275\u0275directiveInject"](p.h),m["\u0275\u0275directiveInject"](d.a),m["\u0275\u0275directiveInject"](g.a),m["\u0275\u0275directiveInject"](h.a))},t.\u0275cmp=m["\u0275\u0275defineComponent"]({type:t,selectors:[["ts-app-submission-terms-modal"]],features:[m["\u0275\u0275NgOnChangesFeature"]],decls:18,vars:1,consts:[[1,"container-fluid","submission-terms-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-1","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","my-auto","ml-3"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[1,"row","pt-3","pb-3"],[1,"col-12"],[1,"row"],[1,"terminologies-panel"],["class","slide-in-right",4,"ngFor","ngForOf"],[1,"slide-in-right"],[2,"font-weight","500"],[2,"color","#cf0001"],[4,"ngIf"],[3,"ngClass",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"pb-1",3,"ngClass"],[3,"ngClass"]],template:function(t,e){1&t&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",3),m["\u0275\u0275elementStart"](4,"mat-icon",4),m["\u0275\u0275text"](5,"info"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"span",5),m["\u0275\u0275text"](7," Terms "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",6),m["\u0275\u0275elementStart"](9,"button",7),m["\u0275\u0275listener"]("click",(function(){return e.closeModal()})),m["\u0275\u0275elementStart"](10,"mat-icon",8),m["\u0275\u0275text"](11,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](12,"div",9),m["\u0275\u0275elementStart"](13,"div",10),m["\u0275\u0275elementStart"](14,"div",11),m["\u0275\u0275elementStart"](15,"div",10),m["\u0275\u0275elementStart"](16,"mat-accordion",12),m["\u0275\u0275template"](17,x,8,4,"mat-expansion-panel",13),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&t&&(m["\u0275\u0275advance"](17),m["\u0275\u0275property"]("ngForOf",e.termsData))},directives:[l.a,a.a,c.a,r.NgForOf,c.c,c.g,c.h,c.f,r.NgIf,r.NgClass],styles:[".submission-terms-styles[_ngcontent-%COMP%]{background-image:url(terms_bg.dc9b2665a1055b9f7781.png);background-size:228px 198px;background-repeat:no-repeat;min-height:95vh;background-position:97% 105%}.submission-terms-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:28px;border-radius:50%;cursor:context-menu!important;width:28px;height:28px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.submission-terms-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:21px;margin-top:3px!important;margin-left:4px!important}.submission-terms-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.submission-terms-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.submission-terms-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.submission-terms-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.submission-terms-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header-description[_ngcontent-%COMP%], .submission-terms-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header-title[_ngcontent-%COMP%]{flex-basis:0}.submission-terms-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header-description[_ngcontent-%COMP%]{justify-content:space-between;align-items:center;color:#ababab;font-weight:400;font-size:14px}.submission-terms-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%] + .mat-form-field[_ngcontent-%COMP%]{margin-left:8px}.submission-terms-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.submission-terms-styles[_ngcontent-%COMP%]   .highlighted-text[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}"]}),t})()}}]);