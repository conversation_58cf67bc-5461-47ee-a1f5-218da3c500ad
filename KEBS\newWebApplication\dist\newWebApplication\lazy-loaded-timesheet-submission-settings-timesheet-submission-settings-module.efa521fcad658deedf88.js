(window.webpackJsonp=window.webpackJsonp||[]).push([[779],{GHYE:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetSubmissionSettingsModule",(function(){return G}));var i=n("ofXK"),o=n("tyNb"),r=n("mrSG"),s=n("XNiG"),a=n("1G5W"),m=n("fXoL"),l=n("1A3m"),d=n("AK7O"),h=n("/zTS"),c=n("f0Cb"),u=n("kmnG"),p=n("d3UM"),g=n("3Pt+"),v=n("qFsG"),f=n("FKr1");function S(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function y(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function _(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function E(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function x(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function M(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function b(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",13),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275text"](2," Weekly Timesheet Cutoff Period "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",19),m["\u0275\u0275elementStart"](4,"div"),m["\u0275\u0275text"](5," From "),m["\u0275\u0275elementStart"](6,"mat-form-field",16),m["\u0275\u0275elementStart"](7,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().weekStartFrom=t})),m["\u0275\u0275template"](8,_,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275text"](9," of "),m["\u0275\u0275elementStart"](10,"mat-form-field",16),m["\u0275\u0275elementStart"](11,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().weekStartOf=t})),m["\u0275\u0275template"](12,E,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](13,"div"),m["\u0275\u0275text"](14," To \xa0\xa0\xa0 "),m["\u0275\u0275elementStart"](15,"mat-form-field",16),m["\u0275\u0275elementStart"](16,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().weekEndFrom=t})),m["\u0275\u0275template"](17,x,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275text"](18," of "),m["\u0275\u0275elementStart"](19,"mat-form-field",16),m["\u0275\u0275elementStart"](20,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().weekEndOf=t})),m["\u0275\u0275template"](21,M,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngModel",e.weekStartFrom),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.dayData),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngModel",e.weekStartOf),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.weekPeriod),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngModel",e.weekEndFrom),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.dayData),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngModel",e.weekEndOf),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.weekPeriod)}}function w(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function k(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function C(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",13),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275text"](2," Weekly Timesheet Cutoff Time "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",19),m["\u0275\u0275elementStart"](4,"div"),m["\u0275\u0275elementStart"](5,"mat-form-field",16),m["\u0275\u0275elementStart"](6,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().submissionEndHour=t})),m["\u0275\u0275template"](7,w,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"mat-form-field",21),m["\u0275\u0275elementStart"](9,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().submissionEndMin=t})),m["\u0275\u0275template"](10,k,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngModel",e.submissionEndHour),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.hours),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngModel",e.submissionEndMin),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.minutes)}}function O(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function T(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function P(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function F(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function H(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function I(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",24),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.display," ")}}const D=[{path:"",component:(()=>{class e{constructor(e,t,n,i){this.toasterService=e,this.tsSettingService=t,this.router=n,this.tsService=i,this.endDateArray=[],this.dayData=[{id:1,value:0,display:"Sunday"},{id:2,value:1,display:"Monday"},{id:3,value:2,display:"Tuesday"},{id:4,value:3,display:"Wednesday"},{id:5,value:4,display:"Thursday"},{id:6,value:5,display:"Friday"},{id:7,value:6,display:"Saturday"}],this.weekPeriod=[{id:1,value:0,display:"Current Week"},{id:2,value:1,display:"Next Week"}],this.monthPeriod=[{id:1,value:0,display:"Current Month"},{id:2,value:1,display:"Next Month"}],this.minHours=0,this.maxHours=23,this.timesheetTypeData=[{id:1,value:"weekly_monthly",display:"Week"},{id:2,value:"monthly",display:"Month"}],this.showWeeklyTimesheetPeriod=!0,this.hours=[],this.minutes=[],this._onDestroy=new s.b}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.checkSettingsAccess();for(let e=1;e<=27;e++)this.endDateArray.push({id:e,value:e,display:e});this.endDateArray.push({id:32,value:"END",display:"Monthly Calendar End Day"});for(let e=0;e<=23;e++)this.hours.push({id:e,value:e,display:e});for(let e=0;e<=59;e++)this.minutes.push({id:e,value:e,display:e});this.getTimesheetSetting()}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-112+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicSubHeight=window.innerHeight-274+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight)}saveTimesheetSettings(){if(this.weekStartOf==this.weekEndOf&&this.weekEndFrom<this.weekStartFrom)return this.toasterService.showError("Timesheet Setting Message","Week End day cannot be less than Week Start Day",7e3);if(1==this.weekStartOf)return this.toasterService.showError("Timesheet Setting Message","Week Start duration cannot be in future week",7e3);if(this.monthStartOf==this.monthEndOf&&this.monthEndFrom<this.monthStartFrom)return this.toasterService.showError("Timesheet Setting Message","Week End day cannot be less than Week Start Day",7e3);if(1==this.monthStartOf)return this.toasterService.showError("Timesheet Setting Message","Week Start duration cannot be in future week",7e3);if(this.minHours>this.maxHours)return this.toasterService.showError("Timesheet Setting Message","Minimum Hours cannot exceed the Maximum Hours Value",7e3);if(this.minHours<0)return this.toasterService.showError("Timesheet Setting Message","Minimum Hours cannot be less than zero",7e3);if(this.maxHours<0)return this.toasterService.showError("Timesheet Setting Message","Maximum Hours cannot be less than zero",7e3);if(this.minHours>24)return this.toasterService.showError("Timesheet Setting Message","Minimum Hours cannot be more than 24",7e3);if(this.maxHours>24)return this.toasterService.showError("Timesheet Setting Message","Maximum Hours cannot be more than 24",7e3);let e={timesheet_type:this.timesheetType,timesheet_month_end_date:this.endDateValue,week_timesheet_submission_end_day:this.weekEndFrom,week_timesheet_subm_falls_on:this.weekEndOf,monthly_timesheet_submission_end_date:this.monthEndFrom,week_timesheet_from:this.weekStartFrom,month_timesheet_from:this.monthStartFrom,week_timesheet_of:this.weekStartOf,month_timesheet_of:this.monthStartOf,month_timesheet_subm_falls_on:this.monthEndOf,week_timesheet_submission_end_hours:this.submissionEndHour,week_timesheet_submission_end_minutes:this.submissionEndMin,monthly_timesheet_submission_end_hours:this.monthSubmissionEndHour,monthly_timesheet_submission_end_minutes:this.monthSubmissionEndMin},t={min_hours:this.minHours,max_hours:this.maxHours};return new Promise((n,i)=>this.tsSettingService.saveTimesheetSettings(e,t,1).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{this.toasterService.showInfo("Timesheet App Message",e.messText,7e3),n(!0)},error:e=>{this.toasterService.showError("Timesheet Setting Message","Error: Timesheet Settings Save Failed, Kindly try after some time",7e3),i()}}))}getTimesheetSetting(){return new Promise((e,t)=>this.tsSettingService.getTimesheetSettings(1).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>{var n,i,o,r,s,a,m,l,d,h,c,u,p,g,v,f,S,y,_,E,x,M;"S"==t.messType?(this.timesheetType=null===(n=t.generalSettings)||void 0===n?void 0:n.timesheet_type,this.endDateValue="END"!=(null===(i=t.generalSettings)||void 0===i?void 0:i.timesheet_month_end_date)?parseInt(null===(o=t.generalSettings)||void 0===o?void 0:o.timesheet_month_end_date):null===(r=t.generalSettings)||void 0===r?void 0:r.timesheet_month_end_date,this.weekEndFrom=parseInt(null===(s=t.generalSettings)||void 0===s?void 0:s.week_timesheet_submission_end_day),this.weekEndOf=parseInt(null===(a=t.generalSettings)||void 0===a?void 0:a.week_timesheet_subm_falls_on),this.monthEndFrom="END"!=(null===(m=t.generalSettings)||void 0===m?void 0:m.monthly_timesheet_submission_end_date)?parseInt(null===(l=t.generalSettings)||void 0===l?void 0:l.monthly_timesheet_submission_end_date):null===(d=t.generalSettings)||void 0===d?void 0:d.monthly_timesheet_submission_end_date,this.minHours=null===(h=t.hourSettings)||void 0===h?void 0:h.min_hours,this.maxHours=null===(c=t.hourSettings)||void 0===c?void 0:c.max_hours,this.weekStartFrom=parseInt(null===(u=t.generalSettings)||void 0===u?void 0:u.week_timesheet_from),this.weekStartOf=parseInt(null===(p=t.generalSettings)||void 0===p?void 0:p.week_timesheet_of),this.monthStartFrom="END"!=(null===(g=t.generalSettings)||void 0===g?void 0:g.month_timesheet_from)?parseInt(null===(v=t.generalSettings)||void 0===v?void 0:v.month_timesheet_from):null===(f=t.generalSettings)||void 0===f?void 0:f.month_timesheet_from,this.monthStartOf=parseInt(null===(S=t.generalSettings)||void 0===S?void 0:S.month_timesheet_of),this.monthEndOf=parseInt(null===(y=t.generalSettings)||void 0===y?void 0:y.month_timesheet_to_of),this.monthSubmissionEndHour=parseInt(null===(_=t.generalSettings)||void 0===_?void 0:_.monthly_timesheet_submission_end_hours),this.monthSubmissionEndMin=parseInt(null===(E=t.generalSettings)||void 0===E?void 0:E.monthly_timesheet_submission_end_minutes),this.submissionEndHour=parseInt(null===(x=t.generalSettings)||void 0===x?void 0:x.week_timesheet_submission_end_hours),this.submissionEndMin=parseInt(null===(M=t.generalSettings)||void 0===M?void 0:M.week_timesheet_submission_end_minutes)):this.toasterService.showInfo("Timesheet App Message",t.messText,7e3),e(!0)},error:e=>{this.toasterService.showError("Timesheet Setting Message","Error: Timesheet Settings Save Failed, Kindly try after some time",7e3),t()}}))}naviagteToTimesheetSettings(){this.router.navigateByUrl("/main/timesheetv2/settings")}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}checkSettingsAccess(){return Object(r.c)(this,void 0,void 0,(function*(){this.tsService.checkTimesheetAccess().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(e.settingAccess||this.router.navigateByUrl("/main/timesheetv2/submission"))})))}))}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](l.a),m["\u0275\u0275directiveInject"](d.a),m["\u0275\u0275directiveInject"](o.g),m["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-submission-settings"]],hostBindings:function(e,t){1&e&&m["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,m["\u0275\u0275resolveWindow"])},decls:77,vars:20,consts:[[1,"settings-container"],[1,"settings"],[1,"settings-header"],[1,"title","d-flex"],[1,"d-flex","back-button",3,"click"],["_ngcontent-gbh-c448","","width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg"],["_ngcontent-gbh-c448","","d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["_ngcontent-gbh-c448","","d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],[1,"buttons"],[1,"save-button",3,"click"],[2,"padding-top","5px"],[1,"mat-start-divider"],[1,"settings-content"],[1,"col-12","pt-2","pb-2","pl-0","pr-0","row"],[1,"col-3","header-text","align-items-center"],[1,"col-4"],["appearance","outline"],[3,"ngModel","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],[1,"col-6"],["class","col-12 pt-2 pb-2 pl-0 pr-0 row",4,"ngIf"],["appearance","outline",2,"padding-left","15px"],["matInput","","type","number","min","0","max","24","name","minhours",3,"ngModel","ngModelChange"],["matInput","","type","number","min","0","max","24","name","maxhours",3,"ngModel","ngModelChange"],[3,"value"]],template:function(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",3),m["\u0275\u0275elementStart"](4,"div",4),m["\u0275\u0275listener"]("click",(function(){return t.naviagteToTimesheetSettings()})),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](5,"svg",5),m["\u0275\u0275element"](6,"path",6),m["\u0275\u0275element"](7,"path",7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275text"](8," Timesheet Submission Settings "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275namespaceHTML"](),m["\u0275\u0275elementStart"](9,"div",8),m["\u0275\u0275elementStart"](10,"div",9),m["\u0275\u0275listener"]("click",(function(){return t.saveTimesheetSettings()})),m["\u0275\u0275text"](11,"Save"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](12,"div",10),m["\u0275\u0275element"](13,"mat-divider",11),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](14,"div",12),m["\u0275\u0275elementStart"](15,"div",13),m["\u0275\u0275elementStart"](16,"div",14),m["\u0275\u0275text"](17," Submission Type "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](18,"div",15),m["\u0275\u0275elementStart"](19,"mat-form-field",16),m["\u0275\u0275elementStart"](20,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.timesheetType=e})),m["\u0275\u0275template"](21,S,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](22,"div",13),m["\u0275\u0275elementStart"](23,"div",14),m["\u0275\u0275text"](24," Timesheet Period "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](25,"div",19),m["\u0275\u0275elementStart"](26,"div"),m["\u0275\u0275elementStart"](27,"mat-form-field",16),m["\u0275\u0275elementStart"](28,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.endDateValue=e})),m["\u0275\u0275template"](29,y,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](30,b,22,8,"div",20),m["\u0275\u0275template"](31,C,11,4,"div",20),m["\u0275\u0275elementStart"](32,"div",13),m["\u0275\u0275elementStart"](33,"div",14),m["\u0275\u0275text"](34," Monthly Timesheet Cutoff Period "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](35,"div",19),m["\u0275\u0275elementStart"](36,"div"),m["\u0275\u0275text"](37," From "),m["\u0275\u0275elementStart"](38,"mat-form-field",16),m["\u0275\u0275elementStart"](39,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthStartFrom=e})),m["\u0275\u0275template"](40,O,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275text"](41," of "),m["\u0275\u0275elementStart"](42,"mat-form-field",16),m["\u0275\u0275elementStart"](43,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthStartOf=e})),m["\u0275\u0275template"](44,T,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](45,"div"),m["\u0275\u0275text"](46," To \xa0\xa0\xa0 "),m["\u0275\u0275elementStart"](47,"mat-form-field",16),m["\u0275\u0275elementStart"](48,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthEndFrom=e})),m["\u0275\u0275template"](49,P,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275text"](50," of "),m["\u0275\u0275elementStart"](51,"mat-form-field",16),m["\u0275\u0275elementStart"](52,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthEndOf=e})),m["\u0275\u0275template"](53,F,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](54,"div",13),m["\u0275\u0275elementStart"](55,"div",14),m["\u0275\u0275text"](56," Monthly Timesheet Cutoff Time "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](57,"div",19),m["\u0275\u0275elementStart"](58,"div"),m["\u0275\u0275elementStart"](59,"mat-form-field",16),m["\u0275\u0275elementStart"](60,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthSubmissionEndHour=e})),m["\u0275\u0275template"](61,H,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](62,"mat-form-field",21),m["\u0275\u0275elementStart"](63,"mat-select",17),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthSubmissionEndMin=e})),m["\u0275\u0275template"](64,I,2,2,"mat-option",18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](65,"div",13),m["\u0275\u0275elementStart"](66,"div",14),m["\u0275\u0275text"](67," Minimum Working Hours Per Day "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](68,"div",19),m["\u0275\u0275elementStart"](69,"mat-form-field",16),m["\u0275\u0275elementStart"](70,"input",22),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.minHours=e})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](71,"div",13),m["\u0275\u0275elementStart"](72,"div",14),m["\u0275\u0275text"](73," Maximum Working Hours Per Day "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](74,"div",19),m["\u0275\u0275elementStart"](75,"mat-form-field",16),m["\u0275\u0275elementStart"](76,"input",23),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.maxHours=e})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e&&(m["\u0275\u0275advance"](20),m["\u0275\u0275property"]("ngModel",t.timesheetType),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.timesheetTypeData),m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngModel",t.endDateValue),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.endDateArray),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.showWeeklyTimesheetPeriod),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.showWeeklyTimesheetPeriod),m["\u0275\u0275advance"](8),m["\u0275\u0275property"]("ngModel",t.monthStartFrom),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.endDateArray),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngModel",t.monthStartOf),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.monthPeriod),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngModel",t.monthEndFrom),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.endDateArray),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngModel",t.monthEndOf),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.monthPeriod),m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngModel",t.monthSubmissionEndHour),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.hours),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngModel",t.monthSubmissionEndMin),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.minutes),m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngModel",t.minHours),m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngModel",t.maxHours))},directives:[c.a,u.c,p.c,g.v,g.y,i.NgForOf,i.NgIf,v.b,g.A,g.e,f.p],styles:[".settings-container[_ngcontent-%COMP%]{background-color:#f1f3f8;overflow:hidden}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]{margin-left:16px;margin-right:16px;margin-bottom:16px;padding:4px 16px 14px;height:var(--dynamicHeight);border-radius:4px;background-color:#fff;border:1px solid #e8e9ee;position:relative;gap:5px;display:flex;flex-direction:column}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:10px 10px 10px 0}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#45546e;font-family:var(--fontFamily);font-size:14px;font-weight:900;line-height:16px;letter-spacing:.02em}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{padding:8px 12px;background-color:#79ba44;color:#fff;font-family:var(--fontFamily);font-size:14px;font-weight:600;line-height:16px;border-radius:5px;cursor:pointer}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]{overflow:auto;height:100%}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:7px!important}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent!important}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .mat-start-divider[_ngcontent-%COMP%]{position:absolute;width:98%;background:#45546e}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-weight:500;font-size:14px;color:#45546e;padding:15px}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .header-detail[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:400;font-size:12px;color:#b9c0ca}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{cursor:pointer;margin-right:15px}"]}),e})()}];let W=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(D)],o.k]}),e})();var j=n("bSwM"),V=n("1jcm"),A=n("QibW"),N=n("NFeN"),$=n("Qu3c"),z=n("Xi0T");let G=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,W,g.E,u.e,v.c,j.b,V.b,A.c,p.d,N.b,c.b,$.b,z.a,g.p]]}),e})()}}]);