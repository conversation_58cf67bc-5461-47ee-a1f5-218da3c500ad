(window.webpackJsonp=window.webpackJsonp||[]).push([[1004],{"4xVo":function(e,t,n){"use strict";n.d(t,"a",(function(){return s})),n.d(t,"b",(function(){return c}));var i=n("jhN1"),o=n("fXoL"),r=n("G9A5"),a=n("PVOt"),l=n("6t9p");let s=(()=>{let e=class extends a.b{constructor(e,t,n,i,o,r,a){super(e,t,n,i,r,a),this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"deferRenderingChange"},{emit:"delayChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"indicatorSrcChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"messageChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showIndicatorChange"},{emit:"showPaneChange"},{emit:"visibleChange"},{emit:"widthChange"}]),o.setHost(this)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get delay(){return this._getOption("delay")}set delay(e){this._setOption("delay",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get indicatorSrc(){return this._getOption("indicatorSrc")}set indicatorSrc(e){this._setOption("indicatorSrc",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get message(){return this._getOption("message")}set message(e){this._setOption("message",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showIndicator(){return this._getOption("showIndicator")}set showIndicator(e){this._setOption("showIndicator",e)}get showPane(){return this._getOption("showPane")}set showPane(e){this._setOption("showPane",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}_createInstance(e,t){return new r.a(e,t)}ngOnDestroy(){this._destroyWidget()}};return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](a.e),o["\u0275\u0275directiveInject"](a.j),o["\u0275\u0275directiveInject"](a.i),o["\u0275\u0275directiveInject"](i.h),o["\u0275\u0275directiveInject"](o.PLATFORM_ID))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-load-panel"]],inputs:{animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",deferRendering:"deferRendering",delay:"delay",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",indicatorSrc:"indicatorSrc",maxHeight:"maxHeight",maxWidth:"maxWidth",message:"message",minHeight:"minHeight",minWidth:"minWidth",position:"position",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showIndicator:"showIndicator",showPane:"showPane",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onShowing:"onShowing",onShown:"onShown",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",deferRenderingChange:"deferRenderingChange",delayChange:"delayChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",indicatorSrcChange:"indicatorSrcChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",messageChange:"messageChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showIndicatorChange:"showIndicatorChange",showPaneChange:"showPaneChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[o["\u0275\u0275ProvidersFeature"]([a.e,a.j,a.i]),o["\u0275\u0275InheritDefinitionFeature"]],decls:0,vars:0,template:function(e,t){},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.bb,l.Gc,l.Vd,l.vd,l.hb,l.lb,l.sb,l.id,l.jd,a.c,a.f,i.b],l.bb,l.Gc,l.Vd,l.vd,l.hb,l.lb,l.sb,l.id,l.jd,a.f]}),e})()},QUrN:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return l}));var i=n("fXoL");const o=n("wd/R");let r=(()=>{class e{transform(e,...t){return e?o(e).format(t[0]):""}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"amDateFormat",type:e,pure:!0}),e})();const a=new i.InjectionToken("NGX_MOMENT_OPTIONS");let l=(()=>{class e{static forRoot(t){return{ngModule:e,providers:[{provide:a,useValue:Object.assign({},t)}]}}}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)}}),e})()},"Qlw+":function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("jhN1"),o=n("fXoL"),r=(n("4ivh"),n("PVOt")),a=n("6t9p");let l=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.p,r.c,r.f,i.b],a.p,r.f]}),e})()},VADE:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("fXoL");let o=(()=>{class e{static isFoundOnWalking(e,t){let n=e,i=!1;do{if(n.hasOwnProperty(t)||Object.getOwnPropertyDescriptor(n,t)){i=!0;break}}while(n=Object.getPrototypeOf(n));return i}static isNumber(e){return!isNaN(parseInt(e,10))&&isFinite(e)}static getValue(e){return"function"==typeof e?e():e}filterByString(e){return e&&(e=e.toLowerCase()),t=>!e||!!t&&-1!==(""+t).toLowerCase().indexOf(e)}filterByBoolean(e){return t=>Boolean(t)===e}filterByObject(t){return n=>{for(const i in t)if("$or"!==i){if(!n||!e.isFoundOnWalking(n,i))return!1;if(!this.isMatching(t[i],e.getValue(n[i])))return!1}else if(!this.filterByOr(t.$or)(e.getValue(n)))return!1;return!0}}isMatching(e,t){switch(typeof e){case"boolean":return this.filterByBoolean(e)(t);case"string":return this.filterByString(e)(t);case"object":return this.filterByObject(e)(t)}return this.filterDefault(e)(t)}filterByOr(e){return t=>{const n=e.length,i=Array.isArray(t)?n=>-1!==t.indexOf(e[n]):n=>this.isMatching(e[n],t);for(let e=0;e<n;e++)if(i(e))return!0;return!1}}filterDefault(e){return t=>void 0===e||e==t}transform(t,n){if(!t)return t;switch(typeof n){case"boolean":return t.filter(this.filterByBoolean(n));case"string":return e.isNumber(n)?t.filter(this.filterDefault(n)):t.filter(this.filterByString(n));case"object":return t.filter(this.filterByObject(n));case"function":return t.filter(n)}return t.filter(this.filterDefault(n))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"filterBy",type:e,pure:!1}),e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac}),e})(),r=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[o]}),e})()},XfUL:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n("ofXK"),o=n("fXoL"),r=n("bTqV"),a=n("bv9b"),l=n("Xa2L"),s=n("FKr1"),c=n("NFeN");const d=new o.InjectionToken("Global Config");let m=(()=>{class e{static forRoot(t){return{ngModule:e,providers:[{provide:d,useValue:t}]}}}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,r.b,a.b,l.b,s.v,c.b]]}),e})()},vJU9:function(e,t,n){"use strict";n.r(t),n.d(t,"AttachmentsModule",(function(){return Ne}));var i=n("ofXK"),o=n("tyNb"),r=n("XNiG"),a=n("1G5W"),l=n("fXoL"),s=n("R/Xf"),c=n("LcQX"),d=n("mrSG"),m=n("FvrZ"),h=n("2Vo4"),u=n("VRyK"),p=n("lJxs"),g=n("xG9w"),f=n("F97M");class v{constructor(e,t=1,n=!1,i=!1){this.item=e,this.level=t,this.expandable=n,this.isLoading=i}}let b=(()=>{class e{constructor(e,t){this.treeControl=e,this.graphApi=t,this.dataChange=new h.a([]),this.currentParentNode=[],this.currentChildNode=[],this.driveId=""}get data(){return this.dataChange.value}set data(e){this.treeControl.dataNodes=e,this.dataChange.next(e)}initialData(e,t){return Object(d.c)(this,void 0,void 0,(function*(){try{return this.driveId=e,(yield this.graphApi.getDriveItems(e,t)).map(e=>new v(e,0,!!e.folder))}catch(n){console.log(n)}}))}getChildren(e,t){return Object(d.c)(this,void 0,void 0,(function*(){try{return yield this.graphApi.getDriveItems(e,t)}catch(n){console.log(n)}}))}connect(e){return this.treeControl.expansionModel.changed.subscribe(e=>{(e.added||e.removed)&&this.handleTreeControl(e)}),Object(u.a)(e.viewChange,this.dataChange).pipe(Object(p.a)(()=>this.data))}handleTreeControl(e){e.added&&e.added.forEach(e=>this.toggleNode(e,!0)),e.removed&&e.removed.slice().reverse().forEach(e=>this.toggleNode(e,!1))}toggleNode(e,t){return Object(d.c)(this,void 0,void 0,(function*(){this.currentParentNode=e,e.isLoading=!0;const n=yield this.getChildren(this.driveId,e.item.id),i=g.findIndex(this.data,t=>t.item.id==e.item.id);if(e.isLoading=!1,n&&!(i<0)){if(t){const t=n.map(t=>new v(t,e.level+1,!!t.folder));this.currentChildNode=g.filter(t,e=>!e.expandable),this.data.splice(i+1,0,...t)}else{let t=0;for(let n=i+1;n<this.data.length&&this.data[n].level>e.level;n++,t++);if(this.data[i].level>0){const e=g.findIndex(this.data,e=>e.item.id==this.currentParentNode.item.parentReference.id);this.currentParentNode=this.data[e],this.currentChildNode=g.filter(this.data,e=>e.level==this.currentParentNode.level+1&&!e.expandable)}else this.currentParentNode={},this.currentChildNode=g.filter(this.data,e=>!e.expandable&&0==e.level);this.data.splice(i+1,t)}return this.dataChange.next(this.data),!0}}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275inject"](m.j),l["\u0275\u0275inject"](f.a))},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const C={"image/png":"ms-Icon ms-Icon--FileImage txt","image/jpeg":"ms-Icon ms-Icon--FileImage txt ","image/jpg":"ms-Icon ms-Icon--FileImage txt",audio:"ms-Icon ms-Icon--MusicNote txt",video:"ms-Icon ms-Icon--MSNVideosSolid txt","application/pdf":"ms-Icon ms-Icon--PDF pdf","application/msword":"ms-Icon ms-Icon--WordDocument word","application/vnd.ms-word":"ms-Icon ms-Icon--WordDocument word","application/vnd.openxmlformats-officedocument.wordprocessingml.document":"ms-Icon ms-Icon--WordDocument word","application/vnd.oasis.opendocum  ent.text":"ms-Icon ms-Icon--TextDocument txt","text/plain":"ms-Icon ms-Icon--TextDocument txt","application/vnd.openxmlformats-officedocument.wordprocessingml":"ms-Icon ms-Icon--WordDocument","application/vnd.ms-excel":"ms-Icon ms-Icon--ExcelDocument excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":"ms-Icon ms-Icon--ExcelDocument excel","application/vnd.oasis.opendocument.spreadsheet":"ms-Icon ms-Icon--ExcelDocument excel","application/vnd.ms-powerpoint":"ms-Icon ms-Icon--PowerPointDocument ppt","application/vnd.openxmlformats-officedocument.presentationml":"ms-Icon ms-Icon--PowerPointDocument ppt","application/vnd.openxmlformats-officedocument.presentationml.presentation":"ms-Icon ms-Icon--PowerPointDocument ppt","application/vnd.oasis.opendocument.presentation":"ms-Icon ms-Icon--PowerPointDocument","text/html":"ms-Icon ms-Icon--FileCode txt","application/json":"ms-Icon ms-Icon--Code txt","message/rfc822":"ms-Icon ms-Icon--Mail mail","application/gzip":"ms-Icon ms-Icon--ZipFolder txt","application/zip":"ms-Icon ms-Icon--ZipFolder txt"};var x=n("PSD3"),y=n.n(x),w=n("tk/3"),S=n("vgCS");let I=(()=>{class e{constructor(e,t,n){this.http=e,this._o365=t,this.http=new w.c(n)}getUploadSession(e,t,n,i){return Object(d.c)(this,void 0,void 0,(function*(){i=i.replace(/\//g,"");let o=`https://graph.microsoft.com/v1.0/groups/${e}${t}/${i}.${n}:/createUploadSession`,r={item:{"@microsoft.graph.conflictBehavior":"rename",name:`${i}.${n}`}};try{const e=yield this.http.post(o,r,{headers:new w.f({Authorization:"Bearer "+(yield this._o365.getToken()),"Content-Type":"application/json"})}).toPromise();return Promise.resolve(e)}catch(a){return Promise.reject(a)}}))}uploadChunks(e,t){return Object(d.c)(this,void 0,void 0,(function*(){new FileReader;let n=0,i=!0;for(;i;){let r;try{i=!0;try{let t=n+327680;if(r=yield this.readFragmentAsync(e,n,t),!(r.byteLength>0))break;i=!0}catch(o){break}try{let o=yield this.uploadChunk(r,t,n,e.size);if(202!=o[0]&&201!=o[0]&&200!=o[0])throw"Put operation did not return expected response";if(201===o[0]||200===o[0])return i=!1,Promise.resolve(o);n=Number(o[1].nextExpectedRanges[0].split("-")[0])}catch(o){console.log("Error occured when calling uploadChunk::"+o)}}catch(o){return i=!1,Promise.reject()}}}))}readFragmentAsync(e,t,n){const i=new FileReader;var o=e.slice(t,n);return i.readAsArrayBuffer(o),new Promise((e,t)=>{i.onloadend=t=>{i.readyState==i.DONE&&e(i.result)}})}uploadChunk(e,t,n,i){return Object(d.c)(this,void 0,void 0,(function*(){let o=n+e.byteLength-1;try{let r=`bytes ${n}-${o}/${i}`;const a=yield this.http.put(t,e,{observe:"response",headers:new w.f({Authorization:"Bearer "+(yield this._o365.getToken()),"Content-Range":r})}).toPromise();return Promise.resolve([a.status,a.body])}catch(r){return console.log("exception inside uploadFragmentAsync::  "+r),Promise.reject(r)}}))}getItemData(e,t){return Object(d.c)(this,void 0,void 0,(function*(){const n=`https://graph.microsoft.com/v1.0/groups/${e}/drive/items/${t}`;try{const e=yield this.http.post(n,{},{headers:new w.f({Authorization:"Bearer "+(yield this._o365.getToken()),"Content-Type":"application/json"})}).toPromise();return console.log(e),Promise.resolve(e)}catch(i){return Promise.reject(i)}}))}uploadFile(e,t,n){return Object(d.c)(this,void 0,void 0,(function*(){try{let i,o=n.name.lastIndexOf("."),r=n.name.substring(o+1),a=n.name.substring(0,o);const l=yield this.getItemData(e,t);i=(yield this.getUploadSession(e,l.parentReference.path+"/"+l.name,r,a)).uploadUrl;const s=yield this.uploadChunks(n,i);return Promise.resolve(s)}catch(i){return console.log(i),Promise.reject()}}))}getUploadSessionByPath(e,t,n,i){return Object(d.c)(this,void 0,void 0,(function*(){const o=`https://graph.microsoft.com/v1.0/groups/${e}${t}/${i}.${n}:/createUploadSession`,r={item:{"@microsoft.graph.conflictBehavior":"rename",name:`${i}.${n}`}};try{const e=yield this.http.post(o,r,{headers:new w.f({Authorization:"Bearer "+(yield this._o365.getToken()),"Content-Type":"application/json"})}).toPromise();return Promise.resolve(e)}catch(a){return Promise.reject(a)}}))}uploadFileByFolderName(e,t,n){return Object(d.c)(this,void 0,void 0,(function*(){try{let i,o=n.name.lastIndexOf("."),r=n.name.substring(o+1),a=n.name.substring(0,o);return i=(yield this.getUploadSessionByPath(e,t,r,a)).uploadUrl,console.log("Reached here"),yield this.uploadChunks(n,i),Promise.resolve()}catch(i){return console.log(i),Promise.reject()}}))}getEmbedLink(e,t){return Object(d.c)(this,void 0,void 0,(function*(){const n=`https://graph.microsoft.com/v1.0/drives/${e}/items/${t}/createLink`,i={type:"view",scope:"anonymous"};try{const e=yield this.http.post(n,JSON.stringify(i),{headers:new w.f({Authorization:"Bearer "+(yield this._o365.getToken()),"Content-Type":"application/json"})}).toPromise();return console.log(e),Promise.resolve(e.link.webUrl)}catch(o){return Promise.reject(o)}}))}createFolder(e,t,n){return Object(d.c)(this,void 0,void 0,(function*(){n=n.replace(/\//g,"");const i=`https://graph.microsoft.com/v1.0/groups/${e}/drive/items/${t}/children`,o={name:n,folder:{},"@microsoft.graph.conflictBehavior":"rename"};try{const e=yield this.http.post(i,o,{headers:new w.f({Authorization:"Bearer "+(yield this._o365.getToken()),"Content-Type":"application/json"})}).toPromise();return Promise.resolve(e)}catch(r){return Promise.reject(r)}}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275inject"](w.c),l["\u0275\u0275inject"](S.a),l["\u0275\u0275inject"](w.b))},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var O=n("0IaG"),P=n("JqCM"),E=n("Wp6s"),_=n("8yBR"),N=n("bTqV"),j=n("Qu3c"),M=n("NFeN"),F=n("Xa2L"),k=n("QUrN");function D(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"mat-tree-node",19),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](2).selectFile(n)})),l["\u0275\u0275elementStart"](1,"button",20),l["\u0275\u0275element"](2,"i",21),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",22),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"](2);l["\u0275\u0275classProp"]("file-selected",(null==n.currentFile||null==n.currentFile.item?null:n.currentFile.item.id)==e.item.id),l["\u0275\u0275advance"](2),l["\u0275\u0275classMap"](n.fileType[null==e||null==e.item||null==e.item.file?null:e.item.file.mimeType]||"ms-Icon ms-Icon--FileTemplate"),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",e.item.name," ")}}function T(e,t){1&e&&l["\u0275\u0275element"](0,"mat-spinner",27)}function B(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-tree-node",23),l["\u0275\u0275elementStart"](1,"button",24),l["\u0275\u0275elementStart"](2,"mat-icon",25),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",22),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](6,T,1,0,"mat-spinner",26),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"](2);l["\u0275\u0275classProp"]("selected",e.item.id==(null==n.dataSource||null==n.dataSource.currentParentNode||null==n.dataSource.currentParentNode.item?null:n.dataSource.currentParentNode.item.id)),l["\u0275\u0275advance"](1),l["\u0275\u0275attribute"]("aria-label","toggle "+e.item.name),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",n.treeControl.isExpanded(e)?"folder_open":"folder"," "),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.item.name),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isLoading)}}function A(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",28),l["\u0275\u0275elementStart"](2,"span",29),l["\u0275\u0275text"](3," This folder is empty "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function V(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",35),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](3).selectFile(n)})),l["\u0275\u0275elementStart"](1,"div",36),l["\u0275\u0275element"](2,"i",37),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",38),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",39),l["\u0275\u0275text"](6),l["\u0275\u0275pipe"](7,"amDateFormat"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",40),l["\u0275\u0275text"](9),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",41),l["\u0275\u0275text"](11),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](12,"div",42),l["\u0275\u0275elementStart"](13,"button",43),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](3).downloadFile(n)})),l["\u0275\u0275text"](14," Download "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"button",44),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](3).deleteFile(n)})),l["\u0275\u0275elementStart"](16,"mat-icon",45),l["\u0275\u0275text"](17,"delete"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275classMap"](n.fileType[null==e||null==e.item||null==e.item.file?null:e.item.file.mimeType]||"ms-Icon ms-Icon--FileTemplate"),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",e.item.name," "),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind2"](7,6,e.item.lastModifiedDateTime,"DD-MM-YY HH:mm:ss")," "),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",null==e.item.lastModifiedBy||null==e.item.lastModifiedBy.user?null:e.item.lastModifiedBy.user.displayName," "),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",n.utilityService.getFileSize(e.item.size)," ")}}function z(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",30),l["\u0275\u0275elementStart"](2,"div",31),l["\u0275\u0275text"](3," Type "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",32),l["\u0275\u0275text"](5," File name "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"div",33),l["\u0275\u0275text"](7," Created on "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",33),l["\u0275\u0275text"](9," Created by "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",33),l["\u0275\u0275text"](11," Size "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](12,V,18,9,"div",34),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](12),l["\u0275\u0275property"]("ngForOf",null==e.dataSource?null:e.dataSource.currentChildNode)}}function $(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",3),l["\u0275\u0275elementStart"](1,"div",4),l["\u0275\u0275elementStart"](2,"mat-card",5),l["\u0275\u0275elementStart"](3,"mat-tree",6),l["\u0275\u0275template"](4,D,5,5,"mat-tree-node",7),l["\u0275\u0275template"](5,B,7,6,"mat-tree-node",8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"div",9),l["\u0275\u0275elementStart"](7,"div",10),l["\u0275\u0275elementStart"](8,"div",11),l["\u0275\u0275text"](9),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",12),l["\u0275\u0275elementStart"](11,"button",13),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().addFolder()})),l["\u0275\u0275elementStart"](12,"mat-icon",14),l["\u0275\u0275text"](13,"create_new_folder"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](14,"button",15),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275reference"](18).click()})),l["\u0275\u0275elementStart"](15,"mat-icon",14),l["\u0275\u0275text"](16,"note_add"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](17,"input",16,17),l["\u0275\u0275listener"]("change",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onFileAdd(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](19,A,4,0,"div",18),l["\u0275\u0275template"](20,z,13,1,"div",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("dataSource",e.dataSource)("treeControl",e.treeControl),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("matTreeNodeDefWhen",e.hasChild),l["\u0275\u0275advance"](4),l["\u0275\u0275textInterpolate2"](" ",null==e.dataSource||null==e.dataSource.currentParentNode||null==e.dataSource.currentParentNode.item||null==e.dataSource.currentParentNode.item.parentReference?null:e.dataSource.currentParentNode.item.parentReference.path.replace("/drive/root:",""),"\xa0 / \xa0",null==e.dataSource||null==e.dataSource.currentParentNode||null==e.dataSource.currentParentNode.item?null:e.dataSource.currentParentNode.item.name," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("disabled",!(null!=e.dataSource&&null!=e.dataSource.currentParentNode&&e.dataSource.currentParentNode.item||e.isPhaseClosure)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("disabled",!(null!=e.dataSource&&null!=e.dataSource.currentParentNode&&e.dataSource.currentParentNode.item)),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("ngIf",0==(null==e.dataSource?null:e.dataSource.currentChildNode.length)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0!=(null==e.dataSource?null:e.dataSource.currentChildNode.length))}}function W(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",35),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](3).selectFile(n)})),l["\u0275\u0275elementStart"](1,"div",52),l["\u0275\u0275element"](2,"i",37),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",53),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",39),l["\u0275\u0275text"](6),l["\u0275\u0275pipe"](7,"amDateFormat"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",54),l["\u0275\u0275text"](9),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",55),l["\u0275\u0275elementStart"](11,"button",43),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](3).downloadFile(n)})),l["\u0275\u0275text"](12," Download "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275classMap"](n.fileType[null==e||null==e.item||null==e.item.file?null:e.item.file.mimeType]||"ms-Icon ms-Icon--FileTemplate"),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",e.item.name," "),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind2"](7,5,e.item.lastModifiedDateTime,"DD-MM-YY")," "),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",null==e.item.lastModifiedBy||null==e.item.lastModifiedBy.user?null:e.item.lastModifiedBy.user.displayName," ")}}function H(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",46),l["\u0275\u0275elementStart"](2,"div",47),l["\u0275\u0275elementStart"](3,"button",48),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275reference"](7).click()})),l["\u0275\u0275elementStart"](4,"mat-icon",49),l["\u0275\u0275text"](5,"note_add"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"input",16,17),l["\u0275\u0275listener"]("change",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).onFileAdd(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",30),l["\u0275\u0275elementStart"](9,"div",31),l["\u0275\u0275text"](10," Type "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](11,"div",50),l["\u0275\u0275text"](12," File name "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](13,"div",33),l["\u0275\u0275text"](14," Created on "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"div",51),l["\u0275\u0275text"](16," Created by "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](17,W,13,8,"div",34),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](17),l["\u0275\u0275property"]("ngForOf",null==e.dataSource?null:e.dataSource.currentChildNode)}}function R(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div"),l["\u0275\u0275elementStart"](2,"h4",28),l["\u0275\u0275text"](3," No Attachments found ! "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",56),l["\u0275\u0275elementStart"](5,"button",57),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275reference"](8).click()})),l["\u0275\u0275text"](6," Upload file "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"input",16,17),l["\u0275\u0275listener"]("change",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).onFileAdd(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function U(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275template"](1,H,18,1,"div",18),l["\u0275\u0275template"](2,R,9,0,"div",18),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0!=(null==e.dataSource?null:e.dataSource.currentChildNode.length)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0==(null==e.dataSource?null:e.dataSource.currentChildNode.length))}}let L=(()=>{class e{constructor(e,t,n,i,o){this.graphApi=e,this.utilityService=t,this.fileUploader=n,this.matDialog=i,this.spinner=o,this.groupId="",this.channelId="",this.folderId="",this.folderName="",this.type="big",this.newFile=new l.EventEmitter,this.newFolder=new l.EventEmitter,this.fileType=[],this.currentFile={},this.getLevel=e=>e.level,this.isExpandable=e=>e.expandable,this.hasChild=(e,t)=>t.expandable,this.treeControl=new m.j(this.getLevel,this.isExpandable),this.dataSource=new b(this.treeControl,this.graphApi),this.dataSource.data=[]}ngOnInit(){this.fileType=C}ngOnChanges(){return Object(d.c)(this,void 0,void 0,(function*(){try{"big"==this.type?this.loadInitialData(this.channelId?this.channelId:"root"):"small"==this.type&&this.folderId?this.loadInitialData(this.folderId):(this.dataSource.data=[],this.dataSource.currentChildNode=[])}catch(e){this.utilityService.showMessage("Unable to load files","OK")}}))}loadInitialData(e){return Object(d.c)(this,void 0,void 0,(function*(){console.log("HIha");try{console.log("dataa",this.dataSource),this.dataSource.data=yield this.dataSource.initialData(this.groupId,e);let t=yield this.graphApi.getGroupDetail(this.groupId);return console.log("dataa",t),console.log("dataasss",this.dataSource.data),this.dataSource.currentChildNode=g.filter(this.dataSource.data,e=>!e.expandable),Promise.resolve()}catch(t){}}))}selectFile(e){this.currentFile=e}downloadFile(e){return Object(d.c)(this,void 0,void 0,(function*(){e&&window.open(e.item["@microsoft.graph.downloadUrl"],"_blank")}))}deleteFile(e){return Object(d.c)(this,void 0,void 0,(function*(){try{y.a.fire({title:"Are sure you want to delete this file?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes",cancelButtonText:"No",customClass:{container:"container-class",popup:"popup-class",header:"header-class",title:"title-class",closeButton:"close-button-class",image:"image-class",content:"content-class",input:"input-class",actions:"actions-class",confirmButton:"confirm-button-class",cancelButton:"cancel-button-class",footer:"footer-class"},preConfirm:()=>Object(d.c)(this,void 0,void 0,(function*(){try{yield this.graphApi.deleteFile(this.groupId,e.item.id),this.refresh(this.dataSource.currentParentNode.item?this.dataSource.currentParentNode.item.id:this.channelId?this.channelId:"root"),this.utilityService.showMessage("File deleted successfully","OK")}catch(t){this.utilityService.showMessage("Unable to delete file","OK")}}))})}catch(t){console.log(t)}}))}onFileAdd(e){return Object(d.c)(this,void 0,void 0,(function*(){try{let t,n=e.srcElement.files[0];if("small"==this.type&&!this.folderId&&this.folderName){const e=yield this.fileUploader.createFolder(this.groupId,this.channelId,this.folderName);this.newFolder.emit(e.id),this.folderId=e.id,yield this.loadInitialData(this.folderId)}t="big"==this.type?this.dataSource.currentParentNode&&this.dataSource.currentParentNode.item?this.dataSource.currentParentNode.item.id:"root:":this.folderId;const i=yield this.fileUploader.uploadFile(this.groupId,t,n);this.utilityService.showMessage("File Uploaded Successfully","Done"),this.refresh(t),this.newFile.emit(i)}catch(t){this.utilityService.showMessage("Unable to upload file","OK")}}))}addFolder(){return Object(d.c)(this,void 0,void 0,(function*(){try{y.a.fire({title:"Enter Folder Name",input:"text",inputAttributes:{autocapitalize:"off"},showCancelButton:!0,confirmButtonText:"Add",showLoaderOnConfirm:!0,customClass:{container:"container-class",popup:"popup-class",header:"header-class",title:"title-class",closeButton:"close-button-class",image:"image-class",content:"content-class",input:"input-class",actions:"actions-class",confirmButton:"confirm-button-class",cancelButton:"cancel-button-class",footer:"footer-class"},preConfirm:e=>Object(d.c)(this,void 0,void 0,(function*(){try{return yield this.fileUploader.createFolder(this.groupId,this.dataSource.currentParentNode.item?this.dataSource.currentParentNode.item.id:this.channelId?this.channelId:"root",e),e}catch(t){console.log(t),y.a.showValidationMessage("Failed to create folder")}})),allowOutsideClick:()=>!y.a.isLoading()}).then(e=>{e.value&&(y.a.fire({title:`Folder ${e.value} created`,customClass:{container:"container-class",popup:"popup-class",header:"header-class",title:"title-class",closeButton:"close-button-class",image:"image-class",content:"content-class",input:"input-class",actions:"actions-class",confirmButton:"confirm-button-class",cancelButton:"cancel-button-class",footer:"footer-class"}}),this.refresh(this.dataSource.currentParentNode.item?this.dataSource.currentParentNode.item.id:this.channelId?this.channelId:"root"))})}catch(e){console.log(e)}}))}refresh(e){return Object(d.c)(this,void 0,void 0,(function*(){if(this.dataSource.currentParentNode.item){let e=this.dataSource.currentParentNode;yield this.dataSource.toggleNode(e,!1),this.dataSource.toggleNode(e,!0)}else this.loadInitialData(e||"root")}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](f.a),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](I),l["\u0275\u0275directiveInject"](O.b),l["\u0275\u0275directiveInject"](P.c))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-file-explorer"]],inputs:{groupId:"groupId",channelId:"channelId",folderId:"folderId",folderName:"folderName",isPhaseClosure:"isPhaseClosure",type:"type"},outputs:{newFile:"newFile",newFolder:"newFolder"},features:[l["\u0275\u0275NgOnChangesFeature"]],decls:3,vars:3,consts:[[3,"ngSwitch"],["class","row docclass",4,"ngSwitchCase"],[4,"ngSwitchCase"],[1,"row","docclass"],[1,"col-lg-4","col-12","pl-0","pr-2"],[1,"directorytreecard"],[3,"dataSource","treeControl"],["matTreeNodePadding","","class","cursor-pointer",3,"file-selected","click",4,"matTreeNodeDef"],["matTreeNodePadding","","class","cursor-pointer","matTreeNodeToggle","",3,"selected",4,"matTreeNodeDef","matTreeNodeDefWhen"],[1,"col-lg-8","col-12","d-flex","flex-column","pl-1"],[1,"row","pt-3","pb-3"],[1,"pt-2",2,"color","#161515"],[1,"ml-auto","mr-5"],["mat-mini-fab","","matTooltip","Add new folder","matTooltip","New folder",1,"fab-btn","mr-4",3,"disabled","click"],["aria-label","upload",2,"font-size","21px !important"],["mat-mini-fab","","matTooltip","Add new file","matTooltip","Upload file",1,"fab-btn","mr-1",3,"disabled","click"],["hidden","","type","file","required","",3,"change"],["fileInput",""],[4,"ngIf"],["matTreeNodePadding","",1,"cursor-pointer",3,"click"],["mat-icon-button","",1,"ic-size"],["aria-hidden","true"],[1,"single-line"],["matTreeNodePadding","","matTreeNodeToggle","",1,"cursor-pointer"],["mat-icon-button",""],[1,"mat-icon-rtl-mirror","ic-size"],["diameter","20","class","example-tree-progress-bar",4,"ngIf"],["diameter","20",1,"example-tree-progress-bar"],[1,"d-flex","justify-content-center","align-items-center","mt-5"],[1,"empty-message"],[1,"row","pb-2"],[1,"col-1","pl-4","txt-heading"],[1,"col-3","pl-2","txt-heading"],[1,"col-2","txt-heading"],["class","pt-1 row pb-2 select-row","style","border-bottom: #b3b3b3 1px solid; margin-left: 12px !important; margin-right: 12px !important",3,"click",4,"ngFor","ngForOf"],[1,"pt-1","row","pb-2","select-row",2,"border-bottom","#b3b3b3 1px solid","margin-left","12px !important","margin-right","12px !important",3,"click"],[1,"col-1","pr-0","pt-1"],["aria-hidden","true",2,"font-size","18px !important"],[1,"col-3","my-auto","file-name","single-line","pl-0"],[1,"col-2","my-auto","file-name"],[1,"col-2","file-name","my-auto","single-line"],[1,"col-1","file-name","my-auto","single-line"],[1,"col-3","activity"],["mat-button","","color","primary",1,"dwn-btn",3,"click"],["mat-icon-button","","matTooltip","Delete",1,"button-delete","ml-3",3,"click"],[2,"font-size","18px !important","line-height","23px !important"],[1,"row","pt-2","pb-2"],[1,"ml-auto","mr-2"],["mat-icon-button","","matTooltip","Add new file","matTooltip","Upload file",3,"click"],["aria-label","upload",2,"color","#292727 !important"],[1,"col-4","pl-2","txt-heading"],[1,"col-3","txt-heading"],[1,"col-1","pr-0"],[1,"col-4","my-auto","file-name","single-line","pl-0"],[1,"col-3","file-name","my-auto","single-line"],[1,"col-2","activity"],[1,"d-flex","justify-content-center","align-items-center","mt-4"],["mat-raised-button","",1,"upload-file",3,"click"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275template"](1,$,21,9,"div",1),l["\u0275\u0275template"](2,U,3,2,"div",2),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275property"]("ngSwitch",t.type),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngSwitchCase","big"),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngSwitchCase","small"))},directives:[i.NgSwitch,i.NgSwitchCase,E.a,_.b,_.h,N.a,j.a,M.a,i.NgIf,_.g,_.j,_.k,F.c,i.NgForOf],pipes:[k.a],styles:[".directorytreecard[_ngcontent-%COMP%]{min-height:550px!important;padding:4px!important;overflow:auto!important}.fab-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff!important;width:35px;height:35px}.mat-tree-node[_ngcontent-%COMP%]{min-height:30px!important}.docclass[_ngcontent-%COMP%]{font-size:12px!important}.label[_ngcontent-%COMP%], .span[_ngcontent-%COMP%]{font-size:13px!important}.file-name[_ngcontent-%COMP%]{font-size:12px!important;color:#161515;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.file-detail[_ngcontent-%COMP%]{font-size:14px!important;color:#161515}.side-heading[_ngcontent-%COMP%]{font-size:12px;color:#847e7e}.file-description-card[_ngcontent-%COMP%]{width:95%!important}.file-selected[_ngcontent-%COMP%], .file[_ngcontent-%COMP%]:hover, .select-row-selected[_ngcontent-%COMP%], .select-row[_ngcontent-%COMP%]:hover{background-color:rgba(223,223,217,.562)!important;cursor:pointer}.select-row-selected[_ngcontent-%COMP%]   .button-delete[_ngcontent-%COMP%], .select-row[_ngcontent-%COMP%]:hover   .button-delete[_ngcontent-%COMP%]{visibility:visible!important}.example-tree-progress-bar[_ngcontent-%COMP%]{margin-left:30px}.single-line[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:13px!important}.selected[_ngcontent-%COMP%]   *[_ngcontent-%COMP%]{color:#cf0001!important}.file[_ngcontent-%COMP%]{font-size:44px!important}.ic-size[_ngcontent-%COMP%]{font-size:20px!important;line-height:1!important}.excel[_ngcontent-%COMP%]{color:green!important}.word[_ngcontent-%COMP%]{color:#2b579a!important}.ppt[_ngcontent-%COMP%]{color:#d24726!important}.txt[_ngcontent-%COMP%]{color:#3e3e3f!important}.pdf[_ngcontent-%COMP%]{color:#bf040d!important}.mail[_ngcontent-%COMP%]{color:#282829!important}.txt-heading[_ngcontent-%COMP%]{color:#2f2f2f;font-size:11px!important;font-weight:400}.dwn-btn[_ngcontent-%COMP%]{line-height:30px;padding:0 14px}.dwn-btn[_ngcontent-%COMP%], .dwn-btn-1[_ngcontent-%COMP%]{min-width:60px;font-size:13px}.dwn-btn-1[_ngcontent-%COMP%]{line-height:22px;padding:0!important}h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:17px}.upload-file[_ngcontent-%COMP%]{line-height:32px;font-size:14px!important;padding:0 13px;background-color:#cf0001;color:#fff;font-weight:400}.empty-message[_ngcontent-%COMP%]{color:hsla(0,0%,41.2%,.9019607843137255);font-weight:400;font-size:12px}.button-delete[_ngcontent-%COMP%]{color:#474444!important;font-size:18px!important;height:31px!important;width:31px!important;line-height:23px!important;visibility:hidden!important}"]}),e})();const X=[{path:"",component:(()=>{class e{constructor(e,t){this._cmsService=e,this._util=t,this._onDestroy=new r.b}ngOnInit(){this._cmsService.getTeamsId().pipe(Object(a.a)(this._onDestroy)).subscribe(e=>{e&&(this.teamsId=e[0].name)},e=>{console.log("err"),this._util.showMessage("Error Retrieving Teams ID","Dismiss")})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](s.a),l["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-attachment-landing-page"]],decls:1,vars:2,consts:[[3,"groupId","channelId"]],template:function(e,t){1&e&&l["\u0275\u0275element"](0,"app-file-explorer",0),2&e&&l["\u0275\u0275property"]("groupId",t.teamsId)("channelId",t.channelId)},directives:[L],styles:[""]}),e})()}];let Y=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(X)],o.k]}),e})();var G=n("v2UZ"),K=n("3Pt+"),Z=n("XfUL"),J=n("iadO"),Q=n("f0Cb"),q=n("jaxi"),ee=n("STbY"),te=n("XhcP"),ne=n("/1cH"),ie=n("7EHt"),oe=n("kmnG"),re=n("qFsG"),ae=n("MutI"),le=n("TU8p"),se=n("WJ5W"),ce=n("5+WD"),de=n("1yaQ"),me=n("VADE"),he=n("ClZT"),ue=n("4xVo"),pe=n("Gkpw"),ge=n("WYlB"),fe=n("XPKZ"),ve=n("Qlw+"),be=n("8hBH"),Ce=n("lVl8"),xe=n("vxfF"),ye=n("dNgK"),we=n("bSwM"),Se=n("FKr1"),Ie=n("d3UM"),Oe=n("1jcm"),Pe=n("xHqg"),Ee=n("wZkO"),_e=n("+0xr");let Ne=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[{provide:Se.c,useClass:de.c,deps:[Se.f]},{provide:Se.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YY",monthYearLabel:"MMM YYYY"}}}],imports:[[i.CommonModule,Y,Ce.b,he.b,ue.b,pe.a,ge.b,fe.b,ve.a,k.b,ae.d,le.b,G.VirtualScrollModule,ye.b,Pe.f,K.E,Z.a,we.b,K.p,oe.e,Ie.d,re.c,E.d,N.b,J.h,Se.n,Q.b,M.b,Ie.d,Ee.g,q.c,se.b,ee.e,j.b,te.g,O.g,ne.c,te.g,Oe.b,ie.b,ce.g,F.b,me.a,P.b,be.c,xe.g,J.h,Se.n,_.e,_e.m]]}),e})()}}]);