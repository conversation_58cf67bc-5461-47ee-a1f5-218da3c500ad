(window.webpackJsonp=window.webpackJsonp||[]).push([[871],{X1Mi:function(e,n,t){"use strict";t.r(n),t.d(n,"CandidatesModule",(function(){return r}));var a=t("ofXK"),l=t("tyNb"),d=t("fXoL");const i=[{path:"",pathMatch:"full",redirectTo:"candidate-list"},{path:"candidate-list",loadChildren:()=>Promise.all([t.e(7),t.e(8),t.e(19),t.e(22),t.e(24),t.e(39),t.e(43),t.e(70),t.e(68),t.e(88),t.e(111),t.e(796)]).then(t.bind(null,"fN7l")).then(e=>e.ManageCandidatesModule),data:{breadcrumb:"All Candidates"}},{path:"talent-pipeline-list",loadChildren:()=>Promise.all([t.e(7),t.e(8),t.e(19),t.e(22),t.e(24),t.e(39),t.e(43),t.e(70),t.e(68),t.e(88),t.e(111),t.e(796)]).then(t.bind(null,"fN7l")).then(e=>e.ManageCandidatesModule),data:{breadcrumb:"Talent Pipeline"}},{path:"campus-job-talent-pipeline-list",loadChildren:()=>Promise.all([t.e(7),t.e(8),t.e(19),t.e(22),t.e(24),t.e(39),t.e(43),t.e(70),t.e(68),t.e(88),t.e(111),t.e(796)]).then(t.bind(null,"fN7l")).then(e=>e.ManageCandidatesModule),data:{breadcrumb:"Talent Pipeline"}}];let o=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[l.k.forChild(i)],l.k]}),e})(),r=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[a.CommonModule,o]]}),e})()}}]);