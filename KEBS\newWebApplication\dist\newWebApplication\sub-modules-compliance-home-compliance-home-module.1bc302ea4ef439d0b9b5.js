(window.webpackJsonp=window.webpackJsonp||[]).push([[1006,634,765,783,821,822,858,861,977,981,983,987,990,991],{H44p:function(e,t,a){"use strict";a.d(t,"a",(function(){return g}));var i=a("xG9w"),r=a("fXoL"),n=a("flaP"),s=a("ofXK"),o=a("Qu3c"),d=a("NFeN");function l(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"div",10),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div"),r["\u0275\u0275elementStart"](5,"p",11),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"p",12),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](e.label),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function c(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",13),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",14),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",16),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",17),r["\u0275\u0275elementStart"](1,"span",18),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function m(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",19),r["\u0275\u0275text"](1,"loop"),r["\u0275\u0275elementEnd"]())}function f(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",1),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().change()})),r["\u0275\u0275template"](1,l,9,4,"div",2),r["\u0275\u0275template"](2,c,3,2,"div",3),r["\u0275\u0275template"](3,u,3,3,"div",4),r["\u0275\u0275template"](4,h,3,3,"div",5),r["\u0275\u0275template"](5,p,3,3,"div",6),r["\u0275\u0275elementStart"](6,"div",7),r["\u0275\u0275template"](7,m,2,0,"mat-icon",8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","big"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","small"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","medium"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","large"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","overview"==e.type),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",e.toDisplay)}}let g=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=i.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=i.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||i.contains(["big","small"],this.type)?0==this.isConvertValue&&i.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](n.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&r["\u0275\u0275template"](0,f,8,6,"div",0),2&e&&r["\u0275\u0275property"]("ngIf",t.currency)},directives:[s.NgIf,o.a,d.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},HmYF:function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));var i=a("mrSG"),r=a("Iab2"),n=a("EUZL"),s=a("wd/R"),o=a("xG9w"),d=a("fXoL");let l=(()=>{class e{constructor(){this.formatColumn=(e,t,a)=>{const i=n.utils.decode_range(e["!ref"]);for(let r=i.s.r+1;r<=i.e.r;++r){const i=n.utils.encode_cell({r:r,c:t});e[i]&&e[i].v&&(e[i].t="d",e[i].z=a)}}}exportAsExcelFile(e,t,a,i,r){console.log("Excel to JSON Service",e);const s=n.utils.json_to_sheet(e);if(r&&r.length){const e=n.utils.sheet_to_json(s,{header:1}).shift();for(const t of r){const a=e.indexOf(t.fieldKey);this.formatColumn(s,a,t.fieldFormat)}}null==a&&(a=[]),null==i&&(i="DD-MM-YYYY"),this.formatExcelDateData(s,a,i);const o=n.write({Sheets:{data:s},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,t)}formatExcelDateData(e,t,a){for(let n of Object.keys(e))if(null!=e[n]&&null!=e[n].t&&null!=e[n].v&&s(e[n].v,a,!0).isValid()){let i=n.replace(/[0-9]/g,"")+"1";0==o.where(t,{value:e[i].v}).length&&null!=e[i]&&null!=e[i].t&&t.push({value:e[i].v,format:a})}let i=[],r=1;for(let n of t)for(let t of Object.keys(e)){let a=parseInt(t.replace(/[^0-9]/g,""));a>r&&(r=a),null!=e[t]&&null!=e[t].v&&e[t].v==n.value&&i.push({value:t.replace(/[0-9]/g,""),format:n.format})}for(let n of i)for(let t=2;t<=r;t++)null!=e[n.value+""+t]&&null!=e[n.value+""+t].t&&(e[n.value+""+t].t="d",null!=e[n.value+""+t].v&&"Invalid date"!=e[n.value+""+t].v?e[n.value+""+t].v=s(e[n.value+""+t].v,n.format).format("YYYY/MM/DD"):(console.log(e[n.value+""+t].t),e[n.value+""+t].v="",e[n.value+""+t].t="s"))}saveAsExcelFile(e,t){const a=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});r.saveAs(a,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,a){const i=n.utils.json_to_sheet(e),r=n.utils.json_to_sheet(t),s=n.write({Sheets:{All_Approvals:i,Pending_Approvals:r},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,a)}exportAsExcelFileForPayroll(e,t,a,i,r,s){const o=n.utils.json_to_sheet(e),d=n.utils.json_to_sheet(t),l=n.utils.json_to_sheet(a),c=n.utils.json_to_sheet(i),u=n.utils.json_to_sheet(r),h=n.write({Sheets:{Regular_Report:o,Intern_Report:d,Contract_Report:l,Perdiem_Report:c,RP_Report:u},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,s)}exportAsCsvFileWithSheetName(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let a=n.utils.book_new();for(let t of e){let e=n.utils.json_to_sheet(t.data);n.utils.book_append_sheet(a,e,t.sheetName)}let i=n.write(a,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(i,t)}))}saveAsCsvFile(e,t){return Object(i.c)(this,void 0,void 0,(function*(){const a=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});r.saveAs(a,t.concat(".csv"))}))}s2ab(e){return Object(i.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),a=new Uint8Array(t),i=0;i<e.length;i++)a[i]=255&e.charCodeAt(i);return t}))}exportAsExcelFileWithCellMerge(e,t,a){const i=n.utils.json_to_sheet(e);i["!merges"]=a;const r=n.write({Sheets:{data:i},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let a=n.utils.book_new();for(let t of e){let e=n.utils.json_to_sheet(t.data);n.utils.book_append_sheet(a,e,t.sheetName)}let i=n.write(a,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(i,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NJ67:function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,a){"use strict";a.d(t,"a",(function(){return u}));var i=a("mrSG"),r=a("XNiG"),n=a("xG9w"),s=a("fXoL"),o=a("tk/3"),d=a("LcQX"),l=a("XXEo"),c=a("flaP");let u=(()=>{class e{constructor(e,t,a,i){this.http=e,this.UtilityService=t,this.loginService=a,this.roleService=i,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,a,i,r,n,s){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:a,objectIds:i,skip:r,limit:n,filterConfig:s,orgIds:o})}getAllRoleAccess(){return n.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,a,i,r,n,s){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:a,objectIds:i,skip:r,limit:n,filterConfig:s,orgIds:o})}getRequestsForAwaitingApproval(e,t,a,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:a,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,a,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:a,filterConfig:i,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,a)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,a)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{a(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,a,r,s,o,d){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=o&&o.length>1&&(yield this.getManpowerCostByOId(o,a,s,2))||(yield this.getManpowerCostBasedOnPosition(e,t,a,s,d));let l=yield this.getNonManpowerCost(t,a,r,s,2),c=yield this.getAllocatedCost(),u=0;u=(i?i.cost:0)+l.length>0?n.reduce(n.pluck(l,"cost"),(e,t)=>e+t,0):0;let h=c.length>0?n.reduce(n.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:l,allocatedCost:c,allocatedCostValue:u*(h/100)}}))}getManpowerCostBasedOnPosition(e,t,a,i,r){return new Promise((n,s)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:a,unit:i,position:r}).subscribe(e=>n(e),e=>(console.log(e),s(e)))})}getNonManpowerCost(e,t,a,i,r){return new Promise((n,s)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:a,unit:i,currency_id:r}).subscribe(e=>n(e),e=>(console.log(e),s(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,a,i){return new Promise((r,n)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:a,currency_id:i}).subscribe(e=>r(e),e=>(console.log(e),n(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](o.c),s["\u0275\u0275inject"](d.a),s["\u0275\u0275inject"](l.a),s["\u0275\u0275inject"](c.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},tKbL:function(e,t,a){"use strict";a.d(t,"a",(function(){return o}));var i=a("xG9w"),r=a("fXoL"),n=a("tk/3"),s=a("BVzC");let o=(()=>{class e{constructor(e,t){this.http=e,this._ErrorService=t}checkIfWfNeedsToBeTriggered(e){return new Promise((t,a)=>{this.http.post("/api/wf/getApplicationWfConfigDetails",{application_id:e.applicationId}).subscribe(r=>{if(!("S"==r.messType&&r.data.length>0))return t({error:!1,messText:"Workflow Config does not exist for application !",data:null});{let n=i.filter(r.data,t=>t.trigger_object_value instanceof Array?t.trigger_object_level==e.triggerObjectLevel&&i.contains(t.trigger_object_value,e.triggerObjectValue):t.trigger_object_level==e.triggerObjectLevel&&t.trigger_object_value==e.triggerObjectValue);if(!(n&&n.length>0))return t({error:!1,messText:"Workflow Config does not exist !",data:null});this.triggerWfBasedOnConfig(n[0],e).then(e=>t({error:!1,messText:"Workflow created Successfully !",data:e})).catch(e=>(this._ErrorService.userErrorAlert(e.error?e.error.errorCode:e.errorCode,"Error creating workflow !",e.error?e.error.errMessage:e.errMessage),a({error:!0,messText:e.messText,data:null})))}},e=>(this._ErrorService.userErrorAlert(e.error?e.error.errorCode:e.errorCode,"Error getting application workflow configs !",e.error?e.error.errMessage:e.errMessage),a({error:!0,messText:e,data:null})))})}triggerWfBasedOnConfig(e,t){return new Promise((a,i)=>{this.http.post("/api/wf/triggerWfBasedOnConfig",{current_wf_config:e,app_details:t}).subscribe(e=>"S"==e.messType&&e.data?a(e.data):i(e),e=>i(e))})}getWfApproverDetails(e,t){return this.http.post("/api/wf/getWfApproverDetails",{wf_header_id:t,wf_plugin_id:e})}updateWfBasedOnConfig(e,t,a,i,r,n){return this.http.post("/api/wf/updateWfBasedOnConfig",{wf_update_action:t,wf_plugin_id:e,approver_oid:a,approver_comments:i,isAdmin:r,adminOId:n})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](n.c),r["\u0275\u0275inject"](s.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,a){"use strict";a.d(t,"a",(function(){return d}));var i=a("mrSG"),r=a("xG9w"),n=a("fXoL"),s=a("tk/3"),o=a("BVzC");let d=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,a)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>a(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,a)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>a(e))})}getApproversHierarchy(e){return new Promise((t,a)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>a(e))})}createWorkflowItems(e){return new Promise((t,a)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>a(e))})}getWorkflowDetails(e){return new Promise((t,a)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let a=0;a<e.length;a++){let i=[],n=r.keys(t["cc"+a]);for(let r=0;r<n.length;r++)for(let s=0;s<t["cc"+a][n[r]].length;s++){let o={name:t["cc"+a][n[r]][s].DELEGATE_NAME,oid:t["cc"+a][n[r]][s].DELEGATE_OID,level:r+1,designation:t["cc"+a][n[r]][s].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+a][n[r]][s].IS_DELEGATED,role:t["cc"+a][n[r]][s].DELEGATE_ROLE_NAME};if(1==t["cc"+a][n[r]][s].IS_DELEGATED&&(o.delegated_by={name:t["cc"+a][n[r]][s].APPROVER_NAME,oid:t["cc"+a][n[r]][s].APPROVER_OID,level:r+1,designation:t["cc"+a][n[r]][s].APPROVER_DESIGNATION_NAME}),i.push(o),a==e.length-1&&r==n.length-1&&s==t["cc"+a][n[r]].length-1)return i}}}))}storeComments(e,t,a){return new Promise((i,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:a}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,a)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),a(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let a=[],i=r.keys(e["cc"+t]);for(let r=0;r<i.length;r++)for(let n=0;n<e["cc"+t][i[r]].length;n++){let s={name:e["cc"+t][i[r]][n].DELEGATE_NAME,oid:e["cc"+t][i[r]][n].DELEGATE_OID,level:e["cc"+t][i[r]][n].APPROVAL_ORDER,designation:e["cc"+t][i[r]][n].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[r]][n].IS_DELEGATED};if(1==e["cc"+t][i[r]][n].IS_DELEGATED&&(s.delegated_by={name:e["cc"+t][i[r]][n].APPROVER_NAME,oid:e["cc"+t][i[r]][n].APPROVER_OID,level:e["cc"+t][i[r]][n].APPROVAL_ORDER,designation:e["cc"+t][i[r]][n].APPROVER_DESIGNATION_NAME}),a.push(s),r==i.length-1&&n==e["cc"+t][i[r]].length-1)return a}}}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275inject"](s.c),n["\u0275\u0275inject"](o.a))},e.\u0275prov=n["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,a){"use strict";a.d(t,"a",(function(){return l})),a.d(t,"b",(function(){return c}));var i=a("jhN1"),r=a("fXoL"),n=a("oHs6"),s=a("PVOt"),o=a("6t9p");const d=["*"];let l=(()=>{let e=class extends s.b{constructor(e,t,a,i,r,n,s,o){super(e,t,a,i,s,o),this._watcherHelper=i,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),n.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new n.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let a=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(a||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](s.e),r["\u0275\u0275directiveInject"](s.j),r["\u0275\u0275directiveInject"](s.g),r["\u0275\u0275directiveInject"](s.i),r["\u0275\u0275directiveInject"](i.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,a){if(1&e&&r["\u0275\u0275contentQuery"](a,o.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([s.e,s.j,s.i,s.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:d,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,s.c,s.f,i.b],o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,s.f]}),e})()},xT6i:function(e,t,a){"use strict";a.r(t),a.d(t,"ComplianceHomeModule",(function(){return ee}));var i=a("ofXK"),r=a("3Pt+"),n=a("tyNb"),s=a("mrSG"),o=a("33Jv"),d=a("wd/R"),l=a("xG9w"),c=a("XNiG"),u=a("1G5W"),h=a("fXoL"),p=a("0IaG"),m=a("LcQX"),f=a("BVzC"),g=a("WGBV"),y=a("GnQ3"),_=a("XXEo"),D=a("R/Xf"),v=a("JLuW"),C=a("tKbL"),S=a("HmYF"),b=a("bTqV"),x=a("NFeN"),T=a("xi/V"),O=a("Wk3H");function I(e,t){if(1&e){const e=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",4),h["\u0275\u0275elementStart"](1,"div",5),h["\u0275\u0275elementStart"](2,"span",6),h["\u0275\u0275text"](3,"Welcome to CMS"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",7),h["\u0275\u0275elementStart"](5,"span",8),h["\u0275\u0275text"](6,"Create Compliance Now"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"div",9),h["\u0275\u0275element"](8,"img",10),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](9,"button",11),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](e),h["\u0275\u0275nextContext"](2).createCompliance()})),h["\u0275\u0275elementStart"](10,"mat-icon",12),h["\u0275\u0275text"](11,"add_circle"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](12," Create New Compliance"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function w(e,t){if(1&e&&(h["\u0275\u0275elementStart"](0,"div",2),h["\u0275\u0275template"](1,I,13,0,"div",3),h["\u0275\u0275elementEnd"]()),2&e){const e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.isLoading&&1!=e.noCompliance)}}function A(e,t){1&e&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275element"](1,"udrf-header"),h["\u0275\u0275element"](2,"div",13),h["\u0275\u0275element"](3,"udrf-body"),h["\u0275\u0275elementEnd"]())}const k=[{path:"",component:(()=>{class e{constructor(e,t,a,i,r,n,s,h,p,m,f,g){this.router=e,this.activeroute=t,this._dialog=a,this._util=i,this._ErrorService=r,this.accountService=n,this._udrfService=s,this._loginService=h,this._cmsService=p,this._lazyService=m,this._wfPlugin=f,this._excelService=g,this.subs=new o.a,this._onDestroy=new c.b,this._onAppApiCalled=new c.b,this.udrfBodyColumns=[{item:"id",header:"Id",isVisible:"true",isActive:!0,type:"text",colSize:"1",textClass:"value13Bold",position:1,sortOrder:"I",width:80,hasColumnClick:!1,onColumnClick:"",filterId:16},{item:"name",header:"Compliance Name",isVisible:"true",isActive:!0,type:"text",colSize:"4",textClass:"value13Bold",position:2,sortOrder:"I",width:300,hasColumnClick:!1,onColumnClick:"",filterId:19},{item:"legal_entity",header:"Legal Entity",isVisible:"true",isActive:!0,type:"text",colSize:"2",textClass:"value13Bold",position:3,sortOrder:"I",width:290,hasColumnClick:!1,onColumnClick:"",filterId:6},{item:"owner_id",header:"Owner",isVisible:"true",isActive:!0,type:"profileImage",colSize:"3",textClass:"value13Bold",position:4,sortOrder:"I",width:180,hasColumnClick:!1,onColumnClick:"",filterId:1},{item:"status_name",header:"Status",isVisible:"true",isActive:!0,type:"compliance-status",colSize:"2",textClass:"value13Bold",position:5,sortOrder:"I",width:250,isInlineEdit:!0,inlineEditVarient:["compliance-status","minimal-dropdown"],filterId:11},{item:"deadline",header:"Due Date",isVisible:"true",isActive:!0,type:"date",colSize:"2",textClass:"value13Bold",position:6,sortOrder:"I",width:200,isInlineEdit:!0,inlineEditVarient:["Due Date","date-picker"],filterId:14},{item:"start_date",header:"Start Date",isVisible:"true",isActive:!0,type:"date",colSize:"1",textClass:"value13Bold",position:7,hasColumnClick:!1,onColumnClick:"",sortOrder:"I",width:130,filterId:17},{item:"end_date",header:"End Date",isVisible:"true",isActive:!0,type:"date",colSize:"1",textClass:"value13Bold",position:8,hasColumnClick:!1,onColumnClick:"",sortOrder:"I",width:130,filterId:18},{item:"country_name",header:"Country",isVisible:"true",isActive:!0,type:"text",colSize:"1",textClass:"value13Bold",position:9,sortOrder:"I",width:200,hasColumnClick:!1,onColumnClick:"",filterId:7},{item:"state_name",header:"State",isVisible:"true",isActive:!0,type:"text",colSize:"1",textClass:"value13Bold",position:10,sortOrder:"I",width:200,hasColumnClick:!1,onColumnClick:"",filterId:20},{item:"priority_name",header:"Prioriy",isVisible:"true",isActive:!0,type:"status",colSize:"2",textClass:"value13Bold",position:11,sortOrder:"I",width:200,hasColumnClick:!1,onColumnClick:"",filterId:10},{item:"priority_value",header:"Priority Value",isVisible:"true",isActive:!0,type:"text",colSize:"1",textClass:"value13Bold",position:12,sortOrder:"I",width:100,hasColumnClick:!1,onColumnClick:"",filterId:21},{item:"org_name",header:"Owner Org",isVisible:"false",isActive:!0,type:"text",colSize:"3",textClass:"value13Bold",position:13,sortOrder:"I",width:250,hasColumnClick:!1,onColumnClick:"",filterId:2},{item:"department",header:"Owner Department",isVisible:"false",isActive:!0,type:"text",colSize:"3",textClass:"value13Bold",position:14,sortOrder:"I",width:250,hasColumnClick:!1,onColumnClick:"",filterId:5},{item:"internal_deadline",header:"Internal Deadline",isVisible:"false",isActive:!0,type:"date",colSize:"2",textClass:"value13Bold",position:15,sortOrder:"I",width:200,isInlineEdit:!0,inlineEditVarient:["Internal Deadline","date-picker"],filterId:15},{item:"task",header:"Task",isVisible:"false",isActive:!1,type:"text",colSize:"1",textClass:"value13Bold",position:16,sortOrder:"N",width:150,hasColumnClick:!1,onColumnClick:""},{item:"approver_name",header:"Approved by",isVisible:"false",isActive:!0,type:"text",colSize:"3",textClass:"value13Bold",position:17,sortOrder:"I",width:250,hasColumnClick:!1,onColumnClick:"",filterId:3},{item:"approver_org_name",header:"Approver Org",isVisible:"false",isActive:!0,type:"text",colSize:"3",textClass:"value13Bold",position:18,sortOrder:"I",width:250,hasColumnClick:!1,onColumnClick:"",filterId:22},{item:"approver_dep",header:"Approved Dept",isVisible:"false",isActive:!0,type:"text",colSize:"3",textClass:"value13Bold",position:19,sortOrder:"N",width:250,hasColumnClick:!1,onColumnClick:""},{item:"completion_date",header:"Completed Date",isVisible:"true",isActive:!0,type:"date",colSize:"1",textClass:"value13Bold",position:20,hasColumnClick:!1,onColumnClick:"",sortOrder:"I",width:130,filterId:23},{item:"",header:"Actions",isVisible:"true",isActive:!0,type:"action",colSize:"2",textClass:"value13light",position:21,hasColumnClick:!1,onColumnClick:"",sortOrder:"N",width:280}],this.dataTypeArray=[{dataType:"Open",dataTypeName:"open",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"O",cardType:"status",statusColor:"#d3d3d3"},{dataType:"On Process",dataTypeName:"on process",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"OP",cardType:"status",statusColor:"#FFA500"},{dataType:"Request for Closure",dataTypeName:"request for closure",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"RFMA",cardType:"status",statusColor:"#8A2BE2"},{dataType:"Approved by Manager",dataTypeName:"approved by manager",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"ABM",cardType:"status",statusColor:"#0000FF"},{dataType:"Close",dataTypeName:"close",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"C",cardType:"status",statusColor:"#00D100"},{dataType:"Hold",dataTypeName:"hold",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"H",cardType:"status",statusColor:"#FF0000"},{dataType:"Total Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"TS",cardType:"status"},{dataType:"Actual Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"AS",cardType:"status"},{dataType:"Compliance Ratings",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"CR",cardType:"status"},{dataType:"Yearly Planned Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"YPS",cardType:"status"},{dataType:"Yearly Actual Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"YAS",cardType:"status"},{dataType:"Yearly Ratings",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"YR",cardType:"status"},{dataType:"Monthly Planned Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"MPS",cardType:"status"},{dataType:"Monthly Actual Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"MAS",cardType:"status"},{dataType:"Monthly Ratings",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"MR",cardType:"status"}],this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:["O","OP","RFMA","ABM","RFDA","ABD","C","H","TS","AS","CR","YPS","YAS","YR","MPS","MAS","MR"],categoryCards:[]}],this.udrfItemStatusColor=[{color:"#FF0000",status:"High"},{color:"#FF8C00",status:"Medium"},{color:"#7CFC00",status:"Low"},{status:"Open",color:"#d3d3d3"},{status:"On Process",color:"#FFA500"},{status:"Close",color:"#00D100"},{status:"Hold",color:"#FF0000"},{status:"Open",color:"#d3d3d3"},{status:"On Process",color:"#FFA500"},{status:"Request for Closure",color:"#8A2BE2"},{status:"Approved by Manager",color:"#0000FF"},{status:"Request for Department Approval",color:"#90EE90"},{status:"Approved by Department",color:"#87ceeb"},{status:"Close",color:"#00D100"},{status:"Hold",color:"#FF0000"}],this.quickCTAInput={applicationId:180,objectId:0,objectType:"O",originatorData:null},this.commentsInput={application_id:72,unique_id_1:"",unique_id_2:"",application_name:"Opportunity Governance",title:"",link:window.location.origin+"/main/opportunities/"},this.commentsContext={"Opportunity Name":"","Account Name":"","Sales SPOC":""},this.complianceTotal=0,this.isLoading=!1,this.noCompliance=!0,this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.selectedCard=[],this.isCardClicked=!1,this.isMoreLoading=!1,this.noData=!0,this.cardClicked="",this.skip=0,this.limit=15,this.defaultDataRetrievalCount=15,this.isAdmin=!1,this.filters=[],this.complianceItemDataIndex=0,this.mainSearchParameter="",this.user_duration_start=d(),this.mainApiDateRangeEnd=d(),this.dateFilters=[{name:"Overdue",date:{startLimit:d("2018-01-01").format(),endLimit:d().format(),clicked:!1}},{name:"This Week",date:{startLimit:d().startOf("week").format(),endLimit:d().endOf("week").format(),clicked:!1}},{name:"This Month",date:{startLimit:d().startOf("month").format(),endLimit:d().endOf("month").format(),clicked:!1}},{name:"Next Month",date:{startLimit:d().add(1,"month").startOf("month").format(),endLimit:d().add(1,"month").endOf("month").format()},clicked:!1},{name:"Last Month",date:{startLimit:d().subtract(1,"month").startOf("month").format(),endLimit:d().subtract(1,"month").endOf("month").format()},clicked:!1},{name:"Upcoming 3 Months",date:{startLimit:d().startOf("month").format(),endLimit:d().add(2,"month").endOf("month").format()},clicked:!1},{name:"This Year",date:{startLimit:d().startOf("year").format(),endLimit:d().endOf("year").format()},clicked:!1}],this.current_year_start=d().startOf("year"),this.current_year_end=d().endOf("year"),this.firstTime=!0,this.getStatusMaster=()=>{this._cmsService.getComplianceStatus({type:"ComplianceStatus"}).pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{let t=l.where(e.data,{show:!0});this._cmsService.getComplianceTaskStatus({type:"TaskStatus"}).pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{this._udrfService.udrfUiData.inlineEditDropDownMasterDatas={statusMasterDataL1:t,statusMasterDataL2:e.data}},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.callInlineEditApi=()=>{if("l1"==this._udrfService.udrfUiData.inlineEditData.hierarchyLevel){let e=this._udrfService.udrfUiData.inlineEditData,t=this._udrfService.udrfUiData.inlineEditData.inlineEditField;if("compliance-status"==t){let t=this._udrfService.udrfUiData.inlineEditData.dataSelected.parent,a=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse,i=this._udrfService.udrfUiData.inlineEditData.dataSelected._id;if(a.status_name!=this._udrfService.udrfUiData.inlineEditData.dataSelected.status_name)if(1==t)this.callWfPluginFunction(this._udrfService.udrfUiData.inlineEditData);else{let t=this._udrfService.udrfUiData.inlineEditData.dataSelected;this._cmsService.updateTaskStatus({task_id:i,compliance_id:(null==t?void 0:t.compliance_id)?null==t?void 0:t.compliance_id:null,upd_status_name:a.status_name,upd_status_color:a.status_color}).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>{var a;if(this._util.showMessage(t.msg,"dismiss",2e3),"All the sub task's should be in the closed status,couldn't update task status"!=t.msg&&"Access Denied for task status edit"!=t.msg){let t,i=this.getBodyData(this._udrfService.udrfBodyData,e.dataSelected,e.dataSelected._id,e.dataSelected.compliance_id,e.dataSelected.parent);i._id==e.dataSelected._id?t=i:(null===(a=i.children)||void 0===a?void 0:a.length)>0?(this.getData1(i.children,e.dataSelected._id),t=this.finalData):this._util.showMessage("Not Valid","Dismiss"),t.status_name=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.status_name}},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this._cmsService.showMessage("Can't Update")}else if("Due Date"==t){let e=this._udrfService.udrfUiData.inlineEditData.dataSelected.parent,t=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse;d(t["Due Date"]).format("YYYY-MM-DD")!=d(new Date(this._udrfService.udrfUiData.inlineEditData.dataSelected.deadline)).format("YYYY-MM-DD")?(d(t["Due Date"]).format("YYYY-MM-DD"),1==e||this._cmsService.showMessage("Can't Update")):this._cmsService.showMessage("Can't Update")}else if("Internal Deadline"==t){let e=this._udrfService.udrfUiData.inlineEditData.dataSelected.parent,t=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse;d(t["Internal Deadline"]).format("YYYY-MM-DD")!=d(new Date(this._udrfService.udrfUiData.inlineEditData.dataSelected.internal_deadline)).format("YYYY-MM-DD")?(d(t["Internal Deadline"]).format("YYYY-MM-DD"),1==e||this._cmsService.showMessage("Can't Update")):this._cmsService.showMessage("Can't Update")}}},this.openRequestDetailPageInNewTab=()=>{let e=`${window.location.origin}/main/compliance/complianceDetails/${this._udrfService.udrfUiData.openinNewTabData.data._id}`;window.open(e)},this.openDetailPageInNewTab=()=>{let e=`${window.location.origin}/main/compliance/complianceDetails/${this._udrfService.udrfUiData.itemCardSelecteditem._id}`;window.open(e)}}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){this._udrfService.udrfBodyData=[];let e=d();this.financial_year_start_date=e.month()>=3?d().year(e.year()).month(3):d().year(e.year()-1).month(3),this.activeroute.parent.params.subscribe(e=>{this.complianceId=e.id}),this.profileId=this._loginService.getProfile().profile.oid,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showSettingsModalButton=!0,this._udrfService.udrfUiData.showCreateNewComponentButton=!0,this._udrfService.udrfUiData.collapseAll=!1,this._udrfService.udrfUiData.showCollapseButton=!0,this._udrfService.udrfUiData.horizontalScroll=!0,this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfUiData.itemHasQuickCta=!0,this._udrfService.udrfUiData.itemHasComments=!0,this._udrfService.udrfUiData.itemHasOpenInNewTab=!0,this._udrfService.udrfUiData.itemHasWfApprovalFunctionBtn=!0,this._udrfService.udrfUiData.itemHasDeleteBtn=!0,this._udrfService.udrfUiData.udrfHasDmcConfig=!0,this._udrfService.udrfUiData.showReportDownloadButton=!0,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.isHeaderSort=!0,this._udrfService.udrfUiData.variant=4,this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.summaryCards=this.dataTypeArray,this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this._udrfService.udrfUiData.summaryCardsItem={},this._udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this._udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this._udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this._udrfService.udrfUiData.selectedCard=this.selectedCard,this._udrfService.udrfUiData.quickCTAInput=this.quickCTAInput,this._udrfService.udrfUiData.commentsInput=this.commentsInput,this._udrfService.udrfUiData.createNewComponent=this.createCompliance.bind(this),this._udrfService.udrfUiData.itemDataScrollDown=this.onComplianceItemDataScrollDown.bind(this),this._udrfService.udrfUiData.openQuickCta=this.openCTA.bind(this),this._udrfService.udrfUiData.openComments=this.openComments.bind(this),this._udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this._udrfService.udrfUiData.wfApprovalFunction=this.openWfApprovals.bind(this),this._udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this._udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this._udrfService.udrfUiData.itemcardSelected=this.openDetailPageInNewTab.bind(this),this._udrfService.udrfUiData.openInNewTab=this.openRequestDetailPageInNewTab.bind(this),this._udrfService.udrfUiData.openDeleteButton=this.openDeleteButton.bind(this),this._udrfService.udrfUiData.downloadItemDataReport=this.downloadComplianceReport.bind(this);let t=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this._util.getFormattedDate(d().startOf("week"),d(d().startOf("week")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().endOf("week"),d(d().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:d().startOf("month").format(),checkboxEndValue:d().endOf("month").format(),isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this._util.getFormattedDate(d().add(1,"month").startOf("month"),d(d().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().add(1,"month").endOf("month"),d(d().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this._util.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().add(2,"month").endOf("month"),d(d().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"OverDue",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD7",checkboxName:"YTD",checkboxStartValue:this._util.getFormattedDate(this.financial_year_start_date,d(this.financial_year_start_date.startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d(),d().date,15,0,0,0),isCheckboxDefaultSelected:!1}],a=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this._util.getFormattedDate(d().startOf("week"),d(d().startOf("week")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().endOf("week"),d(d().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this._util.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().endOf("month"),d(d().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this._util.getFormattedDate(d().add(1,"month").startOf("month"),d(d().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().add(1,"month").endOf("month"),d(d().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this._util.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().add(2,"month").endOf("month"),d(d().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"OverDue",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD7",checkboxName:"YTD",checkboxStartValue:this._util.getFormattedDate(this.financial_year_start_date,d(this.financial_year_start_date.startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d(),d().date,15,0,0,0),isCheckboxDefaultSelected:!1}],i=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this._util.getFormattedDate(d().startOf("week"),d(d().startOf("week")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().endOf("week"),d(d().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this._util.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().endOf("month"),d(d().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this._util.getFormattedDate(d().add(1,"month").startOf("month"),d(d().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().add(1,"month").endOf("month"),d(d().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this._util.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d().add(2,"month").endOf("month"),d(d().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"YTD",checkboxStartValue:this._util.getFormattedDate(this.financial_year_start_date,d(this.financial_year_start_date.startOf("month")).date,15,0,0,0),checkboxEndValue:this._util.getFormattedDate(d(),d().date,15,0,0,0),isCheckboxDefaultSelected:!1}];this._udrfService.udrfFunctions.constructCustomRangeData(14,"date",t),this._udrfService.udrfFunctions.constructCustomRangeData(13,"date",i),this._udrfService.udrfFunctions.constructCustomRangeData(15,"date",a),this._udrfService.getAppUdrfConfig(218,this.setUdrfData.bind(this)),this.getStatusMaster()}))}setUdrfData(){return Object(s.c)(this,void 0,void 0,(function*(){this._onAppApiCalled.next(),this.complianceItemDataIndex=0,this._udrfService.udrfBodyData=[],this.isCardClicked=!1,this.cardClicked="",this._udrfService.udrfUiData.resolveColumnConfig(),this.skip=0,this.limit=this.defaultDataRetrievalCount,yield this.getComplianceItemData(!1)}))}createCompliance(){return Object(s.c)(this,void 0,void 0,(function*(){const{ComplianceCreationComponent:e}=yield Promise.all([a.e(61),a.e(945)]).then(a.bind(null,"OhlT"));this._dialog.open(e,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw"}).afterClosed().subscribe(e=>{""!=e&&this.ngOnInit()})}))}resolveVisibleDataTypeArray(){return Object(s.c)(this,void 0,void 0,(function*(){for(let e of this._udrfService.udrfUiData.summaryCards){let t;this._udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=l.contains(this._udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this._udrfService.udrfUiData.summaryCardsItem=e))}}))}dataTypeCardSelected(){this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.noItemDataFound=!1;let e=this._udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.dataTypeArray.length;t++)if(e.dataTypeCode==this.dataTypeArray[t].dataTypeCode)this.dataTypeArray[t].isActive=!0,e.isActive=!0;else{let e=l.where(this._udrfService.udrfUiData.summaryCards,{dataTypeCode:this.dataTypeArray[t].dataTypeCode});e.length>0&&(e[0].isActive=!1),this.dataTypeArray[t].isActive=!1}this.isCardClicked=!0,this.cardClicked=e.dataTypeName,this.complianceCardClickedData=1==e.isActive?e:null,this.complianceItemDataIndex=0,this._udrfService.udrfBodyData=[],this.skip=0,this.limit=this.defaultDataRetrievalCount,this.getComplianceItemData(!1)}getComplianceItemData(e){return Object(s.c)(this,void 0,void 0,(function*(){let t,a,i,r,n,o,c=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),h=l.where(this.dataTypeArray,{isActive:!0}),p=[],m=!1,f=this.getDateLogic(c);if(t=f.durationStartDate,a=f.durationEndDate,i=f.deadlineStartDate,r=f.deadlineEndDate,n=f.internalDeadlineStartDate,o=f.internalDeadlineEndDate,h.length>0&&(p=l.where(h,{cardType:"status"}),p.length>0))if(c.length>0){for(let e of c)"ComplianceStatus"==e.filterName&&(e.multiOptionSelectSearchValues=l.contains(l.pluck(l.where(c,{filterName:"ComplianceStatus"})[0].masterData,"name"),h[0].dataType)?[h[0].dataType]:[],m=!0);if(0==m){let e=JSON.parse(JSON.stringify(l.where(this._udrfService.udrfData.filterTypeArray,{filterName:"ComplianceStatus"})));c.push(e[0]);for(let t of c)"ComplianceStatus"==t.filterName&&(t.multiOptionSelectSearchValues=l.contains(l.pluck(l.where(c,{filterName:"ComplianceStatus"})[0].masterData,"name"),h[0].dataType)?[h[0].dataType]:[],m=!0)}}else c=JSON.parse(JSON.stringify(l.where(this._udrfService.udrfData.filterTypeArray,{filterName:"ComplianceStatus"}))),c[0].multiOptionSelectSearchValues=l.contains(l.pluck(c[0].masterData,"name"),h[0].dataType)?[h[0].dataType]:[];let g=this.getFilters(c),y={skip:this.skip,limit:this.limit,filter:g,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,cardData:null,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,durationStartDate:t,durationEndDate:a,deadlineStartDate:i,deadlineEndDate:r,internalDeadlineStartDate:n,internalDeadlineEndDate:o};y.cardData=this.complianceCardClickedData,this.selectedCard=this._udrfService.udrfData.udrfSummaryCardCodes,null!=this._udrfService.udrfData.customDateFilterId&&("13"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(y.filter,{filterName:"Duration"}),"filterName"),"Duration")?"14"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(y.filter,{filterName:"Deadline"}),"filterName"),"Deadline")?"15"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(y.filter,{filterName:"InternalDeadline"}),"filterName"),"InternalDeadline")?(this._udrfService.udrfData.customDateFilterId=null,this._udrfService.udrfData.customDateRangeStart="",this._udrfService.udrfData.customDateRangeEnd=""):(y.internalDeadlineStartDate=this._udrfService.udrfData.customDateRangeStart,y.internalDeadlineEndDate=this._udrfService.udrfData.customDateRangeEnd,y.filter.push({filterName:"InternalDeadline",valueId:[""]})):(y.deadlineStartDate=this._udrfService.udrfData.customDateRangeStart,y.deadlineEndDate=this._udrfService.udrfData.customDateRangeEnd,y.filter.push({filterName:"Deadline",valueId:[""]})):(y.durationStartDate=this._udrfService.udrfData.customDateRangeStart,y.durationEndDate=this._udrfService.udrfData.customDateRangeEnd,y.filter.push({filterName:"Duration",valueId:[""]}))),l.contains(l.pluck(l.where(y.filter,{filterName:"Deadline"}),"filterName"),"Deadline")||(y.filter.push({filterName:"Deadline",valueId:["This Month"]}),y.deadlineStartDate=d().startOf("month").format(),y.deadlineEndDate=d().endOf("month").format()),this._cmsService.getComplianceData(y).pipe(Object(u.a)(this._onDestroy)).pipe(Object(u.a)(this._onAppApiCalled)).subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){if("N"==t.err&&t.data&&t.data.length>0)if(this.isCardClicked){let a=t.data;a=yield Promise.all(l.map(a,e=>Object(s.c)(this,void 0,void 0,(function*(){return e.expanded=this._udrfService.udrfUiData.collapseAll,e.deadline=yield this.addLocalTimeOffset(e.deadline),e.internal_deadline=yield this.addLocalTimeOffset(e.internal_deadline),e})))),this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat(a),this.formatComplianceList(this._udrfService.udrfBodyData,!0),e||this.getAllCardCount()}else{let a=t.data;a=yield Promise.all(l.map(a,e=>Object(s.c)(this,void 0,void 0,(function*(){return e.expanded=this._udrfService.udrfUiData.collapseAll,e.deadline=yield this.addLocalTimeOffset(e.deadline),e.internal_deadline=yield this.addLocalTimeOffset(e.internal_deadline),e})))),this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat(a),this.formatComplianceList(this._udrfService.udrfBodyData,!0),e||this.getAllCardCount()}else e||this.getAllCardCount(),this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfData.noItemDataFound=!0;this._udrfService.udrfData.isItemDataLoading=!1})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}formatComplianceList(e,t){var a;for(let i of e)i.loadChildTask=!1,i.showTask=t,i.parent=0!=t,(null===(a=i.children)||void 0===a?void 0:a.length)>0&&this.formatComplianceList(i.children,!1)}getFilters(e){let t=[];for(let a of e)t.push(a.isIdBased?{filterName:a.filterName,valueId:a.multiOptionSelectSearchValuesWithId,currentSortOrder:(null==a?void 0:a.currentSortOrder)?null==a?void 0:a.currentSortOrder:"I"}:{filterName:a.filterName,valueId:a.multiOptionSelectSearchValues,currentSortOrder:(null==a?void 0:a.currentSortOrder)?null==a?void 0:a.currentSortOrder:"I"});return t}onComplianceItemDataScrollDown(){return Object(s.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.noItemDataFound||this._udrfService.udrfData.isItemDataLoading||(this.skip+=this.defaultDataRetrievalCount,this._udrfService.udrfData.isItemDataLoading=!0,yield this.getComplianceItemData(!0))}))}showErrorMessage(e){this._util.showErrorMessage("Error:Unable to fetch Data","KEBS")}callWfPluginFunction(e){return Object(s.c)(this,void 0,void 0,(function*(){let t={applicationId:218,triggerObjectLevel:1,triggerObjectId:{1:e.dataSelected._id},triggerObjectValue:e.inlineEditResponse[e.dropdownConfig.apiDataUpdateKeyName],appActivityId:null,appStatusHistoryId:null,workflowId:yield this.getWfIdByCategory(e.dataSelected.category_name),initiatorOid:this._cmsService.currentUser.oid,costCentreList:[],submission:null,appDeterminedOrgCodes:[e.dataSelected.approver_org_id]};this._wfPlugin.checkIfWfNeedsToBeTriggered(t).then(t=>{t.error||this.updateComplianceStatus(e,t.data)}).catch(e=>{e.messText&&"string"==typeof e.messText&&this._cmsService.showMessage(e.messText)})}))}updateComplianceStatus(e,t){t&&(this._cmsService.updateWfIdByComplianceId(t.wf_plugin_id,e.dataSelected._id).pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{}),e.dataSelected.wf_id=t.wf_plugin_id);let a=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse;this._cmsService.updateComplainceStatus({compliance_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,upd_status_name:a.status_name,upd_status_color:a.status_color,upd_status_id:a._id}).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>{var a;if(this._util.showMessage(t.msg,"dismiss",2e3),"N"==t.err&&"All the task's should be in the closed status,couldn't update compliance status"!=t.msg){let t,i=this.getBodyData(this._udrfService.udrfBodyData,e.dataSelected,e.dataSelected._id,e.dataSelected.compliance_id,e.dataSelected.parent);i._id==e.dataSelected._id?t=i:(null===(a=i.children)||void 0===a?void 0:a.length)>0?(this.getData1(i.children,e.dataSelected._id),t=this.finalData):this._util.showMessage("Not Valid","Dismiss"),t.status_name=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.status_name,this.getAllCardCount()}else this._util.showMessage(t.msg,"Dismiss")},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}openWfApprovals(){return Object(s.c)(this,void 0,void 0,(function*(){let e=this._udrfService.udrfUiData.wfApprovalFunctionData;const{WfApproverDetailsComponent:t}=yield a.e(1001).then(a.bind(null,"4SbF"));this._dialog.open(t,{height:"auto",maxHeight:"100vh",minHeight:"25vw",width:"60%",maxWidth:"86%",data:{modalParams:{wfPluginId:e.wf_id?e.wf_id:null}}}).afterClosed().subscribe(t=>{if(t&&"submit"==t.event&&1==t.data.is_workflow_complete){let a=t.data.updated_object[1].display_value;this._cmsService.updateComplainceStatus({compliance_id:e._id,upd_status_name:t.data.updated_object[1].display_value,upd_status_id:t.data.updated_object[1].value}).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>{this._util.showMessage(t.msg,"dismiss",2e3),"N"==t.err&&"All the task's should be in the closed status,couldn't update compliance status"!=t.msg?e.status_name=a:this._util.showMessage(t.msg,"Dismiss")},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}})}))}getBodyData(e,t,a,i,r){for(let n of e)if(n.compliance_id==i)return n}getData1(e,t){var a;for(let i of e){if(i._id==t){this.finalData=i;break}(null===(a=i.children)||void 0===a?void 0:a.length)>0&&this.getData1(i.children,t)}}openComments(){return Object(s.c)(this,void 0,void 0,(function*(){let e=this._udrfService.udrfUiData.openCommentsData,t={inputData:{application_id:218,unique_id_1:e.data._id,unique_id_2:"",application_name:"Compliance Management",title:e.data.name},context:{Name:e.data.name,Status:e.data.status_name?e.data.status_name:"Not Assigned"},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};const{ChatCommentContextModalComponent:i}=yield Promise.all([a.e(4),a.e(63),a.e(75),a.e(983)]).then(a.bind(null,"vg2w"));this._dialog.open(i,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:t}})}))}openCTA(){this._lazyService.openQuickCTAModal({applicationId:218,objectId:null,objectType:null,originatorData:null,uniqId:this._udrfService.udrfUiData.openQuickCtaData.data._id},this._dialog)}getDateLogic(e){let t={durationStartDate:"",durationEndDate:"",deadlineStartDate:"",deadlineEndDate:"",internalDeadlineStartDate:"",internalDeadlineEndDate:""};for(let a of e)if(14==a.filterId){let e=l.where(a.checkboxValues,{isCheckboxSelected:!0});t.deadlineStartDate=e.length>0?e[0].checkboxStartValue:"",t.deadlineEndDate=e.length>0?e[0].checkboxEndValue:""}else if(13==a.filterId){let e=l.where(a.checkboxValues,{isCheckboxSelected:!0});t.durationStartDate=e.length>0?e[0].checkboxStartValue:"",t.durationEndDate=e.length>0?e[0].checkboxEndValue:""}else if(15==a.filterId){let e=l.where(a.checkboxValues,{isCheckboxSelected:!0});t.internalDeadlineStartDate=e.length>0?e[0].checkboxStartValue:"",t.internalDeadlineEndDate=e.length>0?e[0].checkboxEndValue:""}return t}openDeleteButton(){this._udrfService.udrfUiData.openDeleteButtonData.data.parent?this._util.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this compliance will be deleted !").then(e=>Object(s.c)(this,void 0,void 0,(function*(){e&&(this.subs.sink=this._cmsService.deleteCompliance({compliance_id:this._udrfService.udrfUiData.openDeleteButtonData.data._id}).subscribe(e=>{this.ngOnInit()},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}))}))):this._cmsService.showMessage("You can't Delete Task Here")}ngOnDestroy(){this.subs.unsubscribe(),this._onDestroy.next(),this._onDestroy.complete(),this._udrfService.resetUdrfData()}downloadComplianceReport(){let e,t,a,i,r,n;this._udrfService.udrfUiData.isReportDownloading=!0;let o=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),c=this.getDateLogic(o);e=c.durationStartDate,t=c.durationEndDate,a=c.deadlineStartDate,i=c.deadlineEndDate,r=c.internalDeadlineStartDate,n=c.internalDeadlineEndDate;let h=this.getFilters(o),p={skip:0,limit:this._udrfService.udrfUiData.totalItemDataCount,filter:h,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,cardData:null,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,durationStartDate:e,durationEndDate:t,deadlineStartDate:a,deadlineEndDate:i,internalDeadlineStartDate:r,internalDeadlineEndDate:n};p.cardData=this.complianceCardClickedData,null!=this._udrfService.udrfData.customDateFilterId&&("13"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(p.filter,{filterName:"Duration"}),"filterName"),"Duration")?"14"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(p.filter,{filterName:"Deadline"}),"filterName"),"Deadline")?"15"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(p.filter,{filterName:"InternalDeadline"}),"filterName"),"InternalDeadline")?(this._udrfService.udrfData.customDateFilterId=null,this._udrfService.udrfData.customDateRangeStart="",this._udrfService.udrfData.customDateRangeEnd=""):(p.internalDeadlineStartDate=this._udrfService.udrfData.customDateRangeStart,p.internalDeadlineEndDate=this._udrfService.udrfData.customDateRangeEnd,p.filter.push({filterName:"InternalDeadline",valueId:[""]})):(p.deadlineStartDate=this._udrfService.udrfData.customDateRangeStart,p.deadlineEndDate=this._udrfService.udrfData.customDateRangeEnd,p.filter.push({filterName:"Deadline",valueId:[""]})):(p.durationStartDate=this._udrfService.udrfData.customDateRangeStart,p.durationEndDate=this._udrfService.udrfData.customDateRangeEnd,p.filter.push({filterName:"Duration",valueId:[""]}))),l.contains(l.pluck(l.where(p.filter,{filterName:"Deadline"}),"filterName"),"Deadline")||(p.filter.push({filterName:"Deadline",valueId:["This Month"],currentSortOrder:"I"}),p.deadlineStartDate=d().startOf("month").format(),p.deadlineEndDate=d().endOf("month").format());let m=[];this._cmsService.getComplianceData(p).pipe(Object(u.a)(this._onDestroy)).pipe(Object(u.a)(this._onAppApiCalled)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if(this._udrfService.udrfUiData.isReportDownloading=!1,"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<e.data.length;t++){let a={};a.name=e.data[t].name,a.owner_name=e.data[t].owner_name,a.priority_name=e.data[t].priority_name,a.priority_value=e.data[t].priority_value,a.approver_name=e.data[t].approver_name,a.approver_org_name=e.data[t].approver_org_name,a.status_name=e.data[t].status_name,a.country_name=e.data[t].country_name,a.category_name=e.data[t].category_name,a.legal_entity=e.data[t].legal_entity,a.start_date=d(e.data[t].start_date).format("DD-MM-YYYY"),a.end_date=d(e.data[t].end_date).format("DD-MM-YYYY"),a.deadline=d(e.data[t].deadline).format("DD-MM-YYYY"),a.internal_deadline=d(e.data[t].internal_deadline).format("DD-MM-YYYY"),a.completion_date=e.data[t].completion_date?d(e.data[t].completion_date).format("DD-MM-YYYY"):null,m.push(a)}console.log(m),this._excelService.exportAsExcelFile(m,"Compliance Report")}else this._util.showMessage(e.messText,"Dismiss",3e3)})),e=>{this._udrfService.udrfUiData.isReportDownloading=!1,this._util.showMessage("Something went Wrong!","Dismiss",3e3)})}getAllCardCount(){return Object(s.c)(this,void 0,void 0,(function*(){let e,t,a,i,r,n,o=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),c=this.getDateLogic(o);e=c.durationStartDate,t=c.durationEndDate,a=c.deadlineStartDate,i=c.deadlineEndDate,r=c.internalDeadlineStartDate,n=c.internalDeadlineEndDate;let h=this.getFilters(o),p={skip:this.skip,limit:this.limit,filter:h,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,deadlineStartDate:a,deadlineEndDate:i,internalDeadlineStartDate:r,internalDeadlineEndDate:n};null!=this._udrfService.udrfData.customDateFilterId&&("13"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(p.filter,{filterName:"Duration"}),"filterName"),"Duration")?"14"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(p.filter,{filterName:"Deadline"}),"filterName"),"Deadline")?"15"!=this._udrfService.udrfData.customDateFilterId||l.contains(l.pluck(l.where(p.filter,{filterName:"InternalDeadline"}),"filterName"),"InternalDeadline")||(p.internalDeadlineStartDate=this._udrfService.udrfData.customDateRangeStart,p.internalDeadlineEndDate=this._udrfService.udrfData.customDateRangeEnd,p.filter.push({filterName:"InternalDeadline",valueId:[""]})):(p.deadlineStartDate=this._udrfService.udrfData.customDateRangeStart,p.deadlineEndDate=this._udrfService.udrfData.customDateRangeEnd,p.filter.push({filterName:"Deadline",valueId:[""]})):(p.durationStartDate=this._udrfService.udrfData.customDateRangeStart,p.durationEndDate=this._udrfService.udrfData.customDateRangeEnd,p.filter.push({filterName:"Duration",valueId:[""]}))),l.contains(l.pluck(l.where(p.filter,{filterName:"Deadline"}),"filterName"),"Deadline")||(p.filter.push({filterName:"Deadline",valueId:["This Month"]}),p.deadlineStartDate=d().startOf("month").format(),p.deadlineEndDate=d().endOf("month").format()),this._cmsService.getComplianceCardSummary(p).pipe(Object(u.a)(this._onDestroy)).pipe(Object(u.a)(this._onAppApiCalled)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if(this._udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Open"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].compliance_open),"On Process"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].compliance_on_process),"Request for Closure"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].compliance_request_for_closure),"Approved by Manager"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].compliance_approved_by_manager),"Close"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].compliance_close),"Hold"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].compliance_hold),"Total Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].tot_priority_value),"Actual Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].act_priority_value),"Compliance Ratings"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].rating),"Yearly Planned Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].tot_priority_value),"Yearly Actual Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].act_priority_value),"Yearly Ratings"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].rating),"Monthly Planned Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].tot_priority_value),"Monthly Actual Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].act_priority_value),"Monthly Ratings"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].rating),this.complianceTotal=e.data[0].compliance_total?e.data[0].compliance_total:0,this._udrfService.udrfUiData.totalItemDataCount=e.data[0].compliance_total?e.data[0].compliance_total:0;this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Open"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"On Process"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Request for Closure"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Approved by Manager"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Close"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Hold"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Total Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Actual Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Compliance Ratings"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Yearly Planned Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Yearly Actual Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Yearly Ratings"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Monthly Planned Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Monthly Actual Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Monthly Ratings"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),this.complianceTotal=0,this._udrfService.udrfUiData.totalItemDataCount=0;this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getWfIdByCategory(e){return new Promise((t,a)=>{this._cmsService.getTypeMasterData({type:"Category"}).subscribe(a=>{let i=l.where(a.data,{category_name:e})[0].wf_id;return console.log(i),t(i)},e=>a(e))})}addLocalTimeOffset(e){return Object(s.c)(this,void 0,void 0,(function*(){return e=d(e).subtract(d().local().utcOffset(),"m"),d(e).toISOString()}))}}return e.\u0275fac=function(t){return new(t||e)(h["\u0275\u0275directiveInject"](n.g),h["\u0275\u0275directiveInject"](n.a),h["\u0275\u0275directiveInject"](p.b),h["\u0275\u0275directiveInject"](m.a),h["\u0275\u0275directiveInject"](f.a),h["\u0275\u0275directiveInject"](g.a),h["\u0275\u0275directiveInject"](y.a),h["\u0275\u0275directiveInject"](_.a),h["\u0275\u0275directiveInject"](D.a),h["\u0275\u0275directiveInject"](v.a),h["\u0275\u0275directiveInject"](C.a),h["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=h["\u0275\u0275defineComponent"]({type:e,selectors:[["compliance-landing-page"]],decls:2,vars:2,consts:[["class","container-fluid",4,"ngIf"],[4,"ngIf"],[1,"container-fluid"],["class","my-3","style","text-align: center;",4,"ngIf"],[1,"my-3",2,"text-align","center"],[1,"pt-5","d-flex","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","17px","font-weight","600"],[1,"pt-2","d-flex","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","25px","font-weight","600"],[1,"pt-2","d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/no_compliance.png","height","300","width","325",1,"mt-2","mb-2"],["mat-raised-button","",1,"mt-2","btn-active","slide-from-down",3,"click"],[1,"small-icon","pr-2"],[1,"pt-1"]],template:function(e,t){1&e&&(h["\u0275\u0275template"](0,w,2,1,"div",0),h["\u0275\u0275template"](1,A,4,0,"div",1)),2&e&&(h["\u0275\u0275property"]("ngIf",1!=t.noCompliance),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.noCompliance))},directives:[i.NgIf,b.a,x.a,T.a,O.a],styles:[".btn-active[_ngcontent-%COMP%]{color:#fff;background-color:#cf0001;font-weight:400;font-size:14px!important;min-width:14rem;line-height:3.5em;border:#cf0001;border-radius:9px;box-shadow:0 3px 3px 1px #bbb;transition-duration:.5s}.small-icon[_ngcontent-%COMP%]{font-size:1.6em;color:#fff}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}];let E=(()=>{class e{}return e.\u0275mod=h["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=h["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.k.forChild(k)],n.k]}),e})();var R=a("kmnG"),V=a("qFsG"),N=a("d3UM"),M=a("/1cH"),F=a("iadO"),j=a("FKr1"),P=a("Qu3c"),U=a("Xa2L"),B=a("WJ5W"),L=a("4/q7"),Y=a("xHqg"),W=a("A5z7"),H=a("STbY"),q=a("QibW"),z=a("Wp6s"),G=a("MutI"),J=a("rDax"),X=a("dlKe"),K=a("lVl8"),Q=a("Xi0T"),$=a("7pIB");let Z=(()=>{class e{}return e.\u0275mod=h["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=h["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Q.a,Y.f,r.E,M.c,b.b,W.e,P.b,z.d,R.e,j.n,F.h,x.b,L.b,N.d,q.c,r.p,H.e,V.c,G.d,U.b,J.h,$.c,X.b,B.b,K.b]]}),e})(),ee=(()=>{class e{}return e.\u0275mod=h["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=h["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,E,p.g,r.p,r.E,L.b,R.e,V.c,N.d,B.b,M.c,F.h,j.n,x.b,b.b,P.b,U.b,Q.a,Z]]}),e})()}}]);