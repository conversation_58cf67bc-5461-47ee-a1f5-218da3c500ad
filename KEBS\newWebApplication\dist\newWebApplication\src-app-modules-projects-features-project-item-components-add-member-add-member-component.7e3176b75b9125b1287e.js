(window.webpackJsonp=window.webpackJsonp||[]).push([[973],{"06XO":function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i("xG9w"),a=i("fXoL");let s=(()=>{class e{transform(e,t,i){let a=n.findWhere(t,{field_name:e,type:i});return!!a&&!!a.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},"I/XM":function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i("xG9w"),a=i("fXoL");let s=(()=>{class e{transform(e,t,i){let a=n.findWhere(t,{field_name:e,type:i});return!!a&&!!a.is_disabled}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"checkDisabledField",type:e,pure:!0}),e})()},"PI+6":function(e,t,i){"use strict";i.d(t,"a",(function(){return _}));var n=i("fXoL"),a=i("3Pt+"),s=i("jtHE"),o=i("XNiG"),r=i("NJ67"),d=i("1G5W"),l=i("LcQX"),c=i("kmnG"),p=i("d3UM"),m=i("FKr1"),h=i("WJ5W"),f=i("ofXK");const u=["allSelected"],v=["singleSelect"];function g(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",5),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let _=(()=>{class e extends r.a{constructor(e,t){super(),this.renderer=e,this.utilityService=t,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this._onDestroy=new o.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{if(this.value=e,this.value)if(this.max_selectable){if(!(this.value.length<=this.max_selectable))return this.fieldCtrl.setValue(this.previousSelected),void this.utilityService.showMessage("Max selectable "+this.max_selectable,"Dismiss");this.previousSelected=this.value,this.onChange(e),this.valueChange.emit(e)}else this.previousSelected=this.value,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2),n["\u0275\u0275directiveInject"](l.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(n["\u0275\u0275viewQuery"](u,!0),n["\u0275\u0275viewQuery"](v,!0)),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",max_selectable:"max_selectable"},outputs:{valueChange:"valueChange"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:10,consts:[["appearance","outline",2,"width","100%"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](7,g,2,2,"mat-option",4),n["\u0275\u0275pipe"](8,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](8,8,t.filteredList)))},directives:[c.c,c.g,p.c,a.v,a.k,a.F,m.p,h.a,f.NgForOf],pipes:[f.AsyncPipe],styles:[""]}),e})()},Sbo8:function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i("xG9w"),a=i("fXoL");let s=(()=>{class e{transform(e,t,i){let a=n.findWhere(t,{field_name:e,type:i});return!!a&&!!a.is_mandant}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"checkMandatedField",type:e,pure:!0}),e})()},Ud1n:function(e,t,i){"use strict";i.r(t),i.d(t,"AddMemberComponent",(function(){return Ie}));var n=i("mrSG"),a=i("0IaG"),s=i("xG9w"),o=i("wd/R"),r=i.n(o),d=i("fXoL"),l=i("3Pt+"),c=i("qAI/"),p=i("XXEo"),m=i("LcQX"),h=i("Vym5"),f=i("NFeN"),u=i("ofXK"),v=i("bTqV"),g=i("Qu3c"),_=i("JXvX"),b=i("y5L8"),y=i("PI+6"),C=i("kmnG"),S=i("qFsG"),x=i("iadO"),M=i("bSwM"),D=i("Xa2L"),E=i("06XO");let k=(()=>{class e{constructor(e){this.projectService=e}transform(e,t,i){let n=s.findWhere(t,{field_name:e,type:i});return!!n&&!!n.role_access&&this.projectService.getProjectObjectAccess(n.role_access)}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](h.a))},e.\u0275pipe=d["\u0275\u0275definePipe"]({name:"checkRoleAccess",type:e,pure:!0}),e})();var B=i("YpVr"),L=i("Sbo8"),I=i("I/XM");function F(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function w(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",22),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"checkLabel"),d["\u0275\u0275template"](3,F,2,0,"span",23),d["\u0275\u0275pipe"](4,"checkMandatedField"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](2,2,"employee_name",e.formConfig,"isa","Search for member")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,7,"employee_name",e.formConfig,"isa"))}}function j(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",22),d["\u0275\u0275text"](1," Employee "),d["\u0275\u0275elementEnd"]())}function O(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275element"](1,"app-search-user-e360",25),d["\u0275\u0275pipe"](2,"checkMandatedField"),d["\u0275\u0275pipe"](3,"checkDisabledField"),d["\u0275\u0275pipe"](4,"checkLabel"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("isAutocomplete",!0)("required",d["\u0275\u0275pipeBind3"](2,4,"employee_name",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](3,8,"employee_name",e.formConfig,"isa"))("label",d["\u0275\u0275pipeBind4"](4,12,"employee_name",e.formConfig,"isa","Search for member"))}}function A(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.data?null:e.data.internal_stakeholders_name," ")}}function P(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,w,5,11,"div",21),d["\u0275\u0275template"](2,j,2,0,"div",21),d["\u0275\u0275template"](3,O,5,17,"div",9),d["\u0275\u0275template"](4,A,2,1,"div",9),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Edit"!=e.mode),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Edit"==e.mode),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Edit"!=e.mode),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Edit"==e.mode&&e.data)}}function R(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275elementStart"](1,"div",20),d["\u0275\u0275template"](2,P,5,4,"div",6),d["\u0275\u0275pipe"](3,"checkActive"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](3,1,"employee_name",e.formConfig,"isa"))}}function Y(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function q(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,Y,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275element"](7,"app-input-search-name",26),d["\u0275\u0275pipe"](8,"checkMandatedField"),d["\u0275\u0275pipe"](9,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,5,"practiceList",e.formConfig,"isa","Practice")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,10,"practiceList",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("list",e.practiceList)("required",d["\u0275\u0275pipeBind3"](8,14,"practiceList",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](9,18,"practiceList",e.formConfig,"isa"))}}function V(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275elementStart"](1,"div",20),d["\u0275\u0275template"](2,q,10,22,"div",6),d["\u0275\u0275pipe"](3,"checkActive"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](3,1,"practiceList",e.formConfig,"isa"))}}function N(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"span",31),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).addBusinessEntity("create")})),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"checkLabel"),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("+ Edit ",d["\u0275\u0275pipeBind4"](2,1,"addBusinessDivisionLabel",e.formConfig,"isa","Business Division"),"")}}function W(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",32),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"checkLabel"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](2,1,"addBusinessDivisionLabel",e.formConfig,"isa","Business Division"),"")}}function J(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"span",33),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).addBusinessEntity("edit")})),d["\u0275\u0275elementStart"](1,"mat-icon",34),d["\u0275\u0275text"](2,"edit"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function T(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"span",33),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).clearBusinessEntity()})),d["\u0275\u0275elementStart"](1,"mat-icon",34),d["\u0275\u0275text"](2,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function X(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",27),d["\u0275\u0275elementStart"](1,"div",20),d["\u0275\u0275template"](2,N,3,6,"span",28),d["\u0275\u0275template"](3,W,3,6,"span",29),d["\u0275\u0275template"](4,J,3,0,"span",30),d["\u0275\u0275pipe"](5,"checkActive"),d["\u0275\u0275template"](6,T,3,0,"span",30),d["\u0275\u0275pipe"](7,"checkActive"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",!e.businessUnitAdded),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.businessUnitAdded),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.businessUnitAdded&&d["\u0275\u0275pipeBind3"](5,4,"edit_region",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.businessUnitAdded&&d["\u0275\u0275pipeBind3"](7,8,"edit_region",e.formConfig,"isa"))}}function G(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function U(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,G,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275text"](7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,3,"entity",e.formConfig,"isa","Entity")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,8,"entity",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.entity_name," ")}}function Q(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,Q,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275text"](7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,3,"division",e.formConfig,"isa","Division")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,8,"division",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.division_name," ")}}function H(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function K(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,H,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275text"](7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,3,"subdivision",e.formConfig,"isa","Sub Division")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,8,"subdivision",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.sub_division_name," ")}}function $(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",35),d["\u0275\u0275template"](1,U,8,12,"div",36),d["\u0275\u0275pipe"](2,"checkActive"),d["\u0275\u0275template"](3,z,8,12,"div",36),d["\u0275\u0275pipe"](4,"checkActive"),d["\u0275\u0275template"](5,K,8,12,"div",36),d["\u0275\u0275pipe"](6,"checkActive"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](2,3,"entity",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,7,"division",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](6,11,"subdivision",e.formConfig,"isa"))}}function Z(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function ee(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,Z,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275element"](7,"app-input-search-name",38),d["\u0275\u0275pipe"](8,"checkMandatedField"),d["\u0275\u0275pipe"](9,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,5,"projectRoleMaster",e.formConfig,"isa","Project Role")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,10,"projectRoleMaster",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("list",e.projectRoleMasterList)("required",d["\u0275\u0275pipeBind3"](8,14,"projectRoleMaster",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](9,18,"projectRoleMaster",e.formConfig,"isa")||"Edit"==e.mode)}}function te(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",39),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,te,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275element"](7,"app-multi-select-search2",40),d["\u0275\u0275pipe"](8,"checkMandatedField"),d["\u0275\u0275pipe"](9,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,5,"secondaryProjectRole",e.formConfig,"isa","Secondary Project Role")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,10,"secondaryProjectRole",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("list",e.secprojectRoleMasterList)("required",d["\u0275\u0275pipeBind3"](8,14,"secondaryProjectRole",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](9,18,"secondaryProjectRole",e.formConfig,"isa"))}}function ne(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275template"](1,ee,10,22,"div",36),d["\u0275\u0275pipe"](2,"checkActive"),d["\u0275\u0275template"](3,ie,10,22,"div",36),d["\u0275\u0275pipe"](4,"checkActive"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](2,2,"projectRoleMaster",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,6,"secondaryProjectRole",e.formConfig,"isa")&&e.secondaryRoleLoading)}}function ae(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function se(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,ae,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275elementStart"](7,"mat-form-field",41),d["\u0275\u0275element"](8,"input",42),d["\u0275\u0275pipe"](9,"checkMandatedField"),d["\u0275\u0275pipe"](10,"checkDisabledField"),d["\u0275\u0275element"](11,"mat-datepicker-toggle",43),d["\u0275\u0275element"](12,"mat-datepicker",null,44),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275reference"](13),t=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,8,"startDate",t.formConfig,"isa","Start Date")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,13,"startDate",t.formConfig,"isa")),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matDatepicker",e)("max",t.maxStartDate?t.maxStartDate:t.addMemberForm.get("endDate").value)("min",t.minDate)("required",d["\u0275\u0275pipeBind3"](9,17,"startDate",t.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](10,21,"startDate",t.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("for",e)}}function oe(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function re(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,oe,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275elementStart"](7,"mat-form-field",41),d["\u0275\u0275element"](8,"input",45),d["\u0275\u0275pipe"](9,"checkMandatedField"),d["\u0275\u0275pipe"](10,"checkDisabledField"),d["\u0275\u0275element"](11,"mat-datepicker-toggle",43),d["\u0275\u0275element"](12,"mat-datepicker",null,44),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275reference"](13),t=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,8,"endDate",t.formConfig,"isa","End Date")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,13,"endDate",t.formConfig,"isa")),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matDatepicker",e)("min",t.minEndDate?t.minEndDate:t.addMemberForm.get("startDate").value)("max",t.maxDate)("required",d["\u0275\u0275pipeBind3"](9,17,"endDate",t.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](10,21,"endDate",t.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("for",e)}}function de(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275template"](1,se,14,25,"div",36),d["\u0275\u0275pipe"](2,"checkActive"),d["\u0275\u0275template"](3,re,14,25,"div",36),d["\u0275\u0275pipe"](4,"checkActive"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](2,2,"startDate",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,6,"endDate",e.formConfig,"isa"))}}function le(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function ce(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,le,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275element"](7,"app-input-search-name",46),d["\u0275\u0275pipe"](8,"checkMandatedField"),d["\u0275\u0275pipe"](9,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,5,"location",e.formConfig,"isa","Location")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,10,"location",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("list",e.isaTimesheetLocation)("required",d["\u0275\u0275pipeBind3"](8,14,"location",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](9,18,"location",e.formConfig,"isa"))}}function pe(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,pe,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275elementStart"](7,"mat-form-field",41),d["\u0275\u0275element"](8,"input",47),d["\u0275\u0275pipe"](9,"checkMandatedField"),d["\u0275\u0275pipe"](10,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,5,"split_percentage",e.formConfig,"isa","Split Percentage")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,10,"split_percentage",e.formConfig,"isa")),d["\u0275\u0275advance"](4),d["\u0275\u0275propertyInterpolate1"]("placeholder","",e.split_percentage_lower_percentage,"-100"),d["\u0275\u0275property"]("required",d["\u0275\u0275pipeBind3"](9,14,"split_percentage",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](10,18,"split_percentage",e.formConfig,"isa"))}}function he(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function fe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,he,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275elementStart"](7,"mat-form-field",41),d["\u0275\u0275element"](8,"input",48),d["\u0275\u0275pipe"](9,"checkMandatedField"),d["\u0275\u0275pipe"](10,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,4,"per_hour_rate",e.formConfig,"isa","Per Hour Rate")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,9,"per_hour_rate",e.formConfig,"isa")),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("required",d["\u0275\u0275pipeBind3"](9,13,"per_hour_rate",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](10,17,"per_hour_rate",e.formConfig,"isa"))}}function ue(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275template"](1,ce,10,22,"div",36),d["\u0275\u0275pipe"](2,"checkActive"),d["\u0275\u0275template"](3,me,11,22,"div",36),d["\u0275\u0275pipe"](4,"checkActive"),d["\u0275\u0275template"](5,fe,11,21,"div",36),d["\u0275\u0275pipe"](6,"checkActive"),d["\u0275\u0275pipe"](7,"checkRoleAccess"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](2,3,"location",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,7,"split_percentage",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](6,11,"per_hour_rate",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](7,15,"per_hour_rate",e.formConfig,"isa"))}}function ve(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function ge(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,ve,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275element"](7,"app-search-user-e360",50),d["\u0275\u0275pipe"](8,"checkMandatedField"),d["\u0275\u0275pipe"](9,"checkDisabledField"),d["\u0275\u0275pipe"](10,"checkLabel"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,6,"rmg_spoc",e.formConfig,"isa","RMG SPOC")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,11,"rmg_spoc",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("isAutocomplete",!0)("required",d["\u0275\u0275pipeBind3"](8,15,"rmg_spoc",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](9,19,"rmg_spoc",e.formConfig,"isa"))("label",d["\u0275\u0275pipeBind4"](10,23,"rmg_spoc",e.formConfig,"isa","RMG SPOC"))}}function _e(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function be(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,_e,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275element"](7,"app-input-search-name",51),d["\u0275\u0275pipe"](8,"checkMandatedField"),d["\u0275\u0275pipe"](9,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,5,"shift",e.formConfig,"isa","Shift")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,10,"shift",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("list",e.shiftList)("required",d["\u0275\u0275pipeBind3"](8,14,"shift",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](9,18,"shift",e.formConfig,"isa"))}}function ye(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function Ce(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,ye,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275elementStart"](7,"mat-form-field",41),d["\u0275\u0275element"](8,"input",52),d["\u0275\u0275pipe"](9,"checkMandatedField"),d["\u0275\u0275pipe"](10,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,4,"actual_hours",e.formConfig,"isa","Actual Hours")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,9,"actual_hours",e.formConfig,"isa")),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("required",d["\u0275\u0275pipeBind3"](9,13,"actual_hours",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](10,17,"actual_hours",e.formConfig,"isa"))}}function Se(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",24),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function xe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",53),d["\u0275\u0275elementStart"](1,"div",22),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"checkLabel"),d["\u0275\u0275template"](4,Se,2,0,"span",23),d["\u0275\u0275pipe"](5,"checkMandatedField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",19),d["\u0275\u0275element"](7,"app-input-search-name",54),d["\u0275\u0275pipe"](8,"checkMandatedField"),d["\u0275\u0275pipe"](9,"checkDisabledField"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](3,6,"identity",e.formConfig,"isa","Growth/Flexi")," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](5,11,"identity",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("list",e.identityList)("noSelect",!0)("required",d["\u0275\u0275pipeBind3"](8,15,"identity",e.formConfig,"isa"))("disabled",d["\u0275\u0275pipeBind3"](9,19,"identity",e.formConfig,"isa"))}}function Me(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275template"](1,ge,11,28,"div",36),d["\u0275\u0275pipe"](2,"checkActive"),d["\u0275\u0275template"](3,be,10,22,"div",36),d["\u0275\u0275pipe"](4,"checkActive"),d["\u0275\u0275template"](5,Ce,11,21,"div",36),d["\u0275\u0275pipe"](6,"checkActive"),d["\u0275\u0275template"](7,xe,10,23,"div",49),d["\u0275\u0275pipe"](8,"checkActive"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](2,4,"rmg_spoc",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,8,"shift",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](6,12,"actual_hours",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](8,16,"identity",e.formConfig,"isa")&&!e.addMemberForm.get("isBillable").value)}}function De(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",56),d["\u0275\u0275elementStart"](1,"div",57),d["\u0275\u0275elementStart"](2,"mat-checkbox",58),d["\u0275\u0275text"](3),d["\u0275\u0275pipe"](4,"checkLabel"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](d["\u0275\u0275pipeBind4"](4,1,"isBillable",e.formConfig,"isa","Billable"))}}function Ee(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",56),d["\u0275\u0275elementStart"](1,"div",57),d["\u0275\u0275elementStart"](2,"mat-checkbox",59),d["\u0275\u0275text"](3),d["\u0275\u0275pipe"](4,"checkLabel"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](d["\u0275\u0275pipeBind4"](4,1,"isIncentive",e.formConfig,"isa","Incentive"))}}function ke(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275template"](1,De,5,6,"div",55),d["\u0275\u0275pipe"](2,"checkActive"),d["\u0275\u0275template"](3,Ee,5,6,"div",55),d["\u0275\u0275pipe"](4,"checkActive"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](2,2,"isBillable",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,6,"isIncentive",e.formConfig,"isa"))}}function Be(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275template"](3,R,4,5,"div",9),d["\u0275\u0275pipe"](4,"checkActive"),d["\u0275\u0275template"](5,V,4,5,"div",9),d["\u0275\u0275pipe"](6,"checkActive"),d["\u0275\u0275template"](7,X,8,12,"div",10),d["\u0275\u0275pipe"](8,"checkActive"),d["\u0275\u0275pipe"](9,"checkActive"),d["\u0275\u0275pipe"](10,"checkActive"),d["\u0275\u0275template"](11,$,7,15,"div",11),d["\u0275\u0275pipe"](12,"checkActive"),d["\u0275\u0275pipe"](13,"checkActive"),d["\u0275\u0275pipe"](14,"checkActive"),d["\u0275\u0275template"](15,ne,5,10,"div",9),d["\u0275\u0275pipe"](16,"checkActive"),d["\u0275\u0275pipe"](17,"checkActive"),d["\u0275\u0275template"](18,de,5,10,"div",9),d["\u0275\u0275pipe"](19,"checkActive"),d["\u0275\u0275pipe"](20,"checkActive"),d["\u0275\u0275template"](21,ue,8,19,"div",9),d["\u0275\u0275pipe"](22,"checkActive"),d["\u0275\u0275pipe"](23,"checkActive"),d["\u0275\u0275pipe"](24,"checkActive"),d["\u0275\u0275pipe"](25,"checkRoleAccess"),d["\u0275\u0275template"](26,Me,9,20,"div",9),d["\u0275\u0275pipe"](27,"checkActive"),d["\u0275\u0275pipe"](28,"checkActive"),d["\u0275\u0275pipe"](29,"checkActive"),d["\u0275\u0275pipe"](30,"checkActive"),d["\u0275\u0275template"](31,ke,5,10,"div",9),d["\u0275\u0275pipe"](32,"checkActive"),d["\u0275\u0275pipe"](33,"checkActive"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](34,"div",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](35,"div",13),d["\u0275\u0275elementStart"](36,"div",14),d["\u0275\u0275elementStart"](37,"button",15),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().closeClicked()})),d["\u0275\u0275elementStart"](38,"span",16),d["\u0275\u0275text"](39,"Cancel"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](40,"div",17),d["\u0275\u0275elementStart"](41,"button",18),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().addMember()})),d["\u0275\u0275pipe"](42,"checkLabel"),d["\u0275\u0275elementStart"](43,"span"),d["\u0275\u0275text"](44,"Save"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,10,"employee_name",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](6,14,"practiceList",e.formConfig,"isa")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](8,18,"entity",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](9,22,"division",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](10,26,"subdivision",e.formConfig,"isa")),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",(d["\u0275\u0275pipeBind3"](12,30,"entity",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](13,34,"division",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](14,38,"subdivision",e.formConfig,"isa"))&&e.businessUnitAdded),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](16,42,"projectRoleMaster",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](17,46,"secondaryProjectRole",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](19,50,"startDate",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](20,54,"endDate",e.formConfig,"isa")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](22,58,"location",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](23,62,"split_percentage",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](24,66,"per_hour_rate",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](25,70,"per_hour_rate",e.formConfig,"isa")),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](27,74,"rmg_spoc",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](28,78,"shift",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](29,82,"actual_hours",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](30,86,"identity",e.formConfig,"isa")),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](32,90,"isBillable",e.formConfig,"isa")||d["\u0275\u0275pipeBind3"](33,94,"isIncentive",e.formConfig,"isa")),d["\u0275\u0275advance"](10),d["\u0275\u0275propertyInterpolate"]("matTooltip","Edit"==e.mode?"Edit Member":d["\u0275\u0275pipeBind4"](42,98,"allocate_button",e.formConfig,"isa","Allocate Resource"))}}function Le(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementContainerStart"](1),d["\u0275\u0275elementStart"](2,"div",60),d["\u0275\u0275element"](3,"mat-spinner",61),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementEnd"]())}let Ie=(()=>{class e{constructor(e,t,i,n,a,o,d,l){this.dialogRef=e,this.dialogData=t,this.dialog=i,this.fb=n,this.masterService=a,this.loginService=o,this.utilityService=d,this.projectService=l,this.displayDivisionList=[],this.subDivisionList=[],this.entityList=[],this.currentUser=this.loginService.getProfile().profile,this.isaTimesheetLocation=this.masterService.isaTimesheetLocations,this.practiceList=this.masterService.practiceList,this.projectRoleMasterList=this.masterService.projectRoleMasterList,this.secprojectRoleMasterList=this.masterService.projectRoleMasterList,this.billingMasterList=this.masterService.projectBillableTypeList,this.divisionList=this.masterService.employeeDivisionList,this.shiftList=this.masterService.shiftList,this.identityList=this.masterService.identityList,this.formConfig=[],this.businessUnitAdded=!1,this.secondaryRoleLoading=!1,this.split_percentage_lower_percentage=1,this.addMemberForm=this.fb.group({employee_name:[""],role:[""],location:[""],split_percentage:[""],per_hour_rate:[""],endDate:[""],startDate:[""],role_edit:[""],employee_name_edit:[""],rmg_spoc:[""],isBillable:[""],isIncentive:[""],entity:[""],division:[""],subdivision:[""],projectRole:[""],secondaryProjectRole:[""],stakeholder_reference_id:[""],shift:[""],actual_hours:[""],identity:[""]}),this.per_hour_rate_flag=!1,this.split_percentage_flag=!1,this.rmg_spoc_flag=!1,this.billable_flag=!1,this.incentive_flag=!1,this.orgMappingList=[],this.loading=!0,this.practice_method_followed="choose_from_project_role",this.addMember=()=>{console.log(this.addMemberForm);let e=s.findWhere(this.formConfig,{type:"isa",field_name:"date_control",is_active:!0});if(this.addMemberForm.valid){let e=s.findWhere(this.formConfig,{type:"isa",field_name:"split_percentage",is_active:!0});if((this.addMemberForm.get("split_percentage").value>100||this.addMemberForm.get("split_percentage").value<this.split_percentage_lower_percentage)&&e)this.utilityService.showMessage((e?e.label:"Split Percentage")+" should be between "+this.split_percentage_lower_percentage+"-100!","Dismiss",3e3);else if(this.businessUnitAdded)this.addMemberForm.value.division_type=this.division_type,this.addMemberForm.patchValue({startDate:r()(this.addMemberForm.get("startDate").value).format("YYYY-MM-DD"),endDate:r()(this.addMemberForm.get("endDate").value).format("YYYY-MM-DD")}),this.dialogRef.close({messType:"S",data:this.addMemberForm.value,mode:this.mode,formConfig:this.formConfig});else{let e=s.findWhere(this.formConfig,{type:"isa",field_name:"addBusinessDivisionLabel"});this.utilityService.showMessage(`Kindly select  ${e?e.label:"Business Division"}  to proceed`,"Dismiss",3e3)}}else{let t=r()(this.addMemberForm.get("startDate").value).format("YYYY-MM-DD"),i=r()(this.addMemberForm.get("endDate").value).format("YYYY-MM-DD");if(e&&r()(t).isBefore(this.minDate)){let e=s.findWhere(this.formConfig,{type:"isa",field_name:"startDateCondition"});this.utilityService.showMessage(e?e.label:"The start date is earlier than the item's start date / employee join date","Dismiss",3e3)}else if(e&&r()(i).isAfter(this.maxDate)){let e=s.findWhere(this.formConfig,{type:"isa",field_name:"endDateCondition"});this.utilityService.showMessage(e?e.label:"The end date is later than the item's end date","Dismiss",3e3)}else if(r()(t).isAfter(i)||r()(i).isBefore(t)||r()(t).isBefore(this.empJoinDate)){let e=s.findWhere(this.formConfig,{type:"isa",field_name:"dateCondition"});this.utilityService.showMessage(e?e.label:"Kindly check Start date and End date","Dismiss",3e3)}else this.utilityService.showMessage("Enter all Mandatory Fields!","Dismiss",3e3)}},this.closeClicked=()=>{this.dialogRef.close({messType:"E",data:"Just Close"})}}ngOnChanges(){}updateFormWithAddMember(){if("Edit"==this.mode){let e=this.data?this.data:null,t="",i=s.findWhere(this.formConfig,{type:"isa",field_name:"identity",is_active:!0});i&&(t=i.default&&i.default?i.default_value?i.default_value:2:""),console.log(e),e&&(this.secondaryRoleLoading=!0,this.secprojectRoleMasterList=s.filter(this.projectRoleMasterList,e=>e.id!=this.data.project_role_id),this.empJoinDate=r()(this.data.start_of_join).format("YYYY-MM-DD"),this.minEndDate=this.empJoinDate,this.minDate=this.empJoinDate,this.dateControl(),this.addMemberForm.get("startDate").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){e&&this.dateControl()}))),this.addMemberForm.get("endDate").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){e&&this.dateControl()}))),console.log(this.empJoinDate,this.itemStartDate,this.minDate),this.selectedBusinessEntity={entity_id:this.data.entity_id,entity_name:this.data.entity_name,division_id:this.data.division_id,division_name:this.data.division_name,sub_division_id:this.data.sub_division_id,sub_division_name:this.data.sub_division_name},this.businessUnitAdded=!!this.data.entity_id,this.entity_name=this.data.entity_name,this.division_name=this.data.division_name,this.sub_division_name=this.data.sub_division_name,this.addMemberForm.patchValue({employee_name:this.data.associate_id,role:this.data.practice_id,location:this.data.location,split_percentage:this.data.split_percentage,per_hour_rate:this.data.per_hour_rate,endDate:r()(this.data.end_date).utc().format("YYYY-MM-DD"),startDate:r()(this.data.start_date).utc().format("YYYY-MM-DD"),role_edit:this.data.practice_id,employee_name_edit:this.data.associate_id,rmg_spoc:this.data.rmg_spoc,isBillable:this.data.billable,isIncentive:this.data.incentive,entity:this.data.entity_id,division:this.data.division_id,subdivision:this.data.sub_division_id,projectRole:this.data.project_role_id,secondaryProjectRole:null!=this.data.secondary_role?"string"==typeof this.data.secondary_role?JSON.parse(this.data.secondary_role):this.data.secondary_role:null,stakeholder_reference_id:this.data.stakeholder_reference_id,shift:this.data.shift,actual_hours:this.data.actual_hours,identity:this.data.billable?"":""!=this.data.identity&&null!=this.data.identity?this.data.identity:t}))}else this.addMemberForm.reset()}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){if(this.mode=this.dialogData.mode,this.data=this.dialogData.data,this.mainData=this.dialogData.mainData,this.itemStartDate=r()(this.mainData.itemStartDate).utc().format("YYYY-MM-DD"),this.itemEndDate=r()(this.mainData.itemEndDate).utc().format("YYYY-MM-DD"),this.itemStatus=this.mainData.itemStatus,yield this.masterService.getPracticesList().then(e=>{this.isaTimesheetLocation=this.masterService.isaTimesheetLocations,this.practiceList=this.masterService.practiceList,this.projectRoleMasterList=this.masterService.projectRoleMasterList,this.secprojectRoleMasterList=this.masterService.projectRoleMasterList,this.billingMasterList=this.masterService.projectBillableTypeList,this.divisionList=this.masterService.employeeDivisionList,this.shiftList=this.masterService.shiftList}),yield this.projectService.getProjectFormFieldConfig().then(e=>{if("S"==e.messType){this.formConfig=e.data,this.defaultValueChange();let t=s.findWhere(this.formConfig,{type:"isa",field_name:"practice_method_followed",is_active:!0});this.practice_method_followed=t?t.label:"choose_from_project_role";let i=s.findWhere(this.formConfig,{type:"isa",field_name:"split_percentage_lower_percentage",is_active:!0});this.split_percentage_lower_percentage=i?i.label:1}}),console.log("Project ROle Master List",this.projectRoleMasterList),yield this.projectService.getOrgMapping().then(e=>Object(n.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(this.orgMappingList=e.data,this.entityList=yield this.getListValue("entity_name","entity_id"),this.divisionList=yield this.getListValue("division_name","division_id"),this.subDivisionList=yield this.getListValue("sub_division_name","sub_division_id"))}))),yield this.masterService.getProjectTenantConfig().then(e=>{this.config=e}),this.isaTimesheetLocation=this.masterService.isaTimesheetLocations,this.practiceList=this.masterService.practiceList,this.projectRoleMasterList=this.masterService.projectRoleMasterList,this.secprojectRoleMasterList=this.masterService.projectRoleMasterList,this.billingMasterList=this.masterService.projectBillableTypeList,this.divisionList=this.masterService.employeeDivisionList,this.loading=!1,console.log(this.mode),"Create"==this.mode){let e=s.where(this.formConfig,{field_name:"isBillable",type:"isa",is_active:!0});e.length>0&&this.addMemberForm.patchValue({isBillable:!!e[0].value&&e[0].value}),this.identityPatch()}"Edit"==this.mode?this.updateFormWithAddMember():"Duplicate"==this.mode&&this.duplicateFormWithAddMember(),"Edit"!=this.mode&&(this.addMemberForm.get("employee_name").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(e){let t=r()().format();this.clearBusinessEntity(),yield this.projectService.getOrgMappingForEmployee(e,t).then(e=>{if("S"==e.messType&&(this.selectedBusinessEntity={entity_id:e.data.entity_id,entity_name:e.data.entity_name,division_id:e.data.division_id,division_name:e.data.division_name,sub_division_id:e.data.sub_division_id,sub_division_name:e.data.sub_division_name},this.businessUnitAdded=!!e.data.entity_id,this.entity_name=e.data.entity_name,this.division_name=e.data.division_name,this.sub_division_name=e.data.sub_division_name,this.empJoinDate=r()(e.data.date_of_joining).format("YYYY-MM-DD"),this.minDate=this.empJoinDate,this.minEndDate=this.empJoinDate,this.dateControl(),this.addMemberForm.patchValue({entity:e.data.entity_id,division:e.data.division_id,subdivision:e.data.sub_division_id}),console.log(this.practiceList),"choose_from_practice"==this.practice_method_followed)){let t=s.where(this.practiceList,{entity_id:e.data.entity_id,division_id:e.data.division_id,sub_division_id:e.data.sub_division_id});console.log(t),t.length>0&&this.addMemberForm.patchValue({role:t[0].id})}console.log(this.addMemberForm.value)})}else this.clearEmpJoinDate(),this.clearBusinessEntity()}))),this.addMemberForm.get("startDate").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){e&&this.dateControl()}))),this.addMemberForm.get("endDate").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){e&&this.dateControl()}))),this.dateControl()),this.addMemberForm.get("isBillable").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){console.log("Is Billable",e),this.identityPatch()}))),this.addMemberForm.get("projectRole").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(e){if(this.secondaryRoleLoading=!0,"Edit"!=this.mode&&"choose_from_project_role"==this.practice_method_followed){let t=s.where(this.projectRoleMasterList,{id:e});t.length>0&&this.addMemberForm.patchValue({role:t[0].practice_id})}this.secprojectRoleMasterList=s.filter(this.projectRoleMasterList,t=>t.id!=e),console.log(this.secprojectRoleMasterList)}else this.secondaryRoleLoading=!1}))),this.split_percentage_flag=this.config.show_split_percentage_and_related,this.per_hour_rate_flag=this.config.show_per_hour_rate,this.rmg_spoc_flag=this.config.rmg_spoc_flag,this.incentive_flag=this.config.incentive_flag,this.billable_flag=this.config.billable_flag,this.division_type=this.config.optional_division,this.dateControl(),this.addMemberForm.get("startDate").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){e&&this.dateControl()}))),this.addMemberForm.get("endDate").valueChanges.subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){e&&this.dateControl()})))}))}defaultValueChange(){if("Edit"!=this.mode)for(let e of this.formConfig)console.log(e),e.default_value&&this.addMemberForm.patchValue({[e.field_name]:e.default_value})}addBusinessEntity(e){return Object(n.c)(this,void 0,void 0,(function*(){const{BusinessDivisionEntityComponent:t}=yield i.e(974).then(i.bind(null,"oLjG"));this.dialog.open(t,{height:"75%",width:"75%",data:{mode:e,formConfig:this.formConfig,data:this.orgMappingList,selectedEntity:this.selectedBusinessEntity}}).afterClosed().subscribe(e=>{if(console.log(e),"S"==e.messType){if(this.businessUnitAdded=!0,this.selectedBusinessEntity=e.data,this.addMemberForm.patchValue({entity:e.data.entity_id,division:e.data.division_id,subdivision:e.data.sub_division_id}),"choose_from_practice"==this.practice_method_followed){let t=s.where(this.practiceList,{entity_id:e.data.entity_id,division_id:e.data.division_id,sub_division_id:e.data.sub_division_id});t.length>0&&this.addMemberForm.patchValue({role:t[0].id})}this.entity_name=e.data.entity_name,this.division_name=e.data.division_name,this.sub_division_name=e.data.sub_division_name}})}))}clearBusinessEntity(){this.addMemberForm.get("entity").reset(),this.addMemberForm.get("division").reset(),this.addMemberForm.get("subdivision").reset(),this.entity_name=void 0,this.division_name=void 0,this.sub_division_name=void 0,this.businessUnitAdded=!1}getListValue(e,t){return Object(n.c)(this,void 0,void 0,(function*(){let i=s.uniq(s.pluck(this.orgMappingList,t)),n=[];for(let a of i){let i=s.where(this.orgMappingList,{[t]:a});i.length>0&&n.push({id:i[0][t],name:i[0][e]})}return console.log(e,i,n),n}))}duplicateFormWithAddMember(){let e=this.data?this.data:null,t="",i=s.findWhere(this.formConfig,{type:"isa",field_name:"identity",is_active:!0});i&&(t=i.default&&i.default?i.default_value?i.default_value:2:""),console.log(e),e&&(this.secondaryRoleLoading=!0,this.secprojectRoleMasterList=s.filter(this.projectRoleMasterList,e=>e.id!=this.data.project_role_id),this.addMemberForm.patchValue({location:this.data.location,split_percentage:this.data.split_percentage,per_hour_rate:this.data.per_hour_rate,endDate:r()(this.data.end_date).utc().format("YYYY-MM-DD"),startDate:r()(this.data.start_date).utc().format("YYYY-MM-DD"),rmg_spoc:this.data.rmg_spoc,isBillable:this.data.billable,isIncentive:this.data.incentive,projectRole:this.data.project_role_id,secondaryProjectRole:null!=this.data.secondary_role?"string"==typeof this.data.secondary_role?JSON.parse(this.data.secondary_role):this.data.secondary_role:null,identity:this.data.billable?"":""!=this.data.identity&&null!=this.data.identity?this.data.identity:t}))}clearEmpJoinDate(){this.empJoinDate=null,this.minDate=null,this.minEndDate=null,this.dateControl()}dateControl(){s.findWhere(this.formConfig,{type:"isa",field_name:"date_control",is_active:!0})?(this.empJoinDate?r()(this.empJoinDate).isAfter(this.itemStartDate)?this.minDate=this.empJoinDate:r()(this.empJoinDate).isBefore(this.itemStartDate)&&(this.minDate=this.itemStartDate):this.minDate=this.itemStartDate,this.maxDate=this.itemEndDate,this.minEndDate=this.minDate,this.maxStartDate=this.itemEndDate,this.addMemberForm.get("startDate").value&&(this.startDate=r()(this.addMemberForm.get("startDate").value).format("YYYY-MM-DD"),this.minEndDate=r()(this.startDate).isAfter(this.minDate)?this.startDate:this.minDate),this.addMemberForm.get("endDate").value&&(this.endDate=r()(this.addMemberForm.get("endDate").value).format("YYYY-MM-DD"),this.maxStartDate=r()(this.endDate).isBefore(this.itemEndDate)?this.endDate:this.itemEndDate)):this.addMemberForm.get("startDate").value&&(this.minEndDate=r()(this.addMemberForm.get("startDate").value).format("YYYY-MM-DD"))}identityPatch(){if(this.addMemberForm.get("isBillable").value)this.addMemberForm.patchValue({identity:""});else{let e=s.findWhere(this.formConfig,{type:"isa",field_name:"identity",is_active:!0});e&&this.addMemberForm.patchValue(e.default&&e.default?{identity:e.default_value?e.default_value:2}:{identity:""})}}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](a.a),d["\u0275\u0275directiveInject"](a.b),d["\u0275\u0275directiveInject"](l.i),d["\u0275\u0275directiveInject"](c.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-add-member"]],features:[d["\u0275\u0275NgOnChangesFeature"]],decls:10,vars:4,consts:[[1,"container","addMemberStyles"],[3,"formGroup"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],[1,"close-Icon",2,"cursor","pointer",3,"click"],[4,"ngIf"],[1,"row","pt-2"],[1,"col-11","pl-0","pr-0"],["class","row",4,"ngIf"],["class","row pt-2 pb-2",4,"ngIf"],["class","row pb-3 pt-2",4,"ngIf"],[1,"col-1"],[1,"row","pt-3","pb-4"],[1,"col-2","ml-auto"],["mat-raised-button","","matTooltip","Cancel","type","submit",1,"iconbtnCancel",3,"click"],[2,"color","#9DA8B5"],[1,"col-2"],["mat-raised-button","","type","submit",1,"iconbtnSave",3,"matTooltip","click"],[1,"row"],[1,"col-12"],["class","row field-title",4,"ngIf"],[1,"row","field-title"],["class","required-star",4,"ngIf"],[1,"required-star"],["formControlName","employee_name",3,"isAutocomplete","required","disabled","label"],["formControlName","role","placeholder","Select One",1,"create-account-field",3,"list","required","disabled"],[1,"row","pt-2","pb-2"],["class","add_new_color","style","cursor:pointer;",3,"click",4,"ngIf"],["class","add_new_color","style","cursor:pointer;",4,"ngIf"],["class","pl-2","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"add_new_color",2,"cursor","pointer",3,"click"],[1,"add_new_color",2,"cursor","pointer"],[1,"pl-2",2,"cursor","pointer",3,"click"],[1,"smallCardIcon"],[1,"row","pb-3","pt-2"],["class","col-4",4,"ngIf"],[1,"col-4"],["formControlName","projectRole","placeholder","Select One",1,"create-account-field",3,"list","required","disabled"],[1,"row","field-title","slide-in-top"],["formControlName","secondaryProjectRole","placeholder","Select One",1,"create-account-field",3,"list","required","disabled"],["appearance","outline",1,"create-account-field"],["matInput","","formControlName","startDate","placeholder","Select Date",3,"matDatepicker","max","min","required","disabled"],["matSuffix","",3,"for"],["picker5",""],["matInput","","formControlName","endDate","placeholder","Select Date",3,"matDatepicker","min","max","required","disabled"],["formControlName","location","placeholder","Select one",1,"create-account-field",3,"list","required","disabled"],["matInput","","type","number","formControlName","split_percentage","onkeypress","return event.charCode >= 48 && event.charCode <= 57",3,"required","disabled","placeholder"],["matInput","","type","number","placeholder","Per Hour Rate","formControlName","per_hour_rate","placeholder","Enter Here",3,"required","disabled"],["class","col-4 slide-in-top",4,"ngIf"],["formControlName","rmg_spoc",3,"isAutocomplete","required","disabled","label"],["formControlName","shift","placeholder","Select one",1,"create-account-field",3,"list","required","disabled"],["matInput","","type","number","formControlName","actual_hours","placeholder","Actual Hours",3,"required","disabled"],[1,"col-4","slide-in-top"],["formControlName","identity","placeholder","Select one",1,"create-account-field",3,"list","noSelect","required","disabled"],["class","col-6",4,"ngIf"],[1,"col-6"],[1,"ceo-attention","mx-auto"],["formControlName","isBillable",1,"checkbox-text"],["formControlName","isIncentive",1,"checkbox-text"],[1,"col-12","d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"form",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275elementStart"](3,"div",3),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",4),d["\u0275\u0275elementStart"](6,"mat-icon",5),d["\u0275\u0275listener"]("click",(function(){return t.closeClicked()})),d["\u0275\u0275text"](7,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](8,Be,45,103,"div",6),d["\u0275\u0275template"](9,Le,4,0,"div",6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",t.addMemberForm),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"]("",t.mode," Member"),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",!t.loading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.loading))},directives:[l.J,l.w,l.n,f.a,u.NgIf,v.a,g.a,_.a,l.v,l.l,l.F,b.a,y.a,C.c,S.b,l.e,x.g,x.i,C.i,x.f,l.A,M.a,D.c],pipes:[E.a,k,B.a,L.a,I.a],styles:[".addMemberStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-weight:500}.addMemberStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.addMemberStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.addMemberStyles[_ngcontent-%COMP%]   .iconbtnSave[_ngcontent-%COMP%]{background-color:#79ba44!important;outline:solid;outline-color:#79ba44!important;outline-width:1px}.addMemberStyles[_ngcontent-%COMP%]   .iconbtnCancel[_ngcontent-%COMP%], .addMemberStyles[_ngcontent-%COMP%]   .iconbtnSave[_ngcontent-%COMP%]{border-radius:3px;color:#fff;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12);width:80px}.addMemberStyles[_ngcontent-%COMP%]   .iconbtnCancel[_ngcontent-%COMP%]{outline:solid;outline-color:#bec9d9!important;outline-width:1px}.addMemberStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.addMemberStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.addMemberStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.addMemberStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.addMemberStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.addMemberStyles[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.addMemberStyles[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#ec5f6e}.addMemberStyles[_ngcontent-%COMP%]   .add_new_color[_ngcontent-%COMP%]{color:#79ba44;font-size:14px;font-weight:500}.addMemberStyles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:21px!important;color:#868683!important}.addMemberStyles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()},YpVr:function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i("xG9w"),a=i("fXoL");let s=(()=>{class e{transform(e,t,i,a){let s=n.findWhere(t,{field_name:e,type:i});return s&&s.label?s.label:a}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"checkLabel",type:e,pure:!0}),e})()},y5L8:function(e,t,i){"use strict";i.d(t,"a",(function(){return g}));var n=i("fXoL"),a=i("3Pt+"),s=i("jtHE"),o=i("XNiG"),r=i("NJ67"),d=i("1G5W"),l=i("kmnG"),c=i("d3UM"),p=i("FKr1"),m=i("WJ5W"),h=i("ofXK");const f=["singleSelect"];function u(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275text"](1,"Select one"),n["\u0275\u0275elementEnd"]()),2&e&&n["\u0275\u0275property"]("value",null)}function v(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let g=(()=>{class e extends r.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this.change=new n.EventEmitter,this._onDestroy=new o.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.selectOneNone=!this.noSelect,this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](f,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",noSelect:"noSelect",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:10,consts:[["appearance","outline"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-select",1,2),n["\u0275\u0275elementStart"](3,"mat-option"),n["\u0275\u0275element"](4,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](5,u,2,1,"mat-option",4),n["\u0275\u0275template"](6,v,2,2,"mat-option",5),n["\u0275\u0275pipe"](7,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.selectOneNone),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](7,8,t.filteredList)))},directives:[l.c,c.c,a.v,a.k,a.F,p.p,m.a,h.NgIf,h.NgForOf],pipes:[h.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})()}}]);