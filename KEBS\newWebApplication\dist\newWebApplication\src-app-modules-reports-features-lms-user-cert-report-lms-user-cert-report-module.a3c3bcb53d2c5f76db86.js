(window.webpackJsonp=window.webpackJsonp||[]).push([[977,861,981],{HmYF:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("mrSG"),s=n("Iab2"),o=n("EUZL"),a=n("wd/R"),h=n("xG9w"),l=n("fXoL");let r=(()=>{class e{constructor(){this.formatColumn=(e,t,n)=>{const i=o.utils.decode_range(e["!ref"]);for(let s=i.s.r+1;s<=i.e.r;++s){const i=o.utils.encode_cell({r:s,c:t});e[i]&&e[i].v&&(e[i].t="d",e[i].z=n)}}}exportAsExcelFile(e,t,n,i,s){console.log("Excel to JSON Service",e);const a=o.utils.json_to_sheet(e);if(s&&s.length){const e=o.utils.sheet_to_json(a,{header:1}).shift();for(const t of s){const n=e.indexOf(t.fieldKey);this.formatColumn(a,n,t.fieldFormat)}}null==n&&(n=[]),null==i&&(i="DD-MM-YYYY"),this.formatExcelDateData(a,n,i);const h=o.write({Sheets:{data:a},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,t)}formatExcelDateData(e,t,n){for(let o of Object.keys(e))if(null!=e[o]&&null!=e[o].t&&null!=e[o].v&&a(e[o].v,n,!0).isValid()){let i=o.replace(/[0-9]/g,"")+"1";0==h.where(t,{value:e[i].v}).length&&null!=e[i]&&null!=e[i].t&&t.push({value:e[i].v,format:n})}let i=[],s=1;for(let o of t)for(let t of Object.keys(e)){let n=parseInt(t.replace(/[^0-9]/g,""));n>s&&(s=n),null!=e[t]&&null!=e[t].v&&e[t].v==o.value&&i.push({value:t.replace(/[0-9]/g,""),format:o.format})}for(let o of i)for(let t=2;t<=s;t++)null!=e[o.value+""+t]&&null!=e[o.value+""+t].t&&(e[o.value+""+t].t="d",null!=e[o.value+""+t].v&&"Invalid date"!=e[o.value+""+t].v?e[o.value+""+t].v=a(e[o.value+""+t].v,o.format).format("YYYY/MM/DD"):(console.log(e[o.value+""+t].t),e[o.value+""+t].v="",e[o.value+""+t].t="s"))}saveAsExcelFile(e,t){const n=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});s.saveAs(n,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,n){const i=o.utils.json_to_sheet(e),s=o.utils.json_to_sheet(t),a=o.write({Sheets:{All_Approvals:i,Pending_Approvals:s},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,n)}exportAsExcelFileForPayroll(e,t,n,i,s,a){const h=o.utils.json_to_sheet(e),l=o.utils.json_to_sheet(t),r=o.utils.json_to_sheet(n),d=o.utils.json_to_sheet(i),g=o.utils.json_to_sheet(s),c=o.write({Sheets:{Regular_Report:h,Intern_Report:l,Contract_Report:r,Perdiem_Report:d,RP_Report:g},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(c,a)}exportAsCsvFileWithSheetName(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let i=o.write(n,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(i,t)}))}saveAsCsvFile(e,t){return Object(i.c)(this,void 0,void 0,(function*(){const n=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});s.saveAs(n,t.concat(".csv"))}))}s2ab(e){return Object(i.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),i=0;i<e.length;i++)n[i]=255&e.charCodeAt(i);return t}))}exportAsExcelFileWithCellMerge(e,t,n){const i=o.utils.json_to_sheet(e);i["!merges"]=n;const s=o.write({Sheets:{data:i},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let i=o.write(n,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(i,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return r})),n.d(t,"b",(function(){return d}));var i=n("jhN1"),s=n("fXoL"),o=n("oHs6"),a=n("PVOt"),h=n("6t9p");const l=["*"];let r=(()=>{let e=class extends a.b{constructor(e,t,n,i,s,o,a,h){super(e,t,n,i,a,h),this._watcherHelper=i,this._idh=s,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new o.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](s.ElementRef),s["\u0275\u0275directiveInject"](s.NgZone),s["\u0275\u0275directiveInject"](a.e),s["\u0275\u0275directiveInject"](a.j),s["\u0275\u0275directiveInject"](a.g),s["\u0275\u0275directiveInject"](a.i),s["\u0275\u0275directiveInject"](i.h),s["\u0275\u0275directiveInject"](s.PLATFORM_ID))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&s["\u0275\u0275contentQuery"](n,h.L,!1),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[s["\u0275\u0275ProvidersFeature"]([a.e,a.j,a.i,a.g]),s["\u0275\u0275InheritDefinitionFeature"],s["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(s["\u0275\u0275projectionDef"](),s["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[h.bb,h.Gc,h.Vd,h.vd,h.hb,h.lb,h.sb,h.id,h.jd,h.M,a.c,a.f,i.b],h.bb,h.Gc,h.Vd,h.vd,h.hb,h.lb,h.sb,h.id,h.jd,h.M,a.f]}),e})()}}]);