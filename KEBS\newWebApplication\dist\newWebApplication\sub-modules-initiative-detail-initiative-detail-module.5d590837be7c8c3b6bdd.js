(window.webpackJsonp=window.webpackJsonp||[]).push([[1009],{nCU0:function(e,t,i){"use strict";i.r(t),i.d(t,"InitiativeDetailModule",(function(){return Mt}));var n=i("ofXK"),a=i("tyNb"),r=i("25DO"),l=i("mrSG"),o=i("fXoL"),s=i("LcQX"),d=i("u0xZ");let c=(()=>{class e{constructor(e,t,i){this.UtilityService=e,this.route=t,this._okr=i,this.fileArray=[],this.urls={uploadUrl:"/api/cta/uploadFile",downloadUrl:"api/cta/downloadFile"}}ngOnInit(){this.route.parent.params.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){console.log(e),this.InitId=e.id}))),this._okr.getInitAttachment({id:this.InitId}).subscribe(e=>{null!=e.data.attachments&&(console.log(e.data.attachments),this.fileArray=e.data.attachments);for(let t=0;t<this.fileArray.length;t++){let e=this.fileArray[t].fileName.split("(");this.fileArray[t].name=e[0],this.fileArray[t].deleteInProgress=!1,this.fileArray[t].size=(this.fileArray[t].size/1024).toFixed(2)+"KB"}},e=>{console.error(e)})}updateInitAttachment(e){this._okr.addInitAttachment({id:this.InitId,file:e}).subscribe(e=>{if(console.log(e),null!=e.data.attachments){this.fileArray=[];let t=e.data.attachments;for(let e=0;e<t.length;e++){t[e].deleteInProgress=!1;let i=t[e].fileName.split("(");t[e].name=i[0],t[e].size=(t[e].size/1024).toFixed(2)+"KB",this.fileArray.push(t[e])}this.UtilityService.showMessage("Uploaded Successfully","Dismiss",3e3)}},e=>{console.error(e)})}deleteInitAttachment(e){this._okr.deleteInitAttachment({id:this.InitId,file:e}).subscribe(e=>{if(console.log(e),null!=e.data.attachments){this.fileArray=[];let t=e.data.attachments;for(let e=0;e<t.length;e++){t[e].deleteInProgress=!1;let i=t[e].fileName.split("(");t[e].name=i[0],t[e].size=(t[e].size/1024).toFixed(2)+"KB",this.fileArray.push(t[e])}}else this.fileArray=[];this.UtilityService.showMessage("Deleted Successfully","Dismiss",3e3)},e=>{console.error(e)})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](s.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-initiative-detail-attachments"]],decls:1,vars:2,consts:[[3,"fileArray","urls","updateFileInApp","deleteFileInApp"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"app-s3-attachment",0),o["\u0275\u0275listener"]("updateFileInApp",(function(e){return t.updateInitAttachment(e)}))("deleteFileInApp",(function(e){return t.deleteInitAttachment(e)})),o["\u0275\u0275elementEnd"]()),2&e&&o["\u0275\u0275property"]("fileArray",t.fileArray)("urls",t.urls)},directives:[d.a],styles:[""]}),e})();var u=i("a1z7");let p=(()=>{class e{constructor(e){this.route=e}ngOnInit(){this.route.parent.params.subscribe(e=>{this.initId=e.id})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-initiative-detail-history"]],decls:2,vars:2,consts:[[1,"ml-3"],[3,"objectiveId","isInitiative"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275element"](1,"app-okr-history",1),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("objectiveId",t.initId)("isInitiative",!0))},directives:[u.a],styles:[""]}),e})();var m=i("wZkO");function v(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"a",3,4),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=o["\u0275\u0275reference"](1);o["\u0275\u0275property"]("routerLink",e.path)("active",i.isActive),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.label," ")}}let h=(()=>{class e{constructor(){this.initiativeDetailTabLinks=[{label:"Overview",path:"overview"},{label:"Milestones",path:"tasks"},{label:"Attachments",path:"attachments"},{label:"History",path:"history"}]}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-initiative-detail-landing-page"]],decls:4,vars:1,consts:[["mat-tab-nav-bar",""],["mat-tab-link","","routerLinkActive","",3,"routerLink","active",4,"ngFor","ngForOf"],[1,"initiative-tab-content"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active"],["rla","routerLinkActive"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"nav",0),o["\u0275\u0275template"](1,v,3,3,"a",1),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275element"](3,"router-outlet"),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",t.initiativeDetailTabLinks))},directives:[m.f,n.NgForOf,a.l,a.j,m.e,a.i],styles:["a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}nav[_ngcontent-%COMP%]{position:fixed;width:100%;z-index:14;background:#f8f9fa}.initiative-tab-content[_ngcontent-%COMP%]{padding-top:48px!important}"]}),e})();var f=i("33Jv"),g=i("wd/R"),_=i("3Pt+"),D=i("0IaG"),b=i("to83"),S=i("BVzC"),w=i("WGBV"),y=i("FKDz"),x=i("Qu3c"),E=i("bTqV"),O=i("NFeN"),C=i("CLW7"),k=i("5RNC"),I=i("M7nE"),j=i("me71"),A=i("mS9j"),U=i("jmr5"),M=i("Qcpi"),V=i("STbY"),T=i("sfX4"),F=i("m3gX");function P(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",44),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"titlecase"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"]("",o["\u0275\u0275pipeBind1"](2,1,null==e.initiativeDetails?null:e.initiativeDetails.name)," ")}}function L(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"kebs-text-inline-edit-d1",45),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).getInitUpdatedValue(t,"name")})),o["\u0275\u0275pipe"](1,"titlecase"),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("inputText",o["\u0275\u0275pipeBind1"](1,2,null==e.initiativeDetails?null:e.initiativeDetails.name))("cssData",e.cssClass)}}function B(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](null==e.initiativeDetails?null:e.initiativeDetails.desc)}}const Y=function(){return{color:"black"}};function N(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"kebs-text-inline-edit-d1",46),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).getInitUpdatedValue(t,"desc")})),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("inputText",null==e.initiativeDetails?null:e.initiativeDetails.desc)("cssData",o["\u0275\u0275pureFunction0"](2,Y))}}function R(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",47),o["\u0275\u0275elementStart"](1,"mat-slider",48),o["\u0275\u0275listener"]("change",(function(t){o["\u0275\u0275restoreView"](e);const i=o["\u0275\u0275nextContext"](2);return i.onSliderChange(t,i.initiativeDetails)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](2,"strong",49),o["\u0275\u0275text"](3),o["\u0275\u0275pipe"](4,"number"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",null==e.initiativeDetails?null:e.initiativeDetails.progress_val)("formControl",e.progressControl),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"]("",o["\u0275\u0275pipeBind2"](4,3,null==e.initiativeDetails?null:e.initiativeDetails.progress_val,"1.0-2")," %")}}function H(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span",52),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.initiativeDetails?null:e.initiativeDetails.date_type," ")}}function z(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span",52),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"date"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" C - ",o["\u0275\u0275pipeBind2"](2,1,null==e.initiativeDetails?null:e.initiativeDetails.start_date,"y")," ")}}function W(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,H,2,1,"span",50),o["\u0275\u0275template"](2,z,3,4,"span",50),o["\u0275\u0275elementStart"](3,"span",51),o["\u0275\u0275text"](4),o["\u0275\u0275pipe"](5,"date"),o["\u0275\u0275pipe"](6,"date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","custom"!=(null==e.initiativeDetails?null:e.initiativeDetails.date_type)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","custom"==(null==e.initiativeDetails?null:e.initiativeDetails.date_type)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"]("",o["\u0275\u0275pipeBind2"](5,4,null==e.initiativeDetails?null:e.initiativeDetails.start_date,"MMM d")," - ",o["\u0275\u0275pipeBind2"](6,7,null==e.initiativeDetails?null:e.initiativeDetails.end_date,"MMM d"),"")}}function K(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"kebs-date-inline-edit-d1",53),o["\u0275\u0275listener"]("uptPeriod",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).getUptPeriod(t,"init","")})),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("selectedPeriod",null==e.initiativeDetails?null:e.initiativeDetails.date)("periods",e.periods)("viewPortMargin",40)}}function Q(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"span",54),o["\u0275\u0275element"](2,"app-user-image",55),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"span",56),o["\u0275\u0275element"](4,"app-user-profile",57),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",null==e.initiativeDetails?null:e.initiativeDetails.owner_id),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("type","name")("oid",null==e.initiativeDetails?null:e.initiativeDetails.owner_id)}}function $(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"app-owner-update",58),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).getInitOwnerUpdatedValue(t)})),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("id",null==e.initiativeDetails?null:e.initiativeDetails.owner_id)}}function q(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"span",54),o["\u0275\u0275element"](2,"app-user-image",55),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"strong",60),o["\u0275\u0275element"](4,"app-user-profile",57),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.coOwnerList[0].id),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("type","name")("oid",e.coOwnerList[0].id)}}function X(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"span",61),o["\u0275\u0275text"](2,"-"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,q,5,3,"span",59),o["\u0275\u0275template"](2,X,3,0,"span",59),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.coOwnerList.length>0&&null!=e.coOwnerList&&""!=e.coOwnerList),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==e.coOwnerList.length||null==e.coOwnerList||""==e.coOwnerList)}}function J(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"span",54),o["\u0275\u0275element"](2,"app-user-image",55),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"app-coowner-update",62),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](3).getObjCoOwnerUpdatedValue(t,"init")})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.coOwnerList[0].id),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("id",null==e.initiativeDetails?null:e.initiativeDetails.co_owner)("coOwnerList",e.coOwnerList)("name",e.coOwnerList[0].displayName)}}function Z(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"app-coowner-update",62),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](3).getObjCoOwnerUpdatedValue(t,"init")})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("name",e.noCoOwner)}}function ee(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,J,4,4,"span",59),o["\u0275\u0275template"](2,Z,2,1,"span",59),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.coOwnerList.length>0&&null!=e.coOwnerList&&""!=e.coOwnerList),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==e.coOwnerList.length||null==e.coOwnerList||""==e.coOwnerList)}}function te(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",83),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]().index;return o["\u0275\u0275nextContext"](3).removeEmp(t,"init")})),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2,"cancel"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}const ie=function(e){return{objId:"143017",entityOwner:e,type:"update"}};function ne(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",73),o["\u0275\u0275elementStart"](1,"div",74),o["\u0275\u0275element"](2,"app-user-image",75),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"div",76),o["\u0275\u0275elementStart"](4,"div",77),o["\u0275\u0275elementStart"](5,"div",78),o["\u0275\u0275text"](6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",77),o["\u0275\u0275elementStart"](8,"div",79),o["\u0275\u0275element"](9,"app-user-profile",80),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"div",81),o["\u0275\u0275template"](11,te,3,0,"button",82),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.id),o["\u0275\u0275advance"](3),o["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.displayName," "),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("oid",e.id)("type","designation"),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](6,ie,null==i.initiativeDetails?null:i.initiativeDetails.owner_id))}}function ae(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",63),o["\u0275\u0275elementStart"](1,"button",64),o["\u0275\u0275elementStart"](2,"mat-icon",65),o["\u0275\u0275text"](3,"supervisor_account"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"strong",66),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"mat-menu",67,68),o["\u0275\u0275elementStart"](8,"div",69),o["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),o["\u0275\u0275elementStart"](9,"div",70),o["\u0275\u0275elementStart"](10,"div",71),o["\u0275\u0275text"](11," Selected Co-Owners "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](12,ne,12,8,"div",72),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275reference"](7),t=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matMenuTriggerFor",e),o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"]("+",t.coOwnerList.length,""),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("ngForOf",t.coOwnerList)}}function re(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"date"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind2"](2,1,null==e.initiativeDetails?null:e.initiativeDetails.last_updated,"MMM d,YYYY,hh:mm a"),"")}}function le(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1,"No updates"),o["\u0275\u0275elementEnd"]())}function oe(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"span",84),o["\u0275\u0275text"](2,"Weightage (%)"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](3),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.initiativeDetails.weightage," ")}}const se=function(){return{color:"black",font_size:"16px",font_weight:500}};function de(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",21),o["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",87),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](3).getInitWeightageUpdatedValue(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275propertyInterpolate1"]("matTooltip","Weightage - ",e.initiativeDetails.weightage,"%"),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("inputText",e.initiativeDetails.weightage)("viewPort",70)("cssData",o["\u0275\u0275pureFunction0"](4,se))}}function ce(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",88),o["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",87),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](3).getInitWeightageUpdatedValue(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}2&e&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("inputText",0)("viewPort",70)("cssData",o["\u0275\u0275pureFunction0"](3,se)))}function ue(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"span",84),o["\u0275\u0275text"](2,"Weightage (%)"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](3,de,2,5,"div",85),o["\u0275\u0275template"](4,ce,2,4,"div",86),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",""!=e.initiativeDetails.weightage),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",""==e.initiativeDetails.weightage||!e.initiativeDetails.weightage)}}function pe(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",92),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275propertyInterpolate1"]("matTooltip","Planned Score - ",null==e.initiativeDetails?null:e.initiativeDetails.plnd_score,""),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.initiativeDetails?null:e.initiativeDetails.plnd_score," ")}}function me(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",93),o["\u0275\u0275text"](1," - "),o["\u0275\u0275elementEnd"]())}function ve(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",89),o["\u0275\u0275elementStart"](1,"span",84),o["\u0275\u0275text"](2,"Planned Score"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](3,pe,2,2,"div",90),o["\u0275\u0275template"](4,me,2,0,"div",91),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",""!=(null==e.initiativeDetails?null:e.initiativeDetails.plnd_score)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",""==(null==e.initiativeDetails?null:e.initiativeDetails.plnd_score)||!(null!=e.initiativeDetails&&e.initiativeDetails.plnd_score))}}function he(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",92),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275propertyInterpolate1"]("matTooltip","Actual Score - ",null==e.initiativeDetails?null:e.initiativeDetails.act_score,""),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.initiativeDetails?null:e.initiativeDetails.act_score," ")}}function fe(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",96),o["\u0275\u0275text"](1," - "),o["\u0275\u0275elementEnd"]())}function ge(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",94),o["\u0275\u0275elementStart"](1,"span",84),o["\u0275\u0275text"](2,"Actual Score"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](3,he,2,2,"div",90),o["\u0275\u0275template"](4,fe,2,0,"div",95),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",""!=(null==e.initiativeDetails?null:e.initiativeDetails.act_score)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",""==(null==e.initiativeDetails?null:e.initiativeDetails.act_score)||!(null!=e.initiativeDetails&&e.initiativeDetails.act_score))}}function _e(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",92),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275propertyInterpolate1"]("matTooltip","Total Score - ",null==e.initiativeDetails?null:e.initiativeDetails.tot_score,""),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.initiativeDetails?null:e.initiativeDetails.tot_score," ")}}function De(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",99),o["\u0275\u0275text"](1," - "),o["\u0275\u0275elementEnd"]())}function be(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",97),o["\u0275\u0275elementStart"](1,"span",84),o["\u0275\u0275text"](2,"Total Score"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](3,_e,2,2,"div",90),o["\u0275\u0275template"](4,De,2,0,"div",98),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",""!=(null==e.initiativeDetails?null:e.initiativeDetails.tot_score)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",""==(null==e.initiativeDetails?null:e.initiativeDetails.tot_score)||!(null!=e.initiativeDetails&&e.initiativeDetails.tot_score))}}const Se=function(e){return{objId:"143021",entityOwner:e,type:"view"}},we=function(e){return{objId:"143021",entityOwner:e,type:"update"}},ye=function(e){return{objId:"143019",entityOwner:e,type:"view"}},xe=function(e){return{objId:"143019",entityOwner:e,type:"update"}},Ee=function(e){return{objId:"143025",entityOwner:e,type:"update"}},Oe=function(e){return{objId:"143028",entityOwner:e,type:"view"}},Ce=function(e){return{objId:"143028",entityOwner:e,type:"update"}},ke=function(e){return{objId:"143026",entityOwner:e,type:"view"}},Ie=function(e){return{objId:"143026",entityOwner:e,type:"update"}},je=function(e){return{objId:"143017",entityOwner:e,type:"view"}},Ae=function(e){return{objId:"143018",entityOwner:e,type:"view"}},Ue=function(e){return{objId:"143018",entityOwner:e,type:"update"}},Me=function(e){return{objId:"143035",entityOwner:e,type:"view"}},Ve=function(e){return{objId:"143036",entityOwner:e,type:"view"}},Te=function(e){return{objId:"143037",entityOwner:e,type:"view"}};function Fe(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",13),o["\u0275\u0275elementStart"](1,"div",14),o["\u0275\u0275template"](2,P,3,3,"div",15),o["\u0275\u0275elementStart"](3,"div",16),o["\u0275\u0275template"](4,L,2,4,"kebs-text-inline-edit-d1",17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div"),o["\u0275\u0275elementStart"](6,"button",18),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.openCommentBox("init",t.initiativeDetails,"")})),o["\u0275\u0275elementStart"](7,"mat-icon"),o["\u0275\u0275text"](8,"question_answer"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",19),o["\u0275\u0275elementStart"](10,"div",20),o["\u0275\u0275elementStart"](11,"div",21),o["\u0275\u0275template"](12,B,2,1,"span",22),o["\u0275\u0275template"](13,N,1,3,"kebs-text-inline-edit-d1",23),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](14,R,5,6,"div",24),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",25),o["\u0275\u0275elementStart"](16,"div",26),o["\u0275\u0275elementStart"](17,"span",27),o["\u0275\u0275text"](18,"Target"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](19,W,7,10,"ng-container",22),o["\u0275\u0275template"](20,K,1,3,"kebs-date-inline-edit-d1",28),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](21,"div",29),o["\u0275\u0275elementStart"](22,"span",30),o["\u0275\u0275text"](23,"Owner"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](24,Q,5,3,"ng-container",22),o["\u0275\u0275template"](25,$,1,1,"app-owner-update",31),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](26,"div",32),o["\u0275\u0275elementStart"](27,"div",33),o["\u0275\u0275elementStart"](28,"span",30),o["\u0275\u0275text"](29,"Co-Owner"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](30,G,3,2,"ng-container",22),o["\u0275\u0275template"](31,ee,3,2,"ng-container",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](32,ae,13,3,"div",34),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](33,"div",35),o["\u0275\u0275elementStart"](34,"span",36),o["\u0275\u0275text"](35,"Last updated- "),o["\u0275\u0275template"](36,re,3,4,"span",37),o["\u0275\u0275template"](37,le,2,0,"ng-template",null,38,o["\u0275\u0275templateRefExtractor"]),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](39,"div",39),o["\u0275\u0275elementStart"](40,"div",40),o["\u0275\u0275template"](41,oe,4,1,"ng-container",22),o["\u0275\u0275template"](42,ue,5,2,"ng-container",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](43,ve,5,2,"div",41),o["\u0275\u0275template"](44,ge,5,2,"div",42),o["\u0275\u0275template"](45,be,5,2,"div",43),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](38),t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](21,Se,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275propertyInterpolate"]("matTooltip",null==t.initiativeDetails?null:t.initiativeDetails.name),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](23,we,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](7),o["\u0275\u0275propertyInterpolate"]("matTooltip",null==t.initiativeDetails?null:t.initiativeDetails.desc),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](25,ye,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](27,xe,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](29,Ee,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](31,Oe,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](33,Ce,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](35,ke,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](37,Ie,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](39,je,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](41,ie,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.coOwnerList.length>0&&null!=t.coOwnerList&&""!=t.coOwnerList),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",null==t.initiativeDetails?null:t.initiativeDetails.last_updated)("ngIfElse",e),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](43,Ae,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](45,Ue,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](47,Me,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](49,Ve,null==t.initiativeDetails?null:t.initiativeDetails.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](51,Te,null==t.initiativeDetails?null:t.initiativeDetails.owner_id))}}function Pe(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span",109),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"titlecase"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind1"](2,1,null==e.initiativeDetails||null==e.initiativeDetails.objective_goal_details?null:e.initiativeDetails.objective_goal_details.goal_name))}}function Le(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"kebs-text-inline-edit-d1",110),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).getObjUpdate(t,"name")})),o["\u0275\u0275pipe"](1,"titlecase"),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("inputText",o["\u0275\u0275pipeBind1"](1,2,null==e.initiativeDetails||null==e.initiativeDetails.objective_goal_details?null:e.initiativeDetails.objective_goal_details.goal_name))("cssData",o["\u0275\u0275pureFunction0"](4,Y))}}function Be(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"strong",52),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.date_type," ")}}function Ye(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"strong",52),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"date"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" C - ",o["\u0275\u0275pipeBind2"](2,1,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.start_date,"y")," ")}}function Ne(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"span",54),o["\u0275\u0275element"](2,"app-user-image",55),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"strong",60),o["\u0275\u0275element"](4,"app-user-profile",57),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("type","name")("oid",null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)}}function Re(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"app-owner-update",58),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).getObjUpdate(t,"owner")})),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("id",null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)}}function He(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"span",54),o["\u0275\u0275element"](2,"app-user-image",55),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"strong",60),o["\u0275\u0275element"](4,"app-user-profile",57),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.coOwnerObjList[0].id),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("type","name")("oid",e.coOwnerObjList[0].id)}}function ze(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"span",61),o["\u0275\u0275text"](2,"-"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function We(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,He,5,3,"span",59),o["\u0275\u0275template"](2,ze,3,0,"span",59),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.coOwnerObjList.length>0&&null!=e.coOwnerObjList&&""!=e.coOwnerObjList),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==e.coOwnerObjList.length||null==e.coOwnerObjList||""==e.coOwnerObjList)}}function Ke(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"span",54),o["\u0275\u0275element"](2,"app-user-image",55),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"app-coowner-update",62),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](3).getObjCoOwnerUpdatedValue(t,"obj")})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.coOwnerObjList[0].id),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("id",null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.co_owner)("coOwnerList",e.coOwnerObjList)("name",e.coOwnerObjList[0].displayName)}}function Qe(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"app-coowner-update",62),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](3).getObjCoOwnerUpdatedValue(t,"obj")})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("name",e.noCoOwner)}}function $e(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,Ke,4,4,"span",59),o["\u0275\u0275template"](2,Qe,2,1,"span",59),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.coOwnerObjList.length>0&&null!=e.coOwnerObjList&&""!=e.coOwnerObjList),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==e.coOwnerObjList.length||null==e.coOwnerObjList||""==e.coOwnerObjList)}}function qe(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",83),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]().index;return o["\u0275\u0275nextContext"](3).removeEmp(t,"obj")})),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2,"cancel"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}const Xe=function(e){return{objId:"143002",entityOwner:e,type:"update"}};function Ge(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",73),o["\u0275\u0275elementStart"](1,"div",74),o["\u0275\u0275element"](2,"app-user-image",75),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"div",76),o["\u0275\u0275elementStart"](4,"div",77),o["\u0275\u0275elementStart"](5,"div",78),o["\u0275\u0275text"](6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",77),o["\u0275\u0275elementStart"](8,"div",79),o["\u0275\u0275element"](9,"app-user-profile",80),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"div",81),o["\u0275\u0275template"](11,qe,3,0,"button",82),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.id),o["\u0275\u0275advance"](3),o["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.displayName," "),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("oid",e.id)("type","designation"),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](6,Xe,null==i.initiativeDetails||null==i.initiativeDetails.objective_details?null:i.initiativeDetails.objective_details.owner_id))}}function Je(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",63),o["\u0275\u0275elementStart"](1,"button",64),o["\u0275\u0275elementStart"](2,"mat-icon",65),o["\u0275\u0275text"](3,"supervisor_account"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"strong",66),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"mat-menu",67,68),o["\u0275\u0275elementStart"](8,"div",69),o["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),o["\u0275\u0275elementStart"](9,"div",70),o["\u0275\u0275elementStart"](10,"div",71),o["\u0275\u0275text"](11," Selected Co-Owners "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](12,Ge,12,8,"div",72),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275reference"](7),t=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matMenuTriggerFor",e),o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"]("+",t.coOwnerObjList.length,""),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("ngForOf",t.coOwnerObjList)}}function Ze(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"span",111),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"titlecase"),o["\u0275\u0275pipe"](4,"titlecase"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"]("",o["\u0275\u0275pipeBind1"](3,2,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.visibility_name)," - ",o["\u0275\u0275pipeBind1"](4,4,null==e.initiativeDetails||null==e.initiativeDetails.objective_details||null==e.initiativeDetails.objective_details.visibility_details?null:e.initiativeDetails.objective_details.visibility_details.Org_name),"")}}function et(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",21),o["\u0275\u0275elementStart"](1,"strong",111),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"titlecase"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275propertyInterpolate"]("matTooltip",null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.visibility_name),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind1"](3,2,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.visibility_name))}}function tt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",21),o["\u0275\u0275pipe"](1,"titlecase"),o["\u0275\u0275pipe"](2,"titlecase"),o["\u0275\u0275elementStart"](3,"app-visibility-update",112),o["\u0275\u0275listener"]("uptValChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](3).getObjUpdate(t,"visibility")})),o["\u0275\u0275pipe"](4,"titlecase"),o["\u0275\u0275pipe"](5,"titlecase"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275propertyInterpolate2"]("matTooltip","",o["\u0275\u0275pipeBind1"](1,4,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.visibility_name)," - ",o["\u0275\u0275pipeBind1"](2,6,null==e.initiativeDetails||null==e.initiativeDetails.objective_details||null==e.initiativeDetails.objective_details.visibility_details?null:e.initiativeDetails.objective_details.visibility_details.Org_name),""),o["\u0275\u0275advance"](3),o["\u0275\u0275propertyInterpolate2"]("inputName","",o["\u0275\u0275pipeBind1"](4,8,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.visibility_name)," - ",o["\u0275\u0275pipeBind1"](5,10,null==e.initiativeDetails||null==e.initiativeDetails.objective_details||null==e.initiativeDetails.objective_details.visibility_details?null:e.initiativeDetails.objective_details.visibility_details.Org_name),"")}}function it(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,et,4,4,"div",85),o["\u0275\u0275template"](2,tt,6,12,"div",85),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","across org"!=(null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.visibility_name)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","across org"==(null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.visibility_name))}}const nt=function(e){return{objId:"143005",entityOwner:e,type:"view"}},at=function(e){return{objId:"143005",entityOwner:e,type:"update"}},rt=function(e){return{objId:"143001",entityOwner:e,type:"view"}},lt=function(e){return{objId:"143001",entityOwner:e,type:"update"}},ot=function(e){return{objId:"143002",entityOwner:e,type:"view"}},st=function(e){return{objId:"143003",entityOwner:e,type:"view"}},dt=function(e){return{objId:"143003",entityOwner:e,type:"update"}};function ct(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",100),o["\u0275\u0275elementStart"](1,"div",77),o["\u0275\u0275elementStart"](2,"div",101),o["\u0275\u0275elementStart"](3,"mat-icon"),o["\u0275\u0275text"](4,"golf_course"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",21),o["\u0275\u0275elementStart"](6,"strong",102),o["\u0275\u0275text"](7,"Under objective"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](8,Pe,3,3,"span",103),o["\u0275\u0275template"](9,Le,2,5,"kebs-text-inline-edit-d1",104),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"button",105),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().navigateToObjectivePage()})),o["\u0275\u0275elementStart"](11,"mat-icon"),o["\u0275\u0275text"](12,"launch"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"div",106),o["\u0275\u0275elementStart"](14,"div",26),o["\u0275\u0275elementStart"](15,"span",27),o["\u0275\u0275text"](16,"Target"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](17,Be,2,1,"strong",107),o["\u0275\u0275template"](18,Ye,3,4,"strong",107),o["\u0275\u0275elementStart"](19,"span",51),o["\u0275\u0275text"](20),o["\u0275\u0275pipe"](21,"date"),o["\u0275\u0275pipe"](22,"date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",29),o["\u0275\u0275elementStart"](24,"span",30),o["\u0275\u0275text"](25,"Owner"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](26,Ne,5,3,"ng-container",22),o["\u0275\u0275template"](27,Re,1,1,"app-owner-update",31),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](28,"div",32),o["\u0275\u0275elementStart"](29,"div",33),o["\u0275\u0275elementStart"](30,"span",30),o["\u0275\u0275text"](31,"Co-Owner"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](32,We,3,2,"ng-container",22),o["\u0275\u0275template"](33,$e,3,2,"ng-container",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](34,Je,13,3,"div",34),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](35,"div",108),o["\u0275\u0275elementStart"](36,"span",30),o["\u0275\u0275text"](37,"Visibility"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](38,Ze,5,6,"ng-container",22),o["\u0275\u0275template"](39,it,3,2,"ng-container",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](5),o["\u0275\u0275propertyInterpolate"]("matTooltip",null==e.initiativeDetails||null==e.initiativeDetails.objective_goal_details?null:e.initiativeDetails.objective_goal_details.goal_name),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](20,nt,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](22,at,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)),o["\u0275\u0275advance"](8),o["\u0275\u0275property"]("ngIf","custom"!=(null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.date_type)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","custom"==(null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.date_type)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"]("",o["\u0275\u0275pipeBind2"](21,14,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.start_date,"dd-MMM-yyyy")," - ",o["\u0275\u0275pipeBind2"](22,17,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.end_date,"dd-MMM-yyyy"),""),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](24,rt,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](26,lt,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](28,ot,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](30,Xe,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.coOwnerObjList.length>0&&null!=e.coOwnerObjList&&""!=e.coOwnerObjList),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](32,st,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("okrAuth",o["\u0275\u0275pureFunction1"](34,dt,null==e.initiativeDetails||null==e.initiativeDetails.objective_details?null:e.initiativeDetails.objective_details.owner_id))}}function ut(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",70),o["\u0275\u0275elementStart"](1,"div",118),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"strong"),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"span",119),o["\u0275\u0275text"](6,"Init Name"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"span",120),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",121),o["\u0275\u0275elementStart"](10,"span",122),o["\u0275\u0275text"](11,"ORG Name "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"span",123),o["\u0275\u0275text"](13),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=t.index;o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"]("",i+1,"."),o["\u0275\u0275advance"](3),o["\u0275\u0275propertyInterpolate"]("matTooltip",null==e?null:e.goal_name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e?null:e.goal_name," "),o["\u0275\u0275advance"](4),o["\u0275\u0275propertyInterpolate"]("matTooltip",null==e?null:e.Org_name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e?null:e.Org_name,"")}}function pt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",113),o["\u0275\u0275elementStart"](1,"div",114),o["\u0275\u0275elementStart"](2,"div",115),o["\u0275\u0275elementStart"](3,"mat-icon"),o["\u0275\u0275text"](4,"group"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"span",116),o["\u0275\u0275text"](6,"This Initiative is "),o["\u0275\u0275elementStart"](7,"strong",52),o["\u0275\u0275text"](8,"Collabrated"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](9," with these Initiatives : "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](10,ut,14,5,"div",117),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](10),o["\u0275\u0275property"]("ngForOf",null==e.initiativeDetails?null:e.initiativeDetails.collaborativeInitDetails)}}function mt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",13),o["\u0275\u0275elementStart"](1,"div",114),o["\u0275\u0275elementStart"](2,"div",124),o["\u0275\u0275elementStart"](3,"mat-icon"),o["\u0275\u0275text"](4,"mediation"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"span",116),o["\u0275\u0275text"](6,"This Initiative is "),o["\u0275\u0275elementStart"](7,"strong",52),o["\u0275\u0275text"](8,"Aggregated"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](9),o["\u0275\u0275elementStart"](10,"strong",125),o["\u0275\u0275text"](11),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](12,". "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"div",126),o["\u0275\u0275elementStart"](14,"span",127),o["\u0275\u0275text"](15,"ORG Name "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"span",123),o["\u0275\u0275text"](17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](9),o["\u0275\u0275textInterpolate1"](" with the ",null==e.initiativeDetails?null:e.initiativeDetails.child_aggreg_type,""),o["\u0275\u0275advance"](1),o["\u0275\u0275propertyInterpolate"]("matTooltip",null==e.initiativeDetails||null==e.initiativeDetails.childAggregationDetails?null:e.initiativeDetails.childAggregationDetails.goal_name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.initiativeDetails||null==e.initiativeDetails.childAggregationDetails?null:e.initiativeDetails.childAggregationDetails.goal_name,""),o["\u0275\u0275advance"](5),o["\u0275\u0275propertyInterpolate"]("matTooltip",null==e.initiativeDetails||null==e.initiativeDetails.childAggregationDetails?null:e.initiativeDetails.childAggregationDetails.Org_name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.initiativeDetails||null==e.initiativeDetails.childAggregationDetails?null:e.initiativeDetails.childAggregationDetails.Org_name,"")}}function vt(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-kr-overview-notes",128),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("keyResId",null==e.initiativeDetails?null:e.initiativeDetails._id)}}function ht(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",129),o["\u0275\u0275elementStart"](1,"div",130),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"div"),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.count," "),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.name)}}let ft=(()=>{class e{constructor(e,t,n,a,r,o,s,d){this._okr=e,this._util=t,this.route=n,this._router=a,this._dialog=r,this._initStatus=o,this._ErrorService=s,this.accountService=d,this.tasksOverview=[{name:"Total",count:"00"},{name:"Not Started",count:"00"},{name:"On Track",count:"00"},{name:"Completed",count:"00"},{name:"Delayed",count:"00"},{name:"At Risk",count:"00"},{name:"On Hold",count:"00"},{name:"Recurring tasks",count:"00"}],this.subs=new f.a,this.initiativeDetails=null,this.periods=[],this.selectedPeriod={startDate:"2021-04-01",endDate:"2022-03-31",type:"FY 2022"},this.selectedPeriodCustom={id:"CCCCCC",type:"custom",startDate:"2021-04-01",endDate:"2022-03-31"},this.cssClass={font_weight:500,font_size:"18px",color:"black"},this.coOwnerList=[],this.coOwnerObjList=[],this.noCoOwner="-",this.roleMatrix=null,this.progressControl=new _.j,this.getInitiativeOverview=()=>{this.subs.sink=this._okr.getInitiativeDetail({init_id:this.initiative_id}).subscribe(e=>{var t;this.initiativeDetails=e.data[0],this.getCoOwner(),this.getObjCoOwner(),console.log(this.initiativeDetails),this.progressControl.setValue(null===(t=this.initiativeDetails)||void 0===t?void 0:t.progress_val)},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.getOpenNewTabConfig=()=>{this.subs.sink=this._okr.getOpenNewTabConfig().subscribe(e=>{this.isOpenNewTab=e.data[0].configuration_data},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.navigateToKeyResultPage=()=>{const e=this._router.serializeUrl(this._router.createUrlTree(["main/okr/detail/"+this.initiativeDetails.KR_details._id+"/KeyResult/overview"]));window.open(e,"_blank")},this.onSliderChange=(e,t)=>{this._initStatus.transform(t.status).then(i=>{var n;t.status=i[0],console.log(t),this.openValMetricPopUp(e.value,t,null===(n=null==t?void 0:t.objective_details)||void 0===n?void 0:n._id,!0,!0)})},this.openValMetricPopUp=(e,t,n,a,r)=>Object(l.c)(this,void 0,void 0,(function*(){const{MetricUptValComponent:l}=yield i.e(184).then(i.bind(null,"VEKw"));this._dialog.open(l,{width:"46%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{updatedValue:e,krDetail:t,objectiveId:n,isInitiative:a,showProgressBar:r}}).afterClosed().subscribe(e=>{e?this.route.parent.params.subscribe(e=>{this.initiative_id=e.id,this.getInitiativeOverview()}):this.progressControl.setValue(this.initiativeDetails.progress_val)})})),this.openCommentBox=(e,t,n)=>Object(l.c)(this,void 0,void 0,(function*(){let n;if("obj"==e)n={inputData:{application_id:143,application_name:"OKR",title:t.goal_details.goal_name,unique_id_1:t._id,unique_id_2:""},context:{Objective:t.goal_details.goal_name,Year:t.date_type},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};else if("kr"==e)n={inputData:{application_id:143,application_name:"OKR",title:t.KR_goal_details.goal_name,unique_id_1:t._id,unique_id_2:""},context:{"Key Result":t.KR_goal_details.goal_name,Year:t.date_type,Metric:t.metric_name,progress:t.progress_val},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};else if("init"==e){let e=g(t.start_date).format("YYYY-MM-DD"),i=g(t.end_date).format("YYYY-MM-DD");n={inputData:{application_id:143,application_name:"OKR",title:t.name,unique_id_1:t._id,unique_id_2:""},context:{Initiative:t.name,Objective:t.objective_goal_details?t.objective_goal_details.goal_name:"","Start Date":e,"End Date":i},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"}}const{ChatCommentContextModalComponent:a}=yield Promise.all([i.e(4),i.e(63),i.e(75),i.e(983)]).then(i.bind(null,"vg2w"));this._dialog.open(a,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:n}})}))}ngOnInit(){this._okr.roleMatrix.subscribe(e=>{e&&(this.roleMatrix=e)}),this.route.parent.params.subscribe(e=>{this.initiative_id=e.id,this.getInitiativeOverview(),this.getTaskCount(),this.getPeriods()}),this.getOpenNewTabConfig()}getCoOwner(){return Object(l.c)(this,void 0,void 0,(function*(){let e=this.initiativeDetails.co_owner;console.log(e),this.coOwnerList=[];for(let t=0;t<e.length;t++){let i=yield this.accountService.getUserProfileFromDB(e[t]);this.coOwnerList.push(i[0])}console.log(this.coOwnerList)}))}getObjCoOwner(){return Object(l.c)(this,void 0,void 0,(function*(){let e=this.initiativeDetails.objective_details.co_owner;console.log(e),this.coOwnerObjList=[];for(let t=0;t<e.length;t++){let i=yield this.accountService.getUserProfileFromDB(e[t]);this.coOwnerObjList.push(i[0])}console.log(this.coOwnerObjList)}))}navigateToObjectivePage(){if(1==this.isOpenNewTab)this._router.navigate(["/main/okr/detail",this.initiativeDetails.objective_details._id,"Objective","overview"]);else{const e=this._router.serializeUrl(this._router.createUrlTree(["main/okr/detail/"+this.initiativeDetails.objective_details._id+"/Objective/overview"]));window.open(e,"_blank")}}openInitiativeMetricPopup(e){this.openValMetricPopUp(0,e,0,!0,!0)}getTaskCount(){this._okr.getTaskCount({mapped_under_id:this.initiative_id}).subscribe(e=>{this.tasksOverview[0].count=e.data.totalCount,this.tasksOverview[1].count=e.data.statusCount.notStarted,this.tasksOverview[2].count=e.data.statusCount.onTrack,this.tasksOverview[3].count=e.data.statusCount.completed,this.tasksOverview[4].count=e.data.statusCount.delayed,this.tasksOverview[5].count=e.data.statusCount.atRisk,this.tasksOverview[6].count=e.data.statusCount.onHold,this.tasksOverview[7].count=e.data.recurringCount},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}ngOnDestroy(){this.subs.unsubscribe()}getInitUpdatedValue(e,t){"desc"==t?this._okr.updateInitDesc({initiative_id:this.initiative_id,okr_name:this.initiativeDetails.name,desc:e}).subscribe(e=>{console.log("Description successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):"name"==t&&this._okr.updateInitName({initiative_id:this.initiative_id,okr_name:this.initiativeDetails.name,title:e}).subscribe(e=>{console.log("Name successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getInitOwnerUpdatedValue(e){console.log(e),this._okr.updateInitOwner({initiative_id:this.initiative_id,okr_name:this.initiativeDetails.name,owner_id:e}).subscribe(e=>{console.log("owner updated successfully"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getInitWeightageUpdatedValue(e){this._okr.updateInitWeightage({initiative_id:this.initiative_id,weightage:e}).subscribe(e=>{console.log("Weightage Updated Successfully"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getObjUpdate(e,t){"owner"==t?this._okr.updateObjOwner({objective_id:this.initiativeDetails.objective_details._id,owner_id:e,okr_name:this.initiativeDetails.objectives_goal_details.goal_name}).subscribe(e=>{console.log("owner updated successfully"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):"name"==t?this._okr.updateObjName({objective_id:this.initiativeDetails.objective_details._id,okr_name:this.initiativeDetails.objectives_goal_details.goal_name,title:e}).subscribe(e=>{console.log("Name successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.code,"Some Error Happened in completing the Activity",e.errMessage)}):"visibility"==t&&this._okr.updateObjVisibility({objective_id:this.initiativeDetails.objective_details._id,visibility_id:this.initiativeDetails.objective_details.visibility_id,Org_id:e.code,Org_name:e.name}).subscribe(e=>{console.log("Visibility Updated Successfully"),this.ngOnInit()},e=>{console.log("error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getPeriods(){this._okr.getDurationTypesMaster().subscribe(e=>{console.log(e),this.periods=e.data})}getUptPeriod(e,t,i){"custom"==e.type?(this.startDate=g(e.startDate).format("YYYY-MM-DD"),this.endDate=g(e.endDate).format("YYYY-MM-DD")):(this.startDate=e.startDate,this.endDate=e.endDate),console.log(e),"obj"==t||"init"==t&&this._okr.updateInitdate({init_id:this.initiative_id,type:e.type,startDate:this.startDate,endDate:this.endDate}).subscribe(e=>{console.log("date successfully updated"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getObjCoOwnerUpdatedValue(e,t){var i;return Object(l.c)(this,void 0,void 0,(function*(){if("obj"==t){let t=[...new Set([...this.initiativeDetails.objective_details.co_owner,...e])];console.log(t),this.coOwnerObjList=[],this._okr.updateCoOwnerObj({obj_id:this.initiativeDetails.objective_details._id,co_owner:t}).subscribe(e=>{this._util.showMessage("Objective CoOwner Updated Successfully","Dismiss"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("init"==t){console.log(e);let t=[...new Set([...null===(i=this.initiativeDetails)||void 0===i?void 0:i.co_owner,...e])];console.log(t),this.coOwnerList=[],this._okr.updateCoOwnerInit({co_owner:t,init_id:this.initiative_id}).subscribe(e=>{this._util.showMessage("Initiative CoOwner Updated Successfully","Dismiss"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}}))}removeEmp(e,t){if("obj"==t){console.log(e);let t=this.coOwnerObjList[e].id;this.coOwnerObjList.splice(e,1),console.log(t),this._okr.removeCoOwnerObj({obj_id:this.initiativeDetails.objective_details._id,co_owner:t}).subscribe(e=>{this._util.showMessage("Objective CoOwner removed Successfully","Dismiss")},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("init"==t){console.log(e);let t=this.coOwnerList[e].id;this.coOwnerList.splice(e,1),console.log(t),this._okr.removeCoOwnerInit({co_owner:t,init_id:this.initiative_id}).subscribe(e=>{this._util.showMessage("Initiative CoOwner removed Successfully","Dismiss")},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](r.a),o["\u0275\u0275directiveInject"](s.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](a.g),o["\u0275\u0275directiveInject"](D.b),o["\u0275\u0275directiveInject"](b.a),o["\u0275\u0275directiveInject"](S.a),o["\u0275\u0275directiveInject"](w.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-initiative-detail-overview"]],decls:15,vars:6,consts:[[1,"mt-3","mx-4","position-relative"],[1,"bg-overlay"],["class","row py-3 px-4 initiative-card",4,"ngIf"],["class","pt-3 pb-1 my-2 px-4 initiative-card",4,"ngIf"],["class","row my-2 py-3 px-4 initiative-card",4,"ngIf"],[1,"row","my-3"],[1,"col-7","pl-0"],[3,"keyResId",4,"ngIf"],[1,"col-5"],[1,"mb-2","sub-title"],[1,"row","p-3","w-100","justify-content-center",2,"background","white"],[1,"row","w-100","py-2",2,"box-shadow","-1px 2px 3px 1px #ccc","border-radius","3px"],["class","task-card",4,"ngFor","ngForOf"],[1,"row","py-3","px-4","initiative-card"],[1,"d-flex","w-100","justify-content-between","align-items-center"],["style","font-size: 20px; font-weight: 600;",4,"okrAuth"],[1,"overflow-ctrl",3,"matTooltip"],["label","Initiative Name","type","text",3,"inputText","cssData","uptValChange",4,"okrAuth"],["mat-icon-button","","matTooltip","comments",1,"ml-3","small-icon-btn",3,"click"],[1,"row","w-100","mt-3"],[1,"col-9","pl-0"],[3,"matTooltip"],[4,"okrAuth"],["label","Initiative Description","type","textArea",3,"inputText","cssData","uptValChange",4,"okrAuth"],["class","col-3 my-slider d-flex align-items-end",4,"okrAuth"],[1,"row","w-100","mt-2"],[1,"col-3","pl-0","overflow-ctrl"],[1,"d-inline-block","sub-title",2,"width","50px"],["label","Target","marginInbetween","3rem",3,"selectedPeriod","periods","viewPortMargin","uptPeriod",4,"okrAuth"],[1,"col-2","pl-0","pr-0","overflow-ctrl"],[1,"sub-title"],[3,"id","uptValChange",4,"okrAuth"],[1,"col-4","pl-4","pr-0","d-flex"],[1,"col-8","p-0","overflow-ctrl"],["class","col-3 p-0",4,"ngIf"],[1,"col-3"],[2,"font-weight","100"],[4,"ngIf","ngIfElse"],["temp",""],[1,"row","w-100","pt-2"],[1,"col-3","d-flex","pl-0"],["class","col-2 pl-0 d-flex",4,"okrAuth"],["class","col-2 pl-4 d-flex",4,"okrAuth"],["class","col-2 d-flex",4,"okrAuth"],[2,"font-size","20px","font-weight","600"],["label","Initiative Name","type","text",3,"inputText","cssData","uptValChange"],["label","Initiative Description","type","textArea",3,"inputText","cssData","uptValChange"],[1,"col-3","my-slider","d-flex","align-items-end"],[3,"value","formControl","change"],[1,"ml-2",2,"margin-bottom","8px","font-weight","500"],["style","font-weight: 500;",4,"ngIf"],[1,"ml-3"],[2,"font-weight","500"],["label","Target","marginInbetween","3rem",3,"selectedPeriod","periods","viewPortMargin","uptPeriod"],[1,"pl-2"],["imgWidth","25px","imgHeight","25px",3,"id"],[1,"ml-2"],[3,"type","oid"],[3,"id","uptValChange"],[4,"ngIf"],[1,"pl-2",2,"font-weight","500"],[1,"pl-5"],[3,"id","coOwnerList","name","uptValChange"],[1,"col-3","p-0"],["mat-icon-button","",2,"vertical-align","top",3,"matMenuTriggerFor"],[2,"vertical-align","top"],[2,"vertical-align","text-top"],["yPosition","below"],["menu","matMenu"],[1,"row",2,"height","20rem",3,"click"],[1,"col-12","p-0"],[1,"d-flex","justify-content-center","mt-3","mb-3","menu-title"],["class","row mt-2 mb-2 pb-2 border-bottom solid",4,"ngFor","ngForOf"],[1,"row","mt-2","mb-2","pb-2","border-bottom","solid"],[1,"col-3","pr-0"],[3,"id"],[1,"col-7","pl-3"],[1,"row"],[1,"col-12","pl-0","overflow-ctrl",3,"matTooltip"],[1,"col-12","pl-0","overflow-ctrl"],[3,"oid","type"],[1,"col-2","p-0"],["mat-icon-button","","style","color: gray","matTooltip","remove",3,"click",4,"okrAuth"],["mat-icon-button","","matTooltip","remove",2,"color","gray",3,"click"],[1,"sub-title","pr-3"],[3,"matTooltip",4,"ngIf"],["matTooltip","Weightage - 0%",4,"ngIf"],["label"," Weightage","type","weightage",3,"inputText","viewPort","cssData","uptValChange"],["matTooltip","Weightage - 0%"],[1,"col-2","pl-0","d-flex"],["style","font-weight: 500",3,"matTooltip",4,"ngIf"],["matTooltip","Planned Score - 0",4,"ngIf"],[2,"font-weight","500",3,"matTooltip"],["matTooltip","Planned Score - 0"],[1,"col-2","pl-4","d-flex"],["matTooltip","Actual Score - 0",4,"ngIf"],["matTooltip","Actual Score - 0"],[1,"col-2","d-flex"],["matTooltip","Total Score - 0",4,"ngIf"],["matTooltip","Total Score - 0"],[1,"pt-3","pb-1","my-2","px-4","initiative-card"],[1,"abs-icon",2,"background-color","orange","bottom","50%","transform","translateY(50%)"],[1,"mr-5","pr-2",2,"color","gray","font-weight","500"],["style","font-weight: 500;","class","d-inline-block text-capitalize",4,"okrAuth"],["label","Objective Name","type","text",3,"inputText","cssData","uptValChange",4,"okrAuth"],["mat-icon-button","","matTooltip","Go to Objective",1,"ml-5","small-icon-btn",3,"click"],[1,"row","mt-3","w-100"],["style","font-weight: 500",4,"ngIf"],[1,"col-3","d-flex","overflow-ctrl"],[1,"d-inline-block","text-capitalize",2,"font-weight","500"],["label","Objective Name","type","text",3,"inputText","cssData","uptValChange"],[1,"ml-3",2,"font-weight","500"],[3,"inputName","uptValChange"],[1,"row","my-2","py-3","px-4","initiative-card"],[1,"row","w-100","p-0"],[1,"abs-icon","abs-colab"],[1,"overflow-ctrl"],["class","col-12 p-0",4,"ngFor","ngForOf"],[1,"row","w-100","p-0","pt-1","d-flex"],[1,"pl-1",2,"color","gray","font-weight","500"],[1,"overflow-ctrl","pl-2",2,"font-weight","500",3,"matTooltip"],[1,"row","w-100","pt-1","d-flex"],[1,"pl-3",2,"color","gray","font-weight","500"],[1,"pl-2",2,"font-weight","500",3,"matTooltip"],[1,"abs-icon","abs-aggre"],[1,"overflow-ctrl",2,"font-weight","500",3,"matTooltip"],[1,"row","w-100","mt-3","d-flex"],[2,"color","gray","font-weight","500"],[3,"keyResId"],[1,"task-card"],[1,"pb-4",2,"font-size","26px","font-weight","bold"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275element"](1,"div",1),o["\u0275\u0275template"](2,Fe,46,53,"div",2),o["\u0275\u0275template"](3,ct,40,36,"div",3),o["\u0275\u0275template"](4,pt,11,1,"div",4),o["\u0275\u0275template"](5,mt,18,5,"div",2),o["\u0275\u0275elementStart"](6,"div",5),o["\u0275\u0275elementStart"](7,"div",6),o["\u0275\u0275template"](8,vt,1,1,"app-kr-overview-notes",7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",8),o["\u0275\u0275elementStart"](10,"div",9),o["\u0275\u0275text"](11,"Milestones"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"div",10),o["\u0275\u0275elementStart"](13,"div",11),o["\u0275\u0275template"](14,ht,5,2,"div",12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",t.initiativeDetails),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","objective"==(null==t.initiativeDetails?null:t.initiativeDetails.initiative_type)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",null==t.initiativeDetails?null:t.initiativeDetails.is_collaborative),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",null==t.initiativeDetails?null:t.initiativeDetails.is_child_aggregation),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",null==t.initiativeDetails?null:t.initiativeDetails._id),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngForOf",t.tasksOverview))},directives:[n.NgIf,n.NgForOf,y.a,x.a,E.a,O.a,C.a,k.a,_.v,_.k,I.a,j.a,A.a,U.a,M.a,V.f,V.g,T.a,F.a],pipes:[n.TitleCasePipe,n.DecimalPipe,n.DatePipe],styles:[".small-icon-btn[_ngcontent-%COMP%]{height:20px;width:20px;line-height:10px}.small-icon-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:grey;font-size:16px;line-height:20px}.sub-title[_ngcontent-%COMP%]{color:#777;font-weight:500}.initiative-card[_ngcontent-%COMP%]{background:#fff;position:relative;border-radius:3px;box-shadow:0 1px 2px 0 #ccc}.bg-overlay[_ngcontent-%COMP%]{position:absolute;width:calc(100% + 3.5rem);height:37%;background:#fcf6ea;left:-2rem;top:-2rem}.abs-icon[_ngcontent-%COMP%]{position:absolute;left:-10px;bottom:5%;height:25px;width:25px;border-radius:50%;text-align:center}.abs-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f5f5f5;line-height:25px;font-size:18px}.abs-colab[_ngcontent-%COMP%]{height:23px;width:23px;background:#0077b8;bottom:50%;transform:translateY(50%)}.abs-colab[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{line-height:22px!important;color:#f5f5f5;font-size:18px!important}.abs-aggre[_ngcontent-%COMP%]{height:23px;width:23px;background:#9370db;bottom:50%;transform:translateY(50%)}.abs-aggre[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{line-height:23px!important;color:#f5f5f5;font-size:16px!important}.overflow-ctrl[_ngcontent-%COMP%]{max-width:85%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-card[_ngcontent-%COMP%]{height:120px;width:25%;max-width:150px;display:flex;flex-direction:column;align-items:center;justify-content:center;background:#fff}.task-card[_ngcontent-%COMP%], .task-card[_ngcontent-%COMP%]:last-child{border-right:1px solid #ccc}.my-slider[_ngcontent-%COMP%]   mat-slider[_ngcontent-%COMP%]{width:70%;max-width:240px}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-track-wrapper{border-radius:10px!important;height:12px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-track-background{height:12px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-wrapper{height:22px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-thumb{height:30px;width:30px;transform:scale(.9)!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-track-fill{height:12px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider.mat-slider-horizontal .mat-slider-track-fill{background-color:orange}"]}),e})();var gt=i("FlSJ"),_t=i("GnQ3"),Dt=i("JLuW"),bt=i("XXEo"),St=i("zOg5"),wt=i("+K9r"),yt=i("xi/V"),xt=i("Wk3H");const Et=[{path:"",component:h,children:[{path:"",redirectTo:"overview",pathMatch:"full"},{path:"overview",component:ft,resolve:{roleMatrix:r.a},data:{breadcrumb:"Overview"}},{path:"history",component:p,data:{breadcrumb:"History"}},{path:"attachments",component:c,data:{breadcrumb:"Attachments"}},{path:"tasks",component:(()=>{class e{constructor(e,t,n,a,r,o,s,d,c,u,p,m,v){this.$dialog=e,this._udrfService=t,this._okr=n,this.utilityService=a,this.route=r,this._initStatus=o,this.sharedLazyLoadedComponentsService=s,this._ErrorService=d,this._loginService=c,this._downloadService=u,this.viewContainerRef=p,this.accountService=m,this._okrService=v,this.current_year_start=g().startOf("year"),this.current_year_end=g().endOf("year"),this.coOwnerList=[],this.finalAttachments=[],this.attachments=[],this.statusMasterData=[],this.udrfBodyColumns=[{item:"name",header:"Milestone",sales_unit_name:"",isVisible:"true",isActive:!0,type:"textokr",colSize:"2",textClass:"value13Bold",position:1,isInlineEdit:!0,inlineEditVarient:["name","simple-text"]},{item:"status_name",header:"Status",customer_name:"",isVisible:"true",isActive:!0,type:"status",colSize:"1",textClass:"value13light",position:2,isInlineEdit:!0,inlineEditVarient:["okr-status","minimal-dropdown"]},{item:"start_date",header:"Start date",customer_name:"",isVisible:"true",isActive:!0,type:"date",colSize:"1",textClass:"value13light",position:3,isInlineEdit:!0,inlineEditVarient:["Start Date","date-picker"]},{item:"end_date",header:"End date",customer_name:"",isVisible:"true",isActive:!0,type:"date",colSize:"1",textClass:"value13light",position:4,isInlineEdit:!0,inlineEditVarient:["End Date","date-picker"]},{item:"owner_id",header:"Owner",customer_name:"",isVisible:"true",isActive:!0,type:"profile",colSize:"1",textClass:"value13light",position:5,isInlineEdit:!0,inlineEditVarient:["Owner","search-dropdown"]},{item:"co_owner",header:"Co-Owner",customer_name:"",isVisible:"false",isActive:!0,type:"coOwner",colSize:"1",textClass:"value13light",position:8,hasColumnClick:!1,onColumnClick:""},{item:"type_name",header:"Type",customer_name:"",isVisible:"false",isActive:!0,type:"text1",colSize:"1",textClass:"value13light",position:6,hasColumnClick:!1,onColumnClick:""},{item:"weightage",header:"Weightage(%)",customer_name:"",isVisible:"false",isActive:!0,type:"text1",colSize:"1",textClass:"value13light",position:7,isInlineEdit:!0,inlineEditVarient:["weightage","simple-text"]},{item:"",header:"Actions",customer_name:"",isVisible:"true",isActive:!0,type:"action",colSize:"2",textClass:"value13light",position:9,hasColumnClick:!1,onColumnClick:""}],this.subs=new f.a,this.udrfItemStatusColor=[{status:"Not Started",color:"#ACA9A6"},{status:"In progress",color:"#7dd259"},{status:"Missed",color:"#fe0000"},{status:"Postponed",color:"#febd00"},{status:"Completed",color:"#0077b8"},{status:"On track",color:"#7dd259"},{status:"Delayed",color:"#febd00"},{status:"At risk",color:"#fe0000"},{status:"On Hold",color:"#9370DB"}],this.getInitiativeOverview=e=>new Promise((t,i)=>{this.subs.sink=this._okr.getInitiativeDetail({init_id:e}).subscribe(e=>{this.initiativeDetails=e.data[0],console.log(this.initiativeDetails),t(this.initiativeDetails)},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),i(e)})}),this.createQuickTask=()=>Object(l.c)(this,void 0,void 0,(function*(){let e=yield this._okr.getAppId({appName:"OKR-Initiative"}).toPromise(),t=yield this._okr.getAppId({appName:"OKR"}).toPromise();const{QuickTaskComponent:n}=yield i.e(992).then(i.bind(null,"QCtt"));this.$dialog.open(n,{width:"65%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{appId:t.data.id,subAppId:e.data.id,mapped_under_id:this.mapped_under_id,collection_name:"initiatives"}}).afterClosed().subscribe(e=>{""!=e&&this.ngOnInit()})})),this.openQuickCTA=()=>{this.sharedLazyLoadedComponentsService.openQuickCTAModal(null,this.$dialog)},this.openValMetricPopUp=(e,t,n,a,r)=>Object(l.c)(this,void 0,void 0,(function*(){const{MetricUptValComponent:l}=yield i.e(184).then(i.bind(null,"VEKw"));this.$dialog.open(l,{width:"46%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{updatedValue:e,krDetail:t,objectiveId:r,isInitiative:n,showProgressBar:a}}).afterClosed().subscribe(e=>{})})),this.convertToLocalTime=e=>{let t=new Date(e),i=6e4*t.getTimezoneOffset();return t.setTime(t.getTime()-i),t},this.openAlignTask=()=>Object(l.c)(this,void 0,void 0,(function*(){let e=yield this._okr.getAppId({appName:"OKR-Initiative"}).toPromise();const{AlignTaskComponent:t}=yield i.e(827).then(i.bind(null,"AWeY"));this.$dialog.open(t,{width:"250px",autoFocus:!1,data:{subAppId:e.data.id,mapped_under_id:this.mapped_under_id,collection_name:"initiatives"}}).afterClosed().subscribe(e=>{""!=e&&this.ngOnInit()})})),this.openCollabrativePopup=(e,t,n)=>Object(l.c)(this,void 0,void 0,(function*(){const{CollabrativeOkrComponent:a}=yield i.e(182).then(i.bind(null,"Ozlk"));this.$dialog.open(a,{width:"250px",autoFocus:!1,data:{id:e,name:t,list:n}}).afterClosed().subscribe(e=>{"sucess"==e&&this.ngOnInit()})}))}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){this._udrfService.udrfBodyData=[];let e=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(g().startOf("week"),g(g().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(g().endOf("week"),g(g().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(g().startOf("month"),g(g().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(g().endOf("month"),g(g().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(g().add(1,"month").startOf("month"),g(g().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(g().add(1,"month").endOf("month"),g(g().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(g().startOf("month"),g(g().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(g().add(2,"month").endOf("month"),g(g().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1}];this.route.parent.params.subscribe(e=>{this.mapped_under_id=e.id}),this._udrfService.udrfFunctions.constructCustomRangeData(11,"date",e),console.log("Duration Ranges"),console.log(e);let t=yield this.getInitiativeOverview(this.mapped_under_id);console.log("init details"),console.log(t);let i=this._okr.hasObjAccess("143041","Create",[t.owner_id]);this._udrfService.udrfUiData.showSearchBar=!1,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showSettingsModalButton=!1,this._udrfService.udrfUiData.showCreateNewComponentButton=!!i,this._udrfService.udrfUiData.showAlignTaskButton=!0,this._udrfService.udrfUiData.itemHasDownloadButton=!0,this._udrfService.udrfUiData.createNewComponent=this.createQuickTask.bind(this),this._udrfService.udrfUiData.AlignTaskData=this.alignTask.bind(this),this._udrfService.udrfUiData.openQuickCta=this.openQuickCTA.bind(this),this._udrfService.udrfUiData.openComments=this.openComments.bind(this),this._udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this._udrfService.udrfUiData.openDeleteButton=this.openDeleteButton.bind(this),this._udrfService.udrfUiData.openPopUp=this.openPopUp.bind(this),this._udrfService.udrfUiData.openFileDownload=this.openFileDownload.bind(this),this._udrfService.udrfUiData.okrCollabaration=this.openCollabrative.bind(this),this._udrfService.udrfUiData.variant=0,this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.itemHasComments=!0,this._udrfService.udrfUiData.itemHasDeleteButton=!0,this._udrfService.udrfUiData.itemHasAttachFileButton=!0,this._udrfService.udrfUiData.itemHasOpenBtn=!1,this._udrfService.udrfUiData.itemHasCollabarationButton=!1,this._udrfService.udrfUiData.onProgressItem=this.onProgressItem.bind(this),this._udrfService.udrfUiData.onCompleteItem=this.onCompleteItem.bind(this),this._udrfService.udrfUiData.attachFile=this.attachFile.bind(this),this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,yield this._udrfService.getAppUdrfConfig(141,this.setUdrfData.bind(this)),this._okr.getTaskStatus().subscribe(e=>{this.statusMasterData=e,this._udrfService.udrfUiData.inlineEditDropDownMasterDatas={statusMasterData:this.statusMasterData},console.log("In report",this.statusMasterData)},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}),console.log("onit status",this.statusMasterData);let n=[],a=yield this._okrService.getQuarterDate();for(let r of a)n.push({checkboxId:r.id,checkboxName:r.type,checkboxStartValue:g(r.startDate).format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"),checkboxEndValue:g(r.endDate).format("YYYY-MM-DDTHH:mm:ss.SSS[Z]"),isCheckboxDefaultSelected:r.is_default});this.filterYears=a,yield this._udrfService.udrfFunctions.constructCustomRangeData(10,"date",n),console.log(n,"Financial Year Range"),this.profileId=this._loginService.getProfile().profile.oid}))}setUdrfData(){return Object(l.c)(this,void 0,void 0,(function*(){this._udrfService.udrfBodyData=[],this._udrfService.udrfUiData.resolveColumnConfig(),yield this.getuserTasks()}))}getuserTasks(){return Object(l.c)(this,void 0,void 0,(function*(){let e=yield this._okr.getAppId({appName:"OKR-Initiative"}).toPromise(),t=yield this._okr.getAppId({appName:"OKR"}).toPromise(),i=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),n=this.getFilters(i);console.group(n),this.subs.sink=this._okr.getuserTasks({app_id:t.data.id,sub_app_id:e.data.id,mapped_under_id:this.mapped_under_id,start_date:this._udrfService.udrfData.mainApiDateRangeStart,end_date:this._udrfService.udrfData.mainApiDateRangeEnd,filters:n}).subscribe(e=>{e.data&&e.data.length>0?(this._udrfService.udrfBodyData=e.data,this._udrfService.udrfUiData.totalItemDataCount=e.data.length,console.log(this._udrfService.udrfBodyData),this.getCoOwner()):(this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfData.noItemDataFound=!0),this._udrfService.udrfData.isItemDataLoading=!1},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}ngOnDestroy(){this.subs.unsubscribe(),this._udrfService.resetUdrfData()}openComments(){return Object(l.c)(this,void 0,void 0,(function*(){let e,t=this._udrfService.udrfUiData.openCommentsData.data;console.log(t),console.log(t);{let i=g(t.due_date).format("YYYY-MM-DD");e={inputData:{application_id:143,application_name:"OKR",title:t.name,unique_id_1:t._id,unique_id_2:""},context:{Task:t.name,Status:t.status_name,Type:t.type_name,"Due Date":i},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"}}const{ChatCommentContextModalComponent:n}=yield Promise.all([i.e(4),i.e(63),i.e(75),i.e(983)]).then(i.bind(null,"vg2w"));this.$dialog.open(n,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:e}})}))}callInlineEditApi(){return Object(l.c)(this,void 0,void 0,(function*(){let e=g().format("YYYY-MM-DD"),t=g(this._udrfService.udrfUiData.inlineEditData.dataSelected.due_date).format("YYYY-MM-DD"),i=g(this._udrfService.udrfUiData.inlineEditData.dataSelected.start_date).format("YYYY-MM-DD");if(console.log("current date",e,t,i),console.log("InlineEdit response data",this._udrfService.udrfUiData.inlineEditData),"okr-status"==this._udrfService.udrfUiData.inlineEditData.inlineEditField)this._okr.hasObjAccess("143044","Update",[this._udrfService.udrfUiData.inlineEditData.dataSelected.owner])?1==this._udrfService.udrfUiData.inlineEditData.dataSelected.is_recurring?"l1"==this._udrfService.udrfUiData.inlineEditData.hierarchyLevel&&"okr-status"==this._udrfService.udrfUiData.inlineEditData.inlineEditField&&(this.subs.sink=this._okr.updateTaskStatus({task_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,status:this._udrfService.udrfUiData.inlineEditData.inlineEditResponse}).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){if(console.log(e),1==e.isValidUpdate){this.utilityService.showMessage(e.msg,"Dismiss",3e3);let t=this._udrfService.udrfUiData.inlineEditData.index;this._udrfService.udrfBodyData[t].status_id=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse._id,this._udrfService.udrfBodyData[t].status_name=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.name}else this.utilityService.showMessage(e.msg,"Dismiss",3e3)})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}),console.log(this._udrfService.udrfUiData.inlineEditData.dataSelected._id,this._udrfService.udrfUiData.inlineEditData.inlineEditResponse._id)):0==this._udrfService.udrfUiData.inlineEditData.dataSelected.is_recurring&&"l1"==this._udrfService.udrfUiData.inlineEditData.hierarchyLevel&&"okr-status"==this._udrfService.udrfUiData.inlineEditData.inlineEditField&&(this.subs.sink=this._okr.updateTaskStatus({task_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,status:this._udrfService.udrfUiData.inlineEditData.inlineEditResponse}).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){if(1==e.isValidUpdate){this.utilityService.showMessage(e.msg,"Dismiss",3e3);let t=this._udrfService.udrfUiData.inlineEditData.index;this._udrfService.udrfBodyData[t].status_id=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse._id,this._udrfService.udrfBodyData[t].status_name=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.name}else this.utilityService.showMessage(e.msg,"Dismiss",3e3)})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})):this.utilityService.showMessage("Sorry, you don't have access to update this milestone status","Dismiss");else if("Start Date"==this._udrfService.udrfUiData.inlineEditData.inlineEditField)if(this._okr.hasObjAccess("143045","Update",[this._udrfService.udrfUiData.inlineEditData.dataSelected.owner])){let e=this.convertToLocalTime(this._udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]);console.log("formatedate",e);let t=g(this._udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]).format("YYYY-MM-DD");console.log("formatedate",e),this.subs.sink=this._okr.updateTaskDetail({task_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,update_field:"start date",update_value:t}).subscribe(t=>Object(l.c)(this,void 0,void 0,(function*(){1==t.isValidUpdate?(this.utilityService.showMessage("Start Date Updated Successfully","Dismiss",3e3),this._udrfService.udrfBodyData[this._udrfService.udrfUiData.inlineEditData.index].start_date=e):this.utilityService.showMessage(t.msg,"Dismiss",3e3)})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this milestone date","Dismiss");else if("End Date"==this._udrfService.udrfUiData.inlineEditData.inlineEditField)if(this._okr.hasObjAccess("143045","Update",[this._udrfService.udrfUiData.inlineEditData.dataSelected.owner])){let e=this.convertToLocalTime(this._udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]);console.log("formatedate",e);let t=g(this._udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]).format("YYYY-MM-DD");this.subs.sink=this._okr.updateTaskDetail({task_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,update_field:"end date",update_value:t}).subscribe(t=>Object(l.c)(this,void 0,void 0,(function*(){1==t.isValidUpdate?(this.utilityService.showMessage("End Date Updated Successfully","Dismiss",3e3),this._udrfService.udrfBodyData[this._udrfService.udrfUiData.inlineEditData.index].end_date=e):this.utilityService.showMessage(t.msg,"Dismiss",3e3)})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this milestone date","Dismiss");else if("Owner"==this._udrfService.udrfUiData.inlineEditData.inlineEditField)if(this._okr.hasObjAccess("143043","Update",[this._udrfService.udrfUiData.inlineEditData.dataSelected.owner])){let e=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid;this.subs.sink=this._okr.updateTaskDetail({task_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,update_field:"owner",update_value:e}).subscribe(t=>Object(l.c)(this,void 0,void 0,(function*(){1==t.isValidUpdate?(this.utilityService.showMessage("Owner Updated Successfully","Dismiss",3e3),this._udrfService.udrfBodyData[this._udrfService.udrfUiData.inlineEditData.index].owner_id=e):this.utilityService.showMessage(t.msg,"Dismiss",3e3)})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this milestone owner","Dismiss");else if("name"==this._udrfService.udrfUiData.inlineEditData.inlineEditField)if(this._okr.hasObjAccess("143042","Update",[this._udrfService.udrfUiData.inlineEditData.dataSelected.owner])){console.log(this._udrfService.udrfUiData.inlineEditData);let e=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.name;this.subs.sink=this._okr.updateTaskDetail({task_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,update_field:"title",update_value:e}).subscribe(t=>Object(l.c)(this,void 0,void 0,(function*(){1==t.isValidUpdate?(this.utilityService.showMessage("Name Updated Successfully","Dismiss",3e3),this._udrfService.udrfBodyData[this._udrfService.udrfUiData.inlineEditData.index].name=e):this.utilityService.showMessage(t.msg,"Dismiss",3e3)})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this milestone name","Dismiss");else if("weightage"==this._udrfService.udrfUiData.inlineEditData.inlineEditField){console.log(this._udrfService.udrfUiData.inlineEditData);let e=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.weightage,t={task_id:this._udrfService.udrfUiData.inlineEditData.dataSelected._id,mapped_under_id:this.mapped_under_id,upd_val:Number(e)};this.subs.sink=this._okr.updateTaskWeightage(t).subscribe(t=>Object(l.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage(t.msg,"Dismiss",3e3),"Updated successfully..!"==t.msg&&(this._udrfService.udrfBodyData[this._udrfService.udrfUiData.inlineEditData.index].weightage=e)})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}}))}openDeleteButton(){this._okr.hasObjAccess("143041","Delete",[this._udrfService.udrfUiData.openDeleteButtonData.data.owner])?this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this Task will be deleted !").then(e=>Object(l.c)(this,void 0,void 0,(function*(){if(e){let e=this._udrfService.udrfUiData.openDeleteButtonData.data;console.log(e),this.subs.sink=this._okr.deleteTask({task_id:e._id}).subscribe(e=>{console.log("Task successfully Deleted"),this.ngOnInit()},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}}))):this.utilityService.showMessage("Sorry, you don't have access to delete this milestone","Dismiss")}openPopUp(){let e;console.log("popup data",this._udrfService.udrfUiData.openPopUpData.data),this.subs.sink=this._okr.getKrOrInitDetailForTask({task_id:this._udrfService.udrfUiData.openPopUpData.data._id,mapped_under_id:this._udrfService.udrfUiData.openPopUpData.data.mapped_under_id}).subscribe(t=>{e=t.data,console.log("popup from server",e),this._initStatus.transform(e.krDetail.status).then(t=>{e.krDetail.status=t[0],this.openValMetricPopUp(e.updatedValue,e.krDetail,e.isInitiative,!0,0)})},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getFilters(e){let t=[];for(let i of e)t.push(i.isIdBased?{filterName:i.filterName,filterValues:i.multiOptionSelectSearchValuesWithId}:{filterName:i.filterName,filterValues:i.multiOptionSelectSearchValues});return t}alignTask(){this.subs.sink=this._okr.checkAdmin({employee_oid:this.profileId,configuration_name:"okr_roles",role_name:"Admin"}).subscribe(e=>{e.data?this.openAlignTask():this.utilityService.showMessage("You are not an Admin","Dismiss",3e3)})}attachFile(){return Object(l.c)(this,void 0,void 0,(function*(){this.attachId=this._udrfService.udrfUiData.attachFileData.id,console.log(this.attachId)}))}onProgressItem(){console.log("in Progress")}onCompleteItem(){let e=this._udrfService.udrfUiData.onCompleteItemResponse;this.finalAttachments=[],this.finalAttachments.push(e),console.log(this.finalAttachments),this.subs.sink=this._okr.uploadFile({task_id:this.attachId,attachment_arr:this.finalAttachments}).subscribe(e=>{console.log("file uploaded successfully"),this.utilityService.showMessage("File uploaded successfully","Dismiss",3e3)})}openFileDownload(){let e=this._udrfService.udrfUiData.openFileDownloadData.event,t=this._udrfService.udrfUiData.openFileDownloadData.data,i={task_id:t._id};console.log(t),this.attachments=[],this.subs.sink=this._okr.getTaskAttachments(i).subscribe(i=>{this.attachments=i.data[0].attachments,this.downloadTaskFile(t._id,this.attachments,e)})}downloadTaskFile(e,t,i){this.subs.sink=this._downloadService.open(i,gt.a,this.viewContainerRef,{id:e,attachment:t}).subscribe(e=>{console.log(e)})}getCoOwner(){return Object(l.c)(this,void 0,void 0,(function*(){this.coOwnerList=[];for(let e=0;e<this._udrfService.udrfBodyData.length;e++)if(this._udrfService.udrfBodyData[e].co_owner&&0!=this._udrfService.udrfBodyData[e].co_owner.length){let t=this._udrfService.udrfBodyData[e].co_owner;console.log(t),this.coOwnerDetail=[];for(let e=0;e<t.length;e++){let i=yield this.accountService.getUserProfileFromDB(t[e]);this.coOwnerDetail[e]=i[0]}this.coOwnerList[e]=this.coOwnerDetail}else this.coOwnerList[e]=[];console.log(this.coOwnerList),this._udrfService.udrfUiData.CoOwnerListData=this.coOwnerList,console.log(this._udrfService.udrfUiData.CoOwnerListData)}))}openCollabrative(){let e,t,i,n=this._udrfService.udrfUiData.collabarationData;e=n.data._id,t=n.data.type_name,i=n.data,console.log(e),console.log(t),console.log(i),"Milestone"==t&&(console.log({task_id:e}),this.openCollabrativePopup(e,t,[]))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](D.b),o["\u0275\u0275directiveInject"](_t.a),o["\u0275\u0275directiveInject"](r.a),o["\u0275\u0275directiveInject"](s.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](b.a),o["\u0275\u0275directiveInject"](Dt.a),o["\u0275\u0275directiveInject"](S.a),o["\u0275\u0275directiveInject"](bt.a),o["\u0275\u0275directiveInject"](St.a),o["\u0275\u0275directiveInject"](o.ViewContainerRef),o["\u0275\u0275directiveInject"](w.a),o["\u0275\u0275directiveInject"](wt.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-initiative-detail-tasks"]],decls:4,vars:0,consts:[[1,"mx-3"],[1,"pt-1"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275element"](1,"udrf-header"),o["\u0275\u0275element"](2,"div",1),o["\u0275\u0275element"](3,"udrf-body"),o["\u0275\u0275elementEnd"]())},directives:[yt.a,xt.a],styles:[""]}),e})(),data:{breadcrumb:"Tasks"}}]}];let Ot=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(Et)],a.k]}),e})();var Ct=i("4/q7"),kt=i("QibW"),It=i("Xi0T"),jt=i("3PA3"),At=i("ljNA"),Ut=i("vVoB");let Mt=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,Ot,m.g,E.b,O.b,kt.c,V.e,x.b,It.a,jt.a,Ct.b,_.p,At.KrDetailModule,k.b,C.b,I.b,Ut.a,_.E]]}),e})()}}]);