(window.webpackJsonp=window.webpackJsonp||[]).push([[723,209,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},REeT:function(e,t,n){"use strict";n.r(t),n.d(t,"HrDashboardModule",(function(){return Sn}));var i=n("ofXK"),a=n("tyNb"),o=n("mrSG"),r=n("Kj3r"),l=n("bEYa"),s=n("0IaG"),d=n("xG9w"),p=n("jhN1"),c=n("fXoL"),m=n("XNiG"),g=n("1G5W"),u=n("tk/3"),h=n("flaP"),f=n("t+tn");let y=(()=>{class e{constructor(e,t,n){this._http=e,this.roleService=t,this.tenantService=n,this.routeLinks={homePage:"/main/employee-central/employeeCentralHome",creationPage:"/main/employee-central/employeeCentralCreation/",detailPage:"/main/employee-central/employeeCentralDetail/"},this._filterChanged=new m.b,this.filter=[]}getDashboardFilters(){return new Promise((e,t)=>{this._http.post("/api/employee360/employeeDetails/getDashboardFilters",{}).subscribe(n=>{n.error||(console.log(n),this.userRole=n.userRole,this.entityData=n.entity,this.divisionData=n.division,this.subDivisionData=n.sub_division,this.employmentTypeData=n.employment_type,e(!0)),t()},e=>t(e))})}getEmployeeGenByOrg(){return this._http.post("/api/employee360/employeeDetails/getEmployeeGenByOrg",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}setDateFilter(e){this.dateFilter=e,console.log(this.dateFilter),console.log(e)}getDateFilter(){return this.dateFilter}getEmployeeExitData(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitData",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCertificationData(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCertificationData",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCertificationDataSummary(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCertificationDataSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeExitDataByOrg(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitDataByOrg",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getWidgetConfig(e){return this._http.post("/api/employee360/utilData/getDashboardConfig",{widget_name:e})}getEmployeeCountByEmploymentType(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountByEmploymentType",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountByWorkLocation(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountByWorkLocation",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountByExperience(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountByExperience",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountForFresherExperience(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountForFresherExperience",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountForLateralExperience(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountForLateralExperience",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountForExactExperience(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountForExactExperience",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeExitDataByDivision(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitDataByDivision",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeExitByAttritionValue(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitByAttritionValue",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountForSelfInsured(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountForSelfInsured",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountForSumInsured(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountForSumInsured",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountByVaccination(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountByVaccination",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountByGender(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountByGender",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getGenderCountForDataGrid(e){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountByGender",{filter:e}).pipe(Object(g.a)(this._filterChanged))}getEmployeeCountByGeneration(){return this._http.post("/api/employee360/employeeDetails/getEmployeeCountByGeneration",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getProjectNotMappedCount(){return this._http.post("/api/employee360/employeeDetails/getProjectNotMappedCount",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeExitDataByMonth(e){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitDataByMonth",{filter_config:e,filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeIdTypeValue(){return this._http.post("/api/employee360/employeeDetails/getEmployeeIdTypeValue",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeExitDataByExitType(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitDataByExitType",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getAllDashboardConfig(){return this._http.post("/api/employee360/utilData/getAllDashboardConfig",{})}getEmployeeExitTypeSummary(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitTypeSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeIdTypeValueSummary(){return this._http.post("/api/employee360/employeeDetails/getEmployeeIdTypeValueSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByEmpTypeSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByEmpTypeSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByFresherExpSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByFresherExpSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByExactExpSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByExactExpSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByLateralExpSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByLateralExpSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpBySelfInsuredSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpBySelfInsuredSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpBySumInsuredSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpBySumInsuredSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByWLocSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByWLocSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByVacSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByVacSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByGenderSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByGenderSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByGenSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByGenSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmpByExpSummary(){return this._http.post("/api/employee360/employeeDetails/getEmpByExpSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getProjNotMapSummary(){return this._http.post("/api/employee360/employeeDetails/getProjNotMapSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeExitDataSummary(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitDataSummary",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeExitDataSummaryByType(){return this._http.post("/api/employee360/employeeDetails/getEmployeeExitDataSummaryByType",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getISASLAreport(){return this._http.post("/api/isa/request/getISASLAreport",{filterConfig:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeActionReason(){return this._http.post("/api/employee360/employeeDetails/getEmployeeActionReason",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getEmployeeHireDataByMonth(e){return this._http.post("/api/employee360/employeeDetails/getEmployeeHireDataByMonth",{filter_config:e,filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getAppraisalDue(){return this._http.post("/api/employee360/employeeDetails/getAppraisalDue",{filter:this.filter}).pipe(Object(g.a)(this._filterChanged))}getDivision(e){return[...this.divisionData.filter(t=>e.some(e=>t.entity.includes(e)))]}getSubDivision(e,t){return[...this.subDivisionData.filter(n=>e.some(e=>n.entity.includes(e))&&t.some(e=>n.division.includes(e)))]}getEntities(){return this._http.post("/api/employee360/masterData/getEntities",{})}getDashboardFitlers(){return new Promise((e,t)=>{this._http.post("/api/employee360/employeeDetails/getDashboardFilters",{}).subscribe(n=>{n.error||(this.userRole=n.role,this.entityData=n.data.entity,this.divisionData=n.data.division,this.subDivisionData=n.data.sub_division,this.employmentTypeData=n.data.employment_type,e(!0)),t()},e=>{t()})})}getBenchCount(e,t){return this._http.post("/api/employee360/employeeDetails/getBenchCount",{selectedDate:e,Filter:t})}getBenchCountBySummary(e,t,n){return this._http.post("/api/employee360/employeeDetails/getBenchCountBySummary",{selectedDate:e,Filter:t,Graph:n})}getSunBurstCount(){return this._http.post("/api/employee360/employeeDetails/getSunBurstCount",{})}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275inject"](u.c),c["\u0275\u0275inject"](h.a),c["\u0275\u0275inject"](f.a))},e.\u0275prov=c["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var b=n("ZzPI"),v=n("6t9p"),x=n("PVOt"),_=n("NFeN");function C(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",11),c["\u0275\u0275elementStart"](1,"div",12),c["\u0275\u0275text"](2,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]())}function E(e,t){if(1&e&&c["\u0275\u0275element"](0,"data-table",13),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("tableConfig",e.tableConfig)("tableContent",e.tableContent)}}function w(e,t){if(1&e&&c["\u0275\u0275element"](0,"dxi-column",21),2&e){const e=t.$implicit;c["\u0275\u0275property"]("dataField",e.data_field)("caption",e.column_name)}}const S=function(e,t){return{rowData:e,colConfig:t}},D=function(e,t){return{color:e,"font-size":t}};function O(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"p",23),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit,t=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("matTooltip",t.formatCell(c["\u0275\u0275pureFunction2"](4,S,e.data,e.data.colConfig[e.column.caption])))("ngClass",e.data.colConfig[e.column.caption].text_overflow?"overflow":"")("ngStyle",c["\u0275\u0275pureFunction2"](7,D,null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.text_color_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.text_color_field]:e.data.colConfig[e.column.caption].text_color?e.data.colConfig[e.column.caption].text_color:"#45546E",null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.font_size_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.font_size_field]:e.data.colConfig[e.column.caption].font_size?t.colConfig.font_size:"14px")),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",t.formatCell(c["\u0275\u0275pureFunction2"](10,S,e.data,e.data.colConfig[e.column.caption]))," ")}}function I(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-icon",25),c["\u0275\u0275listener"]("click",(function(n){c["\u0275\u0275restoreView"](e);const i=t.$implicit,a=c["\u0275\u0275nextContext"](2).$implicit;return c["\u0275\u0275nextContext"](2).onActionClick({rowData:a.data,actionData:i},n)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"](2).$implicit,i=c["\u0275\u0275nextContext"](2);c["\u0275\u0275property"]("matTooltip",e.action_name)("ngStyle",c["\u0275\u0275pureFunction2"](3,D,null!=n.data.colConfig[n.column.caption].property_field_binding&&n.data.colConfig[n.column.caption].property_field_binding.text_color_field?n.data[n.data.colConfig[n.column.caption].property_field_binding.text_color_field]:n.data.colConfig[n.column.caption].text_color?n.data.colConfig[n.column.caption].text_color:"#45546E",null!=n.data.colConfig[n.column.caption].property_field_binding&&n.data.colConfig[n.column.caption].property_field_binding.font_size_field?n.data[n.data.colConfig[n.column.caption].property_field_binding.font_size_field]:n.data.colConfig[n.column.caption].font_size?i.colConfig.font_size:"14px")),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.action_icon," ")}}function F(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,I,2,6,"mat-icon",24),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",e.data.colConfig[e.column.caption].actions)}}function M(e,t){1&e&&c["\u0275\u0275elementContainer"](0)}const P=function(e){return{$implicit:e}};function R(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,M,1,0,"ng-container",26),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngTemplateOutlet",e.data.colConfig[e.column.caption].template_ref)("ngTemplateOutletContext",c["\u0275\u0275pureFunction1"](2,P,e.data))}}function T(e,t){}const L=function(e,t){return{col:e,data:t}};function j(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,T,0,0,"ng-template",27),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("cellComponent",c["\u0275\u0275pureFunction2"](1,L,e.data.colConfig[e.column.caption],e))}}function k(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275template"](1,O,3,13,"ng-container",22),c["\u0275\u0275template"](2,F,2,1,"ng-container",22),c["\u0275\u0275template"](3,R,2,4,"ng-container",22),c["\u0275\u0275template"](4,j,2,4,"ng-container",22),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","text"==e.data.colConfig[e.column.caption].column_type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","actions"==e.data.colConfig[e.column.caption].column_type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","template"==e.data.colConfig[e.column.caption].column_type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","component"==e.data.colConfig[e.column.caption].column_type)}}function Y(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"dx-data-grid",14),c["\u0275\u0275listener"]("onExporting",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onExporting(t)})),c["\u0275\u0275element"](1,"dxo-sorting",15),c["\u0275\u0275element"](2,"dxo-export",16),c["\u0275\u0275element"](3,"dxo-header-filter",17),c["\u0275\u0275element"](4,"dxo-scrolling",18),c["\u0275\u0275template"](5,w,1,2,"dxi-column",19),c["\u0275\u0275template"](6,k,5,4,"div",20),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("dataSource",e.tableContent)("allowColumnReordering",!0)("allowColumnResizing",!0)("columnAutoWidth",!0)("showBorders",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("showSortIndexes",!1),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",e.tableConfig.columnConfig),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","cellTemplate")}}const A=function(e){return{"background-color":e}};let B=(()=>{class e{constructor(e,t){this.summaryData=e,this._HRDService=t,this.title="",this.subtitle="",this.subtitle1="",this.headerBg="#FFF3E8",this.isLoading=!0,this.useDxTable=!1,this.summaryColumnConfig=[{column_name:"Associate Id",column_type:"text",data_field:"associate_id",text_overflow:!0},{column_name:"Employee Name",column_type:"text",data_field:"employee_name",text_overflow:!0,gutter_space:"5%"},{column_name:"Entity",column_type:"text",data_field:"entity_name",text_overflow:!0,gutter_space:"5%"},{column_name:"Division",column_type:"text",data_field:"division_name",text_overflow:!0,gutter_space:"5%"},{column_name:"Sub Division",column_type:"text",data_field:"sub_division_name",text_overflow:!0,gutter_space:"5%"},{column_name:"Employement Type",column_type:"text",data_field:"type_name",text_overflow:!0,gutter_space:"5%"}]}ngOnInit(){if(this.useDxTable=!0,this.tableConfig={onRowClick:e=>{try{console.log("RE"),this.summaryData.onRowClick(e)}catch(t){return void console.log(t)}},columnConfig:this.summaryColumnConfig,cell_border_bottom:this.summaryData.noBorder?"none":"solid 1px #E8E9EE",border_collapse:"collapse",layout:"fixed"},this.summaryData.summaryFor)for(let e=0;e<this.summaryData.summaryFor.length;e++)this.summaryColumnConfig.push({column_name:this.summaryData.summaryForColName[e],column_type:"text",data_field:this.summaryData.summaryFor[e]});this.isLoading=!this.tableContent,this._HRDService[this.summaryData.dataObservername]().subscribe(e=>{this.tableContent=e.data,this.tableContent&&this.tableContent.map(e=>e.colConfig=d.indexBy(this.summaryColumnConfig,"column_name")),this.subtitle="Total "+this.tableContent.length,this.isLoading=!this.tableContent}),this.title=this.summaryData.title,this.headerBg=this.summaryData.headerBG}onExporting(e){e.fileName=this.title}onActionClick(e,t){try{e.actionData.action_click(e.rowData),t.stopPropagation()}catch(n){return}}formatCell(e){try{return e.colConfig.formatcell({cellData:e.rowData[e.colConfig.data_field],rowData:e.rowData})||"-"}catch(t){return e.rowData[e.colConfig.data_field]?e.rowData[e.colConfig.data_field]:"-"}}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](s.a),c["\u0275\u0275directiveInject"](y))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-summary-table"]],features:[c["\u0275\u0275ProvidersFeature"]([{provide:p.h}])],decls:16,vars:13,consts:[[1,"card-container"],[1,"card-header","row","justify-content-between","align-items-center",3,"ngStyle"],[1,"title"],[2,"display","flex"],[1,"subtitle"],[1,"subtitle","ml-4"],["matDialogClose","",2,"font-size","small","font-weight","500","cursor","pointer"],[1,"card-body"],["class","loading-wrapper",4,"ngIf"],[3,"tableConfig","tableContent",4,"ngIf"],["style","height:100% !important","columnResizingMode","widget",3,"dataSource","allowColumnReordering","allowColumnResizing","columnAutoWidth","showBorders","onExporting",4,"ngIf"],[1,"loading-wrapper"],[1,"loading"],[3,"tableConfig","tableContent"],["columnResizingMode","widget",2,"height","100% !important",3,"dataSource","allowColumnReordering","allowColumnResizing","columnAutoWidth","showBorders","onExporting"],["mode","single",3,"showSortIndexes"],[3,"enabled"],[3,"visible"],["mode","infinite"],["alignment","left","sortOrder","asc","cellTemplate","cellTemplate",3,"dataField","caption",4,"ngFor","ngForOf"],[4,"dxTemplate","dxTemplateOf"],["alignment","left","sortOrder","asc","cellTemplate","cellTemplate",3,"dataField","caption"],[4,"ngIf"],[1,"p-0",2,"margin-top","5px","margin-bottom","5px",3,"matTooltip","ngClass","ngStyle"],["class","p-0 row ","style","padding-top:6px!important",3,"matTooltip","ngStyle","click",4,"ngFor","ngForOf"],[1,"p-0","row",2,"padding-top","6px!important",3,"matTooltip","ngStyle","click"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"cellComponent"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div"),c["\u0275\u0275elementStart"](3,"p",2),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"div",3),c["\u0275\u0275elementStart"](6,"p",4),c["\u0275\u0275text"](7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"p",5),c["\u0275\u0275text"](9),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](10,"span",6),c["\u0275\u0275text"](11,"Close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](12,"div",7),c["\u0275\u0275template"](13,C,3,0,"div",8),c["\u0275\u0275template"](14,E,1,2,"data-table",9),c["\u0275\u0275template"](15,Y,7,10,"dx-data-grid",10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction1"](11,A,t.headerBg||"#FFF3E8")),c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](t.title),c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](t.subtitle),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](t.subtitle1),c["\u0275\u0275advance"](3),c["\u0275\u0275styleProp"]("height",t.useDxTable?"80%":"0px")("overflow",t.useDxTable?"none":"scroll"),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.useDxTable),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.isLoading&&t.useDxTable))},directives:[i.NgStyle,s.d,i.NgIf,b.a,v.ae,v.Sb,v.Cc,v.Jd,i.NgForOf,x.d,v.g,i.NgClass,_.a,i.NgTemplateOutlet],styles:[".loading[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}  .dx-toolbar .dx-toolbar-items-container{height:32px!important}.card-container[_ngcontent-%COMP%]{display:flex;flex-flow:column;height:100%;overflow-y:hidden}.card-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{justify-self:flex-start;flex:0 1 auto;height:15%;color:#45546e}.card-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;padding:0!important}.card-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-size:12px}.card-container[_ngcontent-%COMP%]   .dx-datagrid[_ngcontent-%COMP%]   .dx-sort.dx-sort-asc[_ngcontent-%COMP%]   .dx-sort-index[_ngcontent-%COMP%], .card-container[_ngcontent-%COMP%]   .dx-datagrid[_ngcontent-%COMP%]   .dx-sort.dx-sort-desc[_ngcontent-%COMP%]   .dx-sort-index[_ngcontent-%COMP%]{display:none}.card-container[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{flex:1 1 auto;overflow-y:auto;height:0}"]}),e})();var z=n("dg+m"),V=n("Wp6s");const N=["loading"],W=["error"];function G(e,t){1&e&&c["\u0275\u0275elementContainer"](0)}function H(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,G,1,0,"ng-container",5),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngTemplateOutlet",e.loadingTemplate)}}function Q(e,t){1&e&&c["\u0275\u0275elementContainer"](0)}function $(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,Q,1,0,"ng-container",5),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngTemplateOutlet",e.errorTemplate)}}function q(e,t){1&e&&c["\u0275\u0275elementContainer"](0)}function J(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,q,1,0,"ng-container",5),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngTemplateOutlet",null==e.widgetAction?null:e.widgetAction.template)}}function U(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",6),c["\u0275\u0275elementStart"](1,"div",7),c["\u0275\u0275elementStart"](2,"div",8),c["\u0275\u0275elementStart"](3,"div",9),c["\u0275\u0275projection"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"p",10),c["\u0275\u0275text"](6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"p",11),c["\u0275\u0275text"](8),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"div",12),c["\u0275\u0275template"](10,J,2,1,"ng-container",1),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275projection"](11,1),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](6),c["\u0275\u0275textInterpolate"](null==e.widgetSummary?null:e.widgetSummary.summary),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](null==e.widgetSummary?null:e.widgetSummary.heading),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",null==e.widgetAction?null:e.widgetAction.isVisible)}}function X(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",13),c["\u0275\u0275text"](1,"Loading..."),c["\u0275\u0275elementEnd"]())}function K(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",14),c["\u0275\u0275text"](1,"Oops! Some error occurred."),c["\u0275\u0275elementEnd"]())}const Z=[[["widget-title"]],[["widget-body"]]],ee=["widget-title","widget-body"];let te=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275dir=c["\u0275\u0275defineDirective"]({type:e,selectors:[["widget-summary"]],inputs:{summary:"summary",heading:"heading"}}),e})(),ne=(()=>{class e{}return e.\u0275fac=function(t){return new(t||e)},e.\u0275dir=c["\u0275\u0275defineDirective"]({type:e,selectors:[["widget-action"]],inputs:{template:"template",isVisible:"isVisible"}}),e})(),ie=(()=>{class e{constructor(){this.isLoading=!1,this.isError=!1}toggleLoadingScreen(){this.isLoading=!this.isLoading}toggleErrorScreen(){this.isError=!this.isError}ngAfterContentInit(){console.log(this.widgetAction)}ngAfterViewInit(){this.loadingTemplate||(this.loadingTemplate=this.loading),this.errorTemplate||(this.errorTemplate=this.error)}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-widget-card"]],contentQueries:function(e,t,n){if(1&e&&(c["\u0275\u0275contentQuery"](n,te,!0),c["\u0275\u0275contentQuery"](n,ne,!0)),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.widgetSummary=e.first),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.widgetAction=e.first)}},viewQuery:function(e,t){if(1&e&&(c["\u0275\u0275viewQuery"](N,!0),c["\u0275\u0275viewQuery"](W,!0)),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.loading=e.first),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.error=e.first)}},inputs:{isLoading:"isLoading",loadingTemplate:"loadingTemplate",errorTemplate:"errorTemplate",isError:"isError"},ngContentSelectors:ee,decls:8,vars:3,consts:[[1,"widget-card"],[4,"ngIf"],["class","container-fluid p-0",4,"ngIf"],["loading",""],["error",""],[4,"ngTemplateOutlet"],[1,"container-fluid","p-0"],[1,"row","justify-content-between","align-items-start","p-1",2,"flex-wrap","nowrap !important"],[1,"title","col-6"],[1,"pb-2"],[1,"summary"],[2,"font-weight","400"],[1,"widget-action"],[1,"loading"],[1,"error"]],template:function(e,t){1&e&&(c["\u0275\u0275projectionDef"](Z),c["\u0275\u0275elementStart"](0,"mat-card",0),c["\u0275\u0275template"](1,H,2,1,"ng-container",1),c["\u0275\u0275template"](2,$,2,1,"ng-container",1),c["\u0275\u0275template"](3,U,12,3,"div",2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](4,X,2,0,"ng-template",null,3,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275template"](6,K,2,0,"ng-template",null,4,c["\u0275\u0275templateRefExtractor"])),2&e&&(c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isError),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError))},directives:[V.a,i.NgIf,i.NgTemplateOutlet],styles:[".widget-card[_ngcontent-%COMP%]{min-height:300px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.widget-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.widget-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .summary[_ngcontent-%COMP%]{font-weight:700;font-size:22px;color:#fa8c16}.error[_ngcontent-%COMP%], .loading[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.error[_ngcontent-%COMP%]{color:#d93636}"]}),e})();var ae=n("VI6+");function oe(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275text"](1),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" (",e.filterDateRange,") ")}}function re(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-icon",33),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().openSummary()})),c["\u0275\u0275text"](1,"open_in_new"),c["\u0275\u0275elementEnd"]()}}function le(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",34),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0))}function se(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",36),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function de(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",37),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function pe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",38),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function ce(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",39),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function me(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",40),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function ge(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",41),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function ue(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",42),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function he(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",43),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function fe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",41),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function ye(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",42),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function be(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",43),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function ve(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",44),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function xe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",45),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function _e(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",46),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Ce(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",47),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Ee(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",48),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function we(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",49),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Se(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",50),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function De(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",51),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Oe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",52),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Ie(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",53),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Fe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",54),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Me(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",55),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Pe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",56),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Re(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",57),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Te(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",58),c["\u0275\u0275element"](1,"dxo-label",35),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("barPadding",1-(null==e.data?null:e.data.length)/22-(null==e.data?null:e.data.length)/382),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Le(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"p"),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate"](e.percentText)}}function je(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"p"),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate"](e.point.data.percentText)}}function ke(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"p"),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](6,Le,2,1,"p",1),c["\u0275\u0275template"](7,je,2,1,"p",1),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](e.value),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate2"]("",e.argument,"-",e.point.series.name,""),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","project not mapped"!=n.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","project not mapped"==n.type)}}let Ye=(()=>{class e{constructor(e,t){this._HRDservice=e,this.dialog=t,this.isLoading=!0,this.isError=!1}ngOnInit(){console.log("this.filterDateRange"),console.log(this.filterDateRange),this.title="generation"==this.type?"Generation by department":"project not mapped"==this.type?"Projects not mapped":"experience fresher"==this.type?"Fresher Experience Range":"experience Lateral"==this.type?"Lateral Experience Range":"employee exact experience"==this.type?"Employee Exact Experience":"employee exit data"==this.type?"Employee Exit Data":"Sum Insured"==this.type?"GMC Covered":"Self Insured"==this.type?"GPA Covered":"ID Proof"==this.type?"Employee Aadhar Count ":"employee exit type"==this.type?"Employee Exit Data Based On Voluntary And InVoluntary":"Certification Type"==this.type?"Employee Certification Details":""}initReport(){this.isLoading=!0,"generation"==this.type?this._HRDservice.getEmployeeGenByOrg().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"project not mapped"==this.type?this._HRDservice.getProjectNotMappedCount().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"experience fresher"==this.type?this._HRDservice.getEmployeeCountForFresherExperience().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"experience Lateral"==this.type?this._HRDservice.getEmployeeCountForLateralExperience().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"employee exact experience"==this.type?this._HRDservice.getEmployeeCountForExactExperience().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"employee exit data"==this.type?this._HRDservice.getEmployeeExitDataByDivision().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"Self Insured"==this.type?this._HRDservice.getEmployeeCountForSelfInsured().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"Sum Insured"==this.type?this._HRDservice.getEmployeeCountForSumInsured().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"Certification Type"==this.type?this._HRDservice.getEmployeeCertificationData().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"ID Proof"==this.type?this._HRDservice.getEmployeeIdTypeValue().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"employee exit type"==this.type&&this._HRDservice.getEmployeeExitDataByExitType().subscribe(e=>{this.data=e.data,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0})}get observername(){return"generation"==this.type?"getEmpByGenSummary":"project not mapped"==this.type?"getProjNotMapSummary":"experience fresher"==this.type?"getEmpByFresherExpSummary":"experience Lateral"==this.type?"getEmpByLateralExpSummary":"employee exact experience"==this.type?"getEmpByExactExpSummary":"employee exit data"==this.type?"getEmployeeExitDataSummary":"Sum Insured"==this.type?"getEmpBySumInsuredSummary":"Self Insured"==this.type?"getEmpBySelfInsuredSummary":"ID Proof"==this.type?"getEmployeeIdTypeValueSummary":"employee exit type"==this.type?"getEmployeeExitTypeSummary":"Certification Type"==this.type?"getEmployeeCertificationDataSummary":void 0}get summaryFor(){return"generation"==this.type?["generation"]:"experience fresher"==this.type||"experience Lateral"==this.type||"employee exact experience"==this.type?["experience"]:"Sum Insured"==this.type||"Self Insured"==this.type?["insurance_amount"]:"ID Proof"==this.type?["type_name"]:"employee exit type"==this.type?["exit_type_name"]:"Certification Type"==this.type?["type_name","certification_institute","certified_by","certification_name","sponser_name","mode","grade"]:void 0}get summaryForColName(){return"generation"==this.type?["Generation"]:"experience fresher"==this.type||"experience Lateral"==this.type||"employee exact experience"==this.type?["Experience"]:"Sum Insured"==this.type||"Self Insured"==this.type?["Insurance_amount"]:"ID Proof"==this.type?["ID Type"]:"employee exit type"==this.type?["Exit Type"]:"Certification Type"==this.type?["Certification Type","Certification Institute","Certified By","Certification Name","Sponsored By","Certification Mode","Grade"]:void 0}openSummary(){this.dialog.open(B,{height:"65%",width:"65%",data:{columnConfig:this.summaryColumnConfig,dataObservername:this.observername,title:this.filterDateRange?this.titleConfig+" ("+this.filterDateRange+")":this.titleConfig,summaryFor:this.summaryFor,summaryForColName:this.summaryForColName},animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.title.toLowerCase()).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_lable)||this.title},e=>{this.isLoading=!1,this.isError=!0})}get summaryColumnConfig(){return[{column_name:"Associate Id",column_type:"text",data_field:"associate_id",text_overflow:!0},{column_name:"Employee Name",column_type:"text",data_field:"employee_name",text_overflow:!0,gutter_space:"5%"}]}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["bar-chart-widget"]],inputs:{type:"type",filterDateRange:"filterDateRange"},decls:40,vars:39,consts:[[3,"click"],[4,"ngIf"],[3,"isVisible","template"],["openSummaryRef",""],["id","chart",3,"dataSource","rotated","palette"],["valueField","dataTypeValue","name","Employee count",4,"ngIf"],["valueField","dataTypeValue","argumentField","sub_division_name","name","Employee Exit count",3,"barPadding",4,"ngIf"],["valueField","Voluntary","name","Voluntary","argumentField","sub_division_name",3,"barPadding",4,"ngIf"],["valueField","InVoluntary","name","InVoluntary","argumentField","sub_division_name",3,"barPadding",4,"ngIf"],["valueField","count","argumentField","sub_division_name","name","Aadhar Count",3,"barPadding",4,"ngIf"],["valueField","count","argumentField","sub_division_name","name","Certification",3,"barPadding",4,"ngIf"],["valueField","<5Yr","name","<5 Years",3,"barPadding",4,"ngIf"],["valueField",">5Yr","name",">5 Years",3,"barPadding",4,"ngIf"],["valueField",">10Yr","name",">10 Years",3,"barPadding",4,"ngIf"],["valueField",">20Yr","name",">20 Years",3,"barPadding",4,"ngIf"],["valueField","5 years","name","5 years",3,"barPadding",4,"ngIf"],["valueField","10 Years","name","10 years",3,"barPadding",4,"ngIf"],["valueField","15 Years","name","15 years",3,"barPadding",4,"ngIf"],["valueField","20 Years","name","20 years",3,"barPadding",4,"ngIf"],["valueField","2 Lakhs","name","2 Lakhs",3,"barPadding",4,"ngIf"],["valueField","3 Lakhs","name","3 Lakhs",3,"barPadding",4,"ngIf"],["valueField","5 Lakhs","name","5 Lakhs",3,"barPadding",4,"ngIf"],["valueField","10 Lakhs","name","10 Lakhs",3,"barPadding",4,"ngIf"],["valueField","15 Lakhs","name","15 Lakhs",3,"barPadding",4,"ngIf"],["valueField","20 Lakhs","name","20 Lakhs",3,"barPadding",4,"ngIf"],["valueField","10 Lakh","name","10 Lakh",3,"barPadding",4,"ngIf"],["valueField","Gen-x","name","Gen-x",3,"barPadding",4,"ngIf"],["valueField","Gen-y","name","Gen-y",3,"barPadding",4,"ngIf"],["valueField","Gen-z","name","Gen-z",3,"barPadding",4,"ngIf"],[3,"barPadding","argumentField","type"],["verticalAlignment","bottom","horizontalAlignment","center","itemTextPosition","top"],["contentTemplate","content","zIndex","1",3,"enabled"],[4,"dxTemplate","dxTemplateOf"],[2,"cursor","pointer","font-size","medium",3,"click"],["valueField","dataTypeValue","name","Employee count"],[3,"visible"],["valueField","dataTypeValue","argumentField","sub_division_name","name","Employee Exit count",3,"barPadding"],["valueField","Voluntary","name","Voluntary","argumentField","sub_division_name",3,"barPadding"],["valueField","InVoluntary","name","InVoluntary","argumentField","sub_division_name",3,"barPadding"],["valueField","count","argumentField","sub_division_name","name","Aadhar Count",3,"barPadding"],["valueField","count","argumentField","sub_division_name","name","Certification",3,"barPadding"],["valueField","<5Yr","name","<5 Years",3,"barPadding"],["valueField",">5Yr","name",">5 Years",3,"barPadding"],["valueField",">10Yr","name",">10 Years",3,"barPadding"],["valueField",">20Yr","name",">20 Years",3,"barPadding"],["valueField","5 years","name","5 years",3,"barPadding"],["valueField","10 Years","name","10 years",3,"barPadding"],["valueField","15 Years","name","15 years",3,"barPadding"],["valueField","20 Years","name","20 years",3,"barPadding"],["valueField","2 Lakhs","name","2 Lakhs",3,"barPadding"],["valueField","3 Lakhs","name","3 Lakhs",3,"barPadding"],["valueField","5 Lakhs","name","5 Lakhs",3,"barPadding"],["valueField","10 Lakhs","name","10 Lakhs",3,"barPadding"],["valueField","15 Lakhs","name","15 Lakhs",3,"barPadding"],["valueField","20 Lakhs","name","20 Lakhs",3,"barPadding"],["valueField","10 Lakh","name","10 Lakh",3,"barPadding"],["valueField","Gen-x","name","Gen-x",3,"barPadding"],["valueField","Gen-y","name","Gen-y",3,"barPadding"],["valueField","Gen-z","name","Gen-z",3,"barPadding"]],template:function(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card",0),c["\u0275\u0275listener"]("click",(function(){return t.openSummary()})),c["\u0275\u0275elementStart"](1,"widget-title"),c["\u0275\u0275text"](2),c["\u0275\u0275template"](3,oe,2,1,"ng-container",1),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](4,"widget-action",2),c["\u0275\u0275template"](5,re,2,0,"ng-template",null,3,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275elementStart"](7,"widget-body"),c["\u0275\u0275elementStart"](8,"dx-chart",4),c["\u0275\u0275template"](9,le,2,1,"dxi-series",5),c["\u0275\u0275template"](10,se,2,2,"dxi-series",6),c["\u0275\u0275template"](11,de,2,2,"dxi-series",7),c["\u0275\u0275template"](12,pe,2,2,"dxi-series",8),c["\u0275\u0275template"](13,ce,2,2,"dxi-series",9),c["\u0275\u0275template"](14,me,2,2,"dxi-series",10),c["\u0275\u0275template"](15,ge,2,2,"dxi-series",11),c["\u0275\u0275template"](16,ue,2,2,"dxi-series",12),c["\u0275\u0275template"](17,he,2,2,"dxi-series",13),c["\u0275\u0275template"](18,fe,2,2,"dxi-series",11),c["\u0275\u0275template"](19,ye,2,2,"dxi-series",12),c["\u0275\u0275template"](20,be,2,2,"dxi-series",13),c["\u0275\u0275template"](21,ve,2,2,"dxi-series",14),c["\u0275\u0275template"](22,xe,2,2,"dxi-series",15),c["\u0275\u0275template"](23,_e,2,2,"dxi-series",16),c["\u0275\u0275template"](24,Ce,2,2,"dxi-series",17),c["\u0275\u0275template"](25,Ee,2,2,"dxi-series",18),c["\u0275\u0275template"](26,we,2,2,"dxi-series",19),c["\u0275\u0275template"](27,Se,2,2,"dxi-series",20),c["\u0275\u0275template"](28,De,2,2,"dxi-series",21),c["\u0275\u0275template"](29,Oe,2,2,"dxi-series",22),c["\u0275\u0275template"](30,Ie,2,2,"dxi-series",23),c["\u0275\u0275template"](31,Fe,2,2,"dxi-series",24),c["\u0275\u0275template"](32,Me,2,2,"dxi-series",25),c["\u0275\u0275template"](33,Pe,2,2,"dxi-series",26),c["\u0275\u0275template"](34,Re,2,2,"dxi-series",27),c["\u0275\u0275template"](35,Te,2,2,"dxi-series",28),c["\u0275\u0275element"](36,"dxo-common-series-settings",29),c["\u0275\u0275element"](37,"dxo-legend",30),c["\u0275\u0275element"](38,"dxo-tooltip",31),c["\u0275\u0275template"](39,ke,8,5,"div",32),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275reference"](6);c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.filterDateRange),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("isVisible",!1)("template",e),c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("dataSource",t.data)("rotated","project not mapped"==t.type)("palette",null!=t.config&&t.config.palette&&(null==t.config||null==t.config.palette?null:t.config.palette.length)>0?null==t.config?null:t.config.palette:"material"),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","project not mapped"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","employee exit data"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","employee exit type"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","employee exit type"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","ID Proof"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Certification Type"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","experience fresher"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","experience fresher"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","experience fresher"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","experience Lateral"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","experience Lateral"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","experience Lateral"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","experience Lateral"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","employee exact experience"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","employee exact experience"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","employee exact experience"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","employee exact experience"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Sum Insured"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Sum Insured"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Sum Insured"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Sum Insured"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Sum Insured"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Sum Insured"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Self Insured"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","generation"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","generation"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","generation"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("barPadding",1-(null==t.data?null:t.data.length)/10-(null==t.data?null:t.data.length)/100)("argumentField","generation"==t.type||"project not mapped"==t.type?"division_name":"experience fresher"==t.type||"experience Lateral"==t.type||"employee exact experience"==t.type||"Sum Insured"==t.type||"Self Insured"==t.type||"Certification Type"==t.type?"sub_division_name":"")("type","stackedBar"),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","content")}},directives:[ie,i.NgIf,ne,ae.a,v.Bb,v.Qc,v.Ie,x.d,_.a,v.E,v.Oc],styles:[""]}),e})();var Ae=n("pzj6");function Be(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-icon",10),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().openSummary()})),c["\u0275\u0275text"](1,"open_in_new"),c["\u0275\u0275elementEnd"]()}}function ze(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"p"),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"p"),c["\u0275\u0275text"](7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](e.value),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.argument),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",(100*e.percent).toFixed(2)+"%"," ")}}function Ve(e,t){if(1&e&&(c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](0,"svg"),c["\u0275\u0275elementStart"](1,"text",11),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275attribute"]("fill","#45546E"),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ","vaccination"==e.type?e.total_rate+" %":e.total," ")}}const Ne=function(e){return{show:e,enabled:!0}},We=function(){return{size:"12px",color:"#45546E"}};let Ge=(()=>{class e{constructor(e,t){this._HRDservice=e,this.dialog=t,this.isLoading=!0,this.isError=!1}get observername(){return"vaccination"==this.type?"getEmpByVacSummary":"experience"==this.type?"getEmpByExpSummary":"gender"==this.type?"getEmpByGenderSummary":"generation"==this.type?"getEmpByGenSummary":void 0}get summaryFor(){return"vaccination"==this.type?["vaccine_type"]:"experience"==this.type?["experience"]:"gender"==this.type?["gender_name"]:"generation"==this.type?["generation"]:void 0}get summaryForColName(){return"vaccination"==this.type?["Vaccine Type"]:"experience"==this.type?["Experience"]:"gender"==this.type?["Gender Name"]:"generation"==this.type?["Generation"]:void 0}openSummary(){this.dialog.open(B,{height:"65%",width:"65%",data:{columnConfig:this.summaryColumnConfig,dataObservername:this.observername,title:this.titleConfig,summaryFor:this.summaryFor,summaryForColName:this.summaryForColName},animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}get title(){return"vaccination"==this.type?"Covid-19 Vaccination Rate":"gender"==this.type?"Gender Distribution Percentage":"generation"==this.type?"Generation Distribution Percentage":"experience"==this.type?"Employee experience range":""}get total(){return d.pluck(this.data,"dataTypeValue").reduce((e,t)=>e+t,0)}get total_rate(){let e=0;if("vaccination"==this.type&&this.data){for(let t=0;t<this.data.length;t++)"not vaccinated"!=this.data[t].dataType.toLowerCase()&&(e+=this.data[t].dataTypeValue);return(e/this.total*100).toFixed(2)}return 0}customizeLabel(e){return`${e.valueText} (${e.argumentText})`}ngOnInit(){}initReport(){"vaccination"==this.type?this._HRDservice.getEmployeeCountByVaccination().subscribe(e=>{this.data=e.data,this.isLoading=!1,this.isError=!1,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"gender"==this.type?this._HRDservice.getEmployeeCountByGender().subscribe(e=>{this.data=e.data,this.isLoading=!1,this.isError=!1,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"generation"==this.type?this._HRDservice.getEmployeeCountByGeneration().subscribe(e=>{this.data=e.data,this.isLoading=!1,this.isError=!1,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):"experience"==this.type&&this._HRDservice.getEmployeeCountByExperience().subscribe(e=>{this.data=e.data,this.isLoading=!1,this.isError=!1,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0})}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.title.toLowerCase()).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_lable)||this.title},e=>{this.isLoading=!1,this.isError=!0})}get summaryColumnConfig(){return[{column_name:"Associate Id",column_type:"text",data_field:"associate_id",text_overflow:!0},{column_name:"Employee Name",column_type:"text",data_field:"employee_name",text_overflow:!0,gutter_space:"5%"}]}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["doughnut-chart-widget"]],inputs:{type:"type"},decls:15,vars:23,consts:[[3,"click"],[3,"isVisible","template"],["openSummaryRef",""],["id","pie","resolveLabelOverlapping","shift","type","doughnut","centerTemplate","centerTemplate",3,"dataSource","animation","loadingIndicator","palette","innerRadius"],["argumentField","dataType","valueField","dataTypeValue"],["position","columns",3,"visible","customizeText"],[3,"visible","width"],["contentTemplate","content","zIndex","1",3,"enabled"],[4,"dxTemplate","dxTemplateOf"],["verticalAlignment","'bottom'",3,"visible","margin","horizontalAlignment","columnCount","font"],[2,"cursor","pointer","font-size","medium",3,"click"],["text-anchor","middle","x","50%","y","50%",2,"font-size","20px","color","#6e7b8f","font-weight","700"]],template:function(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card",0),c["\u0275\u0275listener"]("click",(function(){return t.openSummary()})),c["\u0275\u0275elementStart"](1,"widget-title"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](3,"widget-action",1),c["\u0275\u0275template"](4,Be,2,0,"ng-template",null,2,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275elementStart"](6,"widget-body"),c["\u0275\u0275elementStart"](7,"dx-pie-chart",3),c["\u0275\u0275elementStart"](8,"dxi-series",4),c["\u0275\u0275elementStart"](9,"dxo-label",5),c["\u0275\u0275element"](10,"dxo-connector",6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](11,"dxo-tooltip",7),c["\u0275\u0275template"](12,ze,8,3,"div",8),c["\u0275\u0275element"](13,"dxo-legend",9),c["\u0275\u0275template"](14,Ve,3,2,"svg",8),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275reference"](5);c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("isVisible",!1)("template",e),c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("dataSource",t.data)("animation",!0)("loadingIndicator",c["\u0275\u0275pureFunction1"](20,Ne,t.isLoading))("palette",null!=t.config&&t.config.palette?null==t.config?null:t.config.palette:"material")("innerRadius",.7),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("visible",!0)("customizeText",t.customizeLabel),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)("width",.5),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","content"),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible","experience"!=t.type)("margin",10)("horizontalAlignment","center")("columnCount","vaccination"==t.type?4:0)("font",c["\u0275\u0275pureFunction0"](22,We)),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","centerTemplate")}},directives:[ie,ne,Ae.a,v.E,v.Oc,v.Db,v.Ie,x.d,v.Qc,_.a],styles:["#pie{height:275px;width:100%}"]}),e})();function He(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-icon",11),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().openSummary()})),c["\u0275\u0275text"](1,"open_in_new"),c["\u0275\u0275elementEnd"]()}}function Qe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"p"),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"p"),c["\u0275\u0275text"](7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](e.value),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.argument),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"]("",e.percentText," ")}}const $e=function(){return["#2a2468","#975da1","#ffa2d8","#e9577a","#b40f0c"]},qe=function(e){return{show:e,enabled:!0}},Je=function(){return{size:"12px",color:"#45546E"}};let Ue=(()=>{class e{constructor(e,t){this._HRDservice=e,this.dialog=t,this.isLoading=!0,this.isError=!1}openSummary(){this.dialog.open(B,{height:"65%",width:"65%",data:{columnConfig:this.summaryColumnConfig,dataObservername:"employment type"==this.type?"getEmpByEmpTypeSummary":"getEmpByWLocSummary",title:this.titleConfig},animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}get totalValue(){return d.pluck(this.data,"dataTypeValue").reduce((e,t)=>e+t,0)}customizeLabel(e){return`${e.valueText} (${e.argumentText})`}ngOnInit(){}initReport(){"employment type"==this.type?this._HRDservice.getEmployeeCountByEmploymentType().subscribe(e=>{this.data=e.data,this.isLoading=!1,this.isError=!1,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0}):this._HRDservice.getEmployeeCountByWorkLocation().subscribe(e=>{this.data=e.data,this.isLoading=!1,this.isError=!1,this.getWidgetConfig()},e=>{this.isLoading=!1,this.isError=!0})}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.type).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_lable)||"Head Count By "+this.type},e=>{this.isLoading=!1,this.isError=!0})}get summaryColumnConfig(){return[{column_name:"Associate Id",column_type:"text",data_field:"associate_id",text_overflow:!0},{column_name:"Employee Name",column_type:"text",data_field:"employee_name",text_overflow:!0,gutter_space:"5%"}]}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["head-count-widget"]],inputs:{type:"type"},decls:15,vars:21,consts:[[3,"click"],["heading","Total Employees",3,"summary"],[3,"isVisible","template"],["openSummaryRef",""],["id","pie","resolveLabelOverlapping","shift","type","pie",3,"dataSource","animation","palette","loadingIndicator"],["argumentField","dataType","valueField","dataTypeValue"],["position","columns",3,"visible","customizeText"],[3,"visible","width"],["contentTemplate","content","zIndex","1",3,"enabled"],[4,"dxTemplate","dxTemplateOf"],[3,"visible","font"],[2,"cursor","pointer","font-size","medium",3,"click"]],template:function(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card",0),c["\u0275\u0275listener"]("click",(function(){return t.openSummary()})),c["\u0275\u0275element"](1,"widget-summary",1),c["\u0275\u0275element"](2,"widget-action",2),c["\u0275\u0275template"](3,He,2,0,"ng-template",null,3,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275elementStart"](5,"widget-title"),c["\u0275\u0275text"](6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"widget-body"),c["\u0275\u0275elementStart"](8,"dx-pie-chart",4),c["\u0275\u0275elementStart"](9,"dxi-series",5),c["\u0275\u0275elementStart"](10,"dxo-label",6),c["\u0275\u0275element"](11,"dxo-connector",7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](12,"dxo-tooltip",8),c["\u0275\u0275template"](13,Qe,8,3,"div",9),c["\u0275\u0275element"](14,"dxo-legend",10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275reference"](4);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("summary",t.totalValue),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("isVisible",!1)("template",e),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("dataSource",t.data)("animation",!0)("palette",c["\u0275\u0275pureFunction0"](17,$e))("palette",null!=t.config&&t.config.palette?null==t.config?null:t.config.palette:"material")("loadingIndicator",c["\u0275\u0275pureFunction1"](18,qe,t.isLoading)),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("visible",!0)("customizeText",t.customizeLabel),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)("width",.5),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","content"),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!1)("font",c["\u0275\u0275pureFunction0"](20,Je))}},directives:[ie,te,ne,Ae.a,v.E,v.Oc,v.Db,v.Ie,x.d,v.Qc,_.a],styles:["#pie{height:300px;width:100%}"]}),e})();var Xe=n("33Jv"),Ke=n("bTqV"),Ze=n("STbY");function et(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"button",10),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"](2).setAction(n)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function tt(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"button",7),c["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),c["\u0275\u0275text"](1),c["\u0275\u0275elementStart"](2,"mat-icon"),c["\u0275\u0275text"](3,"keyboard_arrow_down"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"mat-menu",0,8),c["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),c["\u0275\u0275template"](6,et,2,1,"button",9),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275reference"](5),t=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("matMenuTriggerFor",e),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",null==t.selected_action?null:t.selected_action.name," "),c["\u0275\u0275advance"](5),c["\u0275\u0275property"]("ngForOf",t.actions)}}function nt(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275text"](1),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" (",e.filterDateRange,") ")}}function it(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"span",14),c["\u0275\u0275elementStart"](1,"p",15),c["\u0275\u0275text"](2,"/"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"p",16),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"](" ",e.employeeCount," ")}}function at(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",11),c["\u0275\u0275elementStart"](1,"div",12),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](4,it,5,1,"span",13),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"p"),c["\u0275\u0275text"](6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate1"](" ",e.dataTypeValue," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",2==n.selected_action.id&&!n.isLoading),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.dataType)}}let ot=(()=>{class e{constructor(e,t){this._HRDservice=e,this.dialog=t,this.isLoading=!0,this.isError=!1,this.attrition_rate=0,this.actions=[],this.subs=new Xe.a,this.title="Exit employees"}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.getWidgetConfig()}))}openSummary(){var e;console.log(this.selected_action);let t={dataObservername:3!=(null===(e=this.selected_action)||void 0===e?void 0:e.id)?"getEmployeeExitDataSummary":"getEmployeeExitDataSummaryByType",title:this.filterDateRange?this.titleConfig+" ("+this.filterDateRange+")":this.titleConfig,summaryFor:["reason","start_date","exit_type_name"],summaryForColName:["Reason","Exit Date","Exit Type"],columnConfig:this.summaryColumnConfig};this.dialog.open(B,{height:"65%",width:"65%",data:t,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.title).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_lable)||this.title},e=>{this.isLoading=!1,this.isError=!0})}getEmployeeActionReason(){return new Promise((e,t)=>{this.subs.sink=this._HRDservice.getEmployeeActionReason().subscribe(n=>{n.err?(console.log("Response data rejected",n),t(n)):(console.log(n),e(n))},e=>{console.log(e),t(e)})})}setAction(e){var t;this.isLoading=(null===(t=this.selected_action)||void 0===t?void 0:t.id)!=e.id,this.selected_action=e,1==e.id?this._HRDservice.getEmployeeExitData().subscribe(e=>{this.data=e.data,this.total=e.total,this.attrition_rate=e.attrition_rate.toFixed(2),this.isLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isError=!0}):2==e.id?this._HRDservice.getEmployeeExitDataByOrg().subscribe(e=>{this.data=e.data,this.total=e.total,this.attrition_rate=e.attrition_rate.toFixed(2),this.isLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isError=!0}):3==e.id&&this._HRDservice.getEmployeeExitByAttritionValue().subscribe(e=>{this.data=e.data,this.total=e.total,this.attrition_rate=e.attrition_rate.toFixed(2),this.isLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isError=!0})}initReport(){return Object(o.c)(this,void 0,void 0,(function*(){try{this.actions=yield this.getEmployeeActionReason(),this.actions=this.actions.data,console.log("Actions",this.actions),this.actions&&this.actions.length>0?this.setAction(this.actions[0]):console.error("No actions available.")}catch(e){console.log(e)}}))}summaryColumnConfig(){return[{column_name:"Associate Id",column_type:"text",data_field:"associate_id",text_overflow:!0},{column_name:"Employee Name",column_type:"text",data_field:"employee_name",text_overflow:!0},{column_name:"Exit Date",column_type:"text",data_field:"start_date",text_overflow:!0,gutter_space:"5%"}]}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["exit-widget"]],inputs:{filterDateRange:"filterDateRange"},decls:11,vars:7,consts:[[3,"click"],[3,"isVisible","template"],["action",""],["heading","Attrition Rate",3,"summary"],[4,"ngIf"],[1,"row"],["class","col-6 title",4,"ngFor","ngForOf"],["mat-button","",2,"font-size","13px","color","#ffffff","font-weight","bold !important","background","linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%)",3,"matMenuTriggerFor","click"],["actionMenu","matMenu"],["mat-menu-item","",3,"click",4,"ngFor","ngForOf"],["mat-menu-item","",3,"click"],[1,"col-6","title"],[2,"font-size","large","color","#ef4861","font-weight","600","display","inline-flex"],["style","display:inherit",4,"ngIf"],[2,"display","inherit"],[2,"margin-left","2px"],[2,"color","#0fc91e !important","margin-left","2px"]],template:function(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card",0),c["\u0275\u0275listener"]("click",(function(){return t.openSummary()})),c["\u0275\u0275element"](1,"widget-action",1),c["\u0275\u0275template"](2,tt,7,3,"ng-template",null,2,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275element"](4,"widget-summary",3),c["\u0275\u0275elementStart"](5,"widget-title"),c["\u0275\u0275text"](6),c["\u0275\u0275template"](7,nt,2,1,"ng-container",4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"widget-body"),c["\u0275\u0275elementStart"](9,"div",5),c["\u0275\u0275template"](10,at,7,3,"div",6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275reference"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("isVisible",!0)("template",e),c["\u0275\u0275advance"](3),c["\u0275\u0275propertyInterpolate1"]("summary","",t.attrition_rate," %"),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate2"](" ",t.titleConfig," (",t.total,") "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.filterDateRange),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngForOf",t.data)}},directives:[ie,ne,te,i.NgIf,i.NgForOf,Ke.a,Ze.f,_.a,Ze.g,Ze.d],styles:[".title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}"]}),e})();var rt=n("wd/R"),lt=n.n(rt);function st(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"button",8),c["\u0275\u0275text"](1),c["\u0275\u0275elementStart"](2,"mat-icon",9),c["\u0275\u0275text"](3,"arrow_down"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){c["\u0275\u0275nextContext"]();const e=c["\u0275\u0275reference"](2),t=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("matMenuTriggerFor",e),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",t.selected_item.name,"")}}function dt(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"button",10),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"](2).setSelectedItem(n)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function pt(e,t){if(1&e&&(c["\u0275\u0275template"](0,st,4,2,"button",5),c["\u0275\u0275elementStart"](1,"mat-menu",null,6),c["\u0275\u0275template"](3,dt,2,1,"button",7),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("ngIf",e.filterConfig),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngForOf",e.filterConfig)}}function ct(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",11),c["\u0275\u0275element"](1,"dxo-label",12),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275propertyInterpolate"]("valueField",e.id),c["\u0275\u0275propertyInterpolate"]("name",e.name),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function mt(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"p"),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](e.value),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.argument)}}let gt=(()=>{class e{constructor(e){this._HRDservice=e,this.title="Exit Employees By Month",this.isLoading=!0,this.isError=!1,this.this_fy_start=lt()().month(3).startOf("month"),this.this_fy_end=lt()().month(2).endOf("month").add(1,"year"),this.prev_fy_start=lt()(this.this_fy_start).subtract(1,"year"),this.prev_fy_end=lt()(this.this_fy_end).subtract(1,"year"),this.filterConfig=[{id:0,name:this.prev_fy_start.format("MMM YYYY")+" - "+this.prev_fy_end.format("MMM YYYY"),start_date:this.prev_fy_start.format("YYYY-MM-DD"),end_date:this.prev_fy_end.format("YYYY-MM-DD")},{id:1,name:this.this_fy_start.format("MMM YYYY")+" - "+this.this_fy_end.format("MMM YYYY"),start_date:this.this_fy_start.format("YYYY-MM-DD"),end_date:this.this_fy_end.format("YYYY-MM-DD")}]}ngOnInit(){this.getWidgetConfig()}setSelectedItem(e){this.selected_item=e,this.loadData()}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.title.toLowerCase()).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_lable)||this.title},e=>{this.isLoading=!1,this.isError=!0})}loadData(){this.isLoading=!0,this._HRDservice.getEmployeeExitDataByMonth(this.filterConfig).subscribe(e=>{this.data=e.data,this.attritions=e.attrition,this.isLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isError=!0})}initReport(){this.setSelectedItem(this.filterConfig[0])}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["exit-by-month-widget"]],decls:10,vars:5,consts:[["actionTemplate",""],["id","chart",3,"dataSource"],["argumentField","month","type","bar",3,"valueField","name",4,"ngFor","ngForOf"],["contentTemplate","content","zIndex","1",3,"enabled"],[4,"dxTemplate","dxTemplateOf"],["mat-button","",3,"matMenuTriggerFor",4,"ngIf"],["actionMenu","matMenu"],["mat-menu-item","",3,"click",4,"ngFor","ngForOf"],["mat-button","",3,"matMenuTriggerFor"],[2,"color","black"],["mat-menu-item","",3,"click"],["argumentField","month","type","bar",3,"valueField","name"],[3,"visible"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card"),c["\u0275\u0275template"](1,pt,4,2,"ng-template",null,0,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275elementStart"](3,"widget-title"),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"widget-body"),c["\u0275\u0275elementStart"](6,"dx-chart",1),c["\u0275\u0275template"](7,ct,2,3,"dxi-series",2),c["\u0275\u0275element"](8,"dxo-tooltip",3),c["\u0275\u0275template"](9,mt,6,2,"div",4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("dataSource",t.data),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",t.filterConfig),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!1),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","content"))},directives:[ie,ae.a,i.NgForOf,v.Ie,x.d,i.NgIf,Ze.g,Ke.a,Ze.f,_.a,Ze.d,v.E,v.Oc],styles:[""]}),e})();function ut(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"mat-icon",9),c["\u0275\u0275text"](1,"open_in_new"),c["\u0275\u0275elementEnd"]())}function ht(e,t){if(1&e&&c["\u0275\u0275element"](0,"dxi-column",12),2&e){const e=t.$implicit;c["\u0275\u0275property"]("caption",e.title)("width",150)("dataField",e.name)}}function ft(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-column",10),c["\u0275\u0275template"](1,ht,1,3,"dxi-column",11),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275property"]("caption",e.division),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",e.subdivision)}}let yt=(()=>{class e{constructor(e,t){this._HRDservice=e,this.dialog=t,this.isLoading=!0,this.isError=!1,this.subdivision=[],this.subdata={},this.subDataSource=[],this.divgroup=[],this.EntityId_Arr=[],this.SubdivisionId_Arr=[],this.DivisionId_Arr=[],this.GenderObject={}}ngOnInit(){}get observername(){if("gender"==this.type)return"getEmpByGenderSummary"}get summaryFor(){if("gender"==this.type)return["gender_name"]}get summaryForColName(){if("gender"==this.type)return["Gender Name"]}openSummary(){this.dialog.open(B,{height:"65%",width:"65%",data:{columnConfig:this.summaryColumnConfig,dataObservername:this.observername,title:this.titleConfig,summaryFor:this.summaryFor,summaryForColName:this.summaryForColName},animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}get title(){return"gender"==this.type?"Gender Distribution Across BU":""}get total(){return d.pluck(this.data,"dataTypeValue").reduce((e,t)=>e+t,0)}customizeLabel(e){return`${e.valueText} (${e.argumentText})`}initReport(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.subDataSource=[],"gender"==this.type){let e=yield this._HRDservice.entityData;this.EntityId_Arr=d.uniq(d.pluck(e,"id"));let t=yield this._HRDservice.divisionData;this.DivisionId_Arr=d.uniq(d.pluck(t,"id"));let n=yield this._HRDservice.subDivisionData,i=yield this._HRDservice.subDivisionData;this.SubdivisionId_Arr=d.uniq(d.pluck(n,"id")),yield this._HRDservice.filter;let a=yield this._HRDservice.getDateFilter();console.log("date"),console.log(a),this._HRDservice.getGenderCountForDataGrid({entity:this.EntityId_Arr,division:this.DivisionId_Arr,sub_division:this.SubdivisionId_Arr,start_date:a.startDate,end_date:a.endDate}).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.data1=e.data,yield this._HRDservice.filter;for(let e=0;e<i.length;e++){let t=d.reject(d.uniq(i[e].division),d.isNull);for(let n=0;n<t.length;n++){let t=i[e].id,o=i[e].entity,r=d.reject(d.uniq(i[e].division),d.isNull);this.getSubDivisonGenderCount({entity:o,division:[r[n]],sub_division:[t],start_date:a.startDate,end_date:a.endDate})}}this.isLoading=!1,this.isError=!1,this.getWidgetConfig()})),e=>{this.isLoading=!1,this.isError=!0})}}))}getSubDivisonGenderCount(e){this._HRDservice.getGenderCountForDataGrid(e).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){var n;let i=t.data;if(i.length>0){let t=e.sub_division[0],a=yield this._HRDservice.subDivisionData,o=yield this._HRDservice.divisionData,r=d.where(a,{id:t}),l=d.uniq(d.pluck(d.where(a,{id:t})),"id");for(let s=0;s<l.length;s++){let t=d.where(o,{id:e.division[0]});for(let e=0;e<i.length;e++)this.subdata[i[e].dataType]=(null===(n=i[e])||void 0===n?void 0:n.dataTypeValue)||0;this.subdata.name=r[s].name+t[0].name,this.subdata.title=r[s].name,this.subdata.division=t[0].name,this.subDataSource.push(this.subdata);for(let e=0;e<this.data1.length;e++){let n=Object.keys(this.subdata);for(let i=0;i<n.length;i++){if(n[i]==this.data1[e].dataType){this.data1[e][r[s].name+t[0].name]=this.subdata[n[i]];break}this.data1[e][r[s].name+t[0].name]=0}}this.divgroup=[];let a=d.uniq(d.pluck(this.subDataSource,"division"));for(let e=0;e<a.length;e++){let t=d.where(this.subDataSource,{division:a[e]});this.divsubgroup=t[0].division,this.divisionvalues={division:this.divsubgroup,subdivision:t},this.divgroup.push(this.divisionvalues)}}}this.subdata={},this.isLoading=!1,this.isError=!1,this.getWidgetConfig()})),e=>{this.isLoading=!1,this.isError=!0})}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.title.toLowerCase()).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.titleConfig=(null===(t=this.config)||void 0===t?void 0:t.widget_lable)||this.title},e=>{this.isLoading=!1,this.isError=!0})}get summaryColumnConfig(){return[{column_name:"Associate Id",column_type:"text",data_field:"associate_id",text_overflow:!0},{column_name:"Employee Name",column_type:"text",data_field:"employee_name",text_overflow:!0,gutter_space:"5%"}]}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-data-grid-widget"]],inputs:{type:"type"},decls:16,vars:17,consts:[[3,"isVisible","template"],["openSummaryRef",""],["id","gridContainer",2,"margin-top","-20px",3,"dataSource","showBorders","columnAutoWidth","allowColumnReordering","allowFiltering","click"],[3,"visible"],["caption","Gender","fixedPosition","left","dataField","dataType",3,"fixed","width"],["caption","Total","fixedPosition","left","dataField","dataTypeValue",3,"fixed","width"],["alignment","center",3,"caption",4,"ngFor","ngForOf"],[3,"enabled"],["mode","single"],[2,"cursor","pointer","font-size","medium"],["alignment","center",3,"caption"],[3,"caption","width","dataField",4,"ngFor","ngForOf"],[3,"caption","width","dataField"]],template:function(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card"),c["\u0275\u0275elementStart"](1,"widget-title"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](3,"widget-action",0),c["\u0275\u0275template"](4,ut,2,0,"ng-template",null,1,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275elementStart"](6,"widget-body"),c["\u0275\u0275elementStart"](7,"dx-data-grid",2),c["\u0275\u0275listener"]("click",(function(){return t.openSummary()})),c["\u0275\u0275element"](8,"dxo-header-filter",3),c["\u0275\u0275element"](9,"dxi-column",4),c["\u0275\u0275element"](10,"dxi-column",5),c["\u0275\u0275template"](11,ft,2,2,"dxi-column",6),c["\u0275\u0275element"](12,"dxo-column-fixing",7),c["\u0275\u0275element"](13,"dxo-search-panel",3),c["\u0275\u0275element"](14,"dxo-selection",8),c["\u0275\u0275element"](15,"dxo-export",7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275reference"](5);c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("isVisible",!1)("template",e),c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("dataSource",t.data1)("showBorders",!0)("columnAutoWidth",!0)("allowColumnReordering",!0)("allowFiltering",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("fixed",!0)("width",100),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("fixed",!0)("width",100),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",t.divgroup),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("enabled",!0)}},directives:[ie,ne,b.a,v.Cc,v.g,i.NgForOf,v.vb,v.Md,v.Od,v.Sb,_.a],styles:[".options{padding:20px;margin-top:20px;background-color:hsla(0,0%,74.9%,.15)}  .caption{font-size:18px;font-weight:500}  .option{margin-top:10px}"]}),e})();function bt(e,t){if(1&e&&c["\u0275\u0275element"](0,"dxo-column",6),2&e){const e=t.$implicit;c["\u0275\u0275property"]("caption",e)("dataField",e)}}let vt=(()=>{class e{constructor(e,t){this._HRDservice=e,this.dialog=t,this.isLoading=!0,this.isError=!1,this.subs=new Xe.a,this.columns=[]}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.getWidgetConfig()}))}get title(){return"slareport"==this.type?"SLA Report In DataGrid":""}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.title).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_lable)||this.title},e=>{this.isLoading=!1,this.isError=!0})}initReport(){return Object(o.c)(this,void 0,void 0,(function*(){if("slareport"==this.type)try{let e=yield this._HRDservice.getISASLAreport().toPromise();"N"===e.err?(console.log(e.messText),this.data=e.data,this.columns=Object.keys(this.data[0]),this.isLoading=!1):(console.log("Response data rejected:",e.messText),this.isError=!0)}catch(e){console.log("Error occurred:",e),this.isError=!0}}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-sla-report-datagrid"]],inputs:{type:"type"},decls:13,vars:13,consts:[["id","gridContainer",2,"margin-top","-20px",3,"dataSource","showBorders","columnAutoWidth","allowColumnReordering","allowFiltering"],[3,"pageSize"],[3,"visible"],[3,"caption","dataField",4,"ngFor","ngForOf"],[3,"enabled"],["mode","single"],[3,"caption","dataField"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card"),c["\u0275\u0275elementStart"](1,"widget-title"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"widget-body"),c["\u0275\u0275elementStart"](4,"dx-data-grid",0),c["\u0275\u0275element"](5,"dxo-paging",1),c["\u0275\u0275element"](6,"dxo-header-filter",2),c["\u0275\u0275element"](7,"dxo-filter-row",2),c["\u0275\u0275template"](8,bt,1,2,"dxo-column",3),c["\u0275\u0275element"](9,"dxo-column-fixing",4),c["\u0275\u0275element"](10,"dxo-search-panel",2),c["\u0275\u0275element"](11,"dxo-selection",5),c["\u0275\u0275element"](12,"dxo-export",4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("dataSource",t.data)("showBorders",!0)("columnAutoWidth",!0)("allowColumnReordering",!0)("allowFiltering",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("pageSize",10),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",t.columns),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("enabled",!0))},directives:[ie,b.a,v.od,v.Cc,v.dc,i.NgForOf,v.vb,v.Md,v.Od,v.Sb],styles:[""]}),e})();function xt(e,t){if(1&e&&c["\u0275\u0275element"](0,"dxi-column",7),2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("dataField",e)("dataType",n.isDateColumn(e)?"date":"")("format",n.isDateColumn(e)?"dd-MM-yyyy":"")}}let _t=(()=>{class e{constructor(e,t){this._HRDservice=e,this.dialog=t,this.isLoading=!0,this.isError=!1,this.subs=new Xe.a,this.columns=[]}ngOnInit(){this.getWidgetConfig()}get title(){return"appraisal"==this.type?"Appraisal Due DataGrid":""}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.type.toLowerCase()).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_lable)||this.title},e=>{this.isLoading=!1,this.isError=!0})}isDateColumn(e){return["date_of_joining","appraisal_due_date"].includes(e)}initReport(){return Object(o.c)(this,void 0,void 0,(function*(){if("appraisal"==this.type)try{let e=yield this._HRDservice.getAppraisalDue().toPromise();"N"===e.err?(console.log(e.messText),this.data=e.data,this.columns=Object.keys(this.data[0]),this.isLoading=!1):(console.log("Response data rejected:",e.messText),this.isError=!0)}catch(e){console.log("Error occurred:",e),this.isError=!0}}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-appraisal-due-grid"]],inputs:{type:"type"},decls:13,vars:12,consts:[["id","gridContainer",2,"margin-top","-20px",3,"dataSource","showBorders","allowColumnReordering","allowFiltering"],[3,"pageSize"],["mode","select",3,"enabled"],[3,"visible"],[3,"dataField","dataType","format",4,"ngFor","ngForOf"],[3,"enabled"],["mode","single"],[3,"dataField","dataType","format"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card"),c["\u0275\u0275elementStart"](1,"widget-title"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"widget-body"),c["\u0275\u0275elementStart"](4,"dx-data-grid",0),c["\u0275\u0275element"](5,"dxo-paging",1),c["\u0275\u0275element"](6,"dxo-column-chooser",2),c["\u0275\u0275element"](7,"dxo-header-filter",3),c["\u0275\u0275template"](8,xt,1,3,"dxi-column",4),c["\u0275\u0275element"](9,"dxo-column-fixing",5),c["\u0275\u0275element"](10,"dxo-search-panel",3),c["\u0275\u0275element"](11,"dxo-selection",6),c["\u0275\u0275element"](12,"dxo-export",5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("dataSource",t.data)("showBorders",!0)("allowColumnReordering",!0)("allowFiltering",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("pageSize",10),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",t.columns),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("enabled",!0))},directives:[ie,b.a,v.od,v.tb,v.Cc,i.NgForOf,v.vb,v.Md,v.Od,v.Sb,v.g],styles:[".options{padding:20px;margin-top:20px;background-color:hsla(0,0%,74.9%,.15)}  .caption{font-size:18px;font-weight:500}  .option{margin-top:10px}  .dx-datagrid-headers .dx-datagrid-table .dx-row>td,   .dx-datagrid .dx-row-lines>td{text-align:center!important}",".custom-grid[_ngcontent-%COMP%] {\n            display: grid;\n            grid-template-columns: 30px repeat(auto-fit, minmax(100px, 1fr));\n            gap: 20px;\n        }"]}),e})();function Ct(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"button",8),c["\u0275\u0275text"](1),c["\u0275\u0275elementStart"](2,"mat-icon",9),c["\u0275\u0275text"](3,"arrow_down"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){c["\u0275\u0275nextContext"]();const e=c["\u0275\u0275reference"](2),t=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("matMenuTriggerFor",e),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",t.selected_item.name,"")}}function Et(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"button",10),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"](2).setSelectedItem(n)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function wt(e,t){if(1&e&&(c["\u0275\u0275template"](0,Ct,4,2,"button",5),c["\u0275\u0275elementStart"](1,"mat-menu",null,6),c["\u0275\u0275template"](3,Et,2,1,"button",7),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("ngIf",e.filterConfig),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngForOf",e.filterConfig)}}function St(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"dxi-series",11),c["\u0275\u0275element"](1,"dxo-label",12),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275propertyInterpolate"]("valueField",e.id),c["\u0275\u0275propertyInterpolate"]("name",e.name),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)}}function Dt(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"p"),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](e.value),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.argument)}}let Ot=(()=>{class e{constructor(e){this._HRDservice=e,this.title="Employees Hire By Month",this.isLoading=!0,this.isError=!1,this.this_fy_start=lt()().month(3).startOf("month"),this.this_fy_end=lt()().month(2).endOf("month").add(1,"year"),this.prev_fy_start=lt()(this.this_fy_start).subtract(1,"year"),this.prev_fy_end=lt()(this.this_fy_end).subtract(1,"year"),this.filterConfig=[{id:0,name:this.prev_fy_start.format("MMM YYYY")+" - "+this.prev_fy_end.format("MMM YYYY"),start_date:this.prev_fy_start.format("YYYY-MM-DD"),end_date:this.prev_fy_end.format("YYYY-MM-DD")},{id:1,name:this.this_fy_start.format("MMM YYYY")+" - "+this.this_fy_end.format("MMM YYYY"),start_date:this.this_fy_start.format("YYYY-MM-DD"),end_date:this.this_fy_end.format("YYYY-MM-DD")}]}ngOnInit(){this.getWidgetConfig()}setSelectedItem(e){this.selected_item=e,this.loadData()}getWidgetConfig(){this.isLoading=!0,this._HRDservice.getWidgetConfig(this.title).subscribe(e=>{var t;this.isLoading=!1,this.isError=!1,this.config=e.config,this.config=e.config,this.titleConfig=(null===(t=null==this?void 0:this.config)||void 0===t?void 0:t.widget_name)||this.title},e=>{this.isLoading=!1,this.isError=!0})}loadData(){this.isLoading=!0,this._HRDservice.getEmployeeHireDataByMonth(this.filterConfig).subscribe(e=>{this.data=e.data,this.attritions=e.attrition,this.isLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isError=!0})}initReport(){this.setSelectedItem(this.filterConfig[0])}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-hire-widget"]],decls:10,vars:5,consts:[["actionTemplate",""],["id","chart",3,"dataSource"],["argumentField","month","type","bar",3,"valueField","name",4,"ngFor","ngForOf"],["contentTemplate","content","zIndex","1",3,"enabled"],[4,"dxTemplate","dxTemplateOf"],["mat-button","",3,"matMenuTriggerFor",4,"ngIf"],["actionMenu","matMenu"],["mat-menu-item","",3,"click",4,"ngFor","ngForOf"],["mat-button","",3,"matMenuTriggerFor"],[2,"color","black"],["mat-menu-item","",3,"click"],["argumentField","month","type","bar",3,"valueField","name"],[3,"visible"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"app-widget-card"),c["\u0275\u0275template"](1,wt,4,2,"ng-template",null,0,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275elementStart"](3,"widget-title"),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"widget-body"),c["\u0275\u0275elementStart"](6,"dx-chart",1),c["\u0275\u0275template"](7,St,2,3,"dxi-series",2),c["\u0275\u0275element"](8,"dxo-tooltip",3),c["\u0275\u0275template"](9,Dt,6,2,"div",4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"](" ",t.titleConfig," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("dataSource",t.data),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",t.filterConfig),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!1),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","content"))},directives:[ie,ae.a,i.NgForOf,v.Ie,x.d,i.NgIf,Ze.g,Ke.a,Ze.f,_.a,Ze.d,v.E,v.Oc],styles:[""]}),e})();var It=n("wlhg"),Ft=n("3Pt+"),Mt=n("YhS8");function Pt(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"p"),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"p"),c["\u0275\u0275text"](7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](e.argument+e.value),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.point.series.name),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.percentText)}}const Rt=function(){return{applyLabel:"Apply",format:"DD-MM-YYYY"}};let Tt=(()=>{class e{constructor(e,t,n){this.fb=e,this._Exitservice=t,this._dialog=n,this.availableFiscalYears=["FY22","FY23","FY24"],this.graph="bench",this.allMonths=[],this.populationData=[],this.summaryData=[],this.filterForm=this.fb.group({dateFilter:[""],Filter:[""]}),this.dateRangePickerRanges={"This Month":[lt()().startOf("month"),lt()().endOf("month")],"Last Month":[lt()().subtract(1,"month").startOf("month"),lt()().subtract(1,"month").endOf("month")],"Next Month":[lt()().add(1,"month").startOf("month"),lt()().add(1,"month").endOf("month")],"Upcoming 3 Months":[lt()().startOf("month"),lt()().add(2,"month").endOf("month")],"Previous 3 Months":[lt()().subtract(3,"month").startOf("month"),lt()().subtract(1,"month").endOf("month")],"This Year":[lt()().startOf("year"),lt()().endOf("year")],"Previous Year":[lt()().subtract(1,"year").startOf("year"),lt()().subtract(1,"year").endOf("year")],"Current Financial Year":[lt()().month()<3?lt()().subtract(1,"year").startOf("year").month(3):lt()().startOf("year").month(3),lt()().month()<3?lt()().endOf("year").month(2):lt()().add(1,"year").endOf("year").month(2)],"Default Range":[lt()([1920,0,1]).startOf("year"),lt()([9999,11,31]).endOf("year")]},this.attritionOptions=[{value:"gender",label:"Exit by Gender"},{value:"experience",label:"Exit by Experience"},{value:"education",label:"Exit by Education"},{value:"bench",label:"Exit by Vol & Invol"}],this.months=["January","February","March","April","May","June","July","August","September","October","November","December"],this.widget_name="Allocation efficiency Report",this.selectedArgumentField="MONTH",this.subs=new Xe.a,this.customizeTooltip=e=>({text:Math.abs(e.valueText)}),this.customizeLabel=e=>{if(0==e.value)return{visible:!0,backgroundColor:"#FFFFFF",customizeText:e=>e.valueText+"&#000000"}},this.customizeText=e=>e.valueText+"&#000000"}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.initDashboard(),this.loadData()}))}openSummary(){let e=this.summaryData,t=this.graph;console.log(e),this._dialog.open(It.a,{data:{columnData:e,caption:"bench",graph:t},width:"75%",height:"75%"})}initDashboard(){return Object(o.c)(this,void 0,void 0,(function*(){this.filterForm.patchValue({Filter:this.attritionOptions[3].value,dateFilter:{startDate:this.dateRangePickerRanges["Current Financial Year"][0],endDate:this.dateRangePickerRanges["Current Financial Year"][1]}}),console.log(this.filterForm.value),console.log(this.filterForm.value.dateFilter),this.selectedDate=this.filterForm.value.dateFilter,console.log(this.selectedDate);const e=this.selectedDate.endDate._d,t=this.selectedDate.startDate._d,n=lt()(e),i=lt()(t);this.selectedDate=n.format("MMMM D, YYYY")+" - "+i.format("MMMM D, YYYY"),this.selectedDate=JSON.stringify(this.selectedDate).slice(1,-1),this.filterValue="bench",console.log(this.selectedDate),this.loadData()}))}onDateInputChange(e){this.selectedDate=e;const t=this.selectedDate.endDate._d,n=this.selectedDate.startDate._d,i=lt()(t),a=lt()(n);this.selectedDate=i.format("MMMM D, YYYY")+" - "+a.format("MMMM D, YYYY"),this.selectedDate=JSON.stringify(this.selectedDate).slice(1,-1),this.loadData()}onAttritionChange(e){return Object(o.c)(this,void 0,void 0,(function*(){this.filterValue=e.value,this.selectedArgumentField="gender"==this.filterValue||"bench"==this.filterValue?"MONTH":"education"==this.filterValue?"Degree":"Experience",yield this.loadData()}))}loadData(){let e=this.filterForm.get("dateFilter").value.startDate,t=this.filterForm.get("dateFilter").value.endDate;const n=e._d.getFullYear().toString(),i=(e._d.getMonth()+1).toString().padStart(2,"0"),a=e._d.getDate().toString().padStart(2,"0");t._d.getFullYear().toString(),(t._d.getMonth()+1).toString().padStart(2,"0"),t._d.getDate().toString().padStart(2,"0"),console.log([n,i,a]),console.log(e),this.subs.sink=this._Exitservice.getBenchCount(this.selectedDate,this.filterValue).subscribe(e=>{this.populationData=e.data,console.log(this.populationData)},e=>{console.log(e)}),this.subs.sink=this._Exitservice.getBenchCountBySummary(this.selectedDate,this.filterValue,this.graph).subscribe(e=>{this.summaryData=e.data,console.log(this.populationData)},e=>{console.log(e)})}labelCustomize(e){return e.point.isSelected()&&(e.text=e.seriesName,e.text+="\n\n\n\n\n"),e}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](Ft.i),c["\u0275\u0275directiveInject"](y),c["\u0275\u0275directiveInject"](s.b))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-bench-widget"]],decls:22,vars:12,consts:[[1,"row",2,"max-width","1158px"],[1,"container-fluid","total-activities-card3",2,"max-width","1158px"],[1,"title"],[3,"formGroup"],[2,"display","flex"],[2,"display","flex","align-items","center"],[1,"p-0","mr-2","range",2,"margin-left","520px"],["type","text","ngxDaterangepickerMd","","formControlName","dateFilter","name","daterange","placeholder","Select Date",1,"dp-class",3,"showCustomRangeLabel","locale","ranges","change"],[1,"charts-container",3,"click"],["id","chart",3,"dataSource"],["valueField","Allocated","name","Allocated"],[3,"visible"],["valueField","NonAllocated","name","Non-Allocated"],["argumentField","sub_division_name","type","bar"],["verticalAlignment","bottom","horizontalAlignment","center"],["contentTemplate","content","zIndex","1",3,"enabled"],[4,"dxTemplate","dxTemplateOf"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"mat-card",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"p"),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"form",3),c["\u0275\u0275elementStart"](6,"div",4),c["\u0275\u0275elementStart"](7,"div"),c["\u0275\u0275elementStart"](8,"div",5),c["\u0275\u0275elementStart"](9,"div",6),c["\u0275\u0275elementStart"](10,"input",7),c["\u0275\u0275listener"]("change",(function(e){return t.onDateInputChange(e)})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](11,"div",8),c["\u0275\u0275listener"]("click",(function(){return t.openSummary()})),c["\u0275\u0275elementStart"](12,"dx-chart",9),c["\u0275\u0275elementStart"](13,"dxi-series",10),c["\u0275\u0275element"](14,"dxo-label",11),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](15,"dxi-series",12),c["\u0275\u0275element"](16,"dxo-label",11),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](17,"dxo-common-series-settings",13),c["\u0275\u0275element"](18,"dxo-label",11),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](19,"dxo-legend",14),c["\u0275\u0275element"](20,"dxo-tooltip",15),c["\u0275\u0275template"](21,Pt,8,3,"div",16),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate"](t.widget_name),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("formGroup",t.filterForm),c["\u0275\u0275advance"](5),c["\u0275\u0275property"]("showCustomRangeLabel",!0)("locale",c["\u0275\u0275pureFunction0"](11,Rt))("ranges",t.dateRangePickerRanges),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("dataSource",t.populationData),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("dxTemplateOf","content"))},directives:[V.a,Ft.J,Ft.w,Ft.n,Mt.b,Ft.e,Ft.v,Ft.l,ae.a,v.E,v.Oc,v.Bb,v.Qc,v.Ie,x.d],styles:[".total-activities-card3[_ngcontent-%COMP%]{flex-shrink:0;border-radius:2px;background:var(--neutral-white,#fff);margin-bottom:10px;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card3[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;display:flex;justify-content:space-between;width:100%;line-height:16px;letter-spacing:.02em;padding:0!important;color:#45546e}.total-activities-card3[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{white-space:nowrap;overflow:visible;text-overflow:unset;margin:0}.total-activities-card3[_ngcontent-%COMP%]   .dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #bf3606;border-radius:4px;font-size:12px;font-weight:500;margin-left:50px;height:40px;margin-top:4px;cursor:pointer;text-align:center}.total-activities-card3[_ngcontent-%COMP%]   .bordered-select[_ngcontent-%COMP%]{border:1px solid #ccc;border-radius:4px;padding:8px}.total-activities-card3[_ngcontent-%COMP%]     .mat-form-field.mat-focused .mat-form-field-label{color:#bf3606!important}.total-activities-card3[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline{color:#bf3606 1px solid!important;color:#bf3606!important}.total-activities-card3[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]:not(.mat-form-field-disabled)   .mat-form-field-flex[_ngcontent-%COMP%]:hover   .mat-form-field-outline[_ngcontent-%COMP%]{border:.2px solid #bf3606;opacity:1}.total-activities-card3[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]:not(.mat-form-field-disabled)   .mat-form-field-flex[_ngcontent-%COMP%]:hover   .mat-form-field-outline[_ngcontent-%COMP%]   .mat-form-field-outline-end[_ngcontent-%COMP%], .total-activities-card3[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]:not(.mat-form-field-disabled)   .mat-form-field-flex[_ngcontent-%COMP%]:hover   .mat-form-field-outline[_ngcontent-%COMP%]   .mat-form-field-outline-gap[_ngcontent-%COMP%], .total-activities-card3[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]:not(.mat-form-field-disabled)   .mat-form-field-flex[_ngcontent-%COMP%]:hover   .mat-form-field-outline[_ngcontent-%COMP%]   .mat-form-field-outline-start[_ngcontent-%COMP%]{border:none}.total-activities-card3[_ngcontent-%COMP%]   .md-datepicker-input-container[_ngcontent-%COMP%]{width:300px}.total-activities-card3[_ngcontent-%COMP%]    .mat-form-field-flex{width:80%}  .md-drppicker.drops-down-auto.ltr.shown.double.show-ranges{left:409.5px!important}.range[_ngcontent-%COMP%]    .md-drppicker.ltr{top:19px!important}"]}),e})();var Lt=n("cclQ"),jt=n("xJfa"),kt=n("EIvR"),Yt=n("WlQZ");let At=(()=>{class e{constructor(e){this._hrDashboardService=e,this.subs=new Xe.a}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.loadData()}))}loadData(){return Object(o.c)(this,void 0,void 0,(function*(){this.subs.sink=this._hrDashboardService.getSunBurstCount().subscribe(e=>{console.log(e.data),this.data=(null==e?void 0:e.data)||[]},e=>{console.log(e),this.data=[]})}))}ngAfterViewInit(){return Object(o.c)(this,void 0,void 0,(function*(){console.log("Sunburst chart"),yield this.loadData(),Lt.useTheme(Yt.default);const e=Lt.create("chartContainer",kt.a);this.subs.sink=this._hrDashboardService.getSunBurstCount().subscribe(t=>{console.log(t.data),e.data=(null==t?void 0:t.data)||[]},e=>{console.log(e),this.data=[]}),e.dataFields.value="value",e.dataFields.name="name",e.dataFields.children="children",e.dataFields.color="color";let t=e.seriesTemplates.create("0");t.labels.template.truncate=!0,t.labels.template.hideOversized=!0,t.labels.template.text="{name}",t.labels.template.fontSize=11,t.labels.template.fontWeight="bold",t.labels.template.maxWidth=100;let n=e.seriesTemplates.create("1");n.labels.template.truncate=!0,n.labels.template.hideOversized=!0,n.labels.template.text="{name}",n.slices.template.fillOpacity=.8,n.labels.template.fontSize=11,n.labels.template.fontWeight="bold",n.labels.template.maxWidth=100;let i=e.seriesTemplates.create("2");i.labels.template.truncate=!0,i.labels.template.hideOversized=!0,i.labels.template.text="{name}",i.slices.template.fillOpacity=.7,i.labels.template.fontSize=11,i.labels.template.fontWeight="bold",i.labels.template.maxWidth=100;let a=e.seriesTemplates.create("3");a.labels.template.truncate=!0,a.labels.template.hideOversized=!0,a.labels.template.text="{name}",a.slices.template.fillOpacity=.6,a.labels.template.fontSize=11,a.labels.template.fontWeight="bold",a.labels.template.maxWidth=100;let o=e.seriesTemplates.create("4");o.labels.template.truncate=!0,o.labels.template.hideOversized=!0,o.labels.template.text="{name}",o.slices.template.fillOpacity=.5,o.labels.template.fontSize=11,o.labels.template.fontWeight="bold",o.labels.template.maxWidth=100;let r=e.seriesTemplates.create("5");r.labels.template.truncate=!0,r.labels.template.hideOversized=!0,r.labels.template.text="{name}",r.slices.template.fillOpacity=.5,r.labels.template.fontSize=11,r.labels.template.fontWeight="bold",r.labels.template.maxWidth=100;const l=e.series.push(new kt.b);l.dataFields.value="value",l.labels.template.adapter.add("text",(function(e,t){return t.dataItem&&t.dataItem.value<8?"":e})),l.colors.step=2;const s=l.bullets.push(new jt.LabelBullet);console.log(s),s.locationX=.5,s.locationY=.5,s.label.text="{value}",s.label.fontSize=11,s.label.hideOversized=!0,s.label.truncate=!0,l.ticks.template.disabled=!0,l.alignLabels=!1,l.labels.template.fontSize=11,l.labels.template.fontWeight="bold",l.labels.template.maxWidth=100,l.labels.template.truncate=!0,l.labels.template.hideOversized=!0,l.maxLevels=4,l.radius=Lt.percent(90),l.innerRadius=Lt.percent(20)}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](y))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-hr-sun-burst"]],decls:7,vars:0,consts:[[1,"chart-title","pb-3"],[2,"position","relative"],["id","chartContainer",2,"width","100%","height","1000px"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"mat-card"),c["\u0275\u0275elementStart"](1,"mat-card-header",0),c["\u0275\u0275text"](2,"Employee Head Count By Organization"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](3,"mat-divider",1),c["\u0275\u0275elementStart"](4,"mat-card-content"),c["\u0275\u0275elementStart"](5,"div"),c["\u0275\u0275element"](6,"div",2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]())},directives:[V.a,V.c,V.b],styles:["body[_ngcontent-%COMP%]{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol}#chartdiv[_ngcontent-%COMP%]{width:100%;height:700px}"]}),e})();var Bt=n("jtHE"),zt=n("NJ67"),Vt=n("kmnG"),Nt=n("d3UM"),Wt=n("FKr1"),Gt=n("WJ5W");const Ht=["allSelected"],Qt=["singleSelect"];function $t(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"mat-option",5),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275property"]("value",e.id),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let qt=(()=>{class e extends zt.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new Ft.j,this.fieldFilterCtrl=new Ft.j,this.list=[],this.required=!1,this.valueChange=new c.EventEmitter,this.disabled=!1,this.filteredList=new Bt.a,this._onDestroy=new m.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(g.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(g.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](c.Renderer2))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(c["\u0275\u0275viewQuery"](Ht,!0),c["\u0275\u0275viewQuery"](Qt,!0)),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[c["\u0275\u0275ProvidersFeature"]([{provide:Ft.t,useExisting:Object(c.forwardRef)(()=>e),multi:!0}]),c["\u0275\u0275InheritDefinitionFeature"],c["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:10,consts:[["appearance","outline",2,"width","100%"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"mat-form-field",0),c["\u0275\u0275elementStart"](1,"mat-label"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"mat-select",1,2),c["\u0275\u0275elementStart"](5,"mat-option"),c["\u0275\u0275element"](6,"ngx-mat-select-search",3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](7,$t,2,2,"mat-option",4),c["\u0275\u0275pipe"](8,"async"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](t.placeholder),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",c["\u0275\u0275pipeBind1"](8,8,t.filteredList)))},directives:[Vt.c,Vt.g,Nt.c,Ft.v,Ft.k,Ft.F,Wt.p,Gt.a,i.NgForOf],pipes:[i.AsyncPipe],styles:[""]}),e})();const Jt=function(){return{applyLabel:"Apply",format:"DD-MM-YYYY"}};function Ut(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"form",30),c["\u0275\u0275elementStart"](1,"div",31),c["\u0275\u0275elementStart"](2,"input",32),c["\u0275\u0275listener"]("change",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onDateInputChange(t)})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"div",33),c["\u0275\u0275element"](4,"app-multi-select-search2",34),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"div",33),c["\u0275\u0275element"](6,"app-multi-select-search2",35),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div",33),c["\u0275\u0275element"](8,"app-multi-select-search2",36),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"div",33),c["\u0275\u0275element"](10,"app-multi-select-search2",37),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("formGroup",e.filterForm),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("showCustomRangeLabel",!0)("locale",c["\u0275\u0275pureFunction0"](8,Jt))("ranges",e.dateRangePickerRanges),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("list",e.entityData),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("list",e.divisionData),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("list",e.subDivisionData),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("list",e.employmentTypeData)}}function Xt(e,t){1&e&&c["\u0275\u0275element"](0,"head-count-widget",38)}function Kt(e,t){1&e&&c["\u0275\u0275element"](0,"head-count-widget",39)}function Zt(e,t){1&e&&c["\u0275\u0275element"](0,"doughnut-chart-widget",40)}function en(e,t){if(1&e&&c["\u0275\u0275element"](0,"exit-widget",41),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("filterDateRange",e.selectedDate)}}function tn(e,t){1&e&&c["\u0275\u0275element"](0,"doughnut-chart-widget",42)}function nn(e,t){1&e&&c["\u0275\u0275element"](0,"doughnut-chart-widget",43)}function an(e,t){1&e&&c["\u0275\u0275element"](0,"doughnut-chart-widget",44)}function on(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",45)}function rn(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",46)}function ln(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",47)}function sn(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",48)}function dn(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",49)}function pn(e,t){if(1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",50),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("filterDateRange",e.selectedDate)}}function cn(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",51)}function mn(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",52)}function gn(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",53)}function un(e,t){1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",54)}function hn(e,t){if(1&e&&c["\u0275\u0275element"](0,"bar-chart-widget",55),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("filterDateRange",e.selectedDate)}}function fn(e,t){1&e&&c["\u0275\u0275element"](0,"exit-by-month-widget",56)}function yn(e,t){1&e&&c["\u0275\u0275element"](0,"app-hire-widget",57)}function bn(e,t){1&e&&c["\u0275\u0275element"](0,"app-bench-widget",58)}function vn(e,t){1&e&&c["\u0275\u0275element"](0,"app-data-grid-widget",59)}function xn(e,t){1&e&&c["\u0275\u0275element"](0,"app-sla-report-datagrid",60)}function _n(e,t){1&e&&c["\u0275\u0275element"](0,"app-appraisal-due-grid",61)}const Cn=[{path:"",component:(()=>{class e{constructor(e,t){this.fb=e,this._hrdService=t,this.subs=new Xe.a,this.isLoading=!0,this.filterForm=this.fb.group({entity:["",l.e.required],division:["",l.e.required],sub_division:["",l.e.required],employment_type:[""],dateFilter:[""]}),this.widgetConfig={},this.entityData=[],this.divisionData=[],this.employmentTypeData=[],this.subDivisionData=[],this.dateRangePickerRanges={"This Month":[lt()().startOf("month"),lt()().endOf("month")],"Last Month":[lt()().subtract(1,"month").startOf("month"),lt()().subtract(1,"month").endOf("month")],"Next Month":[lt()().add(1,"month").startOf("month"),lt()().add(1,"month").endOf("month")],"Upcoming 3 Months":[lt()().startOf("month"),lt()().add(2,"month").endOf("month")],"Previous 3 Months":[lt()().subtract(3,"month").startOf("month"),lt()().subtract(1,"month").endOf("month")],"This Year":[lt()().startOf("year"),lt()().endOf("year")],"Previous Year":[lt()().subtract(1,"year").startOf("year"),lt()().subtract(1,"year").endOf("year")],"Current Financial Year":[lt()().month()<3?lt()().subtract(1,"year").startOf("year").month(3):lt()().startOf("year").month(3),lt()().month()<3?lt()().endOf("year").month(2):lt()().add(1,"year").endOf("year").month(2)],"Default Range":[lt()("1920-01-01").startOf("year"),lt()("9999-12-31").endOf("year")]}}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.handleWidgetVisibility(),yield this.initDashboard()}))}handleWidgetVisibility(){return Object(o.c)(this,void 0,void 0,(function*(){let e=yield this.getAllDashboardConfig();e.length>0&&e.forEach(e=>{this.widgetConfig[e.widget_term]={isActive:e.is_active?1:0}})}))}getAllDashboardConfig(){return new Promise((e,t)=>{this.subs.sink=this._hrdService.getAllDashboardConfig().subscribe(n=>{n.err?(console.log("Response data rejected",n),t(n)):(console.log(n),e(n.config))},e=>{console.log(e),t(e)})})}initDashboard(){return Object(o.c)(this,void 0,void 0,(function*(){this.isLoading=!0,yield this._hrdService.getDashboardFitlers(),this.entityData=this._hrdService.entityData,this.divisionData=this._hrdService.divisionData,this.subDivisionData=this._hrdService.subDivisionData,this.employmentTypeData=this._hrdService.employmentTypeData,this.filterForm.patchValue({entity:[...this.entityData.map(e=>e.id)],division:[...this.divisionData.map(e=>e.id)],sub_division:[...this.subDivisionData.map(e=>e.id)],employment_type:[...this.employmentTypeData.map(e=>e.id)],dateFilter:{startDate:this.dateRangePickerRanges["Default Range"][0],endDate:this.dateRangePickerRanges["Default Range"][1]}}),this._hrdService.setDateFilter(this.filterForm.get("dateFilter").value),yield this.onDateInputChange(),this.initUserRole()}))}onDateInputChange(e){console.log("onDateInputChange"),console.log(e),this.selectedDate=this.filterForm.get("dateFilter").value,this.selectedDate=this.selectedDate.startDate.format("MMMM D, YYYY")+" - "+this.selectedDate.endDate.format("MMMM D, YYYY"),this.selectedDate=JSON.stringify(this.selectedDate).slice(1,-1)}initUserRole(){"user"==this._hrdService.userRole&&(this.filterForm.get("entity").enable(),this.filterForm.get("division").disable(),this.filterForm.get("sub_division").enable(),this.filterForm.get("entity").valueChanges.pipe(Object(r.a)(1e3)).subscribe(e=>{this._hrdService._filterChanged.next(),this.filterForm.valid&&(null==e?void 0:e.length)>0&&(this.initReport(),this.subDivisionData=this._hrdService.getSubDivision(this.filterForm.get("entity").value,this.filterForm.get("division").value),console.log(this.subDivisionData),this.enableDivision())}),this.filterForm.get("sub_division").valueChanges.pipe(Object(r.a)(1e3)).subscribe(e=>{this._hrdService._filterChanged.next(),this.filterForm.valid&&(null==e?void 0:e.length)>0&&this.initReport()})),"admin"==this._hrdService.userRole&&(Object.keys(this.filterForm.controls).map(e=>this.filterForm.get(e).enable()),this.filterForm.get("entity").valueChanges.pipe(Object(r.a)(1e3)).subscribe(e=>{this._hrdService._filterChanged.next(),this.refreshEntity(),(null==e?void 0:e.length)>0&&(this.initReport(),this.divisionData=this._hrdService.getDivision(e),this.enableEntity())}),this.filterForm.get("division").valueChanges.pipe(Object(r.a)(1e3)).subscribe(e=>{this._hrdService._filterChanged.next(),this.refreshDivision(),this.initReport(),(null==e?void 0:e.length)>0&&(this.subDivisionData=this._hrdService.getSubDivision(this.filterForm.get("entity").value,e),this.enableDivision())}),this.filterForm.get("sub_division").valueChanges.pipe(Object(r.a)(1e3)).subscribe(e=>{this._hrdService._filterChanged.next(),this.initReport()}),this.filterForm.get("dateFilter").valueChanges.pipe(Object(r.a)(1e3)).subscribe(e=>{console.log(e),this._hrdService.setDateFilter(e),this._hrdService._filterChanged.next(),this.initReport()}),this.filterForm.get("employment_type").valueChanges.pipe(Object(r.a)(1e3)).subscribe(e=>{this._hrdService._filterChanged.next(),this.initReport()})),this.isLoading=!1,this.initReport()}refreshEntity(){this.filterForm.get("division").reset(),this.filterForm.get("sub_division").reset(),this.filterForm.get("division").disable(),this.filterForm.get("sub_division").disable()}refreshDivision(){this.filterForm.get("sub_division").reset(),this.filterForm.get("sub_division").disable()}enableEntity(){this.filterForm.get("division").reset(),this.filterForm.get("division").enable(),this.filterForm.get("sub_division").reset(),this.filterForm.get("sub_division").disable()}enableuserEntity(){this.filterForm.get("division").disable(),this.filterForm.get("sub_division").reset()}refreshuserEntity(){this.filterForm.get("division").disable(),this.filterForm.get("sub_division").reset()}enableDivision(){this.filterForm.get("sub_division").reset(),this.filterForm.get("sub_division").enable()}initReport(){this._hrdService.filter={entity:this.filterForm.get("entity").value,division:this.filterForm.get("division").value,sub_division:this.filterForm.get("sub_division").value,employment_type:this.filterForm.get("employment_type").value,start_date:this.filterForm.get("dateFilter").value.startDate,end_date:this.filterForm.get("dateFilter").value.endDate},this.bar_chart_widget.forEach(e=>e.initReport()),this.doughnut_chart_widget.forEach(e=>e.initReport()),this.head_count_widget.forEach(e=>e.initReport()),this.exit_by_month.forEach(e=>e.initReport()),this.data_grid_widget.forEach(e=>e.initReport()),this.sla_grid_widget.forEach(e=>e.initReport()),this.exit_widget.forEach(e=>e.initReport()),this.hire_widget.forEach(e=>e.initReport()),this.appraisal_due_grid.forEach(e=>e.initReport())}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](Ft.i),c["\u0275\u0275directiveInject"](y))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-hr-dashboard"]],viewQuery:function(e,t){if(1&e&&(c["\u0275\u0275viewQuery"](Ye,!0),c["\u0275\u0275viewQuery"](Ge,!0),c["\u0275\u0275viewQuery"](Ue,!0),c["\u0275\u0275viewQuery"](ot,!0),c["\u0275\u0275viewQuery"](gt,!0),c["\u0275\u0275viewQuery"](yt,!0),c["\u0275\u0275viewQuery"](vt,!0),c["\u0275\u0275viewQuery"](_t,!0),c["\u0275\u0275viewQuery"](Ot,!0),c["\u0275\u0275viewQuery"](Tt,!0),c["\u0275\u0275viewQuery"](At,!0)),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.bar_chart_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.doughnut_chart_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.head_count_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.exit_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.exit_by_month=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.data_grid_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.sla_grid_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.appraisal_due_grid=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.hire_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.bench_widget=e),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.sun_burstt=e)}},decls:31,vars:25,consts:[[1,"container-fluid","hr-dashboard-styles","pl-0","pr-0"],[1,"pb-4"],[1,"container-fluid","align-items-center","row","p-0","m-0","mt-1"],[1,"mb-0","ml-2","mr-2",2,"font-size","14px","font-weight","450","color","#45546e","line-height","45px"],["class","col-9 filterRow p-0",3,"formGroup",4,"ngIf"],[1,"row","container-fluid"],["type","employment type","class","col-6 p-0 pb-4",4,"ngIf"],["type","work location","class","col-6 p-0 pb-4",4,"ngIf"],["type","vaccination","class","col-6 p-0 pb-4",4,"ngIf"],["class","col-6 p-0 pb-4",3,"filterDateRange",4,"ngIf"],["type","gender","class","col-6 p-0 pb-4",4,"ngIf"],["type","generation","class","col-6 p-0 pb-4",4,"ngIf"],["type","experience","class","col-6 p-0 pb-4",4,"ngIf"],["type","project not mapped","class","col-6 p-0 pb-4",4,"ngIf"],["type","generation","class","col-12 p-0 pb-4",4,"ngIf"],["type","experience fresher","class","col-12 p-0 pb-4",4,"ngIf"],["type","experience Lateral","class","col-12 p-0 pb-4",4,"ngIf"],["type","employee exact experience","class","col-12 p-0 pb-4",4,"ngIf"],["type","employee exit data","class","col-12 p-0 pb-4",3,"filterDateRange",4,"ngIf"],["type","Sum Insured","class","col-12 p-0 pb-4",4,"ngIf"],["type","Self Insured","class","col-12 p-0 pb-4",4,"ngIf"],["type","ID Proof","class","col-12 p-0 pb-4",4,"ngIf"],["type","Certification Type","class","col-12 p-0 pb-4",4,"ngIf"],["type","employee exit type","class","col-12 p-0 pb-4",3,"filterDateRange",4,"ngIf"],["class","col-12 p-0 pb-4",4,"ngIf"],["type","Employees Hire By Month","class","col-12 p-0 pb-4",4,"ngIf"],["type","Employees Bench Report","class","col-12 p-0 pb-4",4,"ngIf"],["type","gender","class","col-12 p-0 pb-4",4,"ngIf"],["type","slareport","class","col-12 p-0 pb-4",4,"ngIf"],["type","appraisal","class","col-12 p-0 pb-4",4,"ngIf"],[1,"col-9","filterRow","p-0",3,"formGroup"],[1,"p-0","mr-1"],["type","text","ngxDaterangepickerMd","","formControlName","dateFilter","name","daterange","placeholder","Select Date",1,"dp-class",3,"showCustomRangeLabel","locale","ranges","change"],[1,"p-0","mr-2"],["formControlName","entity","placeholder","Entity",3,"list"],["formControlName","division","placeholder","Division",3,"list"],["formControlName","sub_division","placeholder","Sub Division",3,"list"],["formControlName","employment_type","placeholder","Emloyment Type",3,"list"],["type","employment type",1,"col-6","p-0","pb-4"],["type","work location",1,"col-6","p-0","pb-4"],["type","vaccination",1,"col-6","p-0","pb-4"],[1,"col-6","p-0","pb-4",3,"filterDateRange"],["type","gender",1,"col-6","p-0","pb-4"],["type","generation",1,"col-6","p-0","pb-4"],["type","experience",1,"col-6","p-0","pb-4"],["type","project not mapped",1,"col-6","p-0","pb-4"],["type","generation",1,"col-12","p-0","pb-4"],["type","experience fresher",1,"col-12","p-0","pb-4"],["type","experience Lateral",1,"col-12","p-0","pb-4"],["type","employee exact experience",1,"col-12","p-0","pb-4"],["type","employee exit data",1,"col-12","p-0","pb-4",3,"filterDateRange"],["type","Sum Insured",1,"col-12","p-0","pb-4"],["type","Self Insured",1,"col-12","p-0","pb-4"],["type","ID Proof",1,"col-12","p-0","pb-4"],["type","Certification Type",1,"col-12","p-0","pb-4"],["type","employee exit type",1,"col-12","p-0","pb-4",3,"filterDateRange"],[1,"col-12","p-0","pb-4"],["type","Employees Hire By Month",1,"col-12","p-0","pb-4"],["type","Employees Bench Report",1,"col-12","p-0","pb-4"],["type","gender",1,"col-12","p-0","pb-4"],["type","slareport",1,"col-12","p-0","pb-4"],["type","appraisal",1,"col-12","p-0","pb-4"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"p",3),c["\u0275\u0275text"](4," Filters: "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](5,Ut,11,9,"form",4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",5),c["\u0275\u0275template"](7,Xt,1,0,"head-count-widget",6),c["\u0275\u0275template"](8,Kt,1,0,"head-count-widget",7),c["\u0275\u0275template"](9,Zt,1,0,"doughnut-chart-widget",8),c["\u0275\u0275template"](10,en,1,1,"exit-widget",9),c["\u0275\u0275template"](11,tn,1,0,"doughnut-chart-widget",10),c["\u0275\u0275template"](12,nn,1,0,"doughnut-chart-widget",11),c["\u0275\u0275template"](13,an,1,0,"doughnut-chart-widget",12),c["\u0275\u0275template"](14,on,1,0,"bar-chart-widget",13),c["\u0275\u0275template"](15,rn,1,0,"bar-chart-widget",14),c["\u0275\u0275template"](16,ln,1,0,"bar-chart-widget",15),c["\u0275\u0275template"](17,sn,1,0,"bar-chart-widget",16),c["\u0275\u0275template"](18,dn,1,0,"bar-chart-widget",17),c["\u0275\u0275template"](19,pn,1,1,"bar-chart-widget",18),c["\u0275\u0275template"](20,cn,1,0,"bar-chart-widget",19),c["\u0275\u0275template"](21,mn,1,0,"bar-chart-widget",20),c["\u0275\u0275template"](22,gn,1,0,"bar-chart-widget",21),c["\u0275\u0275template"](23,un,1,0,"bar-chart-widget",22),c["\u0275\u0275template"](24,hn,1,1,"bar-chart-widget",23),c["\u0275\u0275template"](25,fn,1,0,"exit-by-month-widget",24),c["\u0275\u0275template"](26,yn,1,0,"app-hire-widget",25),c["\u0275\u0275template"](27,bn,1,0,"app-bench-widget",26),c["\u0275\u0275template"](28,vn,1,0,"app-data-grid-widget",27),c["\u0275\u0275template"](29,xn,1,0,"app-sla-report-datagrid",28),c["\u0275\u0275template"](30,_n,1,0,"app-appraisal-due-grid",29),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](5),c["\u0275\u0275property"]("ngIf",!t.isLoading),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.employment_type?null:t.widgetConfig.employment_type.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.work_location?null:t.widgetConfig.work_location.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.vaccination_count?null:t.widgetConfig.vaccination_count.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.exit_employee_widget?null:t.widgetConfig.exit_employee_widget.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.gender_distribution_percentage?null:t.widgetConfig.gender_distribution_percentage.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.generation_distribution_percentage?null:t.widgetConfig.generation_distribution_percentage.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.experience?null:t.widgetConfig.experience.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.project_not_mapped?null:t.widgetConfig.project_not_mapped.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.generation_by_department?null:t.widgetConfig.generation_by_department.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.fresher_experience?null:t.widgetConfig.fresher_experience.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.lateral_experience?null:t.widgetConfig.lateral_experience.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.employee_exact_experience?null:t.widgetConfig.employee_exact_experience.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.employee_exit_data?null:t.widgetConfig.employee_exit_data.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.sum_insured?null:t.widgetConfig.sum_insured.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.self_insured?null:t.widgetConfig.self_insured.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.id_proof?null:t.widgetConfig.id_proof.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.certificate_details?null:t.widgetConfig.certificate_details.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.employee_exit_type?null:t.widgetConfig.employee_exit_type.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.exit_month_employee?null:t.widgetConfig.exit_month_employee.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.hire_month_employee?null:t.widgetConfig.hire_month_employee.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.employee_bench_report?null:t.widgetConfig.employee_bench_report.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.gender_grid?null:t.widgetConfig.gender_grid.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.sla_report?null:t.widgetConfig.sla_report.isActive),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",null==t.widgetConfig||null==t.widgetConfig.appraisal_due?null:t.widgetConfig.appraisal_due.isActive))},directives:[i.NgIf,Ft.J,Ft.w,Ft.n,Mt.b,Ft.e,Ft.v,Ft.l,qt,Ue,Ge,ot,Ye,gt,Ot,Tt,yt,vt,_t],styles:[".hr-dashboard-styles[_ngcontent-%COMP%]     .udrf-header-card{background-color:#fafafa!important}.hr-dashboard-styles[_ngcontent-%COMP%]     .mat-form-field-infix{display:block;position:relative;flex:auto;min-width:0;width:180px;margin-top:5px}.hr-dashboard-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.hr-dashboard-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.hr-dashboard-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:395px;overflow-y:scroll}.hr-dashboard-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:345px;overflow-y:scroll}.hr-dashboard-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:30px;padding:0 25px;border-radius:0!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .hr-dashboard-styles[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.hr-dashboard-styles[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{color:#4e4c4c;font-weight:400!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.hr-dashboard-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.hr-dashboard-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.hr-dashboard-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.hr-dashboard-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.hr-dashboard-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:20px}.hr-dashboard-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px}.hr-dashboard-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .hr-dashboard-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.hr-dashboard-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px}.hr-dashboard-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .col-2-2[_ngcontent-%COMP%]{flex:0 0 11%;max-width:11%}.hr-dashboard-styles[_ngcontent-%COMP%]   .col-2-8[_ngcontent-%COMP%]{flex:0 0 21.666667%;max-width:21.666667%}.hr-dashboard-styles[_ngcontent-%COMP%]   .alignCenter[_ngcontent-%COMP%]{margin:0 auto;display:block}.hr-dashboard-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.hr-dashboard-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .hr-dashboard-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.hr-dashboard-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.hr-dashboard-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.hr-dashboard-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.hr-dashboard-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.hr-dashboard-styles[_ngcontent-%COMP%]   .dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;width:100%!important;height:40px;margin-top:4px;cursor:pointer;text-align:center}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.hr-dashboard-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.cp[_ngcontent-%COMP%]{cursor:pointer!important}.value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.dialogAninmation[_ngcontent-%COMP%]{height:300px;width:300px;animation:slide-in .5s}@keyframes slide-in{0%{transform:translateY(-100%)}to{transform:translateY(0)}}.filterRow[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr 1fr!important}"]}),e})()}];let En=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(Cn)],a.k]}),e})();var wn=n("Xi0T");let Sn=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,wn.a,En,Gt.b,Nt.d,Vt.e,Ke.b,ae.b,V.d,Ft.E,Ft.p,s.g,Ze.e,_.b,Ae.b,b.b,Mt.c.forRoot()]]}),e})()},WlQZ:function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return a}));var i=n("hM+/"),a=function(e){Object(i.b)(e,"SpriteState")&&(e.transitionDuration=400),Object(i.b)(e,"Component")&&(e.rangeChangeDuration=500,e.interpolationDuration=500,e.sequencedInterpolation=!1,Object(i.b)(e,"SankeyDiagram")&&(e.sequencedInterpolation=!0),Object(i.b)(e,"FunnelSeries")&&(e.sequencedInterpolation=!0)),Object(i.b)(e,"Chart")&&(e.defaultState.transitionDuration=2e3,e.hiddenState.transitionDuration=1e3),Object(i.b)(e,"Tooltip")&&(e.animationDuration=400,e.defaultState.transitionDuration=400,e.hiddenState.transitionDuration=400),Object(i.b)(e,"Scrollbar")&&(e.animationDuration=500),Object(i.b)(e,"Series")&&(e.defaultState.transitionDuration=1e3,e.hiddenState.transitionDuration=700,e.hiddenState.properties.opacity=1,e.showOnInit=!0),Object(i.b)(e,"MapSeries")&&(e.hiddenState.properties.opacity=0),Object(i.b)(e,"PercentSeries")&&(e.hiddenState.properties.opacity=0),Object(i.b)(e,"FunnelSlice")&&(e.defaultState.transitionDuration=800,e.hiddenState.transitionDuration=1e3,e.hiddenState.properties.opacity=1),Object(i.b)(e,"Slice")&&(e.defaultState.transitionDuration=700,e.hiddenState.transitionDuration=1e3,e.hiddenState.properties.opacity=1),Object(i.b)(e,"Preloader")&&(e.hiddenState.transitionDuration=2e3),Object(i.b)(e,"Column")&&(e.defaultState.transitionDuration=700,e.hiddenState.transitionDuration=1e3,e.hiddenState.properties.opacity=1),Object(i.b)(e,"Column3D")&&(e.hiddenState.properties.opacity=0)}},wlhg:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n("0IaG"),a=n("fXoL"),o=n("1A3m"),r=n("ofXK"),l=n("NFeN"),s=n("ZzPI"),d=n("6t9p");function p(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",1),a["\u0275\u0275elementStart"](1,"div",2),a["\u0275\u0275elementStart"](2,"div",3),a["\u0275\u0275text"](3,"Summary"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",4),a["\u0275\u0275elementStart"](5,"mat-icon",5),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onDialogClose()})),a["\u0275\u0275text"](6,"clear"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",6),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"dx-data-grid",7),a["\u0275\u0275element"](10,"dxo-header-filter",8),a["\u0275\u0275element"](11,"dxo-paging",9),a["\u0275\u0275element"](12,"dxo-search-panel",10),a["\u0275\u0275element"](13,"dxo-export",11),a["\u0275\u0275element"](14,"dxi-column",12),a["\u0275\u0275element"](15,"dxi-column",13),a["\u0275\u0275element"](16,"dxi-column",14),a["\u0275\u0275element"](17,"dxi-column",15),a["\u0275\u0275element"](18,"dxi-column",16),a["\u0275\u0275element"](19,"dxi-column",17),a["\u0275\u0275element"](20,"dxi-column",18),a["\u0275\u0275element"](21,"dxi-column",19),a["\u0275\u0275element"](22,"dxi-column",20),a["\u0275\u0275element"](23,"dxi-column",21),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](8),a["\u0275\u0275textInterpolate1"]("Head Count Of Employee ",e.data.columnData.length,""),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("dataSource",e.data.columnData)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("allowColumnResizing",!0)("columnAutoWidth",!0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("visible",!0)("alignment","right"),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("visible",!0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("enabled",!0),a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("dataType","date")("visible",e.isDateVisible1()),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("visible",e.isColumnVisible1()),a["\u0275\u0275advance"](1),a["\u0275\u0275propertyInterpolate"]("caption",e.getCaption()),a["\u0275\u0275property"]("visible",e.isColumnVisible()),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("visible",e.isGenderVisible()),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("visible",e.isStatusVisible())}}function c(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",1),a["\u0275\u0275elementStart"](1,"div",2),a["\u0275\u0275elementStart"](2,"div",3),a["\u0275\u0275text"](3,"Project Details"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",4),a["\u0275\u0275elementStart"](5,"mat-icon",5),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onDialogClose()})),a["\u0275\u0275text"](6,"clear"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"dx-data-grid",7),a["\u0275\u0275element"](8,"dxo-header-filter",8),a["\u0275\u0275element"](9,"dxo-paging",9),a["\u0275\u0275element"](10,"dxo-search-panel",10),a["\u0275\u0275element"](11,"dxo-export",22),a["\u0275\u0275element"](12,"dxi-column",23),a["\u0275\u0275element"](13,"dxi-column",24),a["\u0275\u0275element"](14,"dxi-column",25),a["\u0275\u0275element"](15,"dxi-column",26),a["\u0275\u0275element"](16,"dxi-column",27),a["\u0275\u0275element"](17,"dxi-column",28),a["\u0275\u0275element"](18,"dxi-column",29),a["\u0275\u0275element"](19,"dxi-column",30),a["\u0275\u0275element"](20,"dxi-column",31),a["\u0275\u0275element"](21,"dxi-column",32),a["\u0275\u0275element"](22,"dxi-column",33),a["\u0275\u0275element"](23,"dxi-column",34),a["\u0275\u0275element"](24,"dxi-column",35),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](7),a["\u0275\u0275property"]("dataSource",e.data.columnData)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("allowColumnResizing",!0)("columnAutoWidth",!0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("visible",!0)("alignment","right"),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("visible",!0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("enabled",!0)}}let m=(()=>{class e{constructor(e,t,n){this.data=e,this._dialogRef=t,this._toaster=n,this.external=!1}ngOnInit(){"external"==this.data.graph&&(this.external=!0)}onDialogClose(){this._dialogRef.close()}getCaption(){return"gender"==this.data.caption?"Gender Name":"experience"==this.data.caption?"Experience":"education"==this.data.caption?"Education":"designation"==this.data.caption?"Job":"reason"==this.data.caption?"Hire Reason":"department"==this.data.caption?"Department":"bench"==this.data.caption?"Status":"Type"}getCaptionS(){return"gender"==this.data.caption?"Gender Name":"experience"==this.data.caption?"Experience":"education"==this.data.caption?"Education":"designation"==this.data.caption?"Job":"reason"==this.data.caption?"Hire Reason":"department"==this.data.caption?"Department":"Type"}isColumnVisible(){return"department"!=this.data.caption&&"bench"!=this.data.caption}isGenderVisible(){return"Gender"==this.data.graph&&"gender"!=this.data.caption}isStatusVisible(){return"bench"==this.data.graph}isColumnVisible1(){return"vol_nv"!=this.data.caption&&"Vol"==this.data.graph||"reason"!=this.data.caption&&"Hire"==this.data.graph||"bench"!=this.data.caption}isColumnVisible2(){return"reason"!=this.data.caption&&"Hire"==this.data.graph}isDateVisible1(){return"bench"!=this.data.caption}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](i.a),a["\u0275\u0275directiveInject"](i.h),a["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-voluntary-summary"]],decls:2,vars:2,consts:[["class","dialog",4,"ngIf"],[1,"dialog"],[1,"align-items-btw",2,"margin-bottom","8px"],[1,"dialog-title"],[1,"icon"],[1,"icon",3,"click"],[1,"sub-text"],["height","400",1,"data-grid",3,"dataSource","showBorders","showColumnLines","showRowLines","allowColumnResizing","columnAutoWidth"],[3,"visible","alignment"],["pageSize","10"],["width","250",3,"visible"],[3,"enabled"],["dataField","associate_id","caption","Associate Id"],["dataField","employee_name","caption","Employee Name"],["dataField","entity_name","caption","Entity"],["dataField","division_name","caption","Division"],["dataField","sub_division_name","caption","Sub Division"],["dataField","Date_Of_Exit","caption","Date Of Exit",3,"dataType","visible"],["dataField","SType","caption","Type",3,"visible"],["dataField","Type",3,"caption","visible"],["dataField","gender_name","caption","Gender Name",3,"visible"],["dataField","status","caption","Allocation Status",3,"visible"],["fileName","project_details_export",3,"enabled"],["dataField","start_date","caption","Start Date","dataType","date","format","dd-MMM-yyyy"],["dataField","end_date","caption","End Date","dataType","date","format","dd-MMM-yyyy"],["dataField","project_name","caption","Project Name"],["dataField","industry_type","caption","Industry Type"],["dataField","cost_center","caption","Cost Center"],["dataField","projectType","caption","Project Type"],["dataField","clientName","caption","Client Name"],["dataField","billable","caption","Billable","dataType","boolean"],["dataField","incentive","caption","Incentive"],["dataField","project_role","caption","Project Role"],["dataField","pmo","caption","PMO"],["dataField","lineManager","caption","Project Manager"],["dataField","split_percentage","caption","Split Percentage","dataType","number"]],template:function(e,t){1&e&&(a["\u0275\u0275template"](0,p,24,18,"div",0),a["\u0275\u0275template"](1,c,25,10,"div",0)),2&e&&(a["\u0275\u0275property"]("ngIf",!t.external),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.external))},directives:[r.NgIf,l.a,s.a,d.Cc,d.od,d.Md,d.Sb,d.g],styles:[".dialog[_ngcontent-%COMP%]{overflow:hidden;padding:16px}.dialog-title[_ngcontent-%COMP%]{font-size:16px;font-weight:450;margin:0;color:#ee4961}.align-items-btw[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between;align-items:center}.icon[_ngcontent-%COMP%]{font-size:18px;color:#45546e;padding-top:3px;cursor:pointer}.sub-text[_ngcontent-%COMP%]{font-size:14px;font-weight:600;margin-bottom:8px;color:#45546e}.data-grid[_ngcontent-%COMP%]     .dx-toolbar .dx-toolbar-items-container{height:50px!important}"]}),e})()}}]);