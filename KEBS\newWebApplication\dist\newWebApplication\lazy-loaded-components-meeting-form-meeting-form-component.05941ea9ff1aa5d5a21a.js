(window.webpackJsonp=window.webpackJsonp||[]).push([[758],{"4bTV":function(e,t,i){"use strict";i.r(t),i.d(t,"MeetingFormComponent",(function(){return R}));var n=i("mrSG"),o=i("fXoL"),r=i("3Pt+"),a=i("xG9w"),l=i("wd/R"),s=i("0IaG"),c=i("ofXK"),d=i("jtHE"),m=i("XNiG"),p=i("NJ67"),h=i("1G5W"),u=i("kmnG"),g=i("d3UM"),f=i("FKr1"),v=i("WJ5W");const y=["singleSelect"];function b(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const i=t.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(i)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let C=(()=>{class e extends p.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new d.a,this.change=new o.EventEmitter,this._onDestroy=new m.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](y,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1,2),o["\u0275\u0275elementStart"](5,"mat-option"),o["\u0275\u0275element"](6,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"mat-option",4),o["\u0275\u0275text"](8,"None"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](9,b,2,2,"mat-option",5),o["\u0275\u0275pipe"](10,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",null),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[u.c,u.g,g.c,r.v,r.k,r.F,f.p,v.a,c.NgForOf],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();const w=["allSelected"],S=["singleSelect"];function O(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let E=(()=>{class e extends p.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new d.a,this._onDestroy=new m.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(o["\u0275\u0275viewQuery"](w,!0),o["\u0275\u0275viewQuery"](S,!0)),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:12,vars:11,consts:[["appearance","outline"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value","click"],["allSelected",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1,2),o["\u0275\u0275elementStart"](5,"mat-option"),o["\u0275\u0275element"](6,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"mat-option",4,5),o["\u0275\u0275listener"]("click",(function(){return t.toggleAllSelection()})),o["\u0275\u0275text"](9,"None"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](10,O,2,2,"mat-option",6),o["\u0275\u0275pipe"](11,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",0),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](11,9,t.filteredList)))},directives:[u.c,u.g,g.c,r.v,r.k,r.F,f.p,v.a,c.NgForOf],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var x=i("Kj3r"),D=i("F97M"),_=i("XVR1"),F=i("qFsG"),M=i("/1cH"),P=i("NFeN");function k(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-label"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.label)}}function I(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275elementStart"](1,"small"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let T=(()=>{class e extends p.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new m.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new o.EventEmitter,this.selectedUser=new o.EventEmitter,this.label="",this.blur=new o.EventEmitter,this.required=!1,this.fieldCtrl=new r.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new m.b}ngOnInit(){this.userSearchSubject.pipe(Object(x.a)(600)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](D.a),o["\u0275\u0275directiveInject"](_.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",0),o["\u0275\u0275template"](2,k,2,1,"mat-label",1),o["\u0275\u0275elementStart"](3,"input",2),o["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"mat-icon",3),o["\u0275\u0275text"](5,"person_pin"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),o["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),o["\u0275\u0275template"](8,I,3,4,"mat-option",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275reference"](7);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",t.label),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[u.c,c.NgIf,F.b,M.d,r.e,r.F,r.v,r.k,P.a,u.i,M.b,c.NgForOf,u.g,f.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var L=i("dNgK"),A=i("bTqV"),j=i("iadO"),N=i("8uEH"),U=i("pgif"),V=i("ihCf");function q(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",47),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createMeeting()})),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2," done_all"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function B(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",48),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().editMeeting()})),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2," done_all"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}const z=function(e,t){return{"btn-not-active":e,"btn-active":t}};let R=(()=>{class e{constructor(e,t,i,n,a,l,s){this.fb=e,this.dialogRef=t,this.dialogData=i,this.dialog=n,this.activityToMainService=a,this.snackBar=l,this.opportunityService=s,this.close=new o.EventEmitter,this.revisedLocationList=[],this.currentUser=this.opportunityService.currentUser.oid,this.todaysOrTommorow=[{startDate:null,endDate:null}],this.meetingForm=this.fb.group({phase:[""],title:["",r.H.required],type:[""],location:[""],startDate:[""],endDate:[""],startTime:[""],endTime:[""],people:[""],organizer:["",r.H.required],description:[""],remindMe:[""],applicationName:[""],applicationReferenceId:[""],applicationId:[""],governanceType:[""]}),this.submitted=!1,this.resetButtons=()=>{this.todaysOrTommorow[0].startDate=this.todaysOrTommorow[0].endDate=null},this.patchPeople=e=>{this.meetingForm.patchValue({people:e})},this.dayClicked=(e,t)=>{if(console.log(e,t),"startDate"==e){if(this.todaysOrTommorow[0].startDate=t,"today"==t)this.meetingForm.patchValue({startDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(e.getDate()+1),this.meetingForm.patchValue({startDate:t})}}else if("endDate"==e)if(this.todaysOrTommorow[0].endDate=t,"today"==t)this.meetingForm.patchValue({endDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(t.getDate()+1),this.meetingForm.patchValue({endDate:t})}},this.closeClicked=()=>{this.close.emit()},this.onSubmit=()=>{this.submitted=!0},this.editMeeting=()=>{this.meetingForm.valid?this.opportunityService.editMeeting(this.activityId,this.meetingForm.value).then(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Edited Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createMeeting=()=>{this.meetingForm.valid?this.opportunityService.createMeeting(this.meetingForm.value).subscribe(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Created Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMeetingDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;this.editData=e,e?this.meetingForm.patchValue({phase:parseInt(e.phase),title:e.title,type:e.type,location:e.location,startDate:e.planned_start_date,endDate:e.task_due_date,startTime:e.start_time,endTime:e.end_time,people:e.people_involved,organizer:e.organizer,description:e.description,remindMe:e.reminder,governanceType:e.governance_activity_id}):console.info("waiting for changes!")}else this.meetingForm.reset()}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){let e;console.log(window.location.pathname),this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,this.opportunityId=this.dialogData.opportunityId,"Edit"==this.mode&&this.updateFormWithMeetingDetails(),"opportunities"==window.location.pathname.split("/")[2]&&(e=36),yield this.opportunityService.getLeadsGovernanceType(72).then(e=>{this.leadGovernanceTypes=e},e=>{console.log(e)}),yield this.opportunityService.meetingTypeList().then(e=>{console.log(e),this.meetingTypeList=e}),yield this.opportunityService.locationList().then(e=>{console.log(e),this.locationList=e,"Edit"==this.mode&&(this.revisedLocationList=this.locationList[this.editData.type])}),yield this.opportunityService.peopleList().then(e=>{console.log(e),this.peopleList=e}),yield this.opportunityService.getActivityPhase(this.opportunityId).then(e=>{this.phase=e;let t=a.where(this.phase,{name:"Logs"});this.meetingForm.patchValue({phase:t.length>0?t[0].id:null})},e=>{console.log(e)}),this.meetingForm.get("type").valueChanges.subscribe(e=>{console.log(e),this.locationList&&(this.revisedLocationList=this.locationList[e],this.meetingForm.patchValue({location:this.revisedLocationList.length>0?this.revisedLocationList[0].id:null}))}),this.patchValue(e)}))}patchValue(e){let t=a.where(this.leadGovernanceTypes,{name:"Meetings"}),i=a.where(this.meetingTypeList,{name:"Online"});this.meetingForm.patchValue({applicationName:"opportunities",applicationReferenceId:this.opportunityId,applicationId:e,startDate:l().format(),endDate:l().add(1,"hours").format(),organizer:this.currentUser,governanceType:t.length>0?t[0].id:null,type:i.length>0?i[0].id:null})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](r.i),o["\u0275\u0275directiveInject"](s.h),o["\u0275\u0275directiveInject"](s.a),o["\u0275\u0275directiveInject"](s.b),o["\u0275\u0275directiveInject"](N.a),o["\u0275\u0275directiveInject"](L.a),o["\u0275\u0275directiveInject"](U.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-meeting-form"]],outputs:{close:"close"},features:[o["\u0275\u0275NgOnChangesFeature"]],decls:88,vars:29,consts:[[3,"formGroup","ngSubmit"],[1,"create-meeting-styles","container-fluid"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row"],[1,"col-12"],["placeholder","Phase","formControlName","phase",1,"create-account-field","title",3,"list"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],[1,"row","pt-1"],[1,"col-6"],["placeholder","Type","formControlName","type",1,"create-account-field-inputsearch",3,"list"],["placeholder","Location","formControlName","location",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","pt-2"],["appearance","outline",1,"create-account-field"],["matInput","","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-5"],[1,"row","p-0"],[1,"col-4",2,"padding-top","14px"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-4","pl-0",2,"padding-top","14px"],[1,"col-4","pt-2","pl-0","pr-0"],["matInput","","type","time","placeholder","time","formControlName","startTime"],["matInput","","formControlName","endDate",3,"matDatepicker"],["picker1",""],["matInput","","type","time","placeholder","time","formControlName","endTime"],[1,"row","pt-2"],["placeholder","People","formControlName","people",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","organizer"],["label","Organizer","required","true","formControlName","organizer",3,"isAutocomplete"],[1,"col-9"],["appearance","outline",1,"textArea",2,"width","37rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],[1,"col-3"],[1,"d-flex","justify-content-center","align-items-center"],["src","https://assets.kebs.app/images/create_meeting_bg.png","height","125","width","160"],[1,"row","pt-3"],[1,"col-8","pl-4","d-flex"],[1,"quotes","my-auto"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"form",0),o["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",4),o["\u0275\u0275elementStart"](6,"button",5),o["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close("Close")})),o["\u0275\u0275elementStart"](7,"mat-icon",6),o["\u0275\u0275text"](8,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275elementStart"](10,"div",8),o["\u0275\u0275element"](11,"app-input-search",9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"div",7),o["\u0275\u0275elementStart"](13,"div",8),o["\u0275\u0275elementStart"](14,"mat-form-field",10),o["\u0275\u0275elementStart"](15,"mat-label"),o["\u0275\u0275text"](16,"Title *"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](17,"input",11),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"div",12),o["\u0275\u0275elementStart"](19,"div",13),o["\u0275\u0275element"](20,"app-input-search",14),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](21,"div",13),o["\u0275\u0275element"](22,"app-input-search",15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",7),o["\u0275\u0275elementStart"](24,"div",16),o["\u0275\u0275elementStart"](25,"mat-form-field",17),o["\u0275\u0275elementStart"](26,"mat-label"),o["\u0275\u0275text"](27,"Start Date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](28,"input",18),o["\u0275\u0275element"](29,"mat-datepicker-toggle",19),o["\u0275\u0275element"](30,"mat-datepicker",null,20),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](32,"div",21),o["\u0275\u0275elementStart"](33,"div",22),o["\u0275\u0275elementStart"](34,"div",23),o["\u0275\u0275elementStart"](35,"button",24),o["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","today")})),o["\u0275\u0275text"](36," Today"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](37,"div",25),o["\u0275\u0275elementStart"](38,"button",24),o["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","tommorow")})),o["\u0275\u0275text"](39," Tommorow"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](40,"div",26),o["\u0275\u0275elementStart"](41,"mat-form-field",17),o["\u0275\u0275elementStart"](42,"mat-label"),o["\u0275\u0275text"](43,"Time"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](44,"input",27),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](45,"div",7),o["\u0275\u0275elementStart"](46,"div",16),o["\u0275\u0275elementStart"](47,"mat-form-field",17),o["\u0275\u0275elementStart"](48,"mat-label"),o["\u0275\u0275text"](49,"End Date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](50,"input",28),o["\u0275\u0275element"](51,"mat-datepicker-toggle",19),o["\u0275\u0275element"](52,"mat-datepicker",null,29),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](54,"div",21),o["\u0275\u0275elementStart"](55,"div",22),o["\u0275\u0275elementStart"](56,"div",23),o["\u0275\u0275elementStart"](57,"button",24),o["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","today")})),o["\u0275\u0275text"](58," Today"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](59,"div",25),o["\u0275\u0275elementStart"](60,"button",24),o["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","tommorow")})),o["\u0275\u0275text"](61," Tommorow"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](62,"div",26),o["\u0275\u0275elementStart"](63,"mat-form-field",17),o["\u0275\u0275elementStart"](64,"mat-label"),o["\u0275\u0275text"](65,"Time"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](66,"input",30),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](67,"div",31),o["\u0275\u0275elementStart"](68,"div",13),o["\u0275\u0275element"](69,"app-multi-select-search2",32),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](70,"div",33),o["\u0275\u0275element"](71,"app-search-user",34),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](72,"div",31),o["\u0275\u0275elementStart"](73,"div",35),o["\u0275\u0275elementStart"](74,"mat-form-field",36),o["\u0275\u0275element"](75,"textarea",37,38),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](77,"div",39),o["\u0275\u0275elementStart"](78,"div",40),o["\u0275\u0275element"](79,"img",41),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](80,"div",7),o["\u0275\u0275elementStart"](81,"div",42),o["\u0275\u0275elementStart"](82,"div",43),o["\u0275\u0275elementStart"](83,"span",44),o["\u0275\u0275text"](84,' "In the Middle of difficulty, lies Opportunity" '),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](85,"div",39),o["\u0275\u0275template"](86,q,3,0,"button",45),o["\u0275\u0275template"](87,B,3,0,"button",46),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275reference"](31),i=o["\u0275\u0275reference"](53);o["\u0275\u0275property"]("formGroup",t.meetingForm),o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"]("","Edit"==t.mode?"Edit":"Create new"," Meeting"),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("list",t.phase),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("list",t.meetingTypeList),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("list",t.revisedLocationList),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("matDatepicker",e),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("for",e),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngClass",o["\u0275\u0275pureFunction2"](17,z,"tommorow"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"today"==t.todaysOrTommorow[0].startDate)),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngClass",o["\u0275\u0275pureFunction2"](20,z,"today"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"tommorow"==t.todaysOrTommorow[0].startDate)),o["\u0275\u0275advance"](12),o["\u0275\u0275property"]("matDatepicker",i),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("for",i),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngClass",o["\u0275\u0275pureFunction2"](23,z,"tommorow"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"today"==t.todaysOrTommorow[0].endDate)),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngClass",o["\u0275\u0275pureFunction2"](26,z,"today"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"tommorow"==t.todaysOrTommorow[0].endDate)),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("list",t.peopleList),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("isAutocomplete",!0),o["\u0275\u0275advance"](15),o["\u0275\u0275property"]("ngIf","Edit"!=t.mode),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","Edit"==t.mode)}},directives:function(){return[r.J,r.w,r.n,A.a,P.a,C,r.v,r.l,u.c,u.g,F.b,r.e,j.g,j.i,u.i,j.f,c.NgClass,E,T,r.F,V.b,c.NgIf]},styles:[".create-meeting-styles[_ngcontent-%COMP%]   .createMeeting[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-meeting-styles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:50rem}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:44px}.create-meeting-styles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-meeting-styles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.create-meeting-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-meeting-styles[_ngcontent-%COMP%]   .organizer[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:42px}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{width:24rem}"]}),e})()},"8uEH":function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));var n=i("XNiG"),o=i("fXoL"),r=i("tk/3");let a=(()=>{class e{constructor(e){this.http=e,this.toMain=new n.b,this.sendMsg=e=>{this.toMain.next(e)},this.getMsg=()=>this.toMain.asObservable()}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](r.c))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},PbQz:function(e,t,i){"use strict";i.r(t),i.d(t,"MeetingFormComponent",(function(){return z}));var n=i("fXoL"),o=i("3Pt+"),r=i("0IaG"),a=i("ofXK"),l=i("jtHE"),s=i("XNiG"),c=i("NJ67"),d=i("1G5W"),m=i("kmnG"),p=i("d3UM"),h=i("FKr1"),u=i("WJ5W");const g=["singleSelect"];function f(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends c.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this.change=new n.EventEmitter,this._onDestroy=new s.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](g,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4),n["\u0275\u0275text"](8,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](9,f,2,2,"mat-option",5),n["\u0275\u0275pipe"](10,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,h.p,u.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();const y=["allSelected"],b=["singleSelect"];function C(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let w=(()=>{class e extends c.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this._onDestroy=new s.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(n["\u0275\u0275viewQuery"](y,!0),n["\u0275\u0275viewQuery"](b,!0)),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:12,vars:11,consts:[["appearance","outline"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value","click"],["allSelected",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4,5),n["\u0275\u0275listener"]("click",(function(){return t.toggleAllSelection()})),n["\u0275\u0275text"](9,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](10,C,2,2,"mat-option",6),n["\u0275\u0275pipe"](11,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",0),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](11,9,t.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,h.p,u.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var S=i("mrSG"),O=i("Kj3r"),E=i("F97M"),x=i("XVR1"),D=i("qFsG"),_=i("/1cH"),F=i("NFeN");function M(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.label)}}function P(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275elementStart"](1,"small"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let k=(()=>{class e extends c.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new s.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new n.EventEmitter,this.selectedUser=new n.EventEmitter,this.label="",this.blur=new n.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new s.b}ngOnInit(){this.userSearchSubject.pipe(Object(O.a)(600)).subscribe(e=>Object(S.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(S.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(S.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(S.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(S.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(S.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(S.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](E.a),n["\u0275\u0275directiveInject"](x.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div"),n["\u0275\u0275elementStart"](1,"mat-form-field",0),n["\u0275\u0275template"](2,M,2,1,"mat-label",1),n["\u0275\u0275elementStart"](3,"input",2),n["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"mat-icon",3),n["\u0275\u0275text"](5,"person_pin"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),n["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),n["\u0275\u0275template"](8,P,3,4,"mat-option",6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](7);n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",t.label),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[m.c,a.NgIf,D.b,_.d,o.e,o.F,o.v,o.k,F.a,m.i,_.b,a.NgForOf,m.g,h.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var I=i("dNgK"),T=i("bTqV"),L=i("iadO"),A=i("9jeV"),j=i("VsNQ"),N=i("XXEo"),U=i("ihCf");function V(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",46),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().createMeeting()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function q(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",47),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().editMeeting()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}const B=function(e,t){return{"btn-not-active":e,"btn-active":t}};let z=(()=>{class e{constructor(e,t,i,r,a,l,s,c){this.fb=e,this.dialogRef=t,this.dialogData=i,this.dialog=r,this.activityToMainService=a,this.snackBar=l,this.contactService=s,this.loginService=c,this.close=new n.EventEmitter,this.todaysOrTommorow=[{startDate:null,endDate:null}],this.currentUser=this.loginService.getProfile().profile,this.meetingForm=this.fb.group({title:["",o.H.required],type:[""],location:[""],startDate:[""],endDate:[""],startTime:[""],endTime:[""],people:[""],organizer:["",o.H.required],description:[""],remindMe:[""],applicationName:[""],contactId:[""],applicationId:[""],governanceType:[""]}),this.submitted=!1,this.resetButtons=()=>{this.todaysOrTommorow[0].startDate=this.todaysOrTommorow[0].endDate=null},this.patchPeople=e=>{this.meetingForm.patchValue({people:e})},this.dayClicked=(e,t)=>{if(console.log(e,t),"startDate"==e){if(this.todaysOrTommorow[0].startDate=t,"today"==t)this.meetingForm.patchValue({startDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(e.getDate()+1),this.meetingForm.patchValue({startDate:t})}}else if("endDate"==e)if(this.todaysOrTommorow[0].endDate=t,"today"==t)this.meetingForm.patchValue({endDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(t.getDate()+1),this.meetingForm.patchValue({endDate:t})}},this.closeClicked=()=>{this.close.emit()},this.onSubmit=()=>{this.submitted=!0},this.editMeeting=()=>{this.meetingForm.valid?this.contactService.editMeeting(this.activityId,this.meetingForm.value).then(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Edited Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createMeeting=()=>{this.meetingForm.valid?this.contactService.createMeeting(this.meetingForm.value).subscribe(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Created Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMeetingDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;this.editData=e,console.log("Karthi",this.editData),e?this.meetingForm.patchValue({title:e.title,type:e.type,location:e.location,startDate:e.planned_start_date,endDate:e.task_due_date,startTime:e.start_time,endTime:e.end_time,people:e.people_involved,organizer:e.organizer,description:e.description,remindMe:e.reminder,governanceType:e.governance_activity_id}):console.info("waiting for changes!")}else this.meetingForm.reset()}ngOnInit(){let e;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithMeetingDetails(),"contacts"==window.location.pathname.split("/")[2]&&(e=34),this.contactService.getContactGovernanceType(34).subscribe(e=>{this.contactGovernanceTypes=e},e=>{console.log(e)}),this.meetingForm.patchValue({applicationName:window.location.pathname.split("/")[2],contactId:window.location.pathname.split("/")[3],applicationId:e,governanceType:45,organizer:this.currentUser.oid}),this.contactService.meetingTypeList().then(e=>{console.log(e),this.meetingTypeList=e}),this.contactService.locationList().then(e=>{console.log(e),this.locationList=e,"Edit"==this.mode&&(this.revisedLocationList=this.locationList[this.editData.type])}),this.contactService.peopleList().then(e=>{console.log(e),this.peopleList=e}),this.meetingForm.get("type").valueChanges.subscribe(e=>{console.log(e),this.locationList&&(this.revisedLocationList=this.locationList[e])})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](r.h),n["\u0275\u0275directiveInject"](r.a),n["\u0275\u0275directiveInject"](r.b),n["\u0275\u0275directiveInject"](A.a),n["\u0275\u0275directiveInject"](I.a),n["\u0275\u0275directiveInject"](j.a),n["\u0275\u0275directiveInject"](N.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-meeting-form"]],outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:85,vars:28,consts:[[3,"formGroup","ngSubmit"],[1,"create-meeting-styles","container-fluid"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],[1,"row","pt-1"],[1,"col-6"],["placeholder","Type","formControlName","type",1,"create-account-field-inputsearch",3,"list"],["placeholder","Location","formControlName","location",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","pt-2"],["appearance","outline",1,"create-account-field"],["matInput","","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-5"],[1,"row","p-0"],[1,"col-4",2,"padding-top","14px"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-4","pl-0",2,"padding-top","14px"],[1,"col-4","pt-2","pl-0","pr-0"],["matInput","","type","time","placeholder","time","formControlName","startTime"],["matInput","","formControlName","endDate",3,"matDatepicker"],["picker1",""],["matInput","","type","time","placeholder","time","formControlName","endTime"],[1,"row","pt-2"],["placeholder","People","formControlName","people",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","organizer"],["label","Organizer","required","true","formControlName","organizer",3,"isAutocomplete"],[1,"col-9"],["appearance","outline",1,"textArea",2,"width","37rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],[1,"col-3"],[1,"d-flex","justify-content-center","align-items-center"],["src","https://assets.kebs.app/images/create_meeting_bg.png","height","125","width","160"],[1,"row","pt-3"],[1,"col-8","pl-4","d-flex"],[1,"quotes","my-auto"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"form",0),n["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close("Close")})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"mat-form-field",9),n["\u0275\u0275elementStart"](12,"mat-label"),n["\u0275\u0275text"](13,"Title *"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](14,"input",10),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](15,"div",11),n["\u0275\u0275elementStart"](16,"div",12),n["\u0275\u0275element"](17,"app-input-search",13),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](18,"div",12),n["\u0275\u0275element"](19,"app-input-search",14),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](20,"div",7),n["\u0275\u0275elementStart"](21,"div",15),n["\u0275\u0275elementStart"](22,"mat-form-field",16),n["\u0275\u0275elementStart"](23,"mat-label"),n["\u0275\u0275text"](24,"Start Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](25,"input",17),n["\u0275\u0275element"](26,"mat-datepicker-toggle",18),n["\u0275\u0275element"](27,"mat-datepicker",null,19),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](29,"div",20),n["\u0275\u0275elementStart"](30,"div",21),n["\u0275\u0275elementStart"](31,"div",22),n["\u0275\u0275elementStart"](32,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","today")})),n["\u0275\u0275text"](33," Today"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](34,"div",24),n["\u0275\u0275elementStart"](35,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","tommorow")})),n["\u0275\u0275text"](36," Tommorow"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](37,"div",25),n["\u0275\u0275elementStart"](38,"mat-form-field",16),n["\u0275\u0275elementStart"](39,"mat-label"),n["\u0275\u0275text"](40,"Time"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](41,"input",26),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](42,"div",7),n["\u0275\u0275elementStart"](43,"div",15),n["\u0275\u0275elementStart"](44,"mat-form-field",16),n["\u0275\u0275elementStart"](45,"mat-label"),n["\u0275\u0275text"](46,"End Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](47,"input",27),n["\u0275\u0275element"](48,"mat-datepicker-toggle",18),n["\u0275\u0275element"](49,"mat-datepicker",null,28),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](51,"div",20),n["\u0275\u0275elementStart"](52,"div",21),n["\u0275\u0275elementStart"](53,"div",22),n["\u0275\u0275elementStart"](54,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","today")})),n["\u0275\u0275text"](55," Today"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](56,"div",24),n["\u0275\u0275elementStart"](57,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","tommorow")})),n["\u0275\u0275text"](58," Tommorow"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](59,"div",25),n["\u0275\u0275elementStart"](60,"mat-form-field",16),n["\u0275\u0275elementStart"](61,"mat-label"),n["\u0275\u0275text"](62,"Time"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](63,"input",29),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](64,"div",30),n["\u0275\u0275elementStart"](65,"div",12),n["\u0275\u0275element"](66,"app-multi-select-search2",31),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](67,"div",32),n["\u0275\u0275element"](68,"app-search-user",33),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](69,"div",30),n["\u0275\u0275elementStart"](70,"div",34),n["\u0275\u0275elementStart"](71,"mat-form-field",35),n["\u0275\u0275element"](72,"textarea",36,37),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](74,"div",38),n["\u0275\u0275elementStart"](75,"div",39),n["\u0275\u0275element"](76,"img",40),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](77,"div",7),n["\u0275\u0275elementStart"](78,"div",41),n["\u0275\u0275elementStart"](79,"div",42),n["\u0275\u0275elementStart"](80,"span",43),n["\u0275\u0275text"](81,' "In the Middle of difficulty, lies Opportunity" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](82,"div",38),n["\u0275\u0275template"](83,V,3,0,"button",44),n["\u0275\u0275template"](84,q,3,0,"button",45),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](28),i=n["\u0275\u0275reference"](50);n["\u0275\u0275property"]("formGroup",t.meetingForm),n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"]("","Edit"==t.mode?"Edit":"Create new"," Meeting"),n["\u0275\u0275advance"](13),n["\u0275\u0275property"]("list",t.meetingTypeList),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("list",t.revisedLocationList),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("matDatepicker",e),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",e),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](16,B,"tommorow"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"today"==t.todaysOrTommorow[0].startDate)),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](19,B,"today"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"tommorow"==t.todaysOrTommorow[0].startDate)),n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("matDatepicker",i),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",i),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](22,B,"tommorow"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"today"==t.todaysOrTommorow[0].endDate)),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](25,B,"today"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"tommorow"==t.todaysOrTommorow[0].endDate)),n["\u0275\u0275advance"](9),n["\u0275\u0275property"]("list",t.peopleList),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("isAutocomplete",!0),n["\u0275\u0275advance"](15),n["\u0275\u0275property"]("ngIf","Edit"!=t.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==t.mode)}},directives:function(){return[o.J,o.w,o.n,T.a,F.a,m.c,m.g,D.b,o.e,o.v,o.l,v,L.g,L.i,m.i,L.f,a.NgClass,w,k,o.F,U.b,a.NgIf]},styles:[".create-meeting-styles[_ngcontent-%COMP%]   .createMeeting[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-meeting-styles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:50rem}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:44px}.create-meeting-styles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-meeting-styles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.create-meeting-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-meeting-styles[_ngcontent-%COMP%]   .organizer[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:42px}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{width:24rem}"]}),e})()},QXPo:function(e,t,i){"use strict";i.r(t),i.d(t,"MeetingFormComponent",(function(){return z}));var n=i("fXoL"),o=i("3Pt+"),r=i("0IaG"),a=i("ofXK"),l=i("jtHE"),s=i("XNiG"),c=i("NJ67"),d=i("1G5W"),m=i("kmnG"),p=i("d3UM"),h=i("FKr1"),u=i("WJ5W");const g=["singleSelect"];function f(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends c.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this.change=new n.EventEmitter,this._onDestroy=new s.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](g,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4),n["\u0275\u0275text"](8,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](9,f,2,2,"mat-option",5),n["\u0275\u0275pipe"](10,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,h.p,u.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();const y=["allSelected"],b=["singleSelect"];function C(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let w=(()=>{class e extends c.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this._onDestroy=new s.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(n["\u0275\u0275viewQuery"](y,!0),n["\u0275\u0275viewQuery"](b,!0)),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:12,vars:11,consts:[["appearance","outline"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value","click"],["allSelected",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4,5),n["\u0275\u0275listener"]("click",(function(){return t.toggleAllSelection()})),n["\u0275\u0275text"](9,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](10,C,2,2,"mat-option",6),n["\u0275\u0275pipe"](11,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",0),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](11,9,t.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,h.p,u.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var S=i("mrSG"),O=i("Kj3r"),E=i("F97M"),x=i("XVR1"),D=i("qFsG"),_=i("/1cH"),F=i("NFeN");function M(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.label)}}function P(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275elementStart"](1,"small"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let k=(()=>{class e extends c.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new s.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new n.EventEmitter,this.selectedUser=new n.EventEmitter,this.label="",this.blur=new n.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new s.b}ngOnInit(){this.userSearchSubject.pipe(Object(O.a)(600)).subscribe(e=>Object(S.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(S.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(S.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(S.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(S.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(S.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(S.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](E.a),n["\u0275\u0275directiveInject"](x.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div"),n["\u0275\u0275elementStart"](1,"mat-form-field",0),n["\u0275\u0275template"](2,M,2,1,"mat-label",1),n["\u0275\u0275elementStart"](3,"input",2),n["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"mat-icon",3),n["\u0275\u0275text"](5,"person_pin"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),n["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),n["\u0275\u0275template"](8,P,3,4,"mat-option",6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](7);n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",t.label),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[m.c,a.NgIf,D.b,_.d,o.e,o.F,o.v,o.k,F.a,m.i,_.b,a.NgForOf,m.g,h.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var I=i("dNgK"),T=i("bTqV"),L=i("iadO"),A=i("RUbJ"),j=i("WGBV"),N=i("XXEo"),U=i("ihCf");function V(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",46),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().createMeeting()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function q(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",47),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().editMeeting()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}const B=function(e,t){return{"btn-not-active":e,"btn-active":t}};let z=(()=>{class e{constructor(e,t,i,r,a,l,s,c){this.fb=e,this.dialogRef=t,this.dialogData=i,this.dialog=r,this.activityToMainService=a,this.snackBar=l,this.accountService=s,this.loginService=c,this.close=new n.EventEmitter,this.todaysOrTommorow=[{startDate:null,endDate:null}],this.currentUser=this.loginService.getProfile().profile,this.meetingForm=this.fb.group({title:["",o.H.required],type:[""],location:[""],startDate:[""],endDate:[""],startTime:[""],endTime:[""],people:[""],organizer:["",o.H.required],description:[""],remindMe:[""],applicationName:[""],accountId:[""],applicationId:[""],governanceType:[""]}),this.submitted=!1,this.resetButtons=()=>{this.todaysOrTommorow[0].startDate=this.todaysOrTommorow[0].endDate=null},this.patchPeople=e=>{this.meetingForm.patchValue({people:e})},this.dayClicked=(e,t)=>{if(console.log(e,t),"startDate"==e){if(this.todaysOrTommorow[0].startDate=t,"today"==t)this.meetingForm.patchValue({startDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(e.getDate()+1),this.meetingForm.patchValue({startDate:t})}}else if("endDate"==e)if(this.todaysOrTommorow[0].endDate=t,"today"==t)this.meetingForm.patchValue({endDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(t.getDate()+1),this.meetingForm.patchValue({endDate:t})}},this.closeClicked=()=>{this.close.emit()},this.onSubmit=()=>{this.submitted=!0},this.editMeeting=()=>{this.meetingForm.valid?this.accountService.editMeeting(this.activityId,this.meetingForm.value).then(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Edited Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createMeeting=()=>{this.meetingForm.valid?this.accountService.createMeeting(this.meetingForm.value).subscribe(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Created Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMeetingDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;this.editData=e,e?this.meetingForm.patchValue({title:e.title,type:e.type,location:e.location,startDate:e.planned_start_date,endDate:e.task_due_date,startTime:e.start_time,endTime:e.end_time,people:e.people_involved,organizer:e.organizer,description:e.description,remindMe:e.reminder,governanceType:e.governance_activity_id}):console.info("waiting for changes!")}else this.meetingForm.reset()}ngOnInit(){let e;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithMeetingDetails(),"accounts"==window.location.pathname.split("/")[2]&&(e=33),this.accountService.getAccountGovernanceType(33).subscribe(e=>{this.accountGovernanceTypes=e},e=>{console.log(e)}),this.meetingForm.patchValue({applicationName:window.location.pathname.split("/")[2],accountId:window.location.pathname.split("/")[3],applicationId:e,governanceType:49,organizer:this.currentUser.oid}),this.accountService.meetingTypeList().then(e=>{console.log(e),this.meetingTypeList=e}),this.accountService.locationList().then(e=>{console.log(e),this.locationList=e,"Edit"==this.mode&&(this.revisedLocationList=this.locationList[this.editData.type])}),this.accountService.peopleList().then(e=>{console.log(e),this.peopleList=e}),this.meetingForm.get("type").valueChanges.subscribe(e=>{console.log(e),this.locationList&&(this.revisedLocationList=this.locationList[e])})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](r.h),n["\u0275\u0275directiveInject"](r.a),n["\u0275\u0275directiveInject"](r.b),n["\u0275\u0275directiveInject"](A.a),n["\u0275\u0275directiveInject"](I.a),n["\u0275\u0275directiveInject"](j.a),n["\u0275\u0275directiveInject"](N.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-meeting-form"]],outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:85,vars:28,consts:[[3,"formGroup","ngSubmit"],[1,"create-meeting-styles","container-fluid"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],[1,"row","pt-1"],[1,"col-6"],["placeholder","Type","formControlName","type",1,"create-account-field-inputsearch",3,"list"],["placeholder","Location","formControlName","location",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","pt-2"],["appearance","outline",1,"create-account-field"],["matInput","","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-5"],[1,"row","p-0"],[1,"col-4",2,"padding-top","14px"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-4","pl-0",2,"padding-top","14px"],[1,"col-4","pt-2","pl-0","pr-0"],["matInput","","type","time","placeholder","time","formControlName","startTime"],["matInput","","formControlName","endDate",3,"matDatepicker"],["picker1",""],["matInput","","type","time","placeholder","time","formControlName","endTime"],[1,"row","pt-2"],["placeholder","People","formControlName","people",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","organizer"],["label","Organizer","required","true","formControlName","organizer",3,"isAutocomplete"],[1,"col-9"],["appearance","outline",1,"textArea",2,"width","37rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],[1,"col-3"],[1,"d-flex","justify-content-center","align-items-center"],["src","https://assets.kebs.app/images/create_meeting_bg.png","height","125","width","160"],[1,"row","pt-3"],[1,"col-8","pl-4","d-flex"],[1,"quotes","my-auto"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"form",0),n["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close("Close")})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"mat-form-field",9),n["\u0275\u0275elementStart"](12,"mat-label"),n["\u0275\u0275text"](13,"Title *"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](14,"input",10),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](15,"div",11),n["\u0275\u0275elementStart"](16,"div",12),n["\u0275\u0275element"](17,"app-input-search",13),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](18,"div",12),n["\u0275\u0275element"](19,"app-input-search",14),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](20,"div",7),n["\u0275\u0275elementStart"](21,"div",15),n["\u0275\u0275elementStart"](22,"mat-form-field",16),n["\u0275\u0275elementStart"](23,"mat-label"),n["\u0275\u0275text"](24,"Start Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](25,"input",17),n["\u0275\u0275element"](26,"mat-datepicker-toggle",18),n["\u0275\u0275element"](27,"mat-datepicker",null,19),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](29,"div",20),n["\u0275\u0275elementStart"](30,"div",21),n["\u0275\u0275elementStart"](31,"div",22),n["\u0275\u0275elementStart"](32,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","today")})),n["\u0275\u0275text"](33," Today"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](34,"div",24),n["\u0275\u0275elementStart"](35,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","tommorow")})),n["\u0275\u0275text"](36," Tommorow"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](37,"div",25),n["\u0275\u0275elementStart"](38,"mat-form-field",16),n["\u0275\u0275elementStart"](39,"mat-label"),n["\u0275\u0275text"](40,"Time"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](41,"input",26),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](42,"div",7),n["\u0275\u0275elementStart"](43,"div",15),n["\u0275\u0275elementStart"](44,"mat-form-field",16),n["\u0275\u0275elementStart"](45,"mat-label"),n["\u0275\u0275text"](46,"End Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](47,"input",27),n["\u0275\u0275element"](48,"mat-datepicker-toggle",18),n["\u0275\u0275element"](49,"mat-datepicker",null,28),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](51,"div",20),n["\u0275\u0275elementStart"](52,"div",21),n["\u0275\u0275elementStart"](53,"div",22),n["\u0275\u0275elementStart"](54,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","today")})),n["\u0275\u0275text"](55," Today"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](56,"div",24),n["\u0275\u0275elementStart"](57,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","tommorow")})),n["\u0275\u0275text"](58," Tommorow"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](59,"div",25),n["\u0275\u0275elementStart"](60,"mat-form-field",16),n["\u0275\u0275elementStart"](61,"mat-label"),n["\u0275\u0275text"](62,"Time"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](63,"input",29),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](64,"div",30),n["\u0275\u0275elementStart"](65,"div",12),n["\u0275\u0275element"](66,"app-multi-select-search2",31),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](67,"div",32),n["\u0275\u0275element"](68,"app-search-user",33),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](69,"div",30),n["\u0275\u0275elementStart"](70,"div",34),n["\u0275\u0275elementStart"](71,"mat-form-field",35),n["\u0275\u0275element"](72,"textarea",36,37),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](74,"div",38),n["\u0275\u0275elementStart"](75,"div",39),n["\u0275\u0275element"](76,"img",40),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](77,"div",7),n["\u0275\u0275elementStart"](78,"div",41),n["\u0275\u0275elementStart"](79,"div",42),n["\u0275\u0275elementStart"](80,"span",43),n["\u0275\u0275text"](81,' "In the Middle of difficulty, lies Opportunity" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](82,"div",38),n["\u0275\u0275template"](83,V,3,0,"button",44),n["\u0275\u0275template"](84,q,3,0,"button",45),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](28),i=n["\u0275\u0275reference"](50);n["\u0275\u0275property"]("formGroup",t.meetingForm),n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"]("","Edit"==t.mode?"Edit":"Create new"," Meeting"),n["\u0275\u0275advance"](13),n["\u0275\u0275property"]("list",t.meetingTypeList),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("list",t.revisedLocationList),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("matDatepicker",e),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",e),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](16,B,"tommorow"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"today"==t.todaysOrTommorow[0].startDate)),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](19,B,"today"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"tommorow"==t.todaysOrTommorow[0].startDate)),n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("matDatepicker",i),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",i),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](22,B,"tommorow"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"today"==t.todaysOrTommorow[0].endDate)),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](25,B,"today"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"tommorow"==t.todaysOrTommorow[0].endDate)),n["\u0275\u0275advance"](9),n["\u0275\u0275property"]("list",t.peopleList),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("isAutocomplete",!0),n["\u0275\u0275advance"](15),n["\u0275\u0275property"]("ngIf","Edit"!=t.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==t.mode)}},directives:function(){return[o.J,o.w,o.n,T.a,F.a,m.c,m.g,D.b,o.e,o.v,o.l,v,L.g,L.i,m.i,L.f,a.NgClass,w,k,o.F,U.b,a.NgIf]},styles:[".create-meeting-styles[_ngcontent-%COMP%]   .createMeeting[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-meeting-styles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:50rem}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:44px}.create-meeting-styles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-meeting-styles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.create-meeting-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-meeting-styles[_ngcontent-%COMP%]   .organizer[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:42px}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{width:24rem}"]}),e})()},VsNQ:function(e,t,i){"use strict";i.d(t,"a",(function(){return c}));var n=i("mrSG"),o=i("xG9w"),r=i("XNiG"),a=i("fXoL"),l=i("tk/3"),s=i("flaP");let c=(()=>{class e{constructor(e,t){this.http=e,this._roleService=t,this.contactReload=new r.b,this.isHierarchyVisible=(e,t)=>o.where(this._roleService.roles,{application_id:e,object_id:t}).length>0,this.getContactsByAccounts=e=>this.http.post("api/accounts/getContactsByAccounts",{account_id:e}),this.saveContact=e=>this.http.post("/api/contacts/createContact",{contactDetails:e}),this.deactivateAccess=()=>{let e=o.where(this._roleService.roles,{application_id:34,object_id:29370});return console.log("accessList",e),e.length>0},this.getContactGovernanceType=e=>this.http.post("/api/activity/getGovernanceActivityType",{applicationId:e}),this.getActivityFilterMasterDate=()=>this.http.post("/api/activity/activityFilterMasterData",{}),this.updateTaskDuedate=(e,t)=>this.http.post("/api/contacts/updateTaskDueDate",{activity_id:e,date:t}),this.getActivityNotes=e=>(console.log(e),this.http.post("/api/contacts/activityNotes",{activity_id:e,operation_id:2})),this.createActivityNote=(e,t,i,n)=>this.http.post("/api/contacts/activityNotes",{operation_id:1,activity_id:e,message:n,color_code:t,title:i}),this.editActivityNote=(e,t,i,n)=>this.http.post("/api/contacts/activityNotes",{activity_id:e,operation_id:3,note_id:t,message:i,title:n}),this.deleteActivityNote=(e,t)=>this.http.post("/api/contacts/activityNotes",{activity_id:e,operation_id:4,note_id:t}),this.getUserProfileFromContacts=e=>{try{return new Promise((t,i)=>{this.http.post("/api/contacts/getContactInfo",{oid:e}).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}catch(t){return Promise.reject()}},this.updateTaskDetailInline=(e,t)=>this.http.post("/api/contacts/editTaskInline",{taskFormDetails:t,activityId:e}),this.saveImage=(e,t)=>this.http.post("/api/contacts/saveImage",{result:e,contactId:t}),this.getContactsList=(e,t)=>this.http.post("/api/contacts/getContactsList",{org_codes:t,filterConfig:e}),this.getTotalContacts=(e,t)=>new Promise((i,n)=>{this.http.post("/api/contacts/getTotalContacts",{org_codes:t,filterConfig:e}).subscribe(e=>i(e),e=>n(e))}),this.getContactPersona=()=>new Promise((e,t)=>{this.http.post("/api/contacts/getReportFilterPersonainContact",{}).subscribe(t=>e(t),e=>t(e))}),this.getAccessTokenInfo=()=>this.http.post("/api/collector/getTokenConfigDetails",{}),this.getMailConfigFlag=()=>this.http.post("/api/contacts/getMailConfigFlag",{}),this.getLabelForOpportunity=(e,t)=>{let i=o.where(this._roleService.label,{application_id:e,id:t});return i.length>0?i[0].label_name:""},this.getUDRFContactConfig=()=>new Promise((e,t)=>{this.http.post("/api/contacts/getUDRFContactConfig",{}).subscribe(t=>{e(t)},e=>{t(e)})}),this.getContactHierarchyData=(e,t)=>this.http.post("/api/contacts/getContactHierarchyData",{application_reference_id:e,application_id:t}),this.downloadContactsList=(e,t)=>this.http.post("/api/contacts/downloadContactsList",{org_codes:t,filterConfig:e}),this.checkIfAccountExist=e=>this.http.post("/api/accounts/checkIfAccountExist",{accountId:e})}getAllAccounts(e){return new Promise((t,i)=>{this.http.post("/api/contacts/getAllAccounts",{orgCodes:e}).subscribe(e=>t(e),e=>i(e))})}getContactsByAccountIds(e){return new Promise((t,i)=>{this.http.post("/api/contacts/contactsByAccountIds",{orgCodes:e}).subscribe(e=>t(e),e=>i(e))})}getFilterMaster(){return this.http.get("/api/contacts/masterDataForContactFilter")}getFilterData(e,t){return this.http.post("/api/contacts/ContactsFilter",{filterData:e,orgCodes:t})}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){try{return new Promise((t,i)=>{this.http.post("/api/project/getUserProfileFromDB",{oid:e}).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}catch(t){return Promise.reject()}}))}removeMember(e){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,i)=>{this.http.post("/api/project/removeMember",{stakeholder_table_id:e}).subscribe(e=>t(e))})}))}setContactVipStatus(e,t){return this.http.post("/api/contacts/AddOrRemoveContactFav",{flag:e,contact_id:t})}getContacts(e){return this.http.post("/api/contacts/allActiveContacts",{orgCodes:e})}updateContactById(e,t){return this.http.post("/api/contacts/updateContact",{contact_id:e,contact_details:t})}performAddNotesOperation(e,t,i,n){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:e,operation_id:1,message:t,title:i,color_code:n})}performGetNotes(e){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:e,operation_id:2})}performEditNotes(e,t,i,n,o){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:3,note_id:e,message:t,title:i,color_code:n,contact_id:o})}performDeleteNotes(e,t){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:4,contact_id:e,note_id:t})}getContactsDetailsById(e){return this.http.post("/api/contacts/getContactDetailsById",{contact_id:e})}getContactsOverview(e){return this.http.post("api/contacts/getContactDetailsById",{contact_id:e})}personaMaster(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/contactPersona",{}).subscribe(t=>e(t),e=>t(e))})}getContactAttachments(e){return this.http.post("/api/contacts/getContactAttachments",{contact_id:e})}getCRMAttachmentsConfigs(){return new Promise((e,t)=>{this.http.get("/api/salesMaster/getCRMAttachmentsConfigs").subscribe(t=>e(t),e=>t(e))})}updateContactAttachment(e,t){return this.http.post("/api/contacts/updateContactAttachment",{contact_id:e,file:t})}deleteContactAttachment(e,t){return this.http.post("/api/contacts/deleteContactAttachment",{contact_id:e,file:t})}searchContact(e,t){return this.http.post("/api/contacts/ContactsSearch",{search_parameter:e,orgCodes:t})}deleteContact(e){return this.http.post("/api/contacts/deactivateContact",{contact_id:e})}responseType(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getResponseMaster",{}).subscribe(t=>e(t),e=>t(e))})}contactType(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getContactType",{}).subscribe(t=>e(t),e=>t(e))})}editCallLog(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editCallLog",{activity_id:e,callLogFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createCallLog(e){return this.http.post("/api/contacts/createCallLog",{callLogFormDetails:e})}editMail(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editMail",{activity_id:e,mailFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createMail(e){return this.http.post("/api/contacts/createMail",{mailFormDetails:e})}meetingTypeList(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getSalesActivityLocationType",{}).subscribe(t=>e(t),e=>t(e))})}locationList(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getActivityLocations",{}).subscribe(t=>e(t),e=>t(e))})}peopleList(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/peopleList",{}).subscribe(t=>e(t),e=>t(e))})}editMeeting(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editMeeting",{activity_id:e,meetingFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createMeeting(e){return this.http.post("/api/contacts/createMeeting",{meetingFormDetails:e})}editTask(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editTask",{activity_id:e,taskFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createTask(e){return this.http.post("/api/contacts/createTask",{taskFormDetails:e})}activityList(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/getActivityList",{application_id:e,contact_id:t}).subscribe(e=>i(e),e=>n(e))})}searchContactActivity(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/searchContactActivity",{search_parameter:e,contact_id:t}).subscribe(e=>i(e),e=>n(e))})}getFullDetailOfActivity(e){return this.http.post("/api/contacts/getActivityDetail",{activity_id:e})}openActivity(e){return new Promise((t,i)=>{this.http.post("/api/contacts/openActivity",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}activityDetails(e){return new Promise((t,i)=>{this.http.post("/api/contacts/getActivityDetails",{activity_id:e}).subscribe(e=>(console.log("activitydetail",e),t(e)),e=>i(e))})}completeActivity(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/completeActivity",{activity_id:e,is_completed:t}).subscribe(e=>i(e),e=>n(e))})}startActivity(e){return new Promise((t,i)=>{this.http.post("/api/contacts/startActivity",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}taskFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/taskFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}meetingFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/meetingFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}callLogFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/callLogFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}mailFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/mailFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}deleteActivity(e){return new Promise((t,i)=>{this.http.post("/api/contacts/deleteActivity",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}updateActivityAssignedTo(e,t){return this.http.post("/api/contacts/editActivityAssignedTo",{activityId:e,oid:t})}updatePlannedHours(e,t){return this.http.post("api/contacts/updatePlannedHrsForContactAct",{activity_id:e,planned_hours:t})}updateJobTitle(e,t){return this.http.post("/api/contacts/updateJobTitle",{id:e,jobTitle:t})}updateEmail(e,t){return this.http.post("/api/contacts/updateEmail",{id:e,email:t})}updateMobileNumber(e,t){return this.http.post("/api/contacts/updateMobileNumber",{id:e,mobileNumber:t})}updatePersona(e,t){return this.http.post("/api/contacts/updatePersona",{id:e,personaId:t})}updateContactOwner(e,t,i){return this.http.post("/api/contacts/updateContactOwner",{id:e,name:t,ownerOid:i})}updateLinkedinProfile(e,t){return this.http.post("/api/contacts/updateLinkedinProfile",{id:e,linkedinProfile:t})}updateContactAddress(e,t){return this.http.post("/api/contacts/updateContactAddress",{id:e,address:t})}marketSegment(){return new Promise((e,t)=>{this.http.post("/api/contacts/getMarketSegment",{}).subscribe(t=>e(t),e=>t(e))})}updateMarketSegment(e,t){return this.http.post("/api/contacts/updateMarketSegment",{id:e,market_segment:t})}setContactDunningEmail(e,t){return this.http.post("/api/contacts/setContactAsDunning",{flag:e,contact_id:t})}getFormFieldCollection(){return new Promise((e,t)=>{this.http.post("/api/contacts/getFormFieldCollection",{}).subscribe(t=>{e(t)},e=>{t(e)})})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](l.c),a["\u0275\u0275inject"](s.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},eQOY:function(e,t,i){"use strict";i.r(t),i.d(t,"MeetingFormComponent",(function(){return z}));var n=i("fXoL"),o=i("3Pt+"),r=i("0IaG"),a=i("ofXK"),l=i("jtHE"),s=i("XNiG"),c=i("NJ67"),d=i("1G5W"),m=i("kmnG"),p=i("d3UM"),h=i("FKr1"),u=i("WJ5W");const g=["singleSelect"];function f(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends c.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this.change=new n.EventEmitter,this._onDestroy=new s.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](g,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4),n["\u0275\u0275text"](8,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](9,f,2,2,"mat-option",5),n["\u0275\u0275pipe"](10,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,h.p,u.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();const y=["allSelected"],b=["singleSelect"];function C(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let w=(()=>{class e extends c.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this._onDestroy=new s.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(n["\u0275\u0275viewQuery"](y,!0),n["\u0275\u0275viewQuery"](b,!0)),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:12,vars:11,consts:[["appearance","outline"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value","click"],["allSelected",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4,5),n["\u0275\u0275listener"]("click",(function(){return t.toggleAllSelection()})),n["\u0275\u0275text"](9,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](10,C,2,2,"mat-option",6),n["\u0275\u0275pipe"](11,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",0),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](11,9,t.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,h.p,u.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var S=i("mrSG"),O=i("Kj3r"),E=i("F97M"),x=i("XVR1"),D=i("qFsG"),_=i("/1cH"),F=i("NFeN");function M(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.label)}}function P(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275elementStart"](1,"small"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let k=(()=>{class e extends c.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new s.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new n.EventEmitter,this.selectedUser=new n.EventEmitter,this.label="",this.blur=new n.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new s.b}ngOnInit(){this.userSearchSubject.pipe(Object(O.a)(600)).subscribe(e=>Object(S.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(S.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(S.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(S.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(S.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(S.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(S.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](E.a),n["\u0275\u0275directiveInject"](x.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div"),n["\u0275\u0275elementStart"](1,"mat-form-field",0),n["\u0275\u0275template"](2,M,2,1,"mat-label",1),n["\u0275\u0275elementStart"](3,"input",2),n["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"mat-icon",3),n["\u0275\u0275text"](5,"person_pin"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),n["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),n["\u0275\u0275template"](8,P,3,4,"mat-option",6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](7);n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",t.label),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[m.c,a.NgIf,D.b,_.d,o.e,o.F,o.v,o.k,F.a,m.i,_.b,a.NgForOf,m.g,h.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var I=i("dNgK"),T=i("bTqV"),L=i("iadO"),A=i("RJSY"),j=i("+yIk"),N=i("XXEo"),U=i("ihCf");function V(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",46),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().createMeeting()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function q(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",47),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().editMeeting()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}const B=function(e,t){return{"btn-not-active":e,"btn-active":t}};let z=(()=>{class e{constructor(e,t,i,r,a,l,s,c){this.fb=e,this.dialogRef=t,this.dialogData=i,this.dialog=r,this.activityToMainService=a,this.snackBar=l,this.leadService=s,this.loginService=c,this.close=new n.EventEmitter,this.todaysOrTommorow=[{startDate:null,endDate:null}],this.currentUser=this.loginService.getProfile().profile,this.meetingForm=this.fb.group({title:["",o.H.required],type:[""],location:[""],startDate:[""],endDate:[""],startTime:[""],endTime:[""],people:[""],organizer:["",o.H.required],description:[""],remindMe:[""],applicationName:[""],applicationReferenceId:[""],applicationId:[""],governanceType:[""]}),this.submitted=!1,this.resetButtons=()=>{this.todaysOrTommorow[0].startDate=this.todaysOrTommorow[0].endDate=null},this.patchPeople=e=>{this.meetingForm.patchValue({people:e})},this.dayClicked=(e,t)=>{if(console.log(e,t),"startDate"==e){if(this.todaysOrTommorow[0].startDate=t,"today"==t)this.meetingForm.patchValue({startDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(e.getDate()+1),this.meetingForm.patchValue({startDate:t})}}else if("endDate"==e)if(this.todaysOrTommorow[0].endDate=t,"today"==t)this.meetingForm.patchValue({endDate:new Date});else if("tommorow"==t){const e=new Date,t=new Date(e);t.setDate(t.getDate()+1),this.meetingForm.patchValue({endDate:t})}},this.closeClicked=()=>{this.close.emit()},this.onSubmit=()=>{this.submitted=!0},this.editMeeting=()=>{this.meetingForm.valid?this.leadService.editMeeting(this.activityId,this.meetingForm.value).then(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Edited Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createMeeting=()=>{this.meetingForm.valid?this.leadService.createMeeting(this.meetingForm.value).subscribe(e=>{this.meetingForm.reset(),this.resetButtons(),this.snackBar.open("Meeting Created Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create meeting.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMeetingDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;this.editData=e,console.log("Karthi",this.editData),e?this.meetingForm.patchValue({title:e.title,type:e.type,location:e.location,startDate:e.planned_start_date,endDate:e.task_due_date,startTime:e.start_time,endTime:e.end_time,people:e.people_involved,organizer:e.organizer,description:e.description,remindMe:e.reminder,governanceType:e.governance_activity_id}):console.info("waiting for changes!")}else this.meetingForm.reset()}ngOnInit(){let e;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithMeetingDetails(),"leads"==window.location.pathname.split("/")[2]?e=35:"opportunities"==window.location.pathname.split("/")[2]&&(e=36),this.leadService.getLeadsGovernanceType(75).subscribe(e=>{this.leadGovernanceTypes=e},e=>{console.log(e)}),this.meetingForm.patchValue({applicationName:window.location.pathname.split("/")[2],applicationReferenceId:window.location.pathname.split("/")[3],applicationId:e,governanceType:35,organizer:this.currentUser.oid}),this.leadService.meetingTypeList().then(e=>{console.log(e),this.meetingTypeList=e}),this.leadService.locationList().then(e=>{console.log(e),this.locationList=e,"Edit"==this.mode&&(this.revisedLocationList=this.locationList[this.editData.type])}),this.leadService.peopleList().then(e=>{console.log(e),this.peopleList=e}),this.meetingForm.get("type").valueChanges.subscribe(e=>{console.log(e),this.locationList&&(this.revisedLocationList=this.locationList[e])})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](r.h),n["\u0275\u0275directiveInject"](r.a),n["\u0275\u0275directiveInject"](r.b),n["\u0275\u0275directiveInject"](A.a),n["\u0275\u0275directiveInject"](I.a),n["\u0275\u0275directiveInject"](j.a),n["\u0275\u0275directiveInject"](N.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-meeting-form"]],outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:85,vars:28,consts:[[3,"formGroup","ngSubmit"],[1,"create-meeting-styles","container-fluid"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],[1,"row","pt-1"],[1,"col-6"],["placeholder","Type","formControlName","type",1,"create-account-field-inputsearch",3,"list"],["placeholder","Location","formControlName","location",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","pt-2"],["appearance","outline",1,"create-account-field"],["matInput","","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-5"],[1,"row","p-0"],[1,"col-4",2,"padding-top","14px"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-4","pl-0",2,"padding-top","14px"],[1,"col-4","pt-2","pl-0","pr-0"],["matInput","","type","time","placeholder","time","formControlName","startTime"],["matInput","","formControlName","endDate",3,"matDatepicker"],["picker1",""],["matInput","","type","time","placeholder","time","formControlName","endTime"],[1,"row","pt-2"],["placeholder","People","formControlName","people",1,"create-account-field-inputsearch",3,"list"],[1,"col-6","organizer"],["label","Organizer","required","true","formControlName","organizer",3,"isAutocomplete"],[1,"col-9"],["appearance","outline",1,"textArea",2,"width","37rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],[1,"col-3"],[1,"d-flex","justify-content-center","align-items-center"],["src","https://assets.kebs.app/images/create_meeting_bg.png","height","125","width","160"],[1,"row","pt-3"],[1,"col-8","pl-4","d-flex"],[1,"quotes","my-auto"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Meeting","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Meeting","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"form",0),n["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close("Close")})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"mat-form-field",9),n["\u0275\u0275elementStart"](12,"mat-label"),n["\u0275\u0275text"](13,"Title *"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](14,"input",10),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](15,"div",11),n["\u0275\u0275elementStart"](16,"div",12),n["\u0275\u0275element"](17,"app-input-search",13),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](18,"div",12),n["\u0275\u0275element"](19,"app-input-search",14),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](20,"div",7),n["\u0275\u0275elementStart"](21,"div",15),n["\u0275\u0275elementStart"](22,"mat-form-field",16),n["\u0275\u0275elementStart"](23,"mat-label"),n["\u0275\u0275text"](24,"Start Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](25,"input",17),n["\u0275\u0275element"](26,"mat-datepicker-toggle",18),n["\u0275\u0275element"](27,"mat-datepicker",null,19),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](29,"div",20),n["\u0275\u0275elementStart"](30,"div",21),n["\u0275\u0275elementStart"](31,"div",22),n["\u0275\u0275elementStart"](32,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","today")})),n["\u0275\u0275text"](33," Today"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](34,"div",24),n["\u0275\u0275elementStart"](35,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("startDate","tommorow")})),n["\u0275\u0275text"](36," Tommorow"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](37,"div",25),n["\u0275\u0275elementStart"](38,"mat-form-field",16),n["\u0275\u0275elementStart"](39,"mat-label"),n["\u0275\u0275text"](40,"Time"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](41,"input",26),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](42,"div",7),n["\u0275\u0275elementStart"](43,"div",15),n["\u0275\u0275elementStart"](44,"mat-form-field",16),n["\u0275\u0275elementStart"](45,"mat-label"),n["\u0275\u0275text"](46,"End Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](47,"input",27),n["\u0275\u0275element"](48,"mat-datepicker-toggle",18),n["\u0275\u0275element"](49,"mat-datepicker",null,28),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](51,"div",20),n["\u0275\u0275elementStart"](52,"div",21),n["\u0275\u0275elementStart"](53,"div",22),n["\u0275\u0275elementStart"](54,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","today")})),n["\u0275\u0275text"](55," Today"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](56,"div",24),n["\u0275\u0275elementStart"](57,"button",23),n["\u0275\u0275listener"]("click",(function(){return t.dayClicked("endDate","tommorow")})),n["\u0275\u0275text"](58," Tommorow"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](59,"div",25),n["\u0275\u0275elementStart"](60,"mat-form-field",16),n["\u0275\u0275elementStart"](61,"mat-label"),n["\u0275\u0275text"](62,"Time"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](63,"input",29),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](64,"div",30),n["\u0275\u0275elementStart"](65,"div",12),n["\u0275\u0275element"](66,"app-multi-select-search2",31),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](67,"div",32),n["\u0275\u0275element"](68,"app-search-user",33),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](69,"div",30),n["\u0275\u0275elementStart"](70,"div",34),n["\u0275\u0275elementStart"](71,"mat-form-field",35),n["\u0275\u0275element"](72,"textarea",36,37),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](74,"div",38),n["\u0275\u0275elementStart"](75,"div",39),n["\u0275\u0275element"](76,"img",40),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](77,"div",7),n["\u0275\u0275elementStart"](78,"div",41),n["\u0275\u0275elementStart"](79,"div",42),n["\u0275\u0275elementStart"](80,"span",43),n["\u0275\u0275text"](81,' "In the Middle of difficulty, lies Opportunity" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](82,"div",38),n["\u0275\u0275template"](83,V,3,0,"button",44),n["\u0275\u0275template"](84,q,3,0,"button",45),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](28),i=n["\u0275\u0275reference"](50);n["\u0275\u0275property"]("formGroup",t.meetingForm),n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"]("","Edit"==t.mode?"Edit":"Create new"," Meeting"),n["\u0275\u0275advance"](13),n["\u0275\u0275property"]("list",t.meetingTypeList),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("list",t.revisedLocationList),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("matDatepicker",e),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",e),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](16,B,"tommorow"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"today"==t.todaysOrTommorow[0].startDate)),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](19,B,"today"==t.todaysOrTommorow[0].startDate||null==t.todaysOrTommorow[0].startDate,"tommorow"==t.todaysOrTommorow[0].startDate)),n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("matDatepicker",i),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",i),n["\u0275\u0275advance"](6),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](22,B,"tommorow"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"today"==t.todaysOrTommorow[0].endDate)),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("ngClass",n["\u0275\u0275pureFunction2"](25,B,"today"==t.todaysOrTommorow[0].endDate||null==t.todaysOrTommorow[0].endDate,"tommorow"==t.todaysOrTommorow[0].endDate)),n["\u0275\u0275advance"](9),n["\u0275\u0275property"]("list",t.peopleList),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("isAutocomplete",!0),n["\u0275\u0275advance"](15),n["\u0275\u0275property"]("ngIf","Edit"!=t.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==t.mode)}},directives:function(){return[o.J,o.w,o.n,T.a,F.a,m.c,m.g,D.b,o.e,o.v,o.l,v,L.g,L.i,m.i,L.f,a.NgClass,w,k,o.F,U.b,a.NgIf]},styles:[".create-meeting-styles[_ngcontent-%COMP%]   .createMeeting[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-meeting-styles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-meeting-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.create-meeting-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:50rem}.create-meeting-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:44px}.create-meeting-styles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-meeting-styles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.create-meeting-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.create-meeting-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-meeting-styles[_ngcontent-%COMP%]   .organizer[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:42px}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.create-meeting-styles[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{width:24rem}"]}),e})()}}]);