(window.webpackJsonp=window.webpackJsonp||[]).push([[801],{UFJy:function(t,e,i){"use strict";i.r(e),i.d(e,"SubBusinessUnitsComponent",(function(){return C})),i.d(e,"SubBusinessUnitsModule",(function(){return y}));var s=i("mrSG"),n=i("dHLR"),o=i("33Jv"),a=i("ofXK"),c=i("NFeN"),r=i("bTqV"),l=i("Xa2L"),u=i("Qu3c"),d=i("STbY"),h=i("cguG"),f=i("VI6+"),b=i("fXoL"),g=i("0IaG"),v=i("2Clw"),m=i("ek25");let C=(()=>{class t{constructor(t,e,i,s){this.dialog=t,this._dvService=e,this.changeDectRef=i,this._pmoDashboardService=s,this.subs=new o.a,this.count=0,this.columnConfig=[]}setBodyLayoutConfig(){this.blInstance.layoutConfig.layoutHeight="80vh"}ngOnInit(){}ngAfterViewInit(){return Object(s.c)(this,void 0,void 0,(function*(){this.setBodyLayoutConfig(),this.subs.sink=this._dvService.getUdrfFilterData().subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){t&&(this.filterConfig=t,yield this.getSubBusinessUnitData(t))})))}))}getSubBusinessUnitData(t){return Object(s.c)(this,void 0,void 0,(function*(){this.blInstance.layoutConfig.isMainDataLoading=!0;for(let t=0;t<this._pmoDashboardService.summaryCardData.length;t++)1==this._pmoDashboardService.summaryCardData[t].isSelected&&(this.count+=1,console.log("Count",this.count),this.columnConfig.push(this._pmoDashboardService.summaryCardData[t].type),console.log("columnConfiggg",this.columnConfig));this._dvService.colConfig=this.columnConfig;let e=yield this.getMainListData(t,"sbu");this.formatChildList(e.dataList,!0,0),this.blInstance.mainData.dataList=e.dataList,this.blInstance.mainData.colList=e.colList,this.blInstance.layoutConfig.isMainDataLoading=!1,this.changeDectRef.detectChanges()}))}formatChildList(t,e,i){var s;for(let n of t)n.loadChild=!1,n.showChild=e,n.rowLevel=i,(null===(s=n.children)||void 0===s?void 0:s.length)>0&&this.formatChildList(n.children,!1,i+1)}getMainListData(t,e){return new Promise((i,s)=>{this.subs.sink=this._dvService.getProjectActivityList(t,e).subscribe(t=>{"S"==t.messType&&i(t)},t=>{console.log(t),s(t)})})}openInfoDialog(t){return Object(s.c)(this,void 0,void 0,(function*(){let e={filterConfig:this.filterConfig,dataParams:t,type:"sub-business-units"};const{DetailedTaskDialogComponent:s}=yield i.e(882).then(i.bind(null,"k6no"));this.dialog.open(s,{height:"80%",width:"75%",data:e,panelClass:"detailed-task-dialog"}).afterClosed().subscribe(t=>{console.log(t)})}))}openProjectOverviewDialog(t){}ngOnDestroy(){this.subs.unsubscribe(),console.log("sub business des")}}return t.\u0275fac=function(e){return new(e||t)(b["\u0275\u0275directiveInject"](g.b),b["\u0275\u0275directiveInject"](v.a),b["\u0275\u0275directiveInject"](b.ChangeDetectorRef),b["\u0275\u0275directiveInject"](m.a))},t.\u0275cmp=b["\u0275\u0275defineComponent"]({type:t,selectors:[["app-sub-business-units"]],viewQuery:function(t,e){if(1&t&&b["\u0275\u0275viewQuery"](n.a,!0),2&t){let t;b["\u0275\u0275queryRefresh"](t=b["\u0275\u0275loadQuery"]())&&(e.blInstance=t.first)}},decls:2,vars:0,consts:[[1,"sub-business-units"],[3,"valueClicked"]],template:function(t,e){1&t&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"pmo-body-layout",1),b["\u0275\u0275listener"]("valueClicked",(function(t){return e.openInfoDialog(t)})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]())},directives:[n.a],styles:[""]}),t})(),y=(()=>{class t{}return t.\u0275mod=b["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=b["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.CommonModule,h.a,c.b,r.b,l.b,u.b,d.e,f.b]]}),t})()}}]);