(window.webpackJsonp=window.webpackJsonp||[]).push([[867,861,981],{Vrth:function(e,t,n){"use strict";n.r(t),n.d(t,"RmModule",(function(){return j}));var i=n("ofXK"),o=n("tyNb"),a=n("mrSG"),r=n("5+WD"),s=n("fXoL"),l=n("ECHL"),c=n("0IaG"),d=n("NFeN"),h=n("1jcm");function g(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",7),s["\u0275\u0275elementStart"](1,"mat-icon"),s["\u0275\u0275text"](2,"drag_indicator"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-slide-toggle",8),s["\u0275\u0275listener"]("change",(function(n){s["\u0275\u0275restoreView"](e);const i=t.index;return s["\u0275\u0275nextContext"]().changed(n,i)})),s["\u0275\u0275text"](4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("checked",e.isActive),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](e.colName)}}let m=(()=>{class e{constructor(e,t){this.rmservice=e,this.dialog=t,this.colList=[],this.applyColumn=()=>{this.rmservice.colList.splice(0);for(let e=0;e<this.colList.length;e++)this.rmservice.colList[e]=this.colList[e];this.colList.splice(0),this.dialog.closeAll()}}ngOnInit(){for(let e=0;e<this.rmservice.colList.length;e++)this.colList[e]=this.rmservice.colList[e]}onDrop(e){e.previousContainer===e.container?Object(r.h)(e.container.data,e.previousIndex,e.currentIndex):Object(r.i)(e.previousContainer.data,e.container.data,e.previousIndex,e.currentIndex)}changed(e,t){this.colList[t].isActive=e.checked}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](l.a),s["\u0275\u0275directiveInject"](c.b))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-customize-dialog"]],decls:15,vars:2,consts:[[1,"customize"],[1,"container"],["cdkDropList","",1,"movie-list",3,"cdkDropListData","cdkDropListDropped"],["moviesList","cdkDropList"],["class","movie-block","cdkDrag","",4,"ngFor","ngForOf"],[2,"float","right"],[1,"apply",3,"click"],["cdkDrag","",1,"movie-block"],[3,"checked","change"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div"),s["\u0275\u0275elementStart"](3,"p"),s["\u0275\u0275text"](4,"Settings"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"p"),s["\u0275\u0275text"](6,"Switch ON/OFF the filters of your choice"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"div",2,3),s["\u0275\u0275listener"]("cdkDropListDropped",(function(e){return t.onDrop(e)})),s["\u0275\u0275template"](9,g,5,2,"div",4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",5),s["\u0275\u0275elementStart"](11,"button",6),s["\u0275\u0275listener"]("click",(function(){return t.applyColumn()})),s["\u0275\u0275elementStart"](12,"mat-icon"),s["\u0275\u0275text"](13,"done_all"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](14," Apply "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("cdkDropListData",t.colList),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",t.colList))},directives:[r.e,i.NgForOf,d.a,r.a,h.a],styles:[".customize[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{margin:30px 15px 15px;padding-right:50px;display:inline-block;vertical-align:top}.customize[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.customize[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.customize[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.customize[_ngcontent-%COMP%]   .movie-block[_ngcontent-%COMP%]:last-child{border:none}.customize[_ngcontent-%COMP%]   .movie-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .movie-block[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.customize[_ngcontent-%COMP%]   .apply[_ngcontent-%COMP%]{padding:10px;border-radius:8px;background-color:rgba(208,12,12,.54);color:#fff;border:none;display:flex}.mat-icon[_ngcontent-%COMP%]{height:0}.material-icons[_ngcontent-%COMP%]{font-size:15px}  .mat-slide-toggle .mat-slide-toggle-bar{width:25px}  .mat-slide-toggle .mat-slide-toggle-thumb{background-color:#fff;width:10px;height:10px;margin-left:4px;margin-top:5px}  .mat-slide-toggle.mat-checked .mat-slide-toggle-bar{background-color:rgba(208,12,12,.54);width:25px}  .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb{background-color:#fff;width:10px;height:10px;margin-left:-4px;margin-top:5px}  .mat-dialog-container{overflow:unset}"]}),e})();var p=n("GnQ3"),u=n("LcQX"),b=n("wZkO");function C(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"a",3,4),s["\u0275\u0275listener"]("click",(function(n){s["\u0275\u0275restoreView"](e);const i=t.$implicit;return s["\u0275\u0275nextContext"]().disableTab(n,i.id)})),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=s["\u0275\u0275reference"](1);s["\u0275\u0275property"]("routerLink",e.link)("disabled",!e.isActive)("active",n.isActive),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"]("",e.label," ")}}let O=(()=>{class e{constructor(e,t,n,i,o){this._router=e,this._rmService=t,this.dialog=n,this.udrfService=i,this.utilityService=o,this.show=!0,this.navMenu=[{id:0,label:"Requests",link:"home/requests",isActive:!0},{id:1,label:"People",link:"home/people",isActive:!0}],this.listMenu=e=>{"list"===e?this._rmService.tableView="list":"tile"===e&&(this._rmService.tableView="tile")}}ngOnInit(){}disableTab(e,t){console.log(e,t);for(let n=0;n<this.navMenu.length;n++)this.navMenu[n].isActive=n==t;setTimeout(()=>{for(let e=0;e<this.navMenu.length;e++)this.navMenu[e].isActive=!0},2e3)}customizeDialog(){this.dialog.open(m,{height:"400px",width:"450px",position:{top:"0px"}})}openUdrfModal(){return Object(a.c)(this,void 0,void 0,(function*(){const{UdrfModalComponent:e}=yield Promise.all([n.e(4),n.e(998)]).then(n.bind(null,"UIsE"));this.dialog.open(e,{minWidth:"100%",height:"84%",position:{top:"0px",left:"77px"},disableClose:!0})}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](o.g),s["\u0275\u0275directiveInject"](l.a),s["\u0275\u0275directiveInject"](c.b),s["\u0275\u0275directiveInject"](p.a),s["\u0275\u0275directiveInject"](u.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-rm-landing-page"]],decls:4,vars:1,consts:[[1,"rmg-landing"],["mat-tab-nav-bar",""],["mat-tab-link","","label","app.label","routerLinkActive","",3,"routerLink","disabled","active","click",4,"ngFor","ngForOf"],["mat-tab-link","","label","app.label","routerLinkActive","",3,"routerLink","disabled","active","click"],["rla","routerLinkActive"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"nav",1),s["\u0275\u0275template"](2,C,3,4,"a",2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](3,"router-outlet")),2&e&&(s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",t.navMenu))},directives:[b.f,i.NgForOf,o.l,o.j,b.e,o.i],styles:[".rmg-landing[_ngcontent-%COMP%]   .changeColor[_ngcontent-%COMP%]{color:#79ba44}.rmg-landing[_ngcontent-%COMP%]   body[_ngcontent-%COMP%], .rmg-landing[_ngcontent-%COMP%]   html[_ngcontent-%COMP%]{height:100%}.rmg-landing[_ngcontent-%COMP%]     .mat-tab-nav-bar.mat-primary .mat-ink-bar{background-color:#79ba44!important}.rmg-landing[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%]:focus:not(.mat-tab-disabled){color:#79ba44!important}.rmg-landing[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%]{min-width:0!important}.rmg-landing[_ngcontent-%COMP%]   .mat-tab-nav-bar.mat-primary[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%]{background-color:#79ba44!important;visibility:visible!important;left:0!important;width:92px!important}.rmg-landing[_ngcontent-%COMP%]   .icon-v1[_ngcontent-%COMP%]{border-left:1px solid #e8e9ee;height:40px;right:0;margin-top:6px}.rmg-landing[_ngcontent-%COMP%]   .header-button[_ngcontent-%COMP%]{color:#6b7a99;font-size:12px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;font-family:Roboto;padding:10px}.rmg-landing[_ngcontent-%COMP%]   .header-button-icon[_ngcontent-%COMP%]{color:#526179;font-size:17px;height:16px}.rmg-landing[_ngcontent-%COMP%]   .header-menu-icon-done[_ngcontent-%COMP%]{color:red;font-size:16px;height:20px;margin-right:0!important;margin-top:0;margin-left:8px}.rmg-landing[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]{line-height:8px;width:auto;margin-right:10px!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both;border-radius:10px}.rmg-landing[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{background:none;outline:none;border:unset;padding-top:10px;display:flex;align-items:center}.rmg-landing[_ngcontent-%COMP%]   .example-spacer[_ngcontent-%COMP%]{flex:1.5 0 auto}.rmg-landing[_ngcontent-%COMP%]   .mat-tab-link-container[_ngcontent-%COMP%]{display:flex;flex-grow:1;overflow:hidden;z-index:1;height:17vh}.rmg-landing[_ngcontent-%COMP%]   .menuLine[_ngcontent-%COMP%]{border:.5px solid hsla(0,0%,50.2%,.2784313725490196);width:auto;margin-left:10px;margin-right:10px}.rmg-landing[_ngcontent-%COMP%]   .mat-menu-item[_ngcontent-%COMP%]{height:33px!important;font-size:smaller!important;line-height:0!important}.rmg-landing[_ngcontent-%COMP%]    .mat-menu-panel{min-width:140px!important}.rmg-landing[_ngcontent-%COMP%]   .header-menu-icon[_ngcontent-%COMP%]{font-size:16px;height:16px;margin-right:0!important}.rmg-landing[_ngcontent-%COMP%]   .header-menu[_ngcontent-%COMP%]{font-size:12px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;font-family:Roboto}.rmg-landing[_ngcontent-%COMP%]     .mat-tab-link{font-family:Plus Jakarta Sans}"]}),e})();var f=n("XXEo"),v=n("1A3m");let _=(()=>{class e{constructor(e,t,n,i){this.loginService=e,this.toaster=t,this.router=n,this.rmService=i}canActivate(e){return Object(a.c)(this,void 0,void 0,(function*(){let e=yield this.canAccess();return 0==e&&(console.log("People Allocation Access"),this.toaster.showError("You do not have access to People Allocation Application!","Access Restricted",1e4),this.router.navigateByUrl("/main")),e}))}canAccess(){return new Promise((e,t)=>Object(a.c)(this,void 0,void 0,(function*(){yield this.rmService.checkPeopleAllocationAccess().then(t=>Object(a.c)(this,void 0,void 0,(function*(){e(t)})),e=>{t(!1)})})))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](f.a),s["\u0275\u0275inject"](v.a),s["\u0275\u0275inject"](o.g),s["\u0275\u0275inject"](l.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const x=[{path:"",component:O,children:[{path:"",canActivate:[_],redirectTo:"home/requests",pathMatch:"full"},{path:"home/people",canActivate:[_],loadChildren:()=>Promise.all([n.e(4),n.e(13),n.e(14),n.e(15),n.e(16),n.e(17),n.e(18),n.e(20),n.e(19),n.e(23),n.e(32),n.e(33),n.e(35),n.e(41),n.e(51),n.e(47),n.e(55),n.e(89),n.e(112),n.e(99),n.e(166),n.e(150),n.e(167),n.e(633)]).then(n.bind(null,"XCgv")).then(e=>e.RmEmployeeLandingModule),data:{breadcrumb:"People"}},{path:"home/requests",canActivate:[_],loadChildren:()=>Promise.all([n.e(4),n.e(13),n.e(14),n.e(16),n.e(17),n.e(18),n.e(40),n.e(112),n.e(635)]).then(n.bind(null,"9Nz8")).then(e=>e.RmRequestLandingModule),data:{breadcrumb:"Requests"}}]},{path:"home/request-detail",canActivate:[_],loadChildren:()=>Promise.all([n.e(13),n.e(14),n.e(15),n.e(20),n.e(19),n.e(23),n.e(32),n.e(33),n.e(47),n.e(55),n.e(99),n.e(166),n.e(634)]).then(n.bind(null,"AJhU")).then(e=>e.RmRequestDetailModule),data:{breadcrumb:"Requests"}},{path:"home/admin",canActivate:[_],loadChildren:()=>Promise.all([n.e(31),n.e(32),n.e(35),n.e(41),n.e(49),n.e(165),n.e(631)]).then(n.bind(null,"1CHW")).then(e=>e.RmAdminModule),data:{breadcrumb:"RM Admin"}},{path:"home/no-access",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-no-access"]],decls:2,vars:0,template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"p"),s["\u0275\u0275text"](1,"no-access works!"),s["\u0275\u0275elementEnd"]())},styles:[""]}),e})()}];let w=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(x)],o.k]}),e})();var M=n("Xi0T"),k=n("/U/0"),E=n("lr1n"),P=n("Qu3c"),S=n("/t3+"),y=n("Wp6s"),I=n("bSwM"),z=n("d3UM"),R=n("STbY"),A=n("ZzPI");let j=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,w,h.b,r.g,M.a,b.g,d.b,P.b,S.b,y.d,I.b,z.d,R.e,A.b,k.a,E.a]]}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"b",(function(){return d}));var i=n("jhN1"),o=n("fXoL"),a=n("oHs6"),r=n("PVOt"),s=n("6t9p");const l=["*"];let c=(()=>{let e=class extends r.b{constructor(e,t,n,i,o,a,r,s){super(e,t,n,i,r,s),this._watcherHelper=i,this._idh=o,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),a.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](r.e),o["\u0275\u0275directiveInject"](r.j),o["\u0275\u0275directiveInject"](r.g),o["\u0275\u0275directiveInject"](r.i),o["\u0275\u0275directiveInject"](i.h),o["\u0275\u0275directiveInject"](o.PLATFORM_ID))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&o["\u0275\u0275contentQuery"](n,s.L,!1),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[o["\u0275\u0275ProvidersFeature"]([r.e,r.j,r.i,r.g]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(o["\u0275\u0275projectionDef"](),o["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,r.c,r.f,i.b],s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,r.f]}),e})()}}]);