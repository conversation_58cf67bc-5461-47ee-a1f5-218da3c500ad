(window.webpackJsonp=window.webpackJsonp||[]).push([[835],{"0YNG":function(t,e,i){"use strict";i.r(e),i.d(e,"OwnerNameDialogComponent",(function(){return m})),i.d(e,"taskDetailsDialogModule",(function(){return g}));var n=i("3Pt+"),s=i("0IaG"),r=i("Xi0T"),a=i("ofXK"),o=i("bTqV"),l=i("NFeN"),p=i("kmnG"),c=i("d3UM"),h=i("fXoL"),d=i("aaUa"),u=i("ClnO");let m=(()=>{class t{constructor(t,e,i,s){this.matDialogRef=t,this.fb=e,this.matData=i,this._ProjectV3Service=s,this.taskDetail=this.fb.group({ownerName:["",n.H.required],skillRole:["",n.H.required],location:["",n.H.required],nationality:["",n.H.required]}),this.params={},console.log(this.matData),this.params.projectId=this.matData.projectId,console.log(this.params)}ngOnInit(){}save(){this.taskDetail.invalid?this._ProjectV3Service.showSnack("Kindly fill all details !"):(console.log(this.taskDetail),this.matDialogRef.close({owner:this.taskDetail.value.ownerName,nationality:this.taskDetail.value.nationality,location:this.taskDetail.value.location,skillRole:this.taskDetail.value.skillRole}))}close(){this.matDialogRef.close()}}return t.\u0275fac=function(e){return new(e||t)(h["\u0275\u0275directiveInject"](s.h),h["\u0275\u0275directiveInject"](n.i),h["\u0275\u0275directiveInject"](s.a),h["\u0275\u0275directiveInject"](d.a))},t.\u0275cmp=h["\u0275\u0275defineComponent"]({type:t,selectors:[["app-owner-name-dialog"]],decls:18,vars:16,consts:[[1,"wrapper","m-3"],[1,"d-flex","align-items-center","justify-content-between"],[1,"app-color","m-0","mb-2"],["mat-icon-button","",3,"click"],[1,"d-flex","mt-3",2,"gap","1rem","align-items","end"],[3,"formGroup"],["formControlName","ownerName",3,"label","setObjAsValue","apiParams","apiUri","fieldCtrl"],["formControlName","skillRole",3,"label","apiUri","fieldCtrl"],["formControlName","location",3,"label","apiUri","fieldCtrl"],["formControlName","nationality",3,"label","apiUri","fieldCtrl"],[2,"width","3rem"],[1,"mt-3",2,"float","right"],["mat-icon-button","",1,"app-color",3,"disabled","click"]],template:function(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",0),h["\u0275\u0275elementStart"](1,"div",1),h["\u0275\u0275elementStart"](2,"h3",2),h["\u0275\u0275text"](3,"Owner Details"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"button",3),h["\u0275\u0275listener"]("click",(function(){return e.close()})),h["\u0275\u0275elementStart"](5,"mat-icon"),h["\u0275\u0275text"](6,"close"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"div",4),h["\u0275\u0275elementStart"](8,"div",5),h["\u0275\u0275element"](9,"app-input-search-scroll",6),h["\u0275\u0275element"](10,"app-input-search-scroll",7),h["\u0275\u0275element"](11,"app-input-search-scroll",8),h["\u0275\u0275element"](12,"app-input-search-scroll",9),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](13,"div",10),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](14,"div",11),h["\u0275\u0275elementStart"](15,"button",12),h["\u0275\u0275listener"]("click",(function(){return e.save()})),h["\u0275\u0275elementStart"](16,"mat-icon"),h["\u0275\u0275text"](17,"done_all"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t&&(h["\u0275\u0275advance"](8),h["\u0275\u0275property"]("formGroup",e.taskDetail),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("label","Project Owner")("setObjAsValue",!0)("apiParams",e.params)("apiUri","/api/project/v2/getProjectStakeHolders")("fieldCtrl",e.taskDetail.controls.ownerName),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("label","Skill Role")("apiUri","/api/project/v2/getMasterSkillRole")("fieldCtrl",e.taskDetail.controls.skillRole),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("label","Location")("apiUri","/api/project/v2/getMasterLocation")("fieldCtrl",e.taskDetail.controls.location),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("label","Nationality")("apiUri","/api/project/v2/getMasterNationality")("fieldCtrl",e.taskDetail.controls.nationality),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("disabled",null==e.taskDetail?null:e.taskDetail.invalid))},directives:[o.a,l.a,n.w,n.n,u.a,n.v,n.l],styles:[".app-color[_ngcontent-%COMP%]{color:#cf0001}"]}),t})(),g=(()=>{class t{}return t.\u0275mod=h["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=h["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.CommonModule,p.e,s.g,r.a,c.d,o.b,l.b,n.p,n.E]]}),t})()},ClnO:function(t,e,i){"use strict";i.d(e,"a",(function(){return x}));var n=i("mrSG"),s=i("fXoL"),r=i("3Pt+"),a=i("33Jv"),o=i("Kj3r"),l=i("tk/3"),p=i("aaUa"),c=i("XXEo"),h=i("kmnG"),d=i("d3UM"),u=i("qFsG"),m=i("FKr1"),g=i("ofXK"),v=i("Qu3c"),f=i("mS9j");const j=["input"],y=["txtSearch"];function b(t,e){1&t&&s["\u0275\u0275element"](0,"br")}function S(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"span"),s["\u0275\u0275element"](1,"app-user-profile",10),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"]().$implicit,e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("oid",t[e.optionLable[0]])}}function I(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"mat-option",8),s["\u0275\u0275elementStart"](1,"span",9),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](3,b,1,0,"br",6),s["\u0275\u0275template"](4,S,2,1,"span",6),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,i=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("value",i.setObjAsValue?t:null==t?null:t.id),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matTooltip",null==t?null:t.name),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](null==t?null:t.name),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",i.optionLable.length>0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",i.optionLable.length>0)}}function C(t,e){if(1&t&&(s["\u0275\u0275elementContainerStart"](0),s["\u0275\u0275template"](1,I,5,5,"mat-option",7),s["\u0275\u0275elementContainerEnd"]()),2&t){const t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",t.itemList)}}function k(t,e){1&t&&s["\u0275\u0275element"](0,"br")}function L(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"span"),s["\u0275\u0275element"](1,"app-user-profile",10),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"]().$implicit,e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("oid",t[e.optionLable[0]])}}function w(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"mat-option",8),s["\u0275\u0275elementStart"](1,"span",9),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](3,k,1,0,"br",6),s["\u0275\u0275template"](4,L,2,1,"span",6),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,i=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("value",i.setObjAsValue?t:null==t?null:t.id),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matTooltip",null==t?null:t.name),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](null==t?null:t.name),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",i.optionLable.length>0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",i.optionLable.length>0)}}function _(t,e){if(1&t&&(s["\u0275\u0275elementContainerStart"](0),s["\u0275\u0275template"](1,w,5,5,"mat-option",7),s["\u0275\u0275elementContainerEnd"]()),2&t){const t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",t.searchItemList)}}let x=(()=>{class t{constructor(t,e,i){this._ProjectV3Service=t,this._loginService=e,this.http=i,this.RELOAD_TOP_SCROLL_POSITION=100,this.fieldCtrl=new r.j,this.label="Name",this.setObjAsValue=!1,this.optionLable=[],this.subs=new a.a,this.searchFieldCtrl=new r.j,this.itemList=[],this.isSearchItem=!1,this.searchItemList=[],this.innerValue="",this.isListFull=!1,this.isSearchListFull=!1,this.isScrollingMatSelect=!1,this.errors=["This field is required"],this.getArrayList=(t,e)=>{let i={searchText:t,skip:e,limit:10};return this.apiParams&&(i=Object.assign(Object.assign({},i),this.apiParams)),new Promise((t,e)=>{this.subs.sink=this.http.post(this.apiUri,i,{headers:new l.f({Authorization:"Bearer "+this._loginService.getToken()})}).subscribe(e=>{t(e.data)},t=>{console.log("Error in getting data"),e(t)})})},this.propagateChange=t=>{}}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){try{let t=yield this.getArrayList("",this.itemList.length);this.itemList.push(...t)}catch(t){console.error(t)}}))}ngAfterViewInit(){var t,e;this.subs.sink=null===(t=this.fieldCtrl)||void 0===t?void 0:t.valueChanges.subscribe(()=>{""!=this.fieldCtrl.value&&null!=this.fieldCtrl.value&&null!=this.fieldCtrl.value||(this.innerValue="",this.inputRef.nativeElement.value="")}),this.subs.sink=null===(e=this.searchFieldCtrl)||void 0===e?void 0:e.valueChanges.pipe(Object(o.a)(700)).subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(t),this.searchString=t,""==t||null==t||null==t||0==(null==t?void 0:t.length))this.isSearchItem=!1;else{this.isSearchItem=!0;try{this.searchItemList=[],this.isSearchListFull=!1,console.log("search");let e=yield this.getArrayList(t,this.searchItemList.length);this.searchItemList.push(...e)}catch(e){console.error(e)}}})))}registerPanelScrollEvent(t){var e;if(this.txtSearchRef.nativeElement.focus(),this.isListFull)return;const i=null===(e=null==t?void 0:t.panel)||void 0===e?void 0:e.nativeElement;null==i||i.addEventListener("scroll",t=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log("scroll ",this.isSearchItem,this.isSearchListFull),!(this.isScrollingMatSelect||!this.isSearchItem&&this.isListFull||this.isSearchItem&&this.isSearchListFull)&&t.target.scrollTop>this.RELOAD_TOP_SCROLL_POSITION)try{if(this.isScrollingMatSelect=!0,this.isSearchItem){let t=yield this.getArrayList(this.searchString,this.searchItemList.length);this.searchItemList.push(...t),0==t.length&&(this.isSearchListFull=!0)}else{let t=yield this.getArrayList("",this.itemList.length);this.itemList.push(...t),0==t.length&&(this.isListFull=!0)}this.isScrollingMatSelect=!1}catch(e){console.error(e),this.isScrollingMatSelect=!1}})))}get value(){return this.innerValue}set value(t){t!==this.innerValue&&(this.innerValue=t)}onChange(t){var e;for(var i in this.innerValue=t,this.propagateChange(this.innerValue),this.errors=[],null===(e=this.fieldCtrl)||void 0===e?void 0:e.errors)this.fieldCtrl.errors.hasOwnProperty(i)&&this.errors.push("required"===i?"This field is required":this.fieldCtrl.errors[i])}writeValue(t){this.innerValue=t}registerOnTouched(t){}registerOnChange(t){this.propagateChange=t}ngOnDestroy(){this.subs.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](p.a),s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](l.c))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["app-input-search-scroll"]],viewQuery:function(t,e){if(1&t&&(s["\u0275\u0275viewQuery"](j,!0),s["\u0275\u0275viewQuery"](y,!0)),2&t){let t;s["\u0275\u0275queryRefresh"](t=s["\u0275\u0275loadQuery"]())&&(e.inputRef=t.first),s["\u0275\u0275queryRefresh"](t=s["\u0275\u0275loadQuery"]())&&(e.txtSearchRef=t.first)}},inputs:{fieldCtrl:"fieldCtrl",label:"label",setObjAsValue:"setObjAsValue",apiUri:"apiUri",apiParams:"apiParams",optionLable:"optionLable"},features:[s["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(s.forwardRef)(()=>t),multi:!0}])],decls:11,vars:4,consts:[["appearance","outline",1,"w-100"],[3,"selectionChange","openedChange"],["input",""],["matInput","","placeholder","Search..",1,"user-inp",3,"formControl","keydown"],["txtSearch",""],[1,"custom-opt"],[4,"ngIf"],["class","custom-opt",3,"value",4,"ngFor","ngForOf"],[1,"custom-opt",3,"value"],[3,"matTooltip"],["type","name",3,"oid"]],template:function(t,e){if(1&t){const t=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-form-field",0),s["\u0275\u0275elementStart"](1,"mat-label"),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-select",1,2),s["\u0275\u0275listener"]("selectionChange",(function(t){return e.onChange(t.value)}))("openedChange",(function(){s["\u0275\u0275restoreView"](t);const i=s["\u0275\u0275reference"](4);return e.registerPanelScrollEvent(i)})),s["\u0275\u0275elementStart"](5,"input",3,4),s["\u0275\u0275listener"]("keydown",(function(t){return t.stopPropagation()})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"mat-option",5),s["\u0275\u0275text"](8,"(None)"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](9,C,2,1,"ng-container",6),s["\u0275\u0275template"](10,_,2,1,"ng-container",6),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}2&t&&(s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](e.label),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("formControl",e.searchFieldCtrl),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",!e.isSearchItem),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.isSearchItem))},directives:[h.c,h.g,d.c,u.b,r.e,r.v,r.k,m.p,g.NgIf,g.NgForOf,v.a,f.a],styles:[".user-inp[_ngcontent-%COMP%]{padding:0 1rem;height:40px}.custom-opt[_ngcontent-%COMP%]{line-height:2em!important;height:auto!important;border-bottom:groove;border-bottom-color:#fff}"]}),t})()},aaUa:function(t,e,i){"use strict";i.d(e,"a",(function(){return l}));var n=i("mrSG"),s=i("xG9w"),r=i("fXoL"),a=i("tk/3"),o=i("dNgK");let l=(()=>{class t{constructor(t,e){this._http=t,this._snack=e,this.typeApiMapping=[],this.showSnack=t=>{this._snack.open(t,"Dismiss",{duration:2e3})},this.getCurrencyList=(t,e,i)=>this._http.post("/api/project/v2/getCurrencyList",{searchText:t,skip:e,limit:i}),this.getProjectType=(t,e,i)=>this._http.post("/api/project/v2/getProjectTypes",{searchText:t,skip:e,limit:i}),this.getCustomerTaxList=(t,e,i)=>this._http.post("/api/project/v2/getCustomerTaxList",{searchText:t,skip:e,limit:i}),this.getIndustries=(t,e,i)=>this._http.post("/api/project/v2/getIndustries",{searchText:t,skip:e,limit:i}),this.getCustomerList=(t,e,i)=>this._http.post("/api/project/v2/getCustomerList",{searchText:t,skip:e,limit:i}),this.getProjectMethodology=(t,e,i)=>this._http.post("/api/project/v2/getProjectMethodology",{searchText:t,skip:e,limit:i}),this.getLegalEntities=(t,e,i)=>this._http.post("/api/project/v2/getLegalEntities",{searchText:t,skip:e,limit:i}),this.getEmployeesList=(t,e,i)=>this._http.post("/api/project/v2/getEmployeesList",{searchText:t,skip:e,limit:i}),this.getTypeApiMapping=()=>{this._http.post("/api/project/v2/getTypeApiMapping",{}).subscribe(t=>{console.log(t),this.typeApiMapping=t.data},t=>{console.log(t)})},this.createProject=t=>this._http.post("/api/project/v2/createProject",t),this.getProjectGanttData=(t,e,i)=>this._http.post("/api/project/v2/getProjectGanttData",{projectId:t,id:e,version:i}),this.createItem=t=>this._http.post("/api/project/v2/createItem",{itemDetails:t}),this.updateItem=t=>this._http.post("/api/project/v2/updateItem",{items:t}),this.deleteItem=(t,e,i)=>this._http.post("/api/project/v2/deleteItem",{projectId:t,version:i,id:e}),this.createMilestone=t=>this._http.post("/api/project/v2/createMilestone",{milestoneDetails:t}),this.updateMilestone=t=>this._http.post("/api/project/v2/updateMilestone",{items:t}),this.deleteMilestone=(t,e,i)=>this._http.post("/api/project/v2/deleteMilestone",{projectId:t,version:i,id:e}),this.createActivity=t=>this._http.post("/api/project/v2/createActivity",{activityDetails:t}),this.updateActivity=t=>this._http.post("/api/project/v2/updateActivity",{items:t}),this.deleteActivity=(t,e,i)=>this._http.post("/api/project/v2/deleteActivity",{projectId:t,version:i,id:e}),this.addLink=(t,e,i)=>this._http.post("/api/project/v2/addLink",{projectId:t,version:e,linkDetails:i}),this.deleteLink=(t,e,i)=>this._http.post("/api/project/v2/deleteLink",{projectId:t,version:e,linkId:i}),this.getGanttGroupingFields=t=>this._http.post("/api/project/v2/getGanttGroupingFields",{projectId:t}),this.groupGanttItems=(t,e)=>this._http.post("/api/project/v2/groupGanttItems",{projectId:t,version:e}),this.sortGanttItems=(t,e,i)=>this._http.post("/api/project/v2/sortGanttItems",{projectId:t,version:e,items:i}),this.getItemOwnersForProject=(t,e)=>this._http.post("/api/project/v2/getItemOwnersForProject",{projectId:t,is_resource_view:e}),this.specificFieldsUpdate=t=>this._http.post("/api/project/v2/specificFieldsUpdate",{details:t}),this.createHandler=(t,e)=>Object(n.c)(this,void 0,void 0,(function*(){try{console.log(t);let i=s.find(this.typeApiMapping,e=>e.id==t);return void(i&&(yield this[i.create_api](e)))}catch(i){return console.log(i),void this.showSnack("Error handling the created task !")}})),this.getGanttColumnConfig=()=>Object(n.c)(this,void 0,void 0,(function*(){try{return new Promise((t,e)=>{this._http.post("/api/project/v2/getGanttColumns",{}).subscribe(i=>i.err?e(i):t(i.data),t=>(console.log(t),e(t)))})}catch(t){console.log(t),this.showSnack("Error while getting column config")}})),this.getTypeApiMapping()}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](a.c),r["\u0275\u0275inject"](o.a))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);