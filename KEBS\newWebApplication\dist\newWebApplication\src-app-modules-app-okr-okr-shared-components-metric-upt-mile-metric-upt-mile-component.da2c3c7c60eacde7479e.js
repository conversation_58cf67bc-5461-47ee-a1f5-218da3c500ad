(window.webpackJsonp=window.webpackJsonp||[]).push([[907],{aQly:function(e,t,n){"use strict";n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return v}));var i=n("fXoL"),o=n("3Pt+"),r=n("rDax"),l=n("+rOU"),a=n("ofXK"),s=n("NFeN");const c=["field"],d=function(e){return{"background-color":e}};function p(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"li",7),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit,o=i["\u0275\u0275nextContext"](2);return o.optionSelected(n),o.closeOverlay()})),i["\u0275\u0275element"](1,"span",2),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](2,d,e.color)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"]("\xa0\xa0 ",e.name," ")}}function u(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",4),i["\u0275\u0275text"](1," change_history "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"ul",5),i["\u0275\u0275template"](3,p,3,4,"li",6),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",e.list)}}const f={provide:o.t,useExisting:Object(i.forwardRef)(()=>h),multi:!0};let h=(()=>{class e{constructor(e,t){this.viewContainerRef=e,this.overlay=t,this.selectedStatus=new o.j,this.isOpen=!1}ngOnInit(){}optionSelected(e){this.selectedStatus.patchValue(e),this.isOpen=!1,this.onChange&&this.onChange(e)}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}writeValue(e){this.selectedStatus.patchValue(e)}openOverlay(){var e,t,n;if(null==(null===(e=this.overlayRef)||void 0===e?void 0:e.hasAttached())||0==(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const e=this.overlay.position().flexibleConnectedTo(this.fieldRef.nativeElement).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(50).withGrowAfterOpen(!0).withPositions([{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}]),t=this.overlay.scrollStrategies.close();this.overlayRef=this.overlay.create({positionStrategy:e,scrollStrategy:t,hasBackdrop:!0});const n=new l.h(this.templateRef,this.viewContainerRef);this.overlayRef.attach(n),this.overlayRef.backdropClick().subscribe(()=>{var e;null===(e=this.overlayRef)||void 0===e||e.dispose()})}console.log(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.ViewContainerRef),i["\u0275\u0275directiveInject"](r.e))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["d1-Kebs-statusInline-tooltip"]],viewQuery:function(e,t){if(1&e&&(i["\u0275\u0275staticViewQuery"](c,!0),i["\u0275\u0275staticViewQuery"](i.TemplateRef,!0)),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.fieldRef=e.first),i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.templateRef=e.first)}},inputs:{list:"list"},features:[i["\u0275\u0275ProvidersFeature"]([f])],decls:6,vars:5,consts:[["cdkOverlayOrigin","",2,"cursor","pointer",3,"click"],["trigger","cdkOverlayOrigin","field",""],[1,"dot",3,"ngStyle"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],[1,"material-icons","triangle"],[1,"example-list"],[3,"click",4,"ngFor","ngForOf"],[3,"click"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",0,1),i["\u0275\u0275listener"]("click",(function(){return t.openOverlay()})),i["\u0275\u0275element"](3,"span",2),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,u,4,1,"ng-template",3)),2&e){const e=i["\u0275\u0275reference"](1);i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](3,d,t.selectedStatus.value.color)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"]("\xa0\xa0 ",t.selectedStatus.value.name,""),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e)}},directives:[r.b,a.NgStyle,r.a,a.NgForOf],styles:[".example-list[_ngcontent-%COMP%]{width:145px;border:1px solid #ccc;border-radius:5px;background:#fff;padding:10px;margin:0;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}  .cdk-overlay-pane{margin-top:7px}.example-list[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{list-style-type:none;padding:8px 0;cursor:pointer}.example-list[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]:last-child{border-bottom:none}.dot[_ngcontent-%COMP%]{height:15px;width:15px;background-color:#bbb;border-radius:50%;display:inline-block}.triangle[_ngcontent-%COMP%]{color:#bbb1b1;position:relative;top:-11px;left:77px;z-index:-1;font-size:15px} .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0}"]}),e})(),v=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,r.h,o.E,o.p,s.b]]}),e})()}}]);