(window.webpackJsonp=window.webpackJsonp||[]).push([[766],{Oc0b:function(e,t,n){"use strict";n.r(t),n.d(t,"TsApproversModalComponent",(function(){return F}));var r=n("mrSG"),o=n("xG9w"),s=n("0IaG"),a=n("ofXK"),i=n("bTqV"),p=n("Qu3c"),l=n("d3UM"),c=n("/1cH"),m=n("3Pt+"),d=n("NFeN"),v=n("kmnG"),u=n("qFsG"),C=(n("Xi0T"),n("fXoL")),h=n("XXEo"),f=n("dNgK"),g=n("JLuW"),x=n("me71"),I=n("FKr1");function _(e,t){1&e&&(C["\u0275\u0275elementStart"](0,"div",19),C["\u0275\u0275elementStart"](1,"p",14),C["\u0275\u0275text"](2,"Actions"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]())}function b(e,t){if(1&e&&(C["\u0275\u0275elementStart"](0,"mat-option",32),C["\u0275\u0275text"](1),C["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;C["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.name," - ",e.role,""),C["\u0275\u0275property"]("value",e.name),C["\u0275\u0275advance"](1),C["\u0275\u0275textInterpolate2"](" ",e.name," - ",e.role," ")}}function M(e,t){if(1&e){const e=C["\u0275\u0275getCurrentView"]();C["\u0275\u0275elementStart"](0,"div",27),C["\u0275\u0275elementStart"](1,"mat-form-field",28),C["\u0275\u0275elementStart"](2,"input",29),C["\u0275\u0275listener"]("paste",(function(){return!1}))("ngModelChange",(function(t){return C["\u0275\u0275restoreView"](e),C["\u0275\u0275nextContext"]().$implicit.name=t}))("ngModelOptions",(function(){return{debounce:1e3}}))("ngModelChange",(function(){C["\u0275\u0275restoreView"](e);const t=C["\u0275\u0275nextContext"](),n=t.$implicit,r=t.index;return C["\u0275\u0275nextContext"]().changeFilteredApproverList(n.name,r)})),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](3,"mat-autocomplete",null,30),C["\u0275\u0275template"](5,b,2,5,"mat-option",31),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]()}if(2&e){const e=C["\u0275\u0275reference"](4),t=C["\u0275\u0275nextContext"]().$implicit,n=C["\u0275\u0275nextContext"]();C["\u0275\u0275advance"](2),C["\u0275\u0275property"]("ngModel",t.name)("matAutocomplete",e),C["\u0275\u0275advance"](3),C["\u0275\u0275property"]("ngForOf",n.filteredApproverList)}}function y(e,t){if(1&e&&(C["\u0275\u0275elementStart"](0,"div",27),C["\u0275\u0275elementStart"](1,"p",24),C["\u0275\u0275text"](2),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]()),2&e){const e=C["\u0275\u0275nextContext"]().$implicit;C["\u0275\u0275advance"](2),C["\u0275\u0275textInterpolate"](e.name)}}function O(e,t){if(1&e&&(C["\u0275\u0275elementStart"](0,"mat-option",36),C["\u0275\u0275text"](1),C["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;C["\u0275\u0275property"]("value",e),C["\u0275\u0275advance"](1),C["\u0275\u0275textInterpolate1"](" ",e," ")}}function A(e,t){if(1&e){const e=C["\u0275\u0275getCurrentView"]();C["\u0275\u0275elementStart"](0,"div",23),C["\u0275\u0275elementStart"](1,"mat-form-field",33),C["\u0275\u0275elementStart"](2,"mat-select",34),C["\u0275\u0275listener"]("valueChange",(function(t){return C["\u0275\u0275restoreView"](e),C["\u0275\u0275nextContext"]().$implicit.level=t})),C["\u0275\u0275template"](3,O,2,2,"mat-option",35),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]()}if(2&e){const e=C["\u0275\u0275nextContext"]().$implicit,t=C["\u0275\u0275nextContext"]();C["\u0275\u0275advance"](2),C["\u0275\u0275property"]("value",e.level),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngForOf",t.levelList)}}function w(e,t){if(1&e&&(C["\u0275\u0275elementStart"](0,"div",23),C["\u0275\u0275elementStart"](1,"p",24),C["\u0275\u0275text"](2),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]()),2&e){const e=C["\u0275\u0275nextContext"]().$implicit;C["\u0275\u0275advance"](2),C["\u0275\u0275textInterpolate"](e.level)}}function P(e,t){if(1&e){const e=C["\u0275\u0275getCurrentView"]();C["\u0275\u0275elementStart"](0,"button",40),C["\u0275\u0275listener"]("click",(function(){C["\u0275\u0275restoreView"](e);const t=C["\u0275\u0275nextContext"](2).index;return C["\u0275\u0275nextContext"]().addCostCentreItemApprover(t)})),C["\u0275\u0275elementStart"](1,"mat-icon",4),C["\u0275\u0275text"](2,"add_circle_outline"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]()}}function E(e,t){if(1&e){const e=C["\u0275\u0275getCurrentView"]();C["\u0275\u0275elementStart"](0,"button",41),C["\u0275\u0275listener"]("click",(function(){C["\u0275\u0275restoreView"](e);const t=C["\u0275\u0275nextContext"](2).index;return C["\u0275\u0275nextContext"]().removeCostCentreItemApprover(t)})),C["\u0275\u0275elementStart"](1,"mat-icon",4),C["\u0275\u0275text"](2,"clear"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]()}}function S(e,t){if(1&e&&(C["\u0275\u0275elementStart"](0,"div",37),C["\u0275\u0275template"](1,P,3,0,"button",38),C["\u0275\u0275template"](2,E,3,0,"button",39),C["\u0275\u0275elementEnd"]()),2&e){const e=C["\u0275\u0275nextContext"]().$implicit,t=C["\u0275\u0275nextContext"]();C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",t.canAddApprovers),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",t.canDeleteApprovers&&!e.is_default_approver)}}function L(e,t){if(1&e&&(C["\u0275\u0275elementStart"](0,"div",10),C["\u0275\u0275elementStart"](1,"div",11),C["\u0275\u0275elementStart"](2,"div",20),C["\u0275\u0275elementStart"](3,"div",13),C["\u0275\u0275element"](4,"app-user-image",21),C["\u0275\u0275elementEnd"](),C["\u0275\u0275template"](5,M,6,3,"div",22),C["\u0275\u0275template"](6,y,3,1,"div",22),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](7,"div",23),C["\u0275\u0275elementStart"](8,"p",24),C["\u0275\u0275text"](9),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275template"](10,A,4,2,"div",25),C["\u0275\u0275template"](11,w,3,1,"div",25),C["\u0275\u0275template"](12,S,3,2,"div",26),C["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=C["\u0275\u0275nextContext"]();C["\u0275\u0275advance"](4),C["\u0275\u0275property"]("id",e.oid),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",!n.viewMode&&n.canEditApprovers&&!e.is_default_approver),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",n.viewMode||!n.canEditApprovers||e.is_default_approver),C["\u0275\u0275advance"](3),C["\u0275\u0275textInterpolate"](e.role),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",!n.viewMode&&n.canEditApprovers&&!e.is_default_approver),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",n.viewMode||!n.canEditApprovers||e.is_default_approver),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",!n.viewMode&&n.canEditApprovers)}}function k(e,t){if(1&e){const e=C["\u0275\u0275getCurrentView"]();C["\u0275\u0275elementStart"](0,"div",42),C["\u0275\u0275element"](1,"div",43),C["\u0275\u0275elementStart"](2,"div",44),C["\u0275\u0275elementStart"](3,"button",45),C["\u0275\u0275listener"]("click",(function(){return C["\u0275\u0275restoreView"](e),C["\u0275\u0275nextContext"]().saveCostCentreItemApprovers()})),C["\u0275\u0275elementStart"](4,"mat-icon"),C["\u0275\u0275text"](5,"done_all"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"]()}}let F=(()=>{class e{constructor(e,t,n,r,o){this.dialogRef=e,this.authService=t,this.inData=n,this.snackBar=r,this.sharedLazyLoadedComponentsService=o,this.currentCostCentreItem={costCentre:"",costCentreDescription:"",approvers:[]},this.viewMode=!1,this.currentView="",this.noOfMandLevels=0,this.canAddApprovers=0,this.canEditApprovers=0,this.canDeleteApprovers=0,this.currentCostCentreItemApprovers=[],this.filteredApproverList=[],this.levelList=[],this.currentUser={}}ngOnInit(){this.initDetails()}ngOnChanges(){this.initDetails()}initDetails(){this.modalParams=this.inData.modalParams,this.levelList=[],this.currentCostCentreItem=this.modalParams.currentCostCentreItem,this.viewMode=this.modalParams.viewMode,this.currentView=this.modalParams.currentView,"Approvals"==this.currentView&&this.configureAlteringApprovers(),0==this.viewMode&&(this.noOfMandLevels=this.modalParams.noOfMandLevels,this.canAddApprovers=this.modalParams.canAddApprovers,this.canDeleteApprovers=this.modalParams.canDeleteApprovers,this.canEditApprovers=this.modalParams.canEditApprovers);for(let e=0;e<this.currentCostCentreItem.approvers.length;e++)this.levelList.push(e+1);this.currentCostCentreItemApprovers=JSON.parse(JSON.stringify(this.currentCostCentreItem.approvers))}addCostCentreItemApprover(e){let t=[];for(let n=0;n<=e;n++)t.push(this.currentCostCentreItem.approvers[n]);t.push({name:"",oid:"",level:"",designation:"",role:"",is_delegated:0,is_default_approver:!1});for(let n=e+1;n<this.currentCostCentreItem.approvers.length;n++)t.push(this.currentCostCentreItem.approvers[n]);this.currentCostCentreItem.approvers=t,this.levelList.push(this.levelList[this.levelList.length-1]+1)}removeCostCentreItemApprover(e){if(1==this.currentCostCentreItem.approvers.length)this.showMessage("At least one approver must be present !");else{let t=[];for(let n=0;n<e;n++)t.push(this.currentCostCentreItem.approvers[n]);for(let n=e+1;n<this.currentCostCentreItem.approvers.length;n++)""!=this.currentCostCentreItem.approvers[e].level&&""!=this.currentCostCentreItem.approvers[n].name&&""!=this.currentCostCentreItem.approvers[n].oid&&""!=this.currentCostCentreItem.approvers[n].role&&(this.currentCostCentreItem.approvers[n].level--,this.currentCostCentreItem.approvers[n].level<=0&&(this.currentCostCentreItem.approvers[n].level=1)),t.push(this.currentCostCentreItem.approvers[n]);this.currentCostCentreItem.approvers=t,this.levelList.pop()}}showMessage(e){this.snackBar.open(e,"Dismiss",{duration:5e3})}closeModal(){this.currentCostCentreItemApprovers=o.default.sortBy(this.currentCostCentreItemApprovers,"level"),this.currentCostCentreItem.approvers=this.currentCostCentreItemApprovers,this.dialogRef.close({event:"Submit",data:{currentCostCentreItem:this.currentCostCentreItem}})}saveCostCentreItemApprovers(){let e=!1,t="";for(let r=0;r<this.currentCostCentreItem.approvers.length&&!e;r++)""!=this.currentCostCentreItem.approvers[r].name&&""!=this.currentCostCentreItem.approvers[r].oid&&""!=this.currentCostCentreItem.approvers[r].role||(e=!0,t="The details of one of the approvers is incomplete !");for(let r=0;r<this.currentCostCentreItem.approvers.length&&!e;r++)""==this.currentCostCentreItem.approvers[r].level&&(e=!0,t="All the approvers must have an approval level !");let n=o.default.max(this.currentCostCentreItem.approvers,(function(e){return e.level}));for(let r=n.level;r>0&&!e;r--)0==o.default.where(this.currentCostCentreItem.approvers,{level:r}).length&&(e=!0,t="At least one approver must be present in all the levels below the maximum level "+n.level+" !");if(e)this.showMessage(t);else{if(this.currentCostCentreItem.approvers=o.default.sortBy(this.currentCostCentreItem.approvers,"level"),"Approvals"==this.currentView)for(let e=0;e<this.currentCostCentreItem.approvers.length;e++)this.currentCostCentreItem.approvers[e].is_default_approver_temporary&&(delete this.currentCostCentreItem.approvers[e].is_default_approver_temporary,delete this.currentCostCentreItem.approvers[e].is_default_approver);this.dialogRef.close({event:"Submit",data:{currentCostCentreItem:this.currentCostCentreItem}})}}changeFilteredApproverList(e,t){let n=o.default.where(this.filteredApproverList,{name:e});""==e?this.getFilteredApproverList(this.currentCostCentreItemApprovers):n.length>0?(this.alterApproverList(n,t),this.getFilteredApproverList(this.currentCostCentreItemApprovers)):this.searchConsultants(e).then(e=>{this.getFilteredApproverList(e)})}getFilteredApproverList(e){this.filteredApproverList=[];for(let t of e)0==o.default.where(this.currentCostCentreItem.approvers,{name:t.name}).length&&this.filteredApproverList.push(t)}alterApproverList(e,t){e[0].level=t+1,this.currentCostCentreItem.approvers[t]=e[0]}searchConsultants(e){return new Promise(t=>{this.sharedLazyLoadedComponentsService.searchConsultants(e,"").subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){let n=[];"S"==e.messType&&e.data.length>0&&(n=e.data),t(n)})))})}configureAlteringApprovers(){return Object(r.c)(this,void 0,void 0,(function*(){this.currentUser=yield this.authService.getProfile().profile;let e=0,t=0;for(let r=this.currentCostCentreItem.approvers.length-1;r>=0;r--)this.currentCostCentreItem.approvers[r].oid==this.currentUser.oid&&(e=this.currentCostCentreItem.approvers[r].level,t=1),1!=t||this.currentCostCentreItem.approvers[r].is_default_approver||(this.currentCostCentreItem.approvers[r].is_default_approver=!0,this.currentCostCentreItem.approvers[r].is_default_approver_temporary=!0);for(let r=0;r<this.currentCostCentreItem.approvers.length;r++)this.currentCostCentreItem.approvers[r].level!=e||this.currentCostCentreItem.approvers[r].is_default_approver||(this.currentCostCentreItem.approvers[r].is_default_approver=!0,this.currentCostCentreItem.approvers[r].is_default_approver_temporary=!0);let n=0;for(let r=this.levelList.length-1;r>=0;r--)e==this.levelList[r]?n=1:1==n&&this.levelList.splice(r,1)}))}}return e.\u0275fac=function(t){return new(t||e)(C["\u0275\u0275directiveInject"](s.h),C["\u0275\u0275directiveInject"](h.a),C["\u0275\u0275directiveInject"](s.a),C["\u0275\u0275directiveInject"](f.a),C["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=C["\u0275\u0275defineComponent"]({type:e,selectors:[["ts-approvers-modal"]],features:[C["\u0275\u0275NgOnChangesFeature"]],decls:27,vars:6,consts:[[1,"container-fluid","approver-modal-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"normal-font","my-auto","ml-3"],[1,"ml-2","name","my-auto",3,"matTooltip"],[1,"col-1","d-flex"],["mat-icon-button","","matTooltip","Close",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-4"],[1,"h13","ml-5","pl-5"],[1,"col-3"],[1,"h13"],[1,"h13","ml-2"],["class","col-2",4,"ngIf"],["class","row pt-2 pb-2","style","border-bottom: solid 1px #cacaca",4,"ngFor","ngForOf"],["class","row my-auto pt-4",4,"ngIf"],[1,"col-2"],[1,"row"],[3,"id"],["class","col-9 my-auto",4,"ngIf"],[1,"col-3","my-auto"],[1,"it14"],["class","col-3 my-auto",4,"ngIf"],["class","col-2 my-auto",4,"ngIf"],[1,"col-9","my-auto"],["appearance","outline",1,"custom-ts-search"],["type","text","aria-label","Number","matInput","",1,"custom-ts-search-input",3,"ngModel","matAutocomplete","paste","ngModelChange","ngModelOptions"],["approversAuto","matAutocomplete"],["class","custom-ts-select-option",3,"value","matTooltip",4,"ngFor","ngForOf"],[1,"custom-ts-select-option",3,"value","matTooltip"],["floatLabel","never",1,"custom-ts-select"],[3,"value","valueChange"],["class","custom-ts-select-option",3,"value",4,"ngFor","ngForOf"],[1,"custom-ts-select-option",3,"value"],[1,"col-2","my-auto"],["mat-icon-button","","class","card-action-buttons mr-4","matTooltip","Add Approver",3,"click",4,"ngIf"],["mat-icon-button","","class","card-action-buttons mr-4","matTooltip","Remove Approver",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Add Approver",1,"card-action-buttons","mr-4",3,"click"],["mat-icon-button","","matTooltip","Remove Approver",1,"card-action-buttons","mr-4",3,"click"],[1,"row","my-auto","pt-4"],[1,"col-9"],[1,"col-3","d-flex"],["matTooltip","Save","mat-mini-fab","",1,"mx-auto","mini-tick","mb-2","mr-5",3,"click"]],template:function(e,t){1&e&&(C["\u0275\u0275elementStart"](0,"div",0),C["\u0275\u0275elementStart"](1,"div",1),C["\u0275\u0275elementStart"](2,"div",2),C["\u0275\u0275elementStart"](3,"div",3),C["\u0275\u0275elementStart"](4,"mat-icon",4),C["\u0275\u0275text"](5,"gavel"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](6,"span",5),C["\u0275\u0275text"](7,"Approvers for"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](8,"span",6),C["\u0275\u0275text"](9),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](10,"div",7),C["\u0275\u0275elementStart"](11,"button",8),C["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),C["\u0275\u0275elementStart"](12,"mat-icon",9),C["\u0275\u0275text"](13,"close"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](14,"div",10),C["\u0275\u0275elementStart"](15,"div",11),C["\u0275\u0275elementStart"](16,"p",12),C["\u0275\u0275text"](17,"Approver"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](18,"div",13),C["\u0275\u0275elementStart"](19,"p",14),C["\u0275\u0275text"](20,"Role"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementStart"](21,"div",13),C["\u0275\u0275elementStart"](22,"p",15),C["\u0275\u0275text"](23,"Level"),C["\u0275\u0275elementEnd"](),C["\u0275\u0275elementEnd"](),C["\u0275\u0275template"](24,_,3,0,"div",16),C["\u0275\u0275elementEnd"](),C["\u0275\u0275template"](25,L,13,7,"div",17),C["\u0275\u0275template"](26,k,6,0,"div",18),C["\u0275\u0275elementEnd"]()),2&e&&(C["\u0275\u0275advance"](8),C["\u0275\u0275propertyInterpolate2"]("matTooltip","",t.currentCostCentreItem.costCentre," - ",t.currentCostCentreItem.costCentreDescription,""),C["\u0275\u0275advance"](1),C["\u0275\u0275textInterpolate1"](" ",t.currentCostCentreItem.costCentre," "),C["\u0275\u0275advance"](15),C["\u0275\u0275property"]("ngIf",!t.viewMode&&t.canEditApprovers),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngForOf",t.currentCostCentreItem.approvers),C["\u0275\u0275advance"](1),C["\u0275\u0275property"]("ngIf",!t.viewMode&&t.canEditApprovers))},directives:[d.a,p.a,i.a,a.NgIf,a.NgForOf,x.a,v.c,u.b,m.e,c.d,m.v,m.y,c.b,I.p,l.c],styles:[".approver-modal-styles[_ngcontent-%COMP%]{background-image:url(approvers_bg.46d8dd6ded673479012f.png);background-size:200px 181px;background-repeat:no-repeat;overflow-y:hidden!important;min-height:95vh;background-position:99% 105%}.approver-modal-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{font-size:14px}.approver-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.approver-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:16px;margin-top:4px!important;margin-left:5px!important}.approver-modal-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.approver-modal-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.approver-modal-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.approver-modal-styles[_ngcontent-%COMP%]   .h13[_ngcontent-%COMP%]{font-size:12px;color:#000;font-weight:500;margin-bottom:0!important}.approver-modal-styles[_ngcontent-%COMP%]   .it14[_ngcontent-%COMP%]{font-size:14px;color:#000;font-weight:400;margin-bottom:0!important}.approver-modal-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%]{border:1px solid grey;padding:5px 0!important}.approver-modal-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%], .approver-modal-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{text-align:center;font-size:13px;color:#1a1a1a}.approver-modal-styles[_ngcontent-%COMP%]   .custom-ts-search-input[_ngcontent-%COMP%]{padding:12px 5px 8px;cursor:pointer}.approver-modal-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]{width:100%;text-align:center!important;font-size:14px;color:#1a1a1a}.approver-modal-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{padding:5px!important}.approver-modal-styles[_ngcontent-%COMP%]   .card-action-buttons[_ngcontent-%COMP%]{width:20px!important;height:20px!important;line-height:20px!important}.approver-modal-styles[_ngcontent-%COMP%]   .card-action-buttons[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:21px}.approver-modal-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.approver-modal-styles[_ngcontent-%COMP%]   .mr5[_ngcontent-%COMP%]{margin-right:5px!important}.approver-modal-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.approver-modal-styles[_ngcontent-%COMP%]   .normal-font[_ngcontent-%COMP%]{color:#1a1a1a;font-size:13px;font-weight:400}.approver-modal-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{font-size:14px!important}.approver-modal-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:0!important;border-top:0!important}.approver-modal-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.approver-modal-styles[_ngcontent-%COMP%]     .mat-form-field-underline{display:none}.approver-modal-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-trigger{padding:2px 0!important}"]}),e})()}}]);