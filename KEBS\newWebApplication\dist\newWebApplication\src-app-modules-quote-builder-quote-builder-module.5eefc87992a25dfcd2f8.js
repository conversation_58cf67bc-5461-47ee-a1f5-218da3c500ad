(window.webpackJsonp=window.webpackJsonp||[]).push([[976],{"87Am":function(e,n,o){"use strict";o.r(n),o.d(n,"QuoteBuilderModule",(function(){return a}));var t=o("ofXK"),l=o("tyNb"),r=o("fXoL");const d=[{path:"",pathMatch:"full",redirectTo:"quote"},{path:"rate-card",loadChildren:()=>Promise.all([o.e(1),o.e(2),o.e(3),o.e(4),o.e(5),o.e(6),o.e(9),o.e(10),o.e(11),o.e(12),o.e(13),o.e(14),o.e(16),o.e(17),o.e(18),o.e(21),o.e(31),o.e(32),o.e(35),o.e(41),o.e(49),o.e(87),o.e(165),o.e(876)]).then(o.bind(null,"ipAj")).then(e=>e.RateCardModule)},{path:"rate-card-v2",loadChildren:()=>Promise.all([o.e(1),o.e(2),o.e(3),o.e(4),o.e(8),o.e(15),o.e(19),o.e(21),o.e(22),o.e(23),o.e(24),o.e(27),o.e(28),o.e(30),o.e(33),o.e(47),o.e(49),o.e(0),o.e(877)]).then(o.bind(null,"9SfD")).then(e=>e.RateCardV2Module)},{path:"services",loadChildren:()=>Promise.all([o.e(1),o.e(2),o.e(3),o.e(4),o.e(5),o.e(6),o.e(9),o.e(10),o.e(11),o.e(12),o.e(13),o.e(14),o.e(16),o.e(17),o.e(18),o.e(21),o.e(23),o.e(30),o.e(87),o.e(879)]).then(o.bind(null,"g0Rd")).then(e=>e.ServicesModule)},{path:"quote",loadChildren:()=>Promise.all([o.e(4),o.e(19),o.e(23),o.e(30),o.e(40),o.e(49),o.e(0),o.e(875)]).then(o.bind(null,"TroI")).then(e=>e.QuoteModule)},{path:"config",loadChildren:()=>Promise.all([o.e(4),o.e(23),o.e(30),o.e(35),o.e(41),o.e(51),o.e(49),o.e(0),o.e(874)]).then(o.bind(null,"WUSH")).then(e=>e.QuoteConfigModule)}];let i=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[l.k.forChild(d)],l.k]}),e})(),a=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[t.CommonModule,i]]}),e})()}}]);