(window.webpackJsonp=window.webpackJsonp||[]).push([[1005],{R1B8:function(e,t,i){"use strict";i.r(t),i.d(t,"ComplianceDetailModule",(function(){return C}));var n=i("ofXK"),a=i("Xa2L"),o=i("tyNb"),r=i("fXoL"),l=i("1G5W"),c=i("XNiG"),s=i("BVzC"),m=i("R/Xf");const p=["complianceDetailHeaderContainer"],d=["complianceDetailItemContainer"];function f(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",3),r["\u0275\u0275element"](1,"div",4),r["\u0275\u0275elementStart"](2,"div",5),r["\u0275\u0275element"](3,"mat-spinner",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](4,"div",4),r["\u0275\u0275elementEnd"]())}const h=[{path:"",component:(()=>{class e{constructor(e,t,i,n){this.compiler=e,this.route=t,this._ErrorService=i,this._cmsService=n,this._onDestroy=new c.b,this.isDetailLoading=!1,this.activityTypes=[],this.slaList=[]}ngOnInit(){this.route.parent.params.subscribe(e=>{e.id&&(this.complianceId=e.id,this.getRequestDetails(e.id))})}getRequestDetails(e){this.isDetailLoading=!0,e&&this._cmsService.getComplianceById({compliance_id:e}).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{"N"==e.err&&e.data.length>0?(this.complianceItem=e.data[0],this.complianceDetailHeaderContainerRef&&this.complianceDetailHeaderContainerRef.clear(),this.complianceDetailItemContainerRef&&this.complianceDetailItemContainerRef.clear(),this.loadComplianceDetailHeader(),this.isDetailLoading=!1):this.isDetailLoading=!1},e=>{this.isDetailLoading=!1,this._ErrorService.userErrorAlert(e.error.errorCode,"Error getting Compliance details",e.error.errMessage)})}loadComplianceDetailHeader(){this.complianceDetailHeaderContainerRef&&this.complianceDetailHeaderContainerRef.clear(),Promise.all([i.e(4),i.e(262)]).then(i.bind(null,"alw1")).then(e=>{const t=this.compiler.compileModuleSync(e.ComplianceDetailHeaderModule).create(this.complianceDetailHeaderContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.ComplianceDetailHeaderComponent);this.complianceDetailHeaderContainerRef.createComponent(t).instance.requestItem=this.complianceItem,this.loadComplianceDetailItem()})}loadComplianceDetailItem(){this.complianceDetailItemContainerRef&&this.complianceDetailItemContainerRef.clear(),i.e(263).then(i.bind(null,"3fHU")).then(e=>{const t=this.compiler.compileModuleSync(e.ComplianceDetailItemModule).create(this.complianceDetailHeaderContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.ComplianceDetailItemComponent);this.complianceDetailHeaderContainerRef.createComponent(t).instance.requestItem=this.complianceItem})}ngOnDestroy(){this.complianceDetailHeaderContainerRef&&this.complianceDetailHeaderContainerRef.clear(),this.complianceDetailItemContainerRef&&this.complianceDetailItemContainerRef.clear(),this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.Compiler),r["\u0275\u0275directiveInject"](o.a),r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["compliance-detail-landing-page"]],viewQuery:function(e,t){if(1&e&&(r["\u0275\u0275viewQuery"](p,!0,r.ViewContainerRef),r["\u0275\u0275viewQuery"](d,!0,r.ViewContainerRef)),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.complianceDetailHeaderContainerRef=e.first),r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.complianceDetailItemContainerRef=e.first)}},decls:5,vars:1,consts:[["class","container d-flex h-100 flex-column",4,"ngIf"],["complianceDetailHeaderContainer",""],["complianceDetailItemContainer",""],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"]],template:function(e,t){1&e&&(r["\u0275\u0275template"](0,f,5,0,"div",0),r["\u0275\u0275elementContainer"](1,null,1),r["\u0275\u0275elementContainer"](3,null,2)),2&e&&r["\u0275\u0275property"]("ngIf",t.isDetailLoading)},directives:[n.NgIf,a.c],styles:[""]}),e})()}];let u=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(h)],o.k]}),e})(),C=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,u,a.b]]}),e})()}}]);