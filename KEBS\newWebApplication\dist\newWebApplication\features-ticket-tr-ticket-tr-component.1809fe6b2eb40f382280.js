(window.webpackJsonp=window.webpackJsonp||[]).push([[669],{I3i3:function(t,e,r){"use strict";r.r(e),r.d(e,"TicketTrComponent",(function(){return C})),r.d(e,"TicketTrModule",(function(){return E}));var n=r("mrSG"),i=r("fXoL"),o=r("3Pt+"),s=r("1G5W"),a=r("XNiG"),c=r("wd/R"),l=r("ofXK"),d=r("bTqV"),m=r("NFeN"),p=r("Qu3c"),h=r("kmnG"),u=r("qFsG"),f=r("Xa2L"),g=r("yu80"),b=r("BVzC");function v(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275element"](1,"div",4),i["\u0275\u0275elementStart"](2,"div",5),i["\u0275\u0275element"](3,"mat-spinner",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"div",4),i["\u0275\u0275elementEnd"]())}function T(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",19),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](t);const e=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).deleteTrItem(e)})),i["\u0275\u0275elementStart"](1,"mat-icon",20),i["\u0275\u0275text"](2,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function x(t,e){if(1&t&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",14),i["\u0275\u0275elementStart"](2,"div",15),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",16),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",17),i["\u0275\u0275template"](7,T,3,0,"button",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,r=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"](" ",t.tr_number," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",t.tr_description," "),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",r.isTicketEditable&&r.currentUser.oid==(null==t?null:t.created_by))}}function k(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementContainerStart"](0,21),i["\u0275\u0275elementStart"](1,"div",22),i["\u0275\u0275elementStart"](2,"div",23),i["\u0275\u0275elementStart"](3,"mat-form-field",24),i["\u0275\u0275elementStart"](4,"mat-label"),i["\u0275\u0275text"](5,"TR number"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](6,"input",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",26),i["\u0275\u0275elementStart"](8,"mat-form-field",24),i["\u0275\u0275elementStart"](9,"mat-label"),i["\u0275\u0275text"](10,"Description"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](11,"input",27),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](12,"div",28),i["\u0275\u0275elementStart"](13,"button",29),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](t);const r=e.index;return i["\u0275\u0275nextContext"](2).saveTrItem(r)})),i["\u0275\u0275elementStart"](14,"mat-icon",30),i["\u0275\u0275text"](15,"done"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](16,"button",31),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](t);const r=e.index;return i["\u0275\u0275nextContext"](2).removeTrItem(r)})),i["\u0275\u0275elementStart"](17,"mat-icon",32),i["\u0275\u0275text"](18,"highlight_off"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=e.index,r=i["\u0275\u0275nextContext"](2);i["\u0275\u0275property"]("formGroupName",t),i["\u0275\u0275advance"](13),i["\u0275\u0275property"]("disabled",r.isTrBeingSaved),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("disabled",r.isTrBeingSaved)}}function I(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",22),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275elementStart"](2,"button",34),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).addTrItem()})),i["\u0275\u0275elementStart"](3,"mat-icon",20),i["\u0275\u0275text"](4,"add"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span",35),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).addTrItem()})),i["\u0275\u0275text"](6,"Add TR Item"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function _(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",7),i["\u0275\u0275elementStart"](1,"div",8),i["\u0275\u0275template"](2,x,8,3,"ng-container",9),i["\u0275\u0275elementStart"](3,"form",10),i["\u0275\u0275elementContainerStart"](4,11),i["\u0275\u0275template"](5,k,19,3,"ng-container",12),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,I,7,0,"div",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",t.trList),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroup",t.trForm),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",t.trFormData.trValues.controls),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.isTicketEditable)}}function y(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",42),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).createNewTrList()})),i["\u0275\u0275text"](1," Add TR number "),i["\u0275\u0275elementEnd"]()}}function S(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",36),i["\u0275\u0275elementStart"](1,"div",37),i["\u0275\u0275elementStart"](2,"span",38),i["\u0275\u0275text"](3,"No Transport number found ! "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",39),i["\u0275\u0275element"](5,"img",40),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,y,2,0,"button",41),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngIf",t.isTicketEditable)}}let C=(()=>{class t{constructor(t,e,r){this.fb=t,this._ticket=e,this._ErrorService=r,this._onDestroy=new a.b,this.updateTrId=new i.EventEmitter,this.trList=[],this.isTrLoading=!1,this.addItemVisible=!1,this.isTrBeingSaved=!1,this.currentUser=this._ticket.currentUser,this.isTicketEditable=!0,this.initForm=()=>{this.trForm=this.fb.group({trValues:this.fb.array([this.getTrValues()])})},this.getTrValues=()=>this.fb.group({trNumber:[null,o.H.required],trDescription:[null]}),this.addTrItem=()=>{this.trFormData.trValues.push(this.getTrValues())},this.removeTrItem=t=>{this.trFormData.trValues.removeAt(t)},this.getTrItem=t=>{let e=this.trFormData.trValues.at(t).value;return{tr_number:e.trNumber,tr_description:e.trDescription,created_by:this.currentUser.oid,created_date:c(),is_active:!0}},this.saveTrItem=t=>Object(n.c)(this,void 0,void 0,(function*(){"VALID"==this.trForm.status?this.trId?this.insertTr(this.getTrItem(t),t):this.createTr(this.getTrItem(t),t):this._ticket.showMessage("Please enter TR number")})),this.createTr=(t,e)=>{this.isTrBeingSaved=!0,this._ticket.createTr(this.ticketId,t).pipe(Object(s.a)(this._onDestroy)).subscribe(t=>{this.isTrBeingSaved=!1,"N"==t.error&&t.data?(this.trId=t.data,this._ticket.showMessage("TR added Successfully !"),this.getTrDetails(),this.removeTrItem(e),this.updateTrId.emit(this.trId)):this._ticket.showMessage("Error adding TR !")},t=>{this.isTrBeingSaved=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})},this.insertTr=(t,e)=>{this.isTrBeingSaved=!0,this._ticket.insertTr(this.ticketId,this.trId,t).pipe(Object(s.a)(this._onDestroy)).subscribe(t=>{this.isTrBeingSaved=!1,"N"==t.error?(this._ticket.showMessage("TR added Successfully !"),this.getTrDetails(),this.removeTrItem(e)):this._ticket.showMessage("Error adding TR !")},t=>{this.isTrBeingSaved=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})},this.deleteTrItem=t=>{this.trList[t].is_active=!1,this.editTr(t),this.trList.splice(t,1)},this.editTr=t=>{this.trList[t].modified_by=this.currentUser.oid,this.trList[t].modified_date=c(),this._ticket.editTr(this.ticketId,this.trId,this.trList[t]).pipe(Object(s.a)(this._onDestroy)).subscribe(t=>{this._ticket.showMessage("N"==t.error?"TR updated Successfully !":"Updation Failed !")},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})},this.initForm()}ngOnInit(){this.isTicketEditable=this._ticket.checkIfTicketIsEditable(this.ticketProperties,this.ticketItem),this.removeTrItem(0),this.getTrDetails(),this.resolveSubscriptions()}ngOnChanges(){this.isTicketEditable=this._ticket.checkIfTicketIsEditable(this.ticketProperties,this.ticketItem)}resolveSubscriptions(){this.createResponseSubscription||(this.createResponseSubscription=this._ticket.getActivityObservable.subscribe(t=>{t&&"Create TR"==t.type&&(this.addItemVisible=!0,this.addTrItem())},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)}))}getTrDetails(){this.trId&&(this.isTrLoading=!0,this._ticket.getTrDetails(this.trId).pipe(Object(s.a)(this._onDestroy)).subscribe(t=>{this.isTrLoading=!1,"N"==t.error&&t.data&&(this.trList=t.data.tr_list),this.trList.length>0&&(this.addItemVisible=!0)},t=>{this.isTrLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)}))}get trFormData(){return this.trForm.controls}createNewTrList(){this.addItemVisible=!0,this.addTrItem()}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.createResponseSubscription&&this.createResponseSubscription.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](o.i),i["\u0275\u0275directiveInject"](g.a),i["\u0275\u0275directiveInject"](b.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ticket-tr"]],inputs:{trId:"trId",ticketId:"ticketId",ticketItem:"ticketItem",ticketProperties:"ticketProperties"},outputs:{updateTrId:"updateTrId"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:4,vars:3,consts:[[1,"container-fluid","ticket-tr-styles","pl-0"],[4,"ngIf"],["class","card",4,"ngIf"],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"],[1,"card"],[1,"card-body","p-2"],[4,"ngFor","ngForOf"],[3,"formGroup"],["formArrayName","trValues"],[3,"formGroupName",4,"ngFor","ngForOf"],["class","row pt-2",4,"ngIf"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-3","pt-2","normalFont","pl-4",2,"color","#c92020","font-weight","600"],[1,"col-8","pt-2","normalFont","pl-0","pr-0"],[1,"col-1","pl-0"],["style","float: right;","mat-icon-button","","class","ml-auto close-button","matTooltip","Delete TR",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Delete TR",1,"ml-auto","close-button",2,"float","right",3,"click"],[1,"close-Icon"],[3,"formGroupName"],[1,"row","pt-2"],[1,"col-4"],["appearance","outline",1,"form-field-class"],["matInput","","formControlName","trNumber","placeholder","TR number"],[1,"col-6"],["matInput","","formControlName","trDescription","placeholder","Description"],[1,"col-2","mt-2"],["mat-icon-button","","matTooltip","Save tr",1,"approve-btn","mr-3",3,"disabled","click"],[2,"color","white !important","font-size","18px !important"],["mat-icon-button","","matTooltip","Remove tr",1,"reject-btn",3,"disabled","click"],[2,"color","white !important","font-size","21px !important","margin-bottom","1px"],[1,"col-12","pl-0"],["mat-icon-button","","matTooltip","Add To do",1,"ml-auto","close-button","mt-1",3,"click"],[1,"normalFont","pl-2",2,"cursor","pointer",3,"click"],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","16px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","https://assets.kebs.app/images/nomilestone.png","height","170","width","200",1,"mt-2","mb-2"],["mat-raised-button","","class","mt-2 btn-active slide-from-down",3,"click",4,"ngIf"],["mat-raised-button","",1,"mt-2","btn-active","slide-from-down",3,"click"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275template"](1,v,5,0,"div",1),i["\u0275\u0275template"](2,_,7,4,"div",2),i["\u0275\u0275template"](3,S,7,1,"div",3),i["\u0275\u0275elementEnd"]()),2&t&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.isTrLoading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!e.isTrLoading&&e.addItemVisible),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==e.trList.length&&!e.addItemVisible&&!e.isTrLoading))},directives:[l.NgIf,f.c,l.NgForOf,o.J,o.w,o.n,o.h,d.a,p.a,m.a,o.o,h.c,h.g,u.b,o.e,o.v,o.l],styles:[".ticket-tr-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;border-color:#cf0001;color:#fff;font-weight:400;font-size:12px!important;min-width:10rem;line-height:28px;border-radius:4px}.ticket-tr-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ticket-tr-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#64d464;width:27px;height:27px;line-height:25px}.ticket-tr-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%], .ticket-tr-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-size:18px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.ticket-tr-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{background-color:#fc3939;width:28px;height:28px;line-height:27px}.ticket-tr-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.ticket-tr-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.ticket-tr-styles[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{position:relative;height:2.5rem;width:100%}.ticket-tr-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.ticket-tr-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.ticket-tr-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.ticket-tr-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.ticket-tr-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.ticket-tr-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.ticket-tr-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})(),E=(()=>{class t{}return t.\u0275mod=i["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[l.CommonModule,m.b,d.b,p.b,o.E,h.e,u.c,f.b]]}),t})()}}]);