(window.webpackJsonp=window.webpackJsonp||[]).push([[798],{"et/b":function(t,e,i){"use strict";i.r(e),i.d(e,"BusinessUnitsComponent",(function(){return b})),i.d(e,"BusinessUnitsModule",(function(){return p}));var n=i("mrSG"),o=i("33Jv"),s=i("ofXK"),a=i("NFeN"),c=i("bTqV"),r=i("Xa2L"),l=i("Qu3c"),u=i("STbY"),d=i("cguG"),h=i("VI6+"),m=i("dHLR"),v=i("fXoL"),f=i("0IaG"),g=i("2Clw"),y=i("ek25");let b=(()=>{class t{constructor(t,e,i,n){this.dialog=t,this._dvService=e,this.changeDectRef=i,this._pmoDashboardService=n,this.subs=new o.a,this.colList=[{keyName:"description",colName:"Business Unit",type:"type1",width:300},{keyName:"phase_planned_count",colName:"Phase (PL)",type:"numeric",width:150},{keyName:"phase_actual_count",colName:"Phase (Actual)",type:"numeric",width:150,hasClickEvent:!0},{keyName:"phase_overdue_count",colName:"Phase (OD)",type:"numeric",width:150},{keyName:"activity_planned_count",colName:"Activity (PL)",type:"numeric",width:150,hasClickEvent:!0},{keyName:"activity_actual_count",colName:"Activity (ACTUAL)",type:"numeric",width:150,hasClickEvent:!0},{keyName:"signoff_overdue",colName:"SIGN-OFF (OD)",type:"numeric",width:150,hasClickEvent:!0}],this.count=0,this.columnConfig=[]}setBodyLayoutConfig(){this.blInstance.layoutConfig.layoutHeight="80vh"}ngAfterViewInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.setBodyLayoutConfig(),this.subs.sink=this._dvService.getUdrfFilterData().subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){t&&(this.filterConfig=t,yield this.getBusinessUnitData(t))})))}))}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){}))}getBusinessUnitData(t){return Object(n.c)(this,void 0,void 0,(function*(){this.blInstance.layoutConfig.isMainDataLoading=!0;for(let t=0;t<this._pmoDashboardService.summaryCardData.length;t++)1==this._pmoDashboardService.summaryCardData[t].isSelected&&(this.count+=1,console.log("Count",this.count),this.columnConfig.push(this._pmoDashboardService.summaryCardData[t].type),console.log("columnConfiggg",this.columnConfig));this._dvService.colConfig=this.columnConfig;let e=yield this.getMainListData(t,"bu");this.formatChildList(e.dataList,!0,0),this.blInstance.mainData.dataList=e.dataList,this.blInstance.mainData.colList=e.colList,this.blInstance.layoutConfig.isMainDataLoading=!1}))}formatChildList(t,e,i){var n;for(let o of t)o.loadChild=!1,o.showChild=e,o.rowLevel=i,(null===(n=o.children)||void 0===n?void 0:n.length)>0&&this.formatChildList(o.children,!1,i+1)}getMainListData(t,e){return new Promise((i,n)=>{this.subs.sink=this._dvService.getProjectActivityList(t,e).subscribe(t=>{"S"==t.messType&&i(t)},t=>{console.log(t),n(t)})})}openInfoDialog(t){return Object(n.c)(this,void 0,void 0,(function*(){let e={filterConfig:this.filterConfig,dataParams:t,type:"business-units"};const{DetailedTaskDialogComponent:n}=yield i.e(882).then(i.bind(null,"k6no"));this.dialog.open(n,{height:"80%",width:"75%",data:e,panelClass:"detailed-task-dialog"}).afterClosed().subscribe(t=>{console.log(t)})}))}openProjectOverviewDialog(t){return Object(n.c)(this,void 0,void 0,(function*(){}))}onDataScrolled(t){}ngOnDestroy(){this.subs.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(v["\u0275\u0275directiveInject"](f.b),v["\u0275\u0275directiveInject"](g.a),v["\u0275\u0275directiveInject"](v.ChangeDetectorRef),v["\u0275\u0275directiveInject"](y.a))},t.\u0275cmp=v["\u0275\u0275defineComponent"]({type:t,selectors:[["pmo-business-units"]],viewQuery:function(t,e){if(1&t&&v["\u0275\u0275viewQuery"](m.a,!0),2&t){let t;v["\u0275\u0275queryRefresh"](t=v["\u0275\u0275loadQuery"]())&&(e.blInstance=t.first)}},decls:2,vars:0,consts:[[1,"business-units"],[3,"valueClicked","verticalScrollEvent","progressCircleClicked"]],template:function(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"div",0),v["\u0275\u0275elementStart"](1,"pmo-body-layout",1),v["\u0275\u0275listener"]("valueClicked",(function(t){return e.openInfoDialog(t)}))("verticalScrollEvent",(function(t){return e.onDataScrolled(t)}))("progressCircleClicked",(function(t){return e.openProjectOverviewDialog(t)})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]())},directives:[m.a],styles:[".business-units[_ngcontent-%COMP%]   .complete-task-dialog[_ngcontent-%COMP%]   .mat-dialog-container[_ngcontent-%COMP%], .business-units[_ngcontent-%COMP%]   .mat-dialog-container[_ngcontent-%COMP%]{overflow-y:hidden!important}"]}),t})(),p=(()=>{class t{}return t.\u0275mod=v["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[s.CommonModule,d.a,a.b,c.b,r.b,l.b,u.e,h.b]]}),t})()}}]);