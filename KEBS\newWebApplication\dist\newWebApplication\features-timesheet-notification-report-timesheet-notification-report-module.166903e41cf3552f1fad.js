(window.webpackJsonp=window.webpackJsonp||[]).push([[676],{QHCJ:function(t,e,i){"use strict";i.r(e),i.d(e,"TimesheetNotificationReportModule",(function(){return P}));var o=i("ofXK"),n=i("tyNb"),a=i("mrSG"),s=i("3Pt+"),r=i("1yaQ"),l=i("FKr1"),d=i("wd/R"),c=i("fXoL"),m=i("tk/3");let p=(()=>{class t{constructor(t){this.$http=t,this.misAllInOne=t=>this.$http.post("/api/project/misFunctions/misAllInOne",{posting_period:t}),this.misPost=(t,e)=>this.$http.post("/api/project/misFunctions/misPost",{period_year:t,period_month:e}),this.getMisTableData=(t,e)=>(console.log("getMisTableData"),this.$http.post("/api/project/misFunctions/getMisTableData",{table_params:t,date:e,application_id:283})),this.getMisTableMasterData=()=>(console.log("misTableMasterData"),this.$http.post("/api/project/misFunctions/getMisTableMasterData",{})),this.tallyMonthDataFreezePeriod=()=>this.$http.post("/api/project/misFunctions/tallyMonthDataFreezePeriod",{}),this.tallyMonthDataUpdate=(t,e,i,o,n,a)=>(console.log("tallyMonthDataUpdate"),this.$http.post("/api/project/misFunctions/tallyMonthDataInsert",{start_date:t,end_date:e,is_to_delete:i,is_to_update_ytd:o,freeze_period:n,change_in_freeze_period:a})),this.tsNotifApprovalReminders=()=>(console.log("tsNotifApprovalReminders"),this.$http.post("/api/tsPrimary/tsNotifApprovalReminders",{})),this.tsNotifDataRetrival=(t,e)=>(console.log(t),this.$http.post(t,e)),this.tsNotifFetchReports=()=>(console.log("tsNotifFetchReports"),this.$http.post("/api/tsPrimary/tsNotifFetchReports",{}))}}return t.\u0275fac=function(e){return new(e||t)(c["\u0275\u0275inject"](m.c))},t.\u0275prov=c["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var u=i("LcQX"),f=i("dNgK"),h=i("0IaG"),g=i("bTqV"),v=i("Qu3c"),y=i("STbY"),N=i("NFeN"),b=i("ZzPI"),M=i("6t9p");function S(t,e){if(1&t&&c["\u0275\u0275element"](0,"dxi-column",10),2&t){const t=e.$implicit;c["\u0275\u0275property"]("dataField",t.dataField)("allowReordering",!0)("caption",t.caption)}}function w(t,e){if(1&t&&c["\u0275\u0275element"](0,"dxi-total-item",12),2&t){const t=e.$implicit;c["\u0275\u0275property"]("column",t.dataField)("summaryType",t.summaryType)}}function x(t,e){if(1&t&&(c["\u0275\u0275elementStart"](0,"dxo-summary"),c["\u0275\u0275template"](1,w,1,2,"dxi-total-item",11),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",t.summaryTotalConfig)}}let D=(()=>{class t{constructor(){}ngOnInit(){this.currentDateTime=d().format("DD-MM-YY hh:mm A")}ngOnChanges(t){console.log(this.data)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=c["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ts-report-grid-devextreme"]],viewQuery:function(t,e){if(1&t&&c["\u0275\u0275viewQuery"](b.a,!0),2&t){let t;c["\u0275\u0275queryRefresh"](t=c["\u0275\u0275loadQuery"]())&&(e.dataGrid=t.first)}},inputs:{data:"data",columnConfig:"columnConfig",fileName:"fileName",summaryTotalConfig:"summaryTotalConfig"},features:[c["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:18,consts:[["id","gridContainer",1,"dev-style",3,"allowColumnResizing","dataSource","showBorders","hoverStateEnabled","columnAutoWidth"],[3,"enabled","fileName","allowExportSelectedData"],["mode","select",3,"enabled"],[3,"enabled"],[3,"allowFixing"],["placeholder","Search...",3,"visible","width"],["mode","single"],[3,"visible"],[3,"dataField","allowReordering","caption",4,"ngFor","ngForOf"],[4,"ngIf"],[3,"dataField","allowReordering","caption"],[3,"column","summaryType",4,"ngFor","ngForOf"],[3,"column","summaryType"]],template:function(t,e){1&t&&(c["\u0275\u0275elementStart"](0,"dx-data-grid",0),c["\u0275\u0275element"](1,"dxo-export",1),c["\u0275\u0275element"](2,"dxo-column-chooser",2),c["\u0275\u0275element"](3,"dxo-column-fixing",3),c["\u0275\u0275element"](4,"dxi-column",4),c["\u0275\u0275element"](5,"dxo-search-panel",5),c["\u0275\u0275element"](6,"dxo-selection",6),c["\u0275\u0275element"](7,"dxo-header-filter",7),c["\u0275\u0275element"](8,"dxo-filter-row",7),c["\u0275\u0275template"](9,S,1,3,"dxi-column",8),c["\u0275\u0275template"](10,x,2,1,"dxo-summary",9),c["\u0275\u0275elementEnd"]()),2&t&&(c["\u0275\u0275property"]("allowColumnResizing",!0)("dataSource",e.data)("showBorders",!0)("hoverStateEnabled",!0)("columnAutoWidth",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275propertyInterpolate2"]("fileName","",e.fileName,"-",e.currentDateTime,""),c["\u0275\u0275property"]("enabled",!0)("allowExportSelectedData",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("enabled",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("allowFixing",!1),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0)("width",240),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("visible",!0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",e.columnConfig),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.summaryTotalConfig.length>0))},directives:[b.a,M.Sb,M.tb,M.vb,M.g,M.Md,M.Od,M.Cc,M.dc,o.NgForOf,o.NgIf,M.ve,M.N],styles:[""]}),t})();function T(t,e){if(1&t){const t=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"button",13),c["\u0275\u0275elementStart"](1,"div",14),c["\u0275\u0275elementStart"](2,"div",15),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](t);const i=e.index;return c["\u0275\u0275nextContext"]().loadReport(i)})),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](t.display_name)}}function F(t,e){if(1&t&&(c["\u0275\u0275elementStart"](0,"div",1),c["\u0275\u0275elementStart"](1,"div",16),c["\u0275\u0275element"](2,"app-ts-report-grid-devextreme",17),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("data",t.tsNotifDataGridData)("columnConfig",t.tsNotifDataGridColumnConfig)("summaryTotalConfig",t.tsNotifDataGridSummaryTotalConfig)}}const C=d,I={parse:{dateInput:"MMMM-YYYY"},display:{dateInput:"MMMM-YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},Y=[{path:"",component:(()=>{class t{constructor(t,e,i,o){this._tsNotifFunctionService=t,this._util=e,this.snackBar=i,this.dialog=o,this.misDatePickerVisibility=!1,this.date=new s.j(C()),this.tsNotifMenu=[],this.tsNotifSelectedIndex=0,this.tsNotifTableVisibility=!1,this.tsNotifDataGridVisibility=!1,this.tsNotifDataGridData=[],this.tsNotifDataGridColumnConfig=[],this.tsNotifDataGridSummaryTotalConfig=[],this.tsNotifPivotDataVisibility=!1,this.tsNotifPivotData=[],this.tsNotifCurrentViewIndex=null,this.selectedTSNotifTableDisplayName=null,this.selectedPeriodMonth=null,this.selectedPeriodYear=null,this.formattedDate=null}ngOnInit(){this.updatePeriodMonthYear(),this.updateTSNotifMenu()}updateTSNotifMenu(){return Object(a.c)(this,void 0,void 0,(function*(){this._tsNotifFunctionService.tsNotifFetchReports().subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){console.log(t),"S"==t.messType?(this.tsNotifMenu=t.data,this.loadReport(0),this._util.showMessage(t.messText,"close")):"E"==t.messType&&this._util.showMessage(t.messText,"close")})))}))}loadReport(t){return Object(a.c)(this,void 0,void 0,(function*(){this.tsNotifSelectedIndex=t,this._tsNotifFunctionService.tsNotifDataRetrival(this.tsNotifMenu[t].api_name,this.tsNotifMenu[t].data?this.tsNotifMenu[t].data:{}).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){console.log(t),"S"==t.messType?(this.tsNotifDataGridVisibility=!0,this.tsNotifDataGridData=t.data,this.tsNotifDataGridColumnConfig=t.columnConfig,this._util.showMessage(t.messText,"close")):"E"==t.messType&&(this.tsNotifDataGridVisibility=!1,this.tsNotifDataGridData=[],this.tsNotifDataGridColumnConfig=[],this._util.showMessage(t.messText,"close"))})))}))}updatePeriodMonthYear(){this.date=null!=localStorage.getItem("currentPeriodDate")?new s.j(C(localStorage.getItem("currentPeriodDate"))):this.date,this.formattedDate=C(this.date.value).format("YYYY-MM-01"),this.selectedPeriodMonth=C(this.date.value).format("MMMM"),this.selectedPeriodYear=C(this.date.value).format("YYYY")}setMonthAndYear(t,e){const i=this.date.value;i.month(t.month()),i.year(t.year()),this.date.setValue(i),localStorage.setItem("currentPeriodDate",this.date.value),this.updatePeriodMonthYear(),e.close()}openReportInNewTab(t){localStorage.setItem("currentReportIndex",t),window.open(window.location.origin+"/main/reports/mis_functions ")}}return t.\u0275fac=function(e){return new(e||t)(c["\u0275\u0275directiveInject"](p),c["\u0275\u0275directiveInject"](u.a),c["\u0275\u0275directiveInject"](f.a),c["\u0275\u0275directiveInject"](h.b))},t.\u0275cmp=c["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ts-report-landing-page"]],features:[c["\u0275\u0275ProvidersFeature"]([{provide:l.c,useClass:r.c,deps:[l.f,r.a]},{provide:l.e,useValue:I}])],decls:17,vars:4,consts:[[1,"container-fluid"],[1,"row"],[1,"col-1","p-2"],["mat-raised-button","","matTooltip","TS Notification reports",1,"btn-active",2,"font-weight","normal","margin-left","1%","margin-bottom","1%",3,"matMenuTriggerFor"],[1,"h-50"],["menu","matMenu"],["mat-menu-item","","class","matOptions",4,"ngFor","ngForOf"],[1,"col-3","pt-1","p-2"],["mat-icon-button","","matTooltip","Refresh",1,"iconsSize","ml-2",2,"color","#cf0001",3,"click"],[1,"iconsSize"],[1,"col-3","center","p-3","ml-4","mr-5"],[2,"font-size","medium","font-weight","500","color","#cf0001"],["class","row",4,"ngIf"],["mat-menu-item","",1,"matOptions"],[1,"row","p-1"],[1,"col-8","p-0",3,"click"],[1,"col-12"],[3,"data","columnConfig","summaryTotalConfig"]],template:function(t,e){if(1&t&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"button",3),c["\u0275\u0275text"](4,"Reports"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"mat-menu",4,5),c["\u0275\u0275template"](7,T,4,1,"button",6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"div",7),c["\u0275\u0275elementStart"](9,"span"),c["\u0275\u0275elementStart"](10,"button",8),c["\u0275\u0275listener"]("click",(function(){return e.loadReport(e.tsNotifSelectedIndex)})),c["\u0275\u0275elementStart"](11,"mat-icon",9),c["\u0275\u0275text"](12,"refresh"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](13,"div",10),c["\u0275\u0275elementStart"](14,"span",11),c["\u0275\u0275text"](15),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](16,F,3,3,"div",12),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275reference"](6);c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("matMenuTriggerFor",t),c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("ngForOf",e.tsNotifMenu),c["\u0275\u0275advance"](8),c["\u0275\u0275textInterpolate1"](" Timesheet Notification Reports - ",e.tsNotifMenu[e.tsNotifSelectedIndex].display_name," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.tsNotifDataGridVisibility)}},directives:[g.a,v.a,y.f,y.g,o.NgForOf,N.a,o.NgIf,y.d,D],styles:[""]}),t})()}];let _=(()=>{class t{}return t.\u0275mod=c["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[n.k.forChild(Y)],n.k]}),t})();var R=i("iadO"),E=i("qFsG"),j=i("kmnG"),O=i("Xa2L");let P=(()=>{class t{}return t.\u0275mod=c["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[o.CommonModule,_,g.b,N.b,v.b,y.e,R.h,s.p,E.c,j.e,s.E,b.b,O.b]]}),t})()}}]);