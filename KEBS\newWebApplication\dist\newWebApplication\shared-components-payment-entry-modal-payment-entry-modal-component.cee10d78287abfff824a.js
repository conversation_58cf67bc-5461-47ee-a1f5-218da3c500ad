(window.webpackJsonp=window.webpackJsonp||[]).push([[892],{"6bfw":function(e,t,n){"use strict";n.r(t),n.d(t,"PaymentEntryModalComponent",(function(){return I}));var r=n("XNiG"),a=n("1G5W"),o=n("3Pt+"),l=n("0IaG"),i=n("ofXK"),c=n("qFsG"),m=n("iadO"),d=n("bTqV"),s=n("kmnG"),u=n("Qu3c"),p=n("NFeN"),y=n("xG9w"),x=n("fXoL");function f(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",8),x["\u0275\u0275elementStart"](1,"div",9),x["\u0275\u0275elementStart"](2,"div"),x["\u0275\u0275text"](3),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div"),x["\u0275\u0275elementStart"](5,"p",10),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"p",11),x["\u0275\u0275text"](8),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate"](e.label),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function v(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",12),x["\u0275\u0275elementStart"](1,"span"),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function g(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",13),x["\u0275\u0275elementStart"](1,"span",14),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function h(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",15),x["\u0275\u0275elementStart"](1,"span",16),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function b(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",1),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"]().change()})),x["\u0275\u0275template"](1,f,9,4,"div",2),x["\u0275\u0275template"](2,v,3,2,"div",3),x["\u0275\u0275template"](3,g,3,3,"div",4),x["\u0275\u0275template"](4,h,3,3,"div",5),x["\u0275\u0275elementStart"](5,"div",6),x["\u0275\u0275elementStart"](6,"mat-icon",7),x["\u0275\u0275text"](7,"loop"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","big"==e.type),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","small"==e.type),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","medium"==e.type),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","overview"==e.type)}}let S=(()=>{class e{constructor(){this.currency=[],this.index=0}set currencyList(e){this.currency=e;const t=y.findIndex(e,e=>"INR"==e.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}ngOnInit(){}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":"-"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=x["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency-converter"]],inputs:{type:"type",label:"label",acl:"acl",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["matTooltip","Next currency",1,"change-icon","pl-2"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"]],template:function(e,t){1&e&&x["\u0275\u0275template"](0,b,8,4,"div",0),2&e&&x["\u0275\u0275property"]("ngIf",t.currency)},directives:[i.NgIf,p.a,u.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{font-weight:440!important}.data-medium[_ngcontent-%COMP%], .data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;text-align:center!important;padding-right:4px!important}.data-overview[_ngcontent-%COMP%]{padding-left:24px!important;font-weight:500!important;margin:auto!important}"]}),e})();var _=n("bv9b"),E=n("6xF0"),P=n("dNgK");function C(e,t){1&e&&x["\u0275\u0275element"](0,"mat-progress-bar",36)}let I=(()=>{class e{constructor(e,t,n,a,o){this.fb=e,this.dialogRef=t,this.inData=n,this.billedInvoiceService=a,this.snackBar=o,this.isLoading=!1,this._onDestroy=new r.b}ngOnInit(){this.modalParams=this.inData.modalParams,console.log(this.modalParams),this.paymentForm=this.fb.group({tallyRefNo:["",o.H.required],receivedOn:["",o.H.required],amountReceived:["",o.H.required]}),this.todayDate=new Date}onSubmit(){if(this.paymentForm.valid){this.isLoading=!0;let e=this.modalParams.invoiceInfo.amount_to_be_Collected-this.paymentForm.value.amountReceived,t=this.convertToLocalTime(this.paymentForm.value.receivedOn),n=this.modalParams.invoiceInfo.billing_id;this.billedInvoiceService.savepaymentInfo(this.paymentForm.value.amountReceived,t,n,this.paymentForm.value.tallyRefNo,this.modalParams.invoiceInfo.milestone_id,this.modalParams.itemData.item_id,e,"","",null,null).pipe(Object(a.a)(this._onDestroy)).subscribe(e=>{"Payment Details Updated!"==e.message&&this.billedInvoiceService.getInvoiceInformationInBilledScreen(n).pipe(Object(a.a)(this._onDestroy)).subscribe(e=>{0==e.amount_to_be_Collected?(this.isLoading=!1,this.paymentForm.reset(),this.snackBar.open("Payment for this milestone recorded succesfully !","Dismiss",{duration:3e3}),this.dialogRef.close({event:"Close"})):(this.isLoading=!1,this.paymentForm.reset(),this.snackBar.open("Partial payment recorded succesfully !","Dismiss",{duration:3e3}),this.dialogRef.close({event:"Close"}))},e=>{this.isLoading=!1,console.log(e)})},e=>{this.isLoading=!1,this.snackBar.open("Error while saving the payment entry","Dismiss",{duration:2e3}),console.log(e)})}else this.snackBar.open("Enter all the mandatory fields !","Dismiss",{duration:2e3})}closePaymentEntry(){this.dialogRef.close({event:"Close"})}convertToLocalTime(e){let t=new Date(e),n=6e4*t.getTimezoneOffset();return t.setTime(t.getTime()-n),t}ngOnDestroy(){this.modalParams={},this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(x["\u0275\u0275directiveInject"](o.i),x["\u0275\u0275directiveInject"](l.h),x["\u0275\u0275directiveInject"](l.a),x["\u0275\u0275directiveInject"](E.a),x["\u0275\u0275directiveInject"](P.a))},e.\u0275cmp=x["\u0275\u0275defineComponent"]({type:e,selectors:[["app-payment-entry-modal"]],decls:69,vars:9,consts:[[1,"container-fluid","payment-entry-modal",3,"formGroup"],[1,"row","py-1",2,"border-bottom","solid 1px #cacaca"],["mode","indeterminate",4,"ngIf"],[1,"col-11","d-flex"],["mat-icon-button","",1,"raised-circle","my-auto"],[1,"logo-icon"],[1,"dark-txt-bold","my-auto","ml-3","mr-2"],[1,"customer-name","my-auto"],[1,"col-1","d-flex"],["mat-icon-button","","matTooltip","close",3,"click"],[1,"icon-btn"],[1,"row","pt-2","mt-3","mb-0",2,"height","54vh"],[1,"col-lg-8","px-0"],[1,"row","my-3","slide-in-top"],[1,"col-1","pl-3"],[1,"icon-btn","mt-1"],[1,"col-lg-5","col-sm-12","pl-0","light-txt-bold","my-auto"],[1,"col-lg-6","col-sm-12","cp","my-auto"],["type","medium",1,"flex-1",3,"currencyList"],[1,"row","slide-in-top"],[1,"col-1","pl-3","my-auto"],[1,"icon-btn","pt-1"],[1,"col-lg-5","col-sm-12","light-txt-bold","my-auto","pl-0"],[1,"col-lg-6","col-sm-12","my-auto"],["appearance","outline",2,"width","235px"],["matInput","","type","number","placeholder","Amount received","formControlName","amountReceived"],["matSuffix",""],["appearance","outline"],["matInput","","name","endDate","required","","placeholder","Received Date","formControlName","receivedOn",3,"matDatepicker","max"],["matSuffix","",3,"for"],["picker1",""],["matInput","","type","text","placeholder","Reference Number","formControlName","tallyRefNo"],[1,"row","d-flex","justify-content-center","my-4","slide-from-down"],["mat-raised-button","",1,"submit-btn",3,"disabled","click"],[1,"col-lg-4","mt-auto","px-0"],["_ngcontent-mla-c256","","src","https://assets.kebs.app/images/coinstack.png","height","250","width","250"],["mode","indeterminate"]],template:function(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"form",0),x["\u0275\u0275elementStart"](1,"div",1),x["\u0275\u0275template"](2,C,1,0,"mat-progress-bar",2),x["\u0275\u0275elementStart"](3,"div",3),x["\u0275\u0275elementStart"](4,"div",4),x["\u0275\u0275elementStart"](5,"mat-icon",5),x["\u0275\u0275text"](6," credit_card "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"span",6),x["\u0275\u0275text"](8," Payment entry for "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"span",7),x["\u0275\u0275text"](10),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](11,"div",8),x["\u0275\u0275elementStart"](12,"button",9),x["\u0275\u0275listener"]("click",(function(){return t.closePaymentEntry()})),x["\u0275\u0275elementStart"](13,"mat-icon",10),x["\u0275\u0275text"](14,"close"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](15,"div",11),x["\u0275\u0275elementStart"](16,"div",12),x["\u0275\u0275elementStart"](17,"div",13),x["\u0275\u0275elementStart"](18,"div",14),x["\u0275\u0275elementStart"](19,"mat-icon",15),x["\u0275\u0275text"](20,"attach_money"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](21,"div",16),x["\u0275\u0275text"](22," Amount to be collected "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](23,"div",17),x["\u0275\u0275element"](24,"app-currency-converter",18),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](25,"div",19),x["\u0275\u0275elementStart"](26,"div",20),x["\u0275\u0275elementStart"](27,"mat-icon",21),x["\u0275\u0275text"](28,"price_change"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](29,"div",22),x["\u0275\u0275text"](30," Amount received "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](31,"div",23),x["\u0275\u0275elementStart"](32,"mat-form-field",24),x["\u0275\u0275elementStart"](33,"mat-label"),x["\u0275\u0275text"](34,"Amount Received"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](35,"input",25),x["\u0275\u0275elementStart"](36,"span",26),x["\u0275\u0275text"](37),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](38,"div",19),x["\u0275\u0275elementStart"](39,"div",20),x["\u0275\u0275elementStart"](40,"mat-icon",21),x["\u0275\u0275text"](41,"event"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](42,"div",22),x["\u0275\u0275text"](43," Received on "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](44,"div",23),x["\u0275\u0275elementStart"](45,"mat-form-field",27),x["\u0275\u0275elementStart"](46,"mat-label"),x["\u0275\u0275text"](47,"Received on"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](48,"input",28),x["\u0275\u0275element"](49,"mat-datepicker-toggle",29),x["\u0275\u0275element"](50,"mat-datepicker",null,30),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](52,"div",19),x["\u0275\u0275elementStart"](53,"div",20),x["\u0275\u0275elementStart"](54,"mat-icon",21),x["\u0275\u0275text"](55,"tag"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](56,"div",22),x["\u0275\u0275text"](57," Reference number "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](58,"div",23),x["\u0275\u0275elementStart"](59,"mat-form-field",24),x["\u0275\u0275elementStart"](60,"mat-label"),x["\u0275\u0275text"](61," Reference Number"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](62,"input",31),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](63,"div",32),x["\u0275\u0275elementStart"](64,"button",33),x["\u0275\u0275listener"]("click",(function(){return t.onSubmit()})),x["\u0275\u0275text"](65,"Submit"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](66,"div",34),x["\u0275\u0275elementStart"](67,"span"),x["\u0275\u0275element"](68,"img",35),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275reference"](51);x["\u0275\u0275property"]("formGroup",t.paymentForm),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",t.isLoading),x["\u0275\u0275advance"](8),x["\u0275\u0275textInterpolate1"](" ",null!=t.modalParams&&t.modalParams.itemData.project_name?t.modalParams.itemData.project_name:""," "),x["\u0275\u0275advance"](14),x["\u0275\u0275property"]("currencyList",null!=t.modalParams&&t.modalParams.invoiceInfo.Balance_amount?t.modalParams.invoiceInfo.Balance_amount:""),x["\u0275\u0275advance"](13),x["\u0275\u0275textInterpolate1"](" ",null==t.modalParams?null:t.modalParams.invoiceInfo.currency," "),x["\u0275\u0275advance"](11),x["\u0275\u0275property"]("matDatepicker",e)("max",t.todayDate),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("for",e),x["\u0275\u0275advance"](15),x["\u0275\u0275property"]("disabled",t.isLoading)}},directives:function(){return[o.J,o.w,o.n,i.NgIf,p.a,d.a,u.a,S,s.c,s.g,c.b,o.A,o.e,o.v,o.l,s.i,m.g,o.F,m.i,m.f,_.a]},styles:[".payment-entry-modal[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%]{color:#cf0001;font-size:20px;padding-left:4px;padding-top:4px}.payment-entry-modal[_ngcontent-%COMP%]   .customer-name[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.payment-entry-modal[_ngcontent-%COMP%]   .dark-txt-bold[_ngcontent-%COMP%]{color:#000;font-weight:500}.payment-entry-modal[_ngcontent-%COMP%]   .light-txt-bold[_ngcontent-%COMP%]{color:#5a5957!important;font-weight:500}.payment-entry-modal[_ngcontent-%COMP%]   .raised-circle[_ngcontent-%COMP%]{display:flex;border-radius:50%;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);width:30px;height:30px}.payment-entry-modal[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{color:#4d4d4b!important;font-size:20px}.payment-entry-modal[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;width:100px}.payment-entry-modal[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer}.payment-entry-modal[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.payment-entry-modal[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}}]);