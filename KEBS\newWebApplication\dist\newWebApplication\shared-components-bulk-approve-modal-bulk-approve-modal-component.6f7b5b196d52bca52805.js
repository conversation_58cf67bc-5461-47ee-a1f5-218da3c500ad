(window.webpackJsonp=window.webpackJsonp||[]).push([[884],{idnk:function(e,t,a){"use strict";a.r(t),a.d(t,"BulkApproveModalComponent",(function(){return d}));var n=a("mrSG"),o=a("0IaG"),l=a("XNiG"),i=a("fXoL");let d=(()=>{class e{constructor(e,t,a){this.dialogRef=e,this.deatilModalData=t,this.dialog=a,this._onDestroy=new l.b}ngOnInit(){this.modalData=this.deatilModalData.modalParams.data,console.log(this.modalData),this.leaveNo=this.deatilModalData.modalParams.noOfLeaves?this.deatilModalData.modalParams.noOfLeaves:"",this.displayData=this.deatilModalData.modalParams.text}approveSelectedLeaveRequest(){this.dialogRef.close({event:"Approve",approvedItems:this.modalData})}rejectSelectedLeaveRequest(){return Object(n.c)(this,void 0,void 0,(function*(){console.log(this.deatilModalData.modalParams.data);let e={data:this.deatilModalData.modalParams.data};const{MultiRejectModalComponent:t}=yield a.e(823).then(a.bind(null,"ClqF"));this.dialog.open(t,{height:"55%",minWidth:"40%",position:{left:"400px",bottom:"400px",right:"400px",top:"200px"},data:{modalParams:e}}).afterClosed().subscribe(e=>{"Reject"==e.event?this.dialogRef.close({event:"Reject",reason:e.reason,data:e.data}):this.dialog.closeAll()})}))}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](o.h),i["\u0275\u0275directiveInject"](o.a),i["\u0275\u0275directiveInject"](o.b))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-bulk-approve-modal"]],decls:12,vars:2,consts:[[1,"container-fluild","pl-2","pr-2","bulk-approve-modal-styles"],[1,"col-12","pt-4","pb-4"],[1,"row"],[1,"col-4"],[1,"mainText"],[1,"subText"],[1,"col-4","subText"],[1,"col-2","pr-2"],[1,"col-2","pl-2"],[1,"btn-approve",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"span",4),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](6,"div",5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](7,"div",6),i["\u0275\u0275element"](8,"div",7),i["\u0275\u0275elementStart"](9,"div",8),i["\u0275\u0275elementStart"](10,"button",9),i["\u0275\u0275listener"]("click",(function(){return t.approveSelectedLeaveRequest()})),i["\u0275\u0275text"](11,"Approve"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate2"]("",t.leaveNo?t.leaveNo:""," ",t.displayData,""))},styles:[".bulk-approve-modal-styles[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)}.bulk-approve-modal-styles[_ngcontent-%COMP%]   .mainText[_ngcontent-%COMP%]{font-weight:500;font-size:14px;color:#fff}.bulk-approve-modal-styles[_ngcontent-%COMP%]   .mainText[_ngcontent-%COMP%], .bulk-approve-modal-styles[_ngcontent-%COMP%]   .subText[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;line-height:16px;letter-spacing:.02em;text-transform:capitalize}.bulk-approve-modal-styles[_ngcontent-%COMP%]   .subText[_ngcontent-%COMP%]{font-weight:400;font-size:12px;color:#e8e9ee}.bulk-approve-modal-styles[_ngcontent-%COMP%]   .btn-approve[_ngcontent-%COMP%]{width:88px;height:40px;background:#fff;border-radius:6px;border:none}.bulk-approve-modal-styles[_ngcontent-%COMP%]   .btn-reject[_ngcontent-%COMP%]{border:1px solid #fff;border-radius:4px;background-color:#ef4a61;color:#fff;width:88px;height:40px}"]}),e})()}}]);