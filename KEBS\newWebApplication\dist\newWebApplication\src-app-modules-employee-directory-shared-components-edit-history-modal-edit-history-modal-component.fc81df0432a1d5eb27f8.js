(window.webpackJsonp=window.webpackJsonp||[]).push([[955],{Qcrq:function(t,e,n){"use strict";n.r(e),n.d(e,"EditHistoryModalComponent",(function(){return M})),n.d(e,"EditHistoryModalModule",(function(){return O}));var i=n("mrSG"),o=n("33Jv"),a=n("0IaG"),r=n("ofXK"),d=n("Xi0T"),l=n("NFeN"),s=n("bTqV"),c=n("Xa2L"),m=n("Qu3c"),p=n("1jcm"),g=n("jW8c"),f=n("fXoL"),u=n("jAlA"),h=n("mS9j");let y=(()=>{class t{transform(t){return t?(t=t.replace("Arr","")).replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" "):" "}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=f["\u0275\u0275definePipe"]({name:"camelCaseToFriendly",type:t,pure:!0}),t})();function x(t,e){1&t&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275elementStart"](1,"div",2),f["\u0275\u0275element"](2,"mat-spinner",3),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementContainerEnd"]())}function v(t,e){1&t&&(f["\u0275\u0275elementStart"](0,"div",9),f["\u0275\u0275elementStart"](1,"div",10),f["\u0275\u0275element"](2,"img",11),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",12),f["\u0275\u0275elementStart"](4,"span",13),f["\u0275\u0275text"](5,"No Edit Logs Found !"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function b(t,e){if(1&t&&(f["\u0275\u0275elementStart"](0,"li"),f["\u0275\u0275text"](1),f["\u0275\u0275pipe"](2,"camelCaseToFriendly"),f["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate"](f["\u0275\u0275pipeBind1"](2,1,t))}}function C(t,e){if(1&t&&(f["\u0275\u0275elementStart"](0,"div",14),f["\u0275\u0275elementStart"](1,"div",15),f["\u0275\u0275elementStart"](2,"div",16),f["\u0275\u0275text"](3),f["\u0275\u0275pipe"](4,"date"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](5,"div",17),f["\u0275\u0275text"](6),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](7,"div",18),f["\u0275\u0275elementStart"](8,"ul"),f["\u0275\u0275template"](9,b,3,3,"li",19),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](10,"div",20),f["\u0275\u0275elementStart"](11,"div",21),f["\u0275\u0275text"](12," Action By "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](13,"div",14),f["\u0275\u0275element"](14,"app-user-profile",22),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;f["\u0275\u0275advance"](3),f["\u0275\u0275textInterpolate1"](" ",t.updated_on?f["\u0275\u0275pipeBind3"](4,4,t.updated_on,"d MMM yyyy, h:mm:ss a","UTC"):"-"," "),f["\u0275\u0275advance"](3),f["\u0275\u0275textInterpolate1"](" ",t.action_text?t.action_text:"-"," "),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngForOf",t.edited_fields),f["\u0275\u0275advance"](5),f["\u0275\u0275property"]("oid",t.action_by)}}function _(t,e){if(1&t){const t=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275elementStart"](1,"div",4),f["\u0275\u0275elementStart"](2,"div",5),f["\u0275\u0275text"](3,"Edit History"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"div",6),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](t),f["\u0275\u0275nextContext"]().closeDialog()})),f["\u0275\u0275text"](5,"close"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](6,v,6,0,"div",7),f["\u0275\u0275template"](7,C,15,8,"div",8),f["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](6),f["\u0275\u0275property"]("ngIf",0==t.editHistoryData.length),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",t.editHistoryData)}}let M=(()=>{class t{constructor(t,e){this._edService=t,this.injector=e,this.editHistoryData=[],this.isLoading=!1,this.subs=new o.a,this.dialogData=this.injector.get(a.a,null),this.dialogRef=this.injector.get(a.h,null),this.dialogData.modalParams&&(this.associateId=this.dialogData.modalParams.associateId,this.tabKey=this.dialogData.modalParams.tabKey)}getEditHistory(){return new Promise((t,e)=>{this.subs.sink=this._edService.fetchEditHistory(this.associateId,this.tabKey).subscribe(e=>{e.err||t(e.has_logs?e.log_data:[])},t=>{console.log(t),e(t)})})}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.editHistoryData=yield this.getEditHistory(),this.isLoading=!1}))}closeDialog(){this.dialogRef.close()}ngOnDestory(){this.subs.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(f["\u0275\u0275directiveInject"](u.a),f["\u0275\u0275directiveInject"](f.Injector))},t.\u0275cmp=f["\u0275\u0275defineComponent"]({type:t,selectors:[["ed-edit-history-modal"]],decls:3,vars:2,consts:[[1,"container-fluid","edit-history-modal","px-4"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"d-flex","justify-content-between","my-4"],[1,"main-header"],[1,"close-btn",3,"click"],["class","mt-4",4,"ngIf"],["class","row",4,"ngFor","ngForOf"],[1,"mt-4"],[1,"d-flex","justify-content-center"],["src","https://assets.kebs.app/images/empty_re_opql.png","height","150","width","200",1,"mt-2"],[1,"d-flex","mt-4","justify-content-center"],[2,"font-weight","500","font-size","16px","color","#26303e"],[1,"row"],[1,"col-6","py-4",2,"border-left","1px solid #e8e9ee"],[1,"row","date-txt"],[1,"row","action-txt"],[1,"row","action-txt","updated_field_list"],[4,"ngFor","ngForOf"],[1,"col-6","py-4",2,"border-bottom","1px solid #dadce2"],[1,"row","mb-1",2,"color","#111434","font-size","14px"],["type","small",3,"oid"]],template:function(t,e){1&t&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275template"](1,x,3,0,"ng-container",1),f["\u0275\u0275template"](2,_,8,2,"ng-container",1),f["\u0275\u0275elementEnd"]()),2&t&&(f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.isLoading),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!e.isLoading))},directives:[r.NgIf,c.c,m.a,r.NgForOf,h.a],pipes:[r.DatePipe,y],styles:[".edit-history-modal[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]{color:#26303e;font-size:20px;font-weight:500}.edit-history-modal[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{cursor:pointer;font-size:12px;color:#45546e}.edit-history-modal[_ngcontent-%COMP%]   .date-txt[_ngcontent-%COMP%]{color:#b9c0ca;font-size:12px}.edit-history-modal[_ngcontent-%COMP%]   .action-txt[_ngcontent-%COMP%]{color:#6e7b8f;font-size:14px}.edit-history-modal[_ngcontent-%COMP%]   .check-point[_ngcontent-%COMP%]{box-sizing:border-box;position:absolute;width:8px;height:8px;left:32px;top:84px;border:2px solid #f27a6c}.edit-history-modal[_ngcontent-%COMP%]   .updated-fields-list[_ngcontent-%COMP%]{margin-top:10px;padding-left:20px;color:#666}.edit-history-modal[_ngcontent-%COMP%]   .updated-fields-list[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:700;margin-bottom:5px}.edit-history-modal[_ngcontent-%COMP%]   .updated-fields-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style-type:disc;margin-left:10px}.edit-history-modal[_ngcontent-%COMP%]   .updated-fields-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:3px}"]}),t})(),O=(()=>{class t{}return t.\u0275mod=f["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},providers:[],imports:[[r.CommonModule,d.a,l.b,s.b,c.b,m.b,p.b,g.a]]}),t})()}}]);