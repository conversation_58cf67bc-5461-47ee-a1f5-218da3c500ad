(window.webpackJsonp=window.webpackJsonp||[]).push([[1001],{"4SbF":function(t,e,n){"use strict";n.r(e),n.d(e,"WfApproverDetailsComponent",(function(){return R})),n.d(e,"WfApproverDetailsModule",(function(){return W}));var o=n("mrSG"),i=n("0IaG"),r=n("1G5W"),a=n("XNiG"),l=n("3Pt+"),p=n("ofXK"),s=n("bTqV"),c=n("Qu3c"),d=n("NFeN"),m=n("Xa2L"),u=n("Xi0T"),f=n("qFsG"),g=n("kmnG"),v=n("fXoL"),h=n("tKbL"),x=n("LcQX"),b=n("BVzC"),w=n("XXEo"),_=n("z52X"),C=n("tyNb"),y=n("me71");function O(t,e){1&t&&(v["\u0275\u0275elementContainerStart"](0),v["\u0275\u0275element"](1,"div",10),v["\u0275\u0275elementStart"](2,"div",11),v["\u0275\u0275element"](3,"mat-spinner",12),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](4,"div",10),v["\u0275\u0275elementContainerEnd"]())}function S(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementContainerStart"](0),v["\u0275\u0275elementStart"](1,"button",35),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const e=v["\u0275\u0275nextContext"](2).$implicit;return v["\u0275\u0275nextContext"](2).updateWf(e,!0)})),v["\u0275\u0275elementStart"](2,"mat-icon",36),v["\u0275\u0275text"](3,"done"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](4,"button",37),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](2).$implicit.isRejectActivated=!0})),v["\u0275\u0275elementStart"](5,"mat-icon",38),v["\u0275\u0275text"](6,"highlight_off"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementContainerEnd"]()}}function E(t,e){1&t&&(v["\u0275\u0275elementContainerStart"](0),v["\u0275\u0275elementStart"](1,"div",39),v["\u0275\u0275element"](2,"mat-spinner",40),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementContainerEnd"]())}function M(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div"),v["\u0275\u0275template"](1,S,7,0,"ng-container",9),v["\u0275\u0275template"](2,E,3,0,"ng-container",9),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",!t.isWfUpdating),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",t.isWfUpdating)}}function D(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"span"),v["\u0275\u0275text"](1),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](2).$implicit;v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate1"](" By ",null==t?null:t.actual_approver_name," ")}}function P(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"button",42),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const e=v["\u0275\u0275nextContext"](2).$implicit;return v["\u0275\u0275nextContext"](2).showComments(e)})),v["\u0275\u0275elementStart"](1,"mat-icon",4),v["\u0275\u0275text"](2,"chat"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}}function I(t,e){if(1&t&&(v["\u0275\u0275text"](0),v["\u0275\u0275pipe"](1,"date"),v["\u0275\u0275template"](2,D,2,1,"span",9),v["\u0275\u0275template"](3,P,3,0,"button",41)),2&t){const t=v["\u0275\u0275nextContext"]().$implicit;v["\u0275\u0275textInterpolate1"](" ",v["\u0275\u0275pipeBind2"](1,3,null==t?null:t.action_on,"dd-MMM-yy hh:mm a")," "),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",(null==t?null:t.actual_appr_oid)&&(null==t?null:t.appr_oid)!=(null==t?null:t.actual_appr_oid)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",null==t?null:t.comments)}}function j(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",46),v["\u0275\u0275element"](1,"div",47),v["\u0275\u0275elementStart"](2,"div",48),v["\u0275\u0275elementStart"](3,"button",49),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const e=v["\u0275\u0275nextContext"](2).$implicit;return v["\u0275\u0275nextContext"](2).updateWf(e,!1)})),v["\u0275\u0275elementStart"](4,"mat-icon"),v["\u0275\u0275text"](5,"done_all"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}}function k(t,e){if(1&t&&(v["\u0275\u0275elementContainerStart"](0),v["\u0275\u0275elementStart"](1,"div",16),v["\u0275\u0275elementStart"](2,"mat-form-field",43),v["\u0275\u0275elementStart"](3,"mat-label"),v["\u0275\u0275text"](4,"Reason for rejection"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](5,"textarea",44),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](6,j,6,0,"div",45),v["\u0275\u0275elementContainerEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](5),v["\u0275\u0275property"]("formControl",t.comment),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",!t.isWfUpdating&&!t.comment.disabled)}}function A(t,e){if(1&t&&(v["\u0275\u0275elementContainerStart"](0),v["\u0275\u0275elementStart"](1,"div",26),v["\u0275\u0275elementStart"](2,"div",27),v["\u0275\u0275element"](3,"img",28),v["\u0275\u0275elementStart"](4,"span",29),v["\u0275\u0275text"](5),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](6,"div",23),v["\u0275\u0275elementStart"](7,"span",19),v["\u0275\u0275text"](8),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](9,"div",30),v["\u0275\u0275element"](10,"div",31),v["\u0275\u0275elementStart"](11,"span",32),v["\u0275\u0275text"](12),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](13,"div",24),v["\u0275\u0275template"](14,M,3,2,"div",33),v["\u0275\u0275template"](15,I,4,6,"ng-template",null,34,v["\u0275\u0275templateRefExtractor"]),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](17,k,7,2,"ng-container",9),v["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,n=v["\u0275\u0275reference"](16),o=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("matTooltip",null==t?null:t.approver_name),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("src",t.approver_profile_url?t.approver_profile_url:"https://assets.kebs.app/images/User.png",v["\u0275\u0275sanitizeUrl"]),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngClass",1==(null==t?null:t.is_curr_appr)?"curr-appr":""),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null==t?null:t.approver_name),v["\u0275\u0275advance"](3),v["\u0275\u0275textInterpolate"](null==t?null:t.appr_level),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngClass","Draft"==t.status||"Recalled"==t.status?"is-draft":"Submitted"==t.status||"Awaiting Approval"==t.status?"is-submitted":"Approved"==t.status||"Auto Approved"==t.status||"Escalated"==t.status?"is-approved":"Rejected"==t.status||"Cancelled"==t.status?"is-rejected":"is-draft"),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("matTooltip",null==t?null:t.status),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null==t?null:t.status),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",o.currentUserOid==(null==t?null:t.appr_oid)&&(null==t?null:t.is_curr_appr))("ngIfElse",n),v["\u0275\u0275advance"](3),v["\u0275\u0275property"]("ngIf",null==t?null:t.isRejectActivated)}}function T(t,e){if(1&t&&(v["\u0275\u0275elementContainerStart"](0),v["\u0275\u0275elementStart"](1,"div",13),v["\u0275\u0275elementStart"](2,"div",14),v["\u0275\u0275text"](3," Workflow name "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](4,"div",15),v["\u0275\u0275text"](5),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](6,"div",16),v["\u0275\u0275elementStart"](7,"div",14),v["\u0275\u0275text"](8," Initiator "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](9,"div",17),v["\u0275\u0275element"](10,"app-user-image",18),v["\u0275\u0275elementStart"](11,"span",19),v["\u0275\u0275text"](12),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](13,"div",20),v["\u0275\u0275elementStart"](14,"div",21),v["\u0275\u0275elementStart"](15,"div",22),v["\u0275\u0275text"](16," Approver "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](17,"div",23),v["\u0275\u0275text"](18," Level "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](19,"div",24),v["\u0275\u0275text"](20," Status "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](21,"div",24),v["\u0275\u0275text"](22," Action on "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](23,A,18,11,"ng-container",25),v["\u0275\u0275elementContainerEnd"]()),2&t){const t=v["\u0275\u0275nextContext"]();v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate1"](" ",t.approverDetails[0].workflow_name," "),v["\u0275\u0275advance"](5),v["\u0275\u0275property"]("id",t.approverDetails[0].init_oid),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate"](t.approverDetails[0].initiator_name),v["\u0275\u0275advance"](11),v["\u0275\u0275property"]("ngForOf",t.approverDetails)}}let R=(()=>{class t{constructor(t,e,n,o,i,r,p,s,c){this._wfPlugin=t,this.dialogRef=e,this.inData=n,this._util=o,this._error=i,this._login=r,this._lcdpService=p,this.dialog=s,this.$router=c,this.approverDetails=[],this._onDestroy=new a.b,this.isDetailsLoading=!1,this.isWfUpdating=!1,this.comment=new l.j,this.currentUser=this._login.getProfile().profile}ngOnInit(){this.inputData=this.inData.modalParams,(this.inputData.wfPluginId||this.inputData.wfHeaderId)&&this.getApproverDetails(),this.currentUserOid=this.currentUser.oid,this.inputData.currentUserOid&&(this.currentUserOid=this.inputData.currentUserOid)}getApproverDetails(){this.isDetailsLoading=!0,this._wfPlugin.getWfApproverDetails(this.inputData.wfPluginId,this.inputData.wfHeaderId).pipe(Object(r.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if(this.isDetailsLoading=!1,"S"==t.messType&&t.data.length>0){this.approverDetails=t.data;let e=!1;for(let t=this.approverDetails.length-1;t>=0;t--)this.approverDetails[t].isRejectActivated=!1,this.approverDetails[t].comments=this.approverDetails[t].comments&&"string"==typeof this.approverDetails[t].comments?JSON.parse(this.approverDetails[t].comments):null,e&&(this.approverDetails[t].status="Cancelled"),"Rejected"==this.approverDetails[t].status&&(e=!0)}else this._util.showToastMessage(t.messText)})),t=>{this.isDetailsLoading=!1,this._error.userErrorAlert(t.error.errorCode,"Error getting approver details !",t.error.errMessage)})}closeWfApproverDialog(){this.dialogRef.close({event:"Close"})}updateWf(t,e){this.currentUserOid==t.appr_oid?e||this.comment.value?(this.comment.value&&!e&&(t.comments={text:this.comment.value}),this.isWfUpdating=!0,this.comment.disable(),this._wfPlugin.updateWfBasedOnConfig(this.inputData.wfPluginId,e?"A":"R",t.appr_oid,t.comments?t.comments:null,this.inputData.isAdminViewActivated?1:0,this.currentUser.oid).pipe(Object(r.a)(this._onDestroy)).subscribe(n=>Object(o.c)(this,void 0,void 0,(function*(){if(this.isWfUpdating=!1,"S"==n.messType&&(t.status=e?"Approved":"Rejected",t.action_on=new Date,this._util.showToastMessage((e?"Approved":"Rejected")+" Successfully !"),this.dialogRef.close({event:"submit",data:n.data}),this.inputData.lcdpApplicationId&&this._lcdpService.createGeneralLogForRecord({applicationId:this.inputData.lcdpApplicationId,recordId:this.inputData.recordId,generalLogType:"workflow",isApproved:e,logUser:!0}).subscribe(t=>{console.log(t)},t=>{console.log(t)}),n.data&&n.data.updated_object&&n.data.updated_object[1].feedbackFormsToTrigger&&n.data.updated_object[1].feedbackFormsToTrigger.length)){let t=n.data.updated_object[1].feedbackFormsToTrigger[0];yield this.openFeedbackFormDialog(t.formId,null,this.inputData.recordId)}})),t=>{this.isWfUpdating=!1,this.isDetailsLoading=!1,this._error.userErrorAlert(t.error.errorCode,"Error updating workflow !",t.error.errMessage)})):this._util.showToastMessage("Kindly give reason for rejection !"):this._util.showToastMessage("You are not authorized to approve/reject !")}showComments(t){t.isRejectActivated=!t.isRejectActivated,t.isRejectActivated&&(this.comment.patchValue(t.comments.text),this.comment.disable())}openFeedbackFormDialog(t,e,i=null){return Object(o.c)(this,void 0,void 0,(function*(){const{CustomFormModalComponent:r}=yield Promise.all([n.e(1),n.e(7),n.e(8),n.e(9),n.e(19),n.e(44),n.e(48),n.e(52),n.e(984)]).then(n.bind(null,"mGD6"));this.dialog.open(r,{height:"75%",width:"75%",data:{lcdpApplicationId:this.inputData.lcdpApplicationId,formId:t,isEdit:!0,entryForId:null,recordReferenceId:i},disableClose:!0}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){e&&(this.$router.navigate(["../"],{relativeTo:e}),t&&"S"==t.messType?this._lcdpService.setCreationObservable(!0):this.$router.navigate(["../"],{relativeTo:e}))})))}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return t.\u0275fac=function(e){return new(e||t)(v["\u0275\u0275directiveInject"](h.a),v["\u0275\u0275directiveInject"](i.h),v["\u0275\u0275directiveInject"](i.a),v["\u0275\u0275directiveInject"](x.a),v["\u0275\u0275directiveInject"](b.a),v["\u0275\u0275directiveInject"](w.a),v["\u0275\u0275directiveInject"](_.a),v["\u0275\u0275directiveInject"](i.b),v["\u0275\u0275directiveInject"](C.g))},t.\u0275cmp=v["\u0275\u0275defineComponent"]({type:t,selectors:[["app-wf-approver-details"]],decls:14,vars:2,consts:[[1,"container-fluid","wf-approver-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-1","pb-2","d-flex","pl-2"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","my-auto","ml-3","pt-1"],[1,"col-1","d-flex"],["matTooltip","Close","mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[4,"ngIf"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["matTooltip","Loading workflow details","diameter","30"],[1,"row","pt-3","pb-2"],[1,"col-3","header"],[1,"col-9"],[1,"row","pt-2","pb-2"],[1,"col-6","pl-0"],["imgWidth","28px","imgHeight","28px",1,"ml-2",3,"id"],[1,"pl-2"],[1,"row","pt-2",2,"border-bottom","solid 1px #cacaca"],[1,"row","pt-2","header"],[1,"col-4"],[1,"col-2"],[1,"col-3"],[4,"ngFor","ngForOf"],[1,"row","pt-2","pb-2","d-flex"],[1,"col-4","pl-0","d-flex",3,"matTooltip"],["height","28px","width","28px",1,"ml-2",3,"src"],[1,"pl-2","pt-1","overflow-text",3,"ngClass"],[1,"col-3","d-flex"],[1,"status-circular",3,"ngClass"],[1,"pl-2","overflow-text",3,"matTooltip"],[4,"ngIf","ngIfElse"],["actionOnContent",""],["mat-icon-button","","matTooltip","Approve",1,"approve-btn","mr-3",3,"click"],[2,"color","white !important","font-size","18px !important"],["mat-icon-button","","matTooltip","Reject",1,"reject-btn",3,"click"],[2,"color","white !important","font-size","21px !important"],[1,"justify-content-center"],["matTooltip","Updating Workflow","diameter","25"],["mat-icon-button","","class","week-action-buttons ml-2","matTooltip","See comments",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","See comments",1,"week-action-buttons","ml-2",3,"click"],["appearance","outline",1,"long_desc"],["rows","6","matInput","",3,"formControl"],["class","row",4,"ngIf"],[1,"row"],[1,"col-11"],[1,"col-1"],["matTooltip","Reject","mat-mini-fab","",1,"ml-auto","mini-tick","mb-4",3,"click"]],template:function(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"div",0),v["\u0275\u0275elementStart"](1,"div",1),v["\u0275\u0275elementStart"](2,"div",2),v["\u0275\u0275elementStart"](3,"div",3),v["\u0275\u0275elementStart"](4,"mat-icon",4),v["\u0275\u0275text"](5,"fact_check"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](6,"span",5),v["\u0275\u0275text"](7," Workflow Details "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",6),v["\u0275\u0275elementStart"](9,"button",7),v["\u0275\u0275listener"]("click",(function(){return e.closeWfApproverDialog()})),v["\u0275\u0275elementStart"](10,"mat-icon",8),v["\u0275\u0275text"](11,"close"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](12,O,5,0,"ng-container",9),v["\u0275\u0275template"](13,T,24,4,"ng-container",9),v["\u0275\u0275elementEnd"]()),2&t&&(v["\u0275\u0275advance"](12),v["\u0275\u0275property"]("ngIf",e.isDetailsLoading),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",(null==e.approverDetails?null:e.approverDetails.length)>0))},directives:[d.a,s.a,c.a,p.NgIf,m.c,y.a,p.NgForOf,p.NgClass,g.c,g.g,f.b,l.e,l.v,l.k],pipes:[p.DatePipe],styles:[".wf-approver-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.wf-approver-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.wf-approver-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.wf-approver-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.wf-approver-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.wf-approver-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:13px}.wf-approver-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:12px!important}.wf-approver-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.wf-approver-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.wf-approver-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.wf-approver-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.wf-approver-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.wf-approver-styles[_ngcontent-%COMP%]   .curr-appr[_ngcontent-%COMP%]{color:#ff7200!important}.wf-approver-styles[_ngcontent-%COMP%]   .overflow-text[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.wf-approver-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#64d464;width:27px;height:27px;line-height:25px}.wf-approver-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%], .wf-approver-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-size:18px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.wf-approver-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{background-color:#fc3939;width:28px;height:28px;line-height:27px}.wf-approver-styles[_ngcontent-%COMP%]   .long_desc[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:90%!important;font-size:13px}.wf-approver-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.wf-approver-styles[_ngcontent-%COMP%]   .week-action-buttons[_ngcontent-%COMP%]{width:25px;height:25px!important;line-height:25px!important}.wf-approver-styles[_ngcontent-%COMP%]   .week-action-buttons[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:16px}"]}),t})(),W=(()=>{class t{}return t.\u0275mod=v["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},providers:[],imports:[[p.CommonModule,s.b,c.b,d.b,i.g,m.b,u.a,l.E,f.c,g.e]]}),t})()}}]);