(window.webpackJsonp=window.webpackJsonp||[]).push([[913,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));class a{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},NUiT:function(e,t,n){"use strict";n.r(t),n.d(t,"MY_FORMATS",(function(){return T})),n.d(t,"GenerateAssessmentLinkDialogComponent",(function(){return j}));var a=n("mrSG"),r=n("3Pt+"),i=n("0IaG"),o=n("wd/R"),s=n.n(o),l=n("1yaQ"),m=n("FKr1"),c=n("XNiG"),d=n("1G5W"),p=n("fXoL"),g=n("XNFG"),f=n("tk/3");let u=(()=>{class e{constructor(e){this._http=e}generateAONAssessmentLink(e){return this._http.post("api/ats/bulkActions/generateAONAssessmentLink",e)}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275inject"](f.c))},e.\u0275prov=p["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var h=n("NFeN"),v=n("ofXK"),y=n("QibW"),C=n("Qu3c"),M=n("kmnG"),O=n("qFsG"),_=n("iadO"),x=n("yyQj");function b(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"mat-radio-button",12),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275property"]("value",e.id)("matTooltip",e.name),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function P(e,t){}function w(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",13),p["\u0275\u0275elementStart"](2,"span",14),p["\u0275\u0275text"](3," Static Link: "),p["\u0275\u0275template"](4,P,0,0,"ng-template",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"mat-form-field",16),p["\u0275\u0275element"](6,"input",17),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){p["\u0275\u0275nextContext"]();const e=p["\u0275\u0275reference"](18);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function S(e,t){}function k(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",13),p["\u0275\u0275elementStart"](1,"span",14),p["\u0275\u0275text"](2," Assessment ID: "),p["\u0275\u0275template"](3,S,0,0,"ng-template",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"mat-form-field",16),p["\u0275\u0275elementStart"](5,"input",19),p["\u0275\u0275listener"]("keydown",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).preventInvalidValues(t)}))("paste",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).preventPaste(t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){p["\u0275\u0275nextContext"](2);const e=p["\u0275\u0275reference"](18);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function I(e,t){}function E(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",13),p["\u0275\u0275elementStart"](1,"span",14),p["\u0275\u0275text"](2," Test Start Date And Time: "),p["\u0275\u0275template"](3,I,0,0,"ng-template",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",20),p["\u0275\u0275elementStart"](5,"mat-form-field",21),p["\u0275\u0275elementStart"](6,"div",22),p["\u0275\u0275element"](7,"input",23),p["\u0275\u0275elementStart"](8,"mat-datepicker-toggle",24),p["\u0275\u0275elementStart"](9,"mat-icon",25),p["\u0275\u0275text"](10,"calendar_today"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](11,"mat-datepicker",26,27),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](13,"app-input-time",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275reference"](12),t=p["\u0275\u0275nextContext"](2),n=p["\u0275\u0275reference"](18);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngTemplateOutlet",n),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("min",t.currentDate)("matDatepicker",e),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("for",e)}}function F(e,t){}function D(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",13),p["\u0275\u0275elementStart"](1,"span",14),p["\u0275\u0275text"](2," Test Expiry Days: "),p["\u0275\u0275template"](3,F,0,0,"ng-template",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"mat-form-field",16),p["\u0275\u0275elementStart"](5,"input",29),p["\u0275\u0275listener"]("keydown",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).preventInvalidValues(t)}))("paste",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).preventPaste(t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){p["\u0275\u0275nextContext"](2);const e=p["\u0275\u0275reference"](18);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function A(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,k,6,1,"div",18),p["\u0275\u0275template"](2,E,14,4,"div",18),p["\u0275\u0275template"](3,D,6,1,"div",18),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",6==e.data.stageId),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",5==e.data.stageId),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",5==e.data.stageId)}}function Y(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",30),p["\u0275\u0275text"](1,"*"),p["\u0275\u0275elementEnd"]())}const L=function(e){return{"pointer-events":e}},T={parse:{dateInput:"DD MMM YYYY"},display:{dateInput:"DD MMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let j=(()=>{class e{constructor(e,t,n,a,i){this.data=e,this._toaster=t,this._dialogRef=n,this._fb=a,this._atsBulkActionService=i,this._onDestroy=new c.b,this.assessmentLinkTypeMaster=[{id:1,name:"Static"},{id:2,name:"Dynamic"}],this.currentDate=s()(),this.isApiInProgress=!1,this.assessmentForm=this._fb.group({type:new r.j(1),staticLink:new r.j(null,[r.H.pattern('^(ftp|http|https)://[^ "]+$')]),testExpiryDays:new r.j(null),testStartDate:new r.j(null),testStartTime:new r.j(null,[r.H.pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)]),assessmentId:new r.j(null)})}ngOnInit(){}preventPaste(e){e.preventDefault()}preventInvalidValues(e){["0","1","2","3","4","5","6","7","8","9","Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab"].includes(e.key)||e.preventDefault()}onClose(e){this._dialogRef.close(e)}generateLink(){return Object(a.c)(this,void 0,void 0,(function*(){if(this.assessmentForm.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Enter all Mandatory Fields!",7e3);if(1==this.assessmentForm.get("type").value){if(null==this.assessmentForm.get("staticLink").value)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Enter all Mandatory Fields!",7e3);if(this.assessmentForm.get("staticLink").invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Enter a Valid URL!",7e3)}else if(2==this.assessmentForm.get("type").value&&((null==this.assessmentForm.get("testExpiryDays").value||null==this.assessmentForm.get("testStartDate").value||null==this.assessmentForm.get("testStartTime").value||""==this.assessmentForm.get("testStartTime").value)&&5==this.data.stageId||null==this.assessmentForm.get("assessmentId").value&&6==this.data.stageId))return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Enter all Mandatory Fields!",7e3);let e={jobId:this.data.jobId,stageId:this.data.stageId,candidateIds:this.data.candidateIds,isBulkSelectActive:this.data.isBulkSelectActive,candidatesExcluded:this.data.candidatesExcluded,type:this.assessmentForm.get("type").value,assessmentId:this.assessmentForm.get("assessmentId").value,assessmentLink:this.assessmentForm.get("staticLink").value,testStartDateTime:s()(`${s()(this.assessmentForm.get("testStartDate").value).format("YYYY-MM-DD")} ${this.assessmentForm.get("testStartTime").value}`,"YYYY-MM-DD HH:mm").format("YYYY/MM/DD hh:mm A"),testExpiryDays:this.assessmentForm.get("testExpiryDays").value,filter:this.data.filter,search_params:this.data.search_params};yield this.generateAONAssessmentLink(e)}))}generateAONAssessmentLink(e){return Object(a.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((t,n)=>this._atsBulkActionService.generateAONAssessmentLink(e).pipe(Object(d.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success","Assessment link(s) generated successfully!",7e3),this.onClose(!0),t(!0)):(this._toaster.showError("Error",e.msg,7e3),t(!1)),this.isApiInProgress=!1},error:e=>{this._toaster.showError("Error",e.message?e.message:"Assessment Link Generation Failed!",7e3),this.isApiInProgress=!1,n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](i.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](i.h),p["\u0275\u0275directiveInject"](r.i),p["\u0275\u0275directiveInject"](u))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-generate-assessment-link-dialog"]],features:[p["\u0275\u0275ProvidersFeature"]([{provide:m.c,useClass:l.c,deps:[m.f,l.a]},{provide:m.e,useValue:T}])],decls:19,vars:13,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"close",3,"ngStyle","click"],[1,"main",3,"formGroup"],["formControlName","type"],[3,"value","matTooltip",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"d-flex","justify-content-end",2,"margin-top","16px","gap","8px"],[1,"cancel-btn",3,"ngStyle","click"],[1,"yes-btn",3,"ngStyle","click"],["mandatoryTemplate",""],[3,"value","matTooltip"],[1,"d-flex","flex-column"],[1,"form-label"],[3,"ngTemplateOutlet"],["appearance","outline",1,"form-field-class"],["type","text","matInput","","placeholder","Enter Link","formControlName","staticLink"],["class","d-flex flex-column",4,"ngIf"],["type","number","matInput","","placeholder","Enter Assessment ID","formControlName","assessmentId",3,"keydown","paste"],[1,"d-flex","flex-row","justify-content-between"],["appearance","outline",1,"form-field-class",2,"width","48%"],[1,"date-picker"],["matInput","","placeholder","DD MMM YYYY","formControlName","testStartDate","disabled","",3,"min","matDatepicker"],[3,"for"],["matDatepickerToggleIcon","",1,"calendar-icon"],["disabled","false"],["dp",""],["formControlName","testStartTime",1,"form-field-time","col-6","p-0",2,"width","48%"],["type","number","matInput","","placeholder","Enter Number of Days","formControlName","testExpiryDays",3,"keydown","paste"],[2,"color","#cf0001"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275elementStart"](2,"div",2),p["\u0275\u0275text"](3,"Generate Assessment Link"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div"),p["\u0275\u0275elementStart"](5,"mat-icon",3),p["\u0275\u0275listener"]("click",(function(){return t.onClose(!1)})),p["\u0275\u0275text"](6,"close"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",4),p["\u0275\u0275elementStart"](8,"mat-radio-group",5),p["\u0275\u0275template"](9,b,2,3,"mat-radio-button",6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](10,w,7,1,"ng-container",7),p["\u0275\u0275template"](11,A,4,3,"ng-container",7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",8),p["\u0275\u0275elementStart"](13,"div",9),p["\u0275\u0275listener"]("click",(function(){return t.onClose(!1)})),p["\u0275\u0275text"](14," Cancel "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"div",10),p["\u0275\u0275listener"]("click",(function(){return t.generateLink()})),p["\u0275\u0275text"](16," Generate "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](17,Y,2,0,"ng-template",null,11,p["\u0275\u0275templateRefExtractor"])),2&e&&(p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](7,L,t.isApiInProgress?"none":"")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("formGroup",t.assessmentForm),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",t.assessmentLinkTypeMaster),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",1==t.assessmentForm.get("type").value),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",2==t.assessmentForm.get("type").value),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](9,L,t.isApiInProgress?"none":"")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](11,L,t.isApiInProgress?"none":"")))},directives:[h.a,v.NgStyle,r.w,r.n,y.b,r.v,r.l,v.NgForOf,v.NgIf,y.a,C.a,v.NgTemplateOutlet,M.c,O.b,r.e,r.A,_.g,_.i,_.j,_.f,x.a],styles:[".bg-container[_ngcontent-%COMP%]{padding:24px;overflow:hidden}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{margin-bottom:20px}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .mat-radio-button.mat-accent .mat-radio-inner-circle{background-color:var(--atssecondaryColor)!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:var(--atssecondaryColor)!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .mat-radio-outer-circle{border-color:#dadce2}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .mat-radio-inner-circle, .bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .mat-radio-outer-circle{height:16px!important;width:16px!important;margin-top:2px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .mat-radio-label-content{padding-left:0!important;padding-right:16px!important;font-family:var(--atsfontFamily)!important;color:#5f6c81!important;font-size:14px!important;font-weight:400!important;max-width:500px!important;overflow:hidden!important;text-overflow:ellipsis!important;white-space:nowrap!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]     .mat-input-element[type=time]{padding-top:8px;height:18px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:var(--atsfontFamily)!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-time[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-time[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:var(--atsfontFamily)!important;width:100%!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-time[_ngcontent-%COMP%]     .mat-form-field-wrapper, .bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-field-time[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#5f6c81;font-size:12px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .date-picker[_ngcontent-%COMP%]{display:flex;align-items:center;height:17px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .calendar-icon[_ngcontent-%COMP%]{font-size:16px}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:11px 15px;color:#45546e}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);border-radius:8px;padding:12px 16px;color:#fff}"]}),e})()}}]);