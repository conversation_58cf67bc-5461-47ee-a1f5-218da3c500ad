(window.webpackJsonp=window.webpackJsonp||[]).push([[752],{"1/5p":function(t,e,n){"use strict";n.r(e),n.d(e,"GovRepLegalEntityModalComponent",(function(){return f}));var l=n("0IaG"),a=n("xG9w"),i=n("ofXK"),o=n("bTqV"),s=(n("3Pt+"),n("NFeN")),r=(n("kmnG"),n("qFsG"),n("Qu3c")),d=n("fXoL");function m(t,e){if(1&t){const t=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"button",15),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](t);const e=d["\u0275\u0275nextContext"](2).$implicit;return d["\u0275\u0275nextContext"]().collapseLegalEntity(e)})),d["\u0275\u0275elementStart"](1,"mat-icon",16),d["\u0275\u0275text"](2,"keyboard_arrow_right"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function c(t,e){if(1&t){const t=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"button",15),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](t);const e=d["\u0275\u0275nextContext"](2).$implicit;return d["\u0275\u0275nextContext"]().collapseLegalEntity(e)})),d["\u0275\u0275elementStart"](1,"mat-icon",17),d["\u0275\u0275text"](2,"keyboard_arrow_down"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function g(t,e){if(1&t){const t=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",12),d["\u0275\u0275template"](1,m,3,0,"button",13),d["\u0275\u0275template"](2,c,3,0,"button",13),d["\u0275\u0275elementStart"](3,"p",14),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](t);const e=d["\u0275\u0275nextContext"]().$implicit;return d["\u0275\u0275nextContext"]().selectBaseLegalEntity(e.name)})),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&t){const t=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("ngClass",1==t.leLevel?"pl-3":2==t.leLevel?"pl-5":3==t.leLevel?"pl-12":""),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!(0!=t.leLevel&&1!=t.leLevel&&2!=t.leLevel||t.isExpanded||t.isExpandedOverridden)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(0==t.leLevel||1==t.leLevel||2==t.leLevel)&&t.isExpanded&&!t.isExpandedOverridden),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",t.isExpandedOverridden?"pl-9":""),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("",t.name," ")}}function p(t,e){if(1&t&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275template"](1,g,5,5,"div",11),d["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("matTooltip",t.name)("ngClass",t.name==n.modalParams.baseLegalEntity?"it13Selected":"it13NotSelected"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isVisible)}}let f=(()=>{class t{constructor(t,e){this.dialogRef=t,this.inData=e}ngOnInit(){this.modalParams=this.inData.modalParams}selectBaseLegalEntity(t){let e=a.where(this.modalParams.legalEntities,{name:t,isExpandedOverridden:!0});this.dialogRef.close({event:"Submit",data:{govRepLegalEntityModalResponse:{legalEntities:this.modalParams.legalEntities,baseLegalEntity:t,isExpandOverridden:e.length>0}}})}collapseLegalEntity(t){for(let e=0;e<this.modalParams.legalEntities.length;e++)if(this.modalParams.legalEntities[e].name==t.name)this.modalParams.legalEntities[e].isExpanded=!this.modalParams.legalEntities[e].isExpanded;else if(this.modalParams.legalEntities[e].rollup==t.name){this.modalParams.legalEntities[e].isVisible=!this.modalParams.legalEntities[e].isVisible;for(let t=0;t<this.modalParams.legalEntities.length;t++)if(this.modalParams.legalEntities[t].rollup==this.modalParams.legalEntities[e].name&&this.modalParams.legalEntities[e].isExpanded){this.modalParams.legalEntities[t].isVisible=this.modalParams.legalEntities[e].isVisible;for(let e=0;e<this.modalParams.legalEntities.length;e++)this.modalParams.legalEntities[e].rollup==this.modalParams.legalEntities[t].name&&(this.modalParams.legalEntities[e].isVisible=this.modalParams.legalEntities[t].isVisible)}}}closeModal(){this.dialogRef.close({event:"Close"})}}return t.\u0275fac=function(e){return new(e||t)(d["\u0275\u0275directiveInject"](l.h),d["\u0275\u0275directiveInject"](l.a))},t.\u0275cmp=d["\u0275\u0275defineComponent"]({type:t,selectors:[["gov-rep-legal-entity-modal"]],decls:17,vars:1,consts:[[1,"container-fluid","legal-entity-modal-styles"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","p-0"],[1,"col-2","p-0"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"col-5","p-0","mt-1","name"],["mat-icon-button","","matTooltip","Close",1,"d-flex","ml-auto","mr-2","close-button",3,"click"],[1,"close-Icon"],[3,"matTooltip","ngClass",4,"ngFor","ngForOf"],[3,"matTooltip","ngClass"],["class","row p-1 pCenter13 df","style","border-bottom: solid 1px #cacaca;",3,"ngClass",4,"ngIf"],[1,"row","p-1","pCenter13","df",2,"border-bottom","solid 1px #cacaca",3,"ngClass"],["mat-icon-button","","class","arrow-in-sidnav mr-2",3,"click",4,"ngIf"],[1,"it13","fg-1",3,"ngClass","click"],["mat-icon-button","",1,"arrow-in-sidnav","mr-2",3,"click"],["matTooltip","Expand",1,"expand-icons"],["matTooltip","Collapse",1,"expand-icons"]],template:function(t,e){1&t&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275element"](2,"div",2),d["\u0275\u0275elementStart"](3,"div",3),d["\u0275\u0275elementStart"](4,"div",4),d["\u0275\u0275elementStart"](5,"mat-icon",5),d["\u0275\u0275text"](6,"filter_list"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](7,"div",2),d["\u0275\u0275elementStart"](8,"div",6),d["\u0275\u0275text"](9,"Change P & L"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](10,"div",2),d["\u0275\u0275elementStart"](11,"div",2),d["\u0275\u0275elementStart"](12,"button",7),d["\u0275\u0275listener"]("click",(function(){return e.closeModal()})),d["\u0275\u0275elementStart"](13,"mat-icon",8),d["\u0275\u0275text"](14,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](15,"div",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](16,p,2,3,"div",9),d["\u0275\u0275elementEnd"]()),2&t&&(d["\u0275\u0275advance"](16),d["\u0275\u0275property"]("ngForOf",e.modalParams.legalEntities))},directives:[s.a,o.a,r.a,i.NgForOf,i.NgClass,i.NgIf],styles:[".legal-entity-modal-styles[_ngcontent-%COMP%]{background-size:200px 181px;background-repeat:no-repeat;overflow-y:hidden!important;min-height:95vh;background-position:99% 105%;padding-left:0!important;padding-right:0!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.legal-entity-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:5px!important;margin-left:4px!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center}.legal-entity-modal-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:30px!important;height:30px!important;line-height:30px!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .it13[_ngcontent-%COMP%]{color:#181818!important;font-size:13px!important;font-weight:400!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;margin-bottom:0!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .pl-4[_ngcontent-%COMP%]{padding-left:1rem!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .pl-5[_ngcontent-%COMP%]{padding-left:2rem!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .pl-6[_ngcontent-%COMP%]{padding-left:3.5rem!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .pl-9[_ngcontent-%COMP%]{padding-left:2.5rem!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .pl-12[_ngcontent-%COMP%]{padding-left:3rem!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .it13Selected[_ngcontent-%COMP%]{background-color:#d3d3d3;cursor:pointer}.legal-entity-modal-styles[_ngcontent-%COMP%]   .it13NotSelected[_ngcontent-%COMP%]{cursor:pointer}.legal-entity-modal-styles[_ngcontent-%COMP%]   .fs9[_ngcontent-%COMP%]{font-size:9px!important;align-self:center!important;padding-top:1px!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .pCenter13[_ngcontent-%COMP%]{line-height:26px}.legal-entity-modal-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.legal-entity-modal-styles[_ngcontent-%COMP%]   .mr5[_ngcontent-%COMP%]{margin-right:5px!important}.legal-entity-modal-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.legal-entity-modal-styles[_ngcontent-%COMP%]   .normal-font[_ngcontent-%COMP%]{color:#1a1a1a;font-size:13px;font-weight:400}.legal-entity-modal-styles[_ngcontent-%COMP%]   .arrow-in-sidnav[_ngcontent-%COMP%]{height:30px;width:30px;line-height:30px}.legal-entity-modal-styles[_ngcontent-%COMP%]   .arrow-in-sidnav[_ngcontent-%COMP%]   .expand-icons[_ngcontent-%COMP%]{font-size:18px}.legal-entity-modal-styles[_ngcontent-%COMP%]   .df[_ngcontent-%COMP%]{display:flex}.legal-entity-modal-styles[_ngcontent-%COMP%]   .fg-1[_ngcontent-%COMP%]{flex-grow:1}"]}),t})()}}]);