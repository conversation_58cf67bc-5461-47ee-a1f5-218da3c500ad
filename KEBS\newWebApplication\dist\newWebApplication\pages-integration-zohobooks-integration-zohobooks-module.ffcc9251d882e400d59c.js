(window.webpackJsonp=window.webpackJsonp||[]).push([[840],{"90lq":function(e,t,n){"use strict";n.r(t),n.d(t,"IntegrationZohobooksModule",(function(){return le}));var i=n("ofXK"),o=n("tyNb"),s=n("mrSG"),r=n("3Pt+"),a=n("1G5W"),l=n("XNiG"),c=n("xG9w"),d=n("FKr1"),p=n("1yaQ"),h=n("wd/R"),u=n("fXoL"),m=n("tk/3");let g=(()=>{class e{constructor(e){this.http=e,this.url="/api/integrationLayer/"}getAdminZBConnectionDetails(){return this.http.post(this.url+"getAdminZBConnectionDetails",{})}getZBVoucherTypes(){return this.http.post(this.url+"getZBVoucherTypes",{})}getTokenForAdminConnection(e,t){return this.http.post(this.url+"getTokenForAdminConnection",{book_id:e,company_id:t})}getZBDataFromSource(e){return this.http.post(this.url+"getZBDataFromSource",e)}getbatchSize(){return this.http.post(this.url+"getbatchSize",{})}syncZBReportsData(e,t,n){return this.http.post(this.url+"syncZBReportsData",{url:e,voucher_type:t,voucher_data:n})}getLongPoolingConfig(){return this.http.post(this.url+"getLongPoolingConfig",{})}postNewSyncID(){return this.http.post(this.url+"postNewSyncID",{})}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275inject"](m.c))},e.\u0275prov=u["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var f=n("1A3m"),y=n("dNgK"),S=n("l5mm"),b=n("eIep");let v=(()=>{class e{constructor(e){this.http=e,this.apiUrl="/api/integrationLayer/longPollingService"}longPoll(e,t){return Object(S.a)(e).pipe(Object(b.a)(()=>this.http.post(this.apiUrl,{sync_id:t})))}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275inject"](m.c))},e.\u0275prov=u["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var k=n("Xa2L"),D=n("Wp6s"),C=n("TmG/"),T=n("kmnG"),O=n("qFsG"),_=n("iadO"),I=n("d3UM"),R=n("bSwM"),P=n("bTqV"),E=n("NFeN"),w=n("Qu3c");const x=["select"];function B(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",3),u["\u0275\u0275element"](1,"mat-spinner",4),u["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"mat-option",35),u["\u0275\u0275text"](1),u["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;u["\u0275\u0275property"]("value",e.id),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate"](e.name)}}function j(e,t){1&e&&u["\u0275\u0275element"](0,"div",36)}function F(e,t){1&e&&u["\u0275\u0275element"](0,"div",37)}function Z(e,t){1&e&&u["\u0275\u0275element"](0,"div",37)}function N(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"mat-icon",41),u["\u0275\u0275text"](1,"refresh"),u["\u0275\u0275elementEnd"]())}function V(e,t){1&e&&u["\u0275\u0275element"](0,"mat-spinner",42)}function z(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",7),u["\u0275\u0275elementStart"](1,"button",38),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](2).getZBRefreshToken()})),u["\u0275\u0275template"](2,N,2,0,"mat-icon",39),u["\u0275\u0275template"](3,V,1,0,"mat-spinner",40),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngClass",e.isTokenGeneratedOnRefreshToken?"create-pr-btn-loading":"create-pr-btn"),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!e.isTokenGeneratedOnRefreshToken),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.isTokenGeneratedOnRefreshToken)}}function q(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",43),u["\u0275\u0275elementStart"](1,"div",44),u["\u0275\u0275element"](2,"mat-spinner",45),u["\u0275\u0275elementStart"](3,"p",46),u["\u0275\u0275text"](4),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](4),u["\u0275\u0275textInterpolate1"]("Kindly Don't refresh the page...! ",e.spinnerStatusText,"")}}const A=function(){return{standalone:!0}};function L(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"mat-card",5),u["\u0275\u0275elementStart"](2,"div",6),u["\u0275\u0275elementStart"](3,"div",7),u["\u0275\u0275text"](4," Entity : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"div",8),u["\u0275\u0275elementStart"](6,"app-input-search",9),u["\u0275\u0275listener"]("change",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().changeEntity()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](7,"div",10),u["\u0275\u0275elementStart"](8,"form",11),u["\u0275\u0275elementStart"](9,"div",12),u["\u0275\u0275elementStart"](10,"div",13),u["\u0275\u0275elementStart"](11,"div",14),u["\u0275\u0275elementStart"](12,"div",15),u["\u0275\u0275text"](13,"Start Date : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](14,"mat-form-field",16),u["\u0275\u0275elementStart"](15,"mat-label"),u["\u0275\u0275text"](16,"Select Date"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](17,"input",17),u["\u0275\u0275listener"]("dateChange",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().onStartDateChange()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](18,"mat-datepicker-toggle",18),u["\u0275\u0275element"](19,"mat-datepicker",null,19),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](21,"div",14),u["\u0275\u0275elementStart"](22,"div",15),u["\u0275\u0275text"](23,"End Date : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](24,"mat-form-field",16),u["\u0275\u0275elementStart"](25,"mat-label"),u["\u0275\u0275text"](26,"Select Date"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](27,"input",20),u["\u0275\u0275element"](28,"mat-datepicker-toggle",18),u["\u0275\u0275element"](29,"mat-datepicker",null,21),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](31,"div",22),u["\u0275\u0275elementStart"](32,"div",14),u["\u0275\u0275elementStart"](33,"div",15),u["\u0275\u0275text"](34,"Voucher Type : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](35,"mat-form-field",16),u["\u0275\u0275elementStart"](36,"mat-label"),u["\u0275\u0275text"](37,"Voucher Type "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](38,"mat-select",23,24),u["\u0275\u0275elementStart"](40,"div",25),u["\u0275\u0275elementStart"](41,"mat-checkbox",26),u["\u0275\u0275listener"]("ngModelChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().allSelected=t}))("change",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().toggleAllSelection()})),u["\u0275\u0275text"](42,"Select All"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](43,M,2,2,"mat-option",27),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](44,j,1,0,"div",28),u["\u0275\u0275elementStart"](45,"div",29),u["\u0275\u0275template"](46,F,1,0,"div",30),u["\u0275\u0275template"](47,Z,1,0,"div",30),u["\u0275\u0275template"](48,z,4,3,"div",31),u["\u0275\u0275elementStart"](49,"div",7),u["\u0275\u0275elementStart"](50,"button",32),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().getZBReportsData()})),u["\u0275\u0275elementStart"](51,"mat-icon",33),u["\u0275\u0275text"](52,"done_all"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](53,q,5,1,"div",34),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275reference"](20),t=u["\u0275\u0275reference"](30),n=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](6),u["\u0275\u0275property"]("list",n.availableZBConnections)("formControl",n.selectedEntityBook),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("formGroup",n.queryParamsInputForm),u["\u0275\u0275advance"](9),u["\u0275\u0275property"]("matDatepicker",e)("min",n.minStartDate)("disabled",n.queryFormDisabled),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",e),u["\u0275\u0275advance"](9),u["\u0275\u0275property"]("matDatepicker",t)("min",n.minEndDate)("disabled",n.queryFormDisabled||!n.isStartDateSelected),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",t),u["\u0275\u0275advance"](10),u["\u0275\u0275property"]("disabled",n.queryFormDisabled),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngModel",n.allSelected)("ngModelOptions",u["\u0275\u0275pureFunction0"](22,A)),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngForOf",n.availableZBReportVoucherTypes),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.isTokenNotAvailable),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",!n.isTokenNotAvailable),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.isTokenNotAvailable),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.isTokenNotAvailable),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngClass",n.isDataRetrived?"create-pr-btn-loading":"create-pr-btn")("disabled",n.isDataRetrived),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",!n.isRetrievalCompletedforSpinner)}}const G=[{path:"",component:(()=>{class e{constructor(e,t,n,i,o){this.fb=e,this.api=t,this._toaster=n,this.snackBar=i,this.longPollingService=o,this.isZBFormCreationCompleted=!1,this.availableZBConnections=[],this.selectedEntityBook=new r.j(""),this.userSelectedEntityName="",this.userSelectedEntityID="",this.userSelectedBookID=0,this.userSelectedSourceURL="",this.isTokenNotAvailable=!1,this.isTokenGeneratedOnRefreshToken=!1,this.availableZBReportVoucherTypes=[],this.appliedVoucherTypesforZB=[],this.authenticationFieldMapping={key:"Bearer",value:""},this.queryParamsInputForm=this.fb.group({date_start:["",r.H.required],date_end:["",r.H.required],voucher_type:["",r.H.required]}),this.minStartDate=new Date("1900-01-01"),this.isStartDateSelected=!1,this.queryFormDisabled=!0,this.responseProcess=!1,this.onSyncBatchSize=10,this.body={url:""},this.sampleResponse=[],this.response=[],this.tempResponse=[],this.viewLogs=!1,this.isDataRetrived=!1,this.isSyncOnProgress=!0,this.syncProgress=0,this.onSyncBatchSizeZBReports=10,this.onSyncFlag=!1,this.longPoolingTimerConfig=0,this.$onDestroy=new l.b,this.newSyncID=0,this.isRetrievalCompletedforSpinner=!0,this.spinnerStatusText="",this.allSelected=!1}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){yield this.api.getLongPoolingConfig().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.longPoolingTimerConfig=e.data)}),yield this.api.getAdminZBConnectionDetails().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isZBFormCreationCompleted=!0,this.availableZBConnections=e.data):(this.availableZBConnections=[],this.userSelectedEntityName="",this.userSelectedEntityID="")}),yield this.api.getZBVoucherTypes().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{this.availableZBReportVoucherTypes="S"==e.messType?e.data:[]}),yield this.api.getbatchSize().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{this.onSyncBatchSize=e.Result.on_sync_batch_size,this.onSyncBatchSizeZBReports=e.Result.on_sync_zb_reports_size})}))}changeEntity(){return Object(s.c)(this,void 0,void 0,(function*(){let e=this.selectedEntityBook.value,t=this.availableZBConnections.filter(t=>t.id==e);this.userSelectedEntityName=t[0].entity_name,this.userSelectedEntityID=t[0].company_id,this.userSelectedBookID=t[0].book_id,this.userSelectedSourceURL=t[0].source_url,this.queryFormDisabled=!1,console.log("Changed Entity Value : ",this.userSelectedEntityName,this.userSelectedEntityID,this.userSelectedBookID),yield this.getTokenDetails()}))}addOperationValue(){return this.fb.group({voucher_type:new r.j("",r.H.required)})}onVoucherTypeAddRow(){this.queryParamsInputForm.controls.voucherTypeArray.push(this.addOperationValue())}onVoucherTypeRemoveRow(e){let t=this.queryParamsInputForm.controls.voucherTypeArray;console.log("Control length : ",t.length),t.length<=1?(t.clear(),this.onVoucherTypeAddRow()):(t.removeAt(e),console.log("Voucher Type afer deleting rows : ",this.queryParamsInputForm.value))}getZBReportsData(){return Object(s.c)(this,void 0,void 0,(function*(){let e;if(!this.queryParamsInputForm.valid)return this.snackBar.open("Kindly give the values for all the required fields!","Dismiss",{duration:3e3});{this.isDataRetrived=!0,this.appliedVoucherTypesforZB=[],e=this.queryParamsInputForm.value,e.date_start_value=h(e.date_start).format("YYYY-MM-DD"),e.date_end_value=h(e.date_end).format("YYYY-MM-DD"),console.log("Submission QueryParams Details : ",e);let t=[];if(t=[...new Set(e.voucher_type)],t&&t.length>0&&c.each(t,e=>{var t;let n=this.availableZBReportVoucherTypes.filter(t=>t.id==e);this.appliedVoucherTypesforZB.push(null===(t=n[0])||void 0===t?void 0:t.name)}),e.selectedVouchers=this.appliedVoucherTypesforZB,console.log("User Given Query params Values : ",e),this.userSelectedSourceURL.includes("date_start")||this.userSelectedSourceURL.includes("date_end")){let t=new URL(this.userSelectedSourceURL),n=t.searchParams.get("date_start"),i=t.searchParams.get("date_end");n!=e.date_start_value&&t.searchParams.set("date_start",e.date_start_value),i!=e.date_end_value&&t.searchParams.set("date_end",e.date_end_value),this.userSelectedSourceURL=String(t)}else this.userSelectedSourceURL+=`&date_start=${e.date_start_value}&date_end=${e.date_end_value}`;console.log("Source URL : ",this.userSelectedSourceURL),console.log("Selected Voucher Types : ",this.appliedVoucherTypesforZB),console.log("Authorization Details : ",this.authenticationFieldMapping),yield this.getZBDataFromSource()}}))}getTokenDetails(){return Object(s.c)(this,void 0,void 0,(function*(){yield this.api.getTokenForAdminConnection(this.userSelectedBookID,this.userSelectedEntityID).pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isTokenNotAvailable=!1,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value=e.tokenDetails.token):(this.isTokenNotAvailable=!0,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value="",this._toaster.showError("Token Status","Token Not Generated! Kindly refresh the token ! ",3e3))})}))}getZBRefreshToken(){return Object(s.c)(this,void 0,void 0,(function*(){this.isTokenGeneratedOnRefreshToken=!0,yield this.api.getTokenForAdminConnection(this.userSelectedBookID,this.userSelectedEntityID).pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isTokenNotAvailable=!1,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value=e.tokenDetails.token):(this.isTokenNotAvailable=!0,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value="",this._toaster.showError("Token Status","Token Not Generated for Sync ! Kindly reach out to KEBS team ! ",3e3))})}))}getZBDataFromSource(){return Object(s.c)(this,void 0,void 0,(function*(){this.isRetrievalCompletedforSpinner=!1,this.spinnerStatusText="Data is being retrieved from ZohoBooks",yield this.api.postNewSyncID().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if("S"==e.messType){let t;this.newSyncID=e.data,this.isDataRetrived=!0,this.viewLogs=!1,this.responseProcess=!1,this.body.url=this.userSelectedSourceURL,this.sampleResponse=[],this.response=[],Object.assign(this.body,{method:"GET"}),Object.assign(this.body,{key:this.authenticationFieldMapping.key}),Object.assign(this.body,{value:this.authenticationFieldMapping.key+" "+this.authenticationFieldMapping.value}),Object.assign(this.body,{uniqueVoucherTypes:this.appliedVoucherTypesforZB}),Object.assign(this.body,{sync_id:this.newSyncID});let n=JSON.stringify(this.body);t=JSON.parse(n),yield this.api.getZBDataFromSource(t).pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if("S"===e.messType){this.response.push(e.data),this.tempResponse=Object.assign([],this.response);let t=0,n={},i=Object.keys(this.response[0]);c.each(i,e=>{n[e]=[]});for(let e of Object.keys(this.response[0]))if(t+=this.response[0][e].length,this.response[0][e].length>10){let t=0;for(let i of this.response[0][e]){if(!(t<10))break;n[e].push(i),t++}}else n[e].push(...this.response[0][e]);console.log("Response : ",this.response[0]," with length : ",t),this.responseProcess=!0,this.sampleResponse=[n]}else"E"===e.messType&&(this.responseProcess=!1,this.isDataRetrived=!1,this.response.push(e.error),this.tempResponse=Object.assign([],this.response),console.log("Error on Response : ",this.response[0]),this.response=this.response[0],this.sampleResponse=this.response)}))),yield this.initLongPolling(this.newSyncID)}else this.newSyncID=0})))}))}ViewLogs(){this.viewLogs=!this.viewLogs}syncDataOnKEBS(){return Object(s.c)(this,void 0,void 0,(function*(){this.onSyncFlag=!0,this.isSyncOnProgress=!1,this.syncProgress=0,console.log("this.response on sync : ",this.response[0]),this.sampleOnSyncResponse=yield this.onReturnArrayResponse(this.response[0]),console.log("this.sampleOnSyncResponse on sync : ",this.sampleOnSyncResponse),this.onSyncSliceSize=0;for(let i of Object.keys(this.response[0]))this.onSyncSliceSize+=this.response[0][i].length;let e=Object.keys(this.response[0]),t=0,n=0;for(let i of Object.keys(this.response[0])){t+=1;let e=i,o=this.response[0][i].length,s=this.response[0][i],r=0;for(let t=0;t<o;t+=this.onSyncBatchSizeZBReports){let o=s.slice(t,t+this.onSyncBatchSizeZBReports),a=o.length,l=yield this.voucherDataSyncforZB(this.userSelectedSourceURL,i,o);if(console.log("SyncResult : ",l),"S"==l.messType)r+=a,n+=a,console.log(`Current voucher : ${e} with length ${r} is synced !`),console.log(`${n} out of ${this.onSyncSliceSize} records synced...`),this.syncProgress=Math.ceil(n/this.onSyncSliceSize*100);else if("E"==l.messType)return console.log(`Error on ${e} data sync`),this.onSyncFlag=!1,this._toaster.showError(l.messText,"Status Info : "+l.error,5e3)}this.syncProgress>=100&&(this.syncProgress=0)}if(t==e.length)return this.onSyncFlag=!1,this.isSyncOnProgress=!0,this._toaster.showSuccess("Data Synced",this.userSelectedSourceURL,5e3)}))}onReturnArrayResponse(e){return Object(s.c)(this,void 0,void 0,(function*(){return null===JSON||void 0===JSON?void 0:JSON.parse(null===JSON||void 0===JSON?void 0:JSON.stringify(e))}))}voucherDataSyncforZB(e,t,n){return Object(s.c)(this,void 0,void 0,(function*(){try{return yield this.api.syncZBReportsData(e,t,n).pipe(Object(a.a)(this.$onDestroy)).toPromise()}catch(i){throw console.error("Error syncing data:",i),i}}))}initLongPolling(e){return Object(s.c)(this,void 0,void 0,(function*(){this.pollingSubscription=yield this.longPollingService.longPoll(this.longPoolingTimerConfig,e).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){"Started"===e.messStatus||""===e.messStatus?this.updateSpinnerStatus("Data is being retrieved from ZohoBooks"):"In-Progress"===e.messStatus?this.updateSpinnerStatus("Data is being synced in KEBS"):"S"!==e.messType||"In-Progress"!==e.messStatus&&"Completed"!==e.messStatus||!0!==e.isNoDataFound?"E"===e.messType||"Error"===e.messStatus?this.handleError(e):"S"===e.messType&&"Completed"===e.messStatus&&this.handleCompletedResponse(e):this.handleNoDataFoundResponse(e)})),e=>{console.error("Error fetching data:",e)})}))}updateSpinnerStatus(e){this.isRetrievalCompletedforSpinner=!1,this.spinnerStatusText=e}handleCompletedResponse(e){this.isDataRetrived=!1,this.responseProcess=!0,this.isRetrievalCompletedforSpinner=!0,this._toaster.showSuccess("ZohoBooks Reports Synced Successfully !","",5e3),this.stopPolling()}handleNoDataFoundResponse(e){this.isDataRetrived=!1,this.responseProcess=!0,this.isRetrievalCompletedforSpinner=!0,this._toaster.showInfo("No Data Available to Sync !","",5e3),this.stopPolling()}handleError(e){this.isDataRetrived=!1,this.responseProcess=!0,this.isRetrievalCompletedforSpinner=!0,this._toaster.showError("Error on ZohoBooks Sync !","",5e3),this.stopPolling()}stopPolling(){this.pollingSubscription&&(this.pollingSubscription.unsubscribe(),this.pollingSubscription=void 0)}onStartDateChange(){let e=new Date(this.queryParamsInputForm.value.date_start),t=h(this.queryParamsInputForm.value.date_start);this.minEndDate=e,t&&(this.isStartDateSelected=!0)}toggleAllSelection(){this.select.options.forEach(this.allSelected?e=>e.select():e=>e.deselect())}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](r.i),u["\u0275\u0275directiveInject"](g),u["\u0275\u0275directiveInject"](f.a),u["\u0275\u0275directiveInject"](y.a),u["\u0275\u0275directiveInject"](v))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-integration-zohobooks-landing-page"]],viewQuery:function(e,t){if(1&e&&u["\u0275\u0275viewQuery"](x,!0),2&e){let e;u["\u0275\u0275queryRefresh"](e=u["\u0275\u0275loadQuery"]())&&(t.select=e.first)}},features:[u["\u0275\u0275ProvidersFeature"]([{provide:d.c,useClass:p.c,deps:[d.f,p.a]},{provide:d.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:3,vars:2,consts:[[1,"container-fluid","zbIntegration"],["class","row justify-content-center","style","padding-top: 10vh;",4,"ngIf"],[4,"ngIf"],[1,"row","justify-content-center",2,"padding-top","10vh"],["diameter","30"],[1,"add-form-card","mt-2"],[1,"col","d-flex","align-items-baseline"],[1,"col-1"],[1,"col-6"],["placeholder","Select Entity","required","",2,"width","66%","height","45px",3,"list","formControl","change"],[1,"queryParamsInput"],[2,"margin-top","2%",3,"formGroup"],[1,"row","d-flex"],[1,"col-12","d-flex"],[1,"row","d-flex","align-items-center"],[1,"col"],["appearance","outline",1,"col"],["matInput","","required","","formControlName","date_start",3,"matDatepicker","min","disabled","dateChange"],["matSuffix","",3,"for"],["picker1",""],["matInput","","required","","formControlName","date_end",3,"matDatepicker","min","disabled"],["picker2",""],[1,"col-12","d-flex","align-items-center","mt-3"],["formControlName","voucher_type","multiple","","required","","placeholder","Voucher Type",3,"disabled"],["select",""],[1,"col","select-all",2,"margin-top","10px"],[3,"ngModel","ngModelOptions","ngModelChange","change"],[3,"value",4,"ngFor","ngForOf"],["class","tokenGenerationDetails",4,"ngIf"],[1,"col","d-flex",2,"position","relative","margin-top","5vh"],["class","col-10",4,"ngIf"],["class","col-1",4,"ngIf"],["mat-mini-fab","",1,"create-pr-btn",3,"ngClass","disabled","click"],["matTooltip","Sync Data"],["class","content d-flex justify-content-center mt-5 mb-2",4,"ngIf"],[3,"value"],[1,"tokenGenerationDetails"],[1,"col-10"],["mat-mini-fab","",1,"create-pr-btn",3,"ngClass","click"],["matTooltip","Refresh Token",4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","20",4,"ngIf"],["matTooltip","Refresh Token"],["matTooltip","Please wait...","diameter","20",1,"spinner-align"],[1,"content","d-flex","justify-content-center","mt-5","mb-2"],[2,"align-items","center","display","flex","flex-direction","column"],["diameter","50"],[2,"margin-top","5px"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275template"](1,B,2,0,"div",1),u["\u0275\u0275template"](2,L,54,23,"div",2),u["\u0275\u0275elementEnd"]()),2&e&&(u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!t.isZBFormCreationCompleted),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.isZBFormCreationCompleted))},directives:[i.NgIf,k.c,D.a,C.a,r.F,r.v,r.k,r.J,r.w,r.n,T.c,T.g,O.b,r.e,_.g,r.l,_.i,T.i,_.f,I.c,R.a,r.y,i.NgForOf,P.a,i.NgClass,E.a,w.a,d.p],styles:[".zbIntegration[_ngcontent-%COMP%]   .add_btn[_ngcontent-%COMP%]{height:33px;width:33px;display:flex;cursor:pointer;border:1px solid #efa6a6;align-items:center;border-radius:4px;justify-content:center}.zbIntegration[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.zbIntegration[_ngcontent-%COMP%]   .submit-btn-loading[_ngcontent-%COMP%]{background-color:#fff}.zbIntegration[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{cursor:pointer}.zbIntegration[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{background-color:rgba(102,97,91,.13725490196078433);border-radius:2px}.zbIntegration[_ngcontent-%COMP%]   .create-pr-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.zbIntegration[_ngcontent-%COMP%]   .create-pr-btn-loading[_ngcontent-%COMP%]{background-color:#fff}.zbIntegration[_ngcontent-%COMP%]   .create-pr-btn[_ngcontent-%COMP%]:disabled{background-color:#cf0001;color:#fff;opacity:.5}.zbIntegration[_ngcontent-%COMP%]   .button-base[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.zbIntegration[_ngcontent-%COMP%]   .button-base[_ngcontent-%COMP%]:disabled{background-color:#cf0001;color:#fff;opacity:.5}.zbIntegration[_ngcontent-%COMP%]   .title-name[_ngcontent-%COMP%]{color:#9a9a9a;font-size:18px!important}.zbIntegration[_ngcontent-%COMP%]   .card-header-text[_ngcontent-%COMP%]{color:#9a9a9a;font-size:14px!important;border-bottom:1px solid hsla(0,0%,70.6%,.8);margin-left:7px!important;margin-right:7px!important}.zbIntegration[_ngcontent-%COMP%]   .blacklist-mail-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}"]}),e})(),redirectTo:"",pathMatch:"full"}];let Y=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(G)],o.k]}),e})();var $=n("XhcP"),U=n("lVl8"),J=n("dlKe"),K=n("M9IT"),Q=n("+0xr"),H=n("Dh3D"),X=n("0IaG"),W=n("xHqg"),ee=n("wZkO"),te=n("Xi0T"),ne=n("STbY"),ie=n("ZzPI"),oe=n("1jcm"),se=n("bv9b"),re=n("/1cH"),ae=n("f0Cb");let le=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Y,te.a,r.p,r.E,P.b,X.g,O.c,K.b,Q.m,T.e,H.c,E.b,$.g,W.f,D.d,I.d,R.b,ee.g,k.b,_.h,d.n,p.b,w.b,U.b,J.b,ne.e,ie.b,oe.b,se.b,re.c,ae.b]]}),e})()}}]);