(window.webpackJsonp=window.webpackJsonp||[]).push([[765],{hJL4:function(t,e,s){"use strict";s.d(e,"a",(function(){return h}));var i=s("mrSG"),r=s("XNiG"),o=s("xG9w"),a=s("fXoL"),n=s("tk/3"),p=s("LcQX"),l=s("XXEo"),u=s("flaP");let h=(()=>{class t{constructor(t,e,s,i){this.http=t,this.UtilityService=e,this.loginService=s,this.roleService=i,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(t,e)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:t,approverOid:e}),this.rejectRequest=(t,e)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:t,approverOid:e}),this.getTaskStatusColor()}getAllRequestsOfUser(t,e,s,i,r,o,a){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:t,statusId:e,statusCode:s,objectIds:i,skip:r,limit:o,filterConfig:a,orgIds:n})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(t,e,s,i,r,o,a){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:t,statusId:e,statusCode:s,objectIds:i,skip:r,limit:o,filterConfig:a,orgIds:n})}getRequestsForAwaitingApproval(t,e,s,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:t,skip:e,limit:s,filterConfig:i})}getObjectIdsBasedOnOid(t){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:t})}getStatusCountBasedOnOid(t,e,s,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:t,objectIds:e,dataTypeArray:s,filterConfig:i,orgIds:r})}getHeaderCount(t){return this.http.post("/api/isa/request/getHeaderCount",{oid:t})}getRequestBasedOnStatus(t,e){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:t,status:e})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(t){this.msg.next(t)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((t,e)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(e=>(this.taskStatusColor=e,t(e)),t=>{})})}getAllRequestsForRmg(t){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:t})}getTotalForRmg(t){return new Promise((e,s)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:t}).subscribe(t=>e(t),t=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(t){return new Promise((e,s)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",t).subscribe(t=>e(t.data),t=>{s(t)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(t){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}saveAndApproveRequest(t){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",t)}getVisibilityMatrix(t){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getISAAttachmentFromS3(t){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(t){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:t})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(t){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:t})}getISACustomReport(t){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:t})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(t,e,s,r,a,n,p){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=n&&n.length>1&&(yield this.getManpowerCostByOId(n,s,a,2))||(yield this.getManpowerCostBasedOnPosition(t,e,s,a,p));let l=yield this.getNonManpowerCost(e,s,r,a,2),u=yield this.getAllocatedCost(),h=0;h=(i?i.cost:0)+l.length>0?o.reduce(o.pluck(l,"cost"),(t,e)=>t+e,0):0;let g=u.length>0?o.reduce(o.pluck(u,"percentage"),(t,e)=>t+e,0):0;return{cost:h,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:l,allocatedCost:u,allocatedCostValue:h*(g/100)}}))}getManpowerCostBasedOnPosition(t,e,s,i,r){return new Promise((o,a)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:t,nationalityId:e,locationGroupId:s,unit:i,position:r}).subscribe(t=>o(t),t=>(console.log(t),a(t)))})}getNonManpowerCost(t,e,s,i,r){return new Promise((o,a)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:t,locationGroupId:e,locationId:s,unit:i,currency_id:r}).subscribe(t=>o(t),t=>(console.log(t),a(t)))})}getAllocatedCost(){return new Promise((t,e)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(e=>t(e),t=>(console.log(t),e(t)))})}getManpowerCostByOId(t,e,s,i){return new Promise((r,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:t,location_group_id:e,unit_id:s,currency_id:i}).subscribe(t=>r(t),t=>(console.log(t),o(t)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(t,e){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:t,docID:e})}getVendorList(t,e){return this.http.post("/api/isa/request/getVendorList",{documentID:t,limitval:e})}removeVendor(t,e){return this.http.post("/api/isa/request/removeVendors",{vendorOid:t,documentID:e})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275inject"](n.c),a["\u0275\u0275inject"](p.a),a["\u0275\u0275inject"](l.a),a["\u0275\u0275inject"](u.a))},t.\u0275prov=a["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);