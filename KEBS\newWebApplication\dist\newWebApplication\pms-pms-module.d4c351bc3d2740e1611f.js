(window.webpackJsonp=window.webpackJsonp||[]).push([[850],{Kvnt:function(t,e,n){"use strict";n.r(e),n.d(e,"PmsModule",(function(){return _}));var o=n("ofXK"),r=n("tyNb"),i=n("xG9w"),s=n("fXoL"),a=n("flaP");let p=(()=>{class t{constructor(t){this.roleService=t}getAllRoleAccess(){let t=i.where(this.roleService.roles,{application_id:217});return this.roleList=t,t}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275inject"](a.a))},t.\u0275prov=s["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();function c(t,e){if(1&t){const t=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",10),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](t);const e=s["\u0275\u0275nextContext"]().$implicit;return s["\u0275\u0275nextContext"](2).openApp(e)})),s["\u0275\u0275elementStart"](1,"div",11),s["\u0275\u0275elementStart"](2,"div",12),s["\u0275\u0275element"](3,"img",13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](4,"div",14),s["\u0275\u0275elementStart"](5,"div",15),s["\u0275\u0275elementStart"](6,"span",16),s["\u0275\u0275text"](7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&t){const t=s["\u0275\u0275nextContext"]().$implicit;s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("src",t.img_url,s["\u0275\u0275sanitizeUrl"]),s["\u0275\u0275advance"](4),s["\u0275\u0275textInterpolate1"](" ",t.display_name,"")}}function l(t,e){if(1&t&&(s["\u0275\u0275elementContainerStart"](0),s["\u0275\u0275template"](1,c,8,2,"div",9),s["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit;s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",null==t?null:t.hasAccess)}}function m(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",5),s["\u0275\u0275elementStart"](1,"div",6),s["\u0275\u0275elementStart"](2,"div",7),s["\u0275\u0275template"](3,l,2,1,"ng-container",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngForOf",t.apps)}}let d=(()=>{class t{constructor(t,e){this._router=t,this._pmsService=e,this.totalApps=0,this.apps=[{display_name:"Appraisal",img_url:"assets/reports_images/activity_gov.png",path:"appraisal",objectId:323,hasAccess:!1},{display_name:"Rewards",img_url:"assets/reports_images/activity_gov.png",path:"awards",objectId:324,hasAccess:!1}]}ngOnInit(){this.applicationRoleAccessList=this._pmsService.getAllRoleAccess();for(let t of this.apps)t.hasAccess=i.where(this.applicationRoleAccessList,{object_id:t.objectId}).length>0,this.totalApps+=1==t.hasAccess?1:0}openApp(t){console.log(t),this._router.navigateByUrl("/main/pms/"+t.path)}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](r.g),s["\u0275\u0275directiveInject"](p))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["app-pms"]],decls:7,vars:2,consts:[[1,"container-fluid","reports-home-styles","pl-2","pr-0"],[1,"col-3","d-flex","pl-0","pb-2",2,"padding-top","15px"],[1,"my-auto","sub-heading","pl-2"],[1,"my-auto","heading","pl-2"],["class","row pt-0 pb-2",4,"ngIf"],[1,"row","pt-0","pb-2"],[1,"col-12","pl-2","pr-2"],[1,"tiles-wrapper"],[4,"ngFor","ngForOf"],["class","card tiles mr-3 mb-3",3,"click",4,"ngIf"],[1,"card","tiles","mr-3","mb-3",3,"click"],[1,"card-body","pt-2","pb-1","pl-1","pr-1"],[1,"row","pt-2","pb-1","d-flex","justify-content-center"],["height","55px","width","55px",3,"src"],[1,"layer"],[1,"row","d-flex","pt-2","pb-2"],[1,"mx-auto","tiles-title"]],template:function(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"span",2),s["\u0275\u0275text"](3,"Total Apps : "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"span",3),s["\u0275\u0275text"](5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](6,m,4,1,"div",4),s["\u0275\u0275elementEnd"]()),2&t&&(s["\u0275\u0275advance"](5),s["\u0275\u0275textInterpolate"](e.totalApps),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.apps.length>0))},directives:[o.NgIf,o.NgForOf],styles:[".reports-home-styles[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{color:#66615b;font-size:14px;font-weight:400}.reports-home-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.reports-home-styles[_ngcontent-%COMP%]   .arrow-icons[_ngcontent-%COMP%]{color:#868683;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{margin-top:3px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;margin-top:3px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px}.reports-home-styles[_ngcontent-%COMP%]   .tiles-wrapper[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]{width:140px;height:119px;transition:all .3s;overflow:hidden;animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .tiles-title[_ngcontent-%COMP%]{z-index:5;position:relative;font-size:14px;font-weight:300;color:#66615b;transition:all .1s}.reports-home-styles[_ngcontent-%COMP%]   .bookmark-button[_ngcontent-%COMP%]{visibility:hidden}.reports-home-styles[_ngcontent-%COMP%]   .bookmark-button[_ngcontent-%COMP%], .reports-home-styles[_ngcontent-%COMP%]   .bookmark-button-marked[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;position:absolute;right:0;top:-5px}.reports-home-styles[_ngcontent-%COMP%]   .tile-bookmark-icon[_ngcontent-%COMP%]{font-size:21px!important;color:#66615b!important}.reports-home-styles[_ngcontent-%COMP%]   .layer[_ngcontent-%COMP%]{height:100px;border-radius:50%;right:-5px;width:108%;bottom:-34px;position:absolute;background-color:rgba(207,0,1,.6705882352941176);visibility:hidden}.reports-home-styles[_ngcontent-%COMP%]   .layer[_ngcontent-%COMP%], .reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover{box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover{cursor:pointer}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .tiles-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#fff}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .bookmark-button[_ngcontent-%COMP%]{visibility:visible;animation:slide-in-top-bookmark-icon .2s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .layer[_ngcontent-%COMP%]{visibility:visible;animation:slide-in-bottom .3s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-in-top-bookmark-icon{0%{transform:translateY(-7px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-in-bottom{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})();var h=n("mrSG"),g=n("XXEo"),f=n("LcQX");let b=(()=>{class t{constructor(t,e,n,o){this._auth=t,this._pmsService=e,this._util=n,this.router=o,this.checkAccess=t=>new Promise((e,n)=>{try{let n=this._pmsService.getAllRoleAccess();e(i.where(n,{object_id:t}).length>0)}catch(o){n(o)}}),this.profile=this._auth.getProfile().profile}canActivate(t){return Object(h.c)(this,void 0,void 0,(function*(){let e=yield this.checkAccess(t.data.objectId);return 0==e&&(this._util.showMessage("You Dont Have Access! Contact KEBS Support.","dismiss",2e3),this.router.navigateByUrl("/main/pms")),e}))}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275inject"](g.a),s["\u0275\u0275inject"](p),s["\u0275\u0275inject"](f.a),s["\u0275\u0275inject"](r.g))},t.\u0275prov=s["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();const u=[{path:"",component:d},{path:"appraisal",canActivate:[b],loadChildren:()=>Promise.all([n.e(0),n.e(845)]).then(n.bind(null,"NVmD")).then(t=>t.PerformanceAppraisalModule),data:{breadcrumb:"Appraisal",objectId:323}},{path:"awards",canActivate:[b],loadChildren:()=>n.e(846).then(n.bind(null,"5BXY")).then(t=>t.PerformanceAwardsModule),data:{breadcrumb:"Rewards",objectId:324}}];let x=(()=>{class t{}return t.\u0275mod=s["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[r.k.forChild(u)],r.k]}),t})(),_=(()=>{class t{}return t.\u0275mod=s["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[o.CommonModule,x]]}),t})()}}]);