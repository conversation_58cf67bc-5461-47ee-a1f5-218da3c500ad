(window.webpackJsonp=window.webpackJsonp||[]).push([[956],{x5i8:function(e,t,n){"use strict";n.r(t),n.d(t,"ValidationDialogComponent",(function(){return O})),n.d(t,"ValidationDialogModule",(function(){return x}));var a=n("33Jv"),i=n("0IaG"),l=n("ofXK"),o=n("Xi0T"),s=n("NFeN"),r=n("bTqV"),d=n("Xa2L"),c=n("Qu3c"),p=n("STbY"),m=n("kmnG"),g=n("qFsG"),u=n("3Pt+"),f=n("QibW"),v=n("d3UM"),h=n("fXoL"),_=n("1A3m"),b=n("jAlA"),C=n("me71");function y(e,t){1&e&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",2),h["\u0275\u0275element"](2,"mat-spinner",3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]())}function P(e,t){if(1&e){const e=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",4),h["\u0275\u0275elementStart"](2,"div",5),h["\u0275\u0275elementStart"](3,"div",6),h["\u0275\u0275elementStart"](4,"div",7),h["\u0275\u0275element"](5,"app-user-image",8),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",9),h["\u0275\u0275elementStart"](7,"div",10),h["\u0275\u0275elementStart"](8,"div",11),h["\u0275\u0275elementStart"](9,"div",12),h["\u0275\u0275elementStart"](10,"span",13),h["\u0275\u0275text"](11),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](12,"div",6),h["\u0275\u0275elementStart"](13,"div",14),h["\u0275\u0275elementStart"](14,"div",15),h["\u0275\u0275elementStart"](15,"span",13),h["\u0275\u0275text"](16),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](17,"div",6),h["\u0275\u0275elementStart"](18,"div",14),h["\u0275\u0275elementStart"](19,"div",15),h["\u0275\u0275elementStart"](20,"span",13),h["\u0275\u0275text"](21),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](22,"div",4),h["\u0275\u0275elementStart"](23,"div",16),h["\u0275\u0275elementStart"](24,"div",6),h["\u0275\u0275elementStart"](25,"span",17),h["\u0275\u0275text"](26,"Profile Validation"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](27,"div",10),h["\u0275\u0275elementStart"](28,"span",18),h["\u0275\u0275text"](29,"Cross-check the details given in Employee Directory and confirm the data furnished in here is accurate & true."),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](30,"div",19),h["\u0275\u0275elementStart"](31,"div",20),h["\u0275\u0275elementStart"](32,"button",21),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](e),h["\u0275\u0275nextContext"]().cancel()})),h["\u0275\u0275text"](33," Cancel "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](34,"button",22),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](e),h["\u0275\u0275nextContext"]().submitDetails()})),h["\u0275\u0275text"](35," Submit "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](5),h["\u0275\u0275property"]("id",null==e.personalDetails?null:e.personalDetails.oid)("hasDefaultImg",!0)("defaultImg",null!=e.personalDetails&&e.personalDetails.default_profile_logo_url?null==e.personalDetails?null:e.personalDetails.default_profile_logo_url:"https://assets.kebs.app/images/kebs_favicon.png"),h["\u0275\u0275advance"](5),h["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.personalDetails&&e.personalDetails.employee_full_name?null==e.personalDetails?null:e.personalDetails.employee_full_name:"-"),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null!=e.personalDetails&&e.personalDetails.employee_full_name?null==e.personalDetails?null:e.personalDetails.employee_full_name:"-",""),h["\u0275\u0275advance"](4),h["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.personalDetails&&e.personalDetails.associate_id?null==e.personalDetails?null:e.personalDetails.associate_id:"-"),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" Emp ID : ",null!=e.personalDetails&&e.personalDetails.associate_id?null==e.personalDetails?null:e.personalDetails.associate_id:"-",""),h["\u0275\u0275advance"](4),h["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.personalDetails&&e.personalDetails.national_id?null==e.personalDetails?null:e.personalDetails.national_id:"-"),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"](" ",null!=e.personalDetails&&e.personalDetails.national_id_type?null==e.personalDetails?null:e.personalDetails.national_id_type:"-"," : ",null!=e.personalDetails&&e.personalDetails.national_id?null==e.personalDetails?null:e.personalDetails.national_id:"-"," "),h["\u0275\u0275advance"](11),h["\u0275\u0275property"]("disabled",e.loaderObject.isFormSubmitLoading),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("disabled",e.loaderObject.isFormSubmitLoading)}}let O=(()=>{class e{constructor(e,t,n,l){this.injector=e,this.fb=t,this._toaster=n,this._edService=l,this.dialogRef=null,this.subs=new a.a,this.profileValidationFormGroup=this.fb.group({isValidation:["",u.H.required],addRemarks:[""]}),this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.dialogRef=this.injector.get(i.h,null),this.dialogData=this.injector.get(i.a,null)}ngOnInit(){var e,t,n,a,i,l,o,s;this.associateId=(null===(t=null===(e=this.dialogData)||void 0===e?void 0:e.modalParams)||void 0===t?void 0:t.associateId)?null===(a=null===(n=this.dialogData)||void 0===n?void 0:n.modalParams)||void 0===a?void 0:a.associateId:"",this.personalDetails=(null===(l=null===(i=this.dialogData)||void 0===i?void 0:i.modalParams)||void 0===l?void 0:l.personalDetails)?null===(s=null===(o=this.dialogData)||void 0===o?void 0:o.modalParams)||void 0===s?void 0:s.personalDetails:{}}submitDetails(){this.loaderObject.isFormSubmitLoading=!0;let e={associate_id:this.associateId,is_validated:!0};return new Promise((t,n)=>{this.subs.sink=this._edService.validateProfile(e).subscribe(e=>{e.err||(this.closeDialog("success"),this._toaster.showSuccess("Success","Profile Validation Updated successfully!",2e3),this.loaderObject.isFormSubmitLoading=!1,t(e.data))},e=>{this.loaderObject.isFormSubmitLoading=!1,this._toaster.showError("Error","Failed to Update Profile Validation!",2e3),console.log(e),n(e)})})}cancel(){this.closeDialog("close")}closeDialog(e){this.dialogRef.close(e)}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(h["\u0275\u0275directiveInject"](h.Injector),h["\u0275\u0275directiveInject"](u.i),h["\u0275\u0275directiveInject"](_.a),h["\u0275\u0275directiveInject"](b.a))},e.\u0275cmp=h["\u0275\u0275defineComponent"]({type:e,selectors:[["app-validation-dialog"]],decls:3,vars:2,consts:[[1,"container-fluid","p-0","pr-1","mt-0","validation-dialog-styles"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","pt-3","slide-from-down"],[1,"col-12","px-2","py-2"],[1,"row"],[1,"col-2","p-0","justify-content-center","img-section"],["imgWidth","65px","imgHeight","65px",3,"id","hasDefaultImg","defaultImg"],[1,"col-10","p-0","pl-3"],[1,"row","pt-2"],[1,"col-12","emp-heading","p-0"],[1,"emp-name-heading"],[3,"matTooltip"],[1,"col-12","p-0"],[1,"item-value"],[1,"col-12","px-3","py-2"],[1,"title-header"],[1,"content"],[1,"row","mt-4","slide-from-down"],[1,"d-flex","pl-3","flex-row"],["type","button","mat-stroked-button","",1,"cancel-btn",3,"disabled","click"],["type","button","mat-raised-button","",1,"ml-3","create-employee-btn",3,"disabled","click"]],template:function(e,t){1&e&&(h["\u0275\u0275elementStart"](0,"div",0),h["\u0275\u0275template"](1,y,3,0,"ng-container",1),h["\u0275\u0275template"](2,P,36,12,"ng-container",1),h["\u0275\u0275elementEnd"]()),2&e&&(h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[l.NgIf,d.c,c.a,C.a,r.a],styles:[".validation-dialog-styles[_ngcontent-%COMP%]{overflow-x:auto;height:70vh;scrollbar-width:none}.validation-dialog-styles[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.validation-dialog-styles[_ngcontent-%COMP%]   .title-header[_ngcontent-%COMP%]{font-weight:700;font-size:14px;line-height:16px;color:#45546e}.validation-dialog-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:24px;color:#8b95a5}.validation-dialog-styles[_ngcontent-%COMP%]   .radio-content[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:24px;color:#5f6c81}.validation-dialog-styles[_ngcontent-%COMP%]   .mat-radio-outer-circle[_ngcontent-%COMP%], .validation-dialog-styles[_ngcontent-%COMP%]     .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:#f27a6c}.validation-dialog-styles[_ngcontent-%COMP%]     .mat-radio-button.mat-accent .mat-radio-inner-circle{background-color:#f27a6c}.validation-dialog-styles[_ngcontent-%COMP%]   .img-section[_ngcontent-%COMP%]{display:flex}.validation-dialog-styles[_ngcontent-%COMP%]   .circular[_ngcontent-%COMP%]{width:65px;height:65px;border-radius:50%;border:1px solid #000}.validation-dialog-styles[_ngcontent-%COMP%]   .emp-heading[_ngcontent-%COMP%]{display:flex;align-items:center}.validation-dialog-styles[_ngcontent-%COMP%]   .circular[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:65px;height:65px;border-radius:50%;transition:transform .5s ease;transform:scale(.8)}.validation-dialog-styles[_ngcontent-%COMP%]   .emp-name-heading[_ngcontent-%COMP%]{font-size:16px;font-weight:700;color:#45546e;line-height:24px}.validation-dialog-styles[_ngcontent-%COMP%]   .card1-title-heading[_ngcontent-%COMP%], .validation-dialog-styles[_ngcontent-%COMP%]   .emp-name-heading[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.validation-dialog-styles[_ngcontent-%COMP%]   .card1-title-heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#5f6c81}.validation-dialog-styles[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.validation-dialog-styles[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.validation-dialog-styles[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{width:100%;border:1px solid #fff;background-color:#fff;border-radius:5px}.validation-dialog-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%], .validation-dialog-styles[_ngcontent-%COMP%]   .title-heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#8b95a5;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.validation-dialog-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%]{line-height:16px}.validation-dialog-styles[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#cf0001}.validation-dialog-styles[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.validation-dialog-styles[_ngcontent-%COMP%]     .mat-form-field-outline{color:#b9c0ca!important}.validation-dialog-styles[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{color:#fff;line-height:24px;padding:2}.validation-dialog-styles[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-size:12px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.validation-dialog-styles[_ngcontent-%COMP%]   .section-heading[_ngcontent-%COMP%]{color:#ee4961;font-size:15px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.validation-dialog-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.validation-dialog-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),x=(()=>{class e{}return e.\u0275mod=h["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=h["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.CommonModule,o.a,s.b,r.b,d.b,c.b,p.e,m.e,g.c,u.E,u.p,f.c,v.d]]}),e})()}}]);