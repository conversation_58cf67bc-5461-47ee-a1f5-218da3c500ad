(window.webpackJsonp=window.webpackJsonp||[]).push([[894],{"3GNX":function(e,t,n){"use strict";n.r(t),n.d(t,"ProvisionalEntryModalComponent",(function(){return V})),n.d(t,"ProvisionalEntryModalModule",(function(){return R}));var r=n("mrSG"),i=n("0IaG"),o=n("bEYa"),a=n("33Jv"),l=n("Kj3r"),s=n("xG9w"),d=n("ofXK"),c=n("kmnG"),m=n("3Pt+"),p=n("NFeN"),v=n("bTqV"),u=n("Xi0T"),g=n("Xa2L"),h=n("Qu3c"),f=n("1jcm"),y=n("qFsG"),E=n("xHqg"),x=n("fXoL"),S=n("1A3m"),b=n("a1r6"),w=n("TmG/"),P=n("H44p");const _=["stepper"];function C(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"div",8),x["\u0275\u0275element"](1,"mat-spinner",9),x["\u0275\u0275elementEnd"]())}function I(e,t){1&e&&x["\u0275\u0275text"](0,"New Provisional")}function A(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",34),x["\u0275\u0275elementStart"](1,"div",35),x["\u0275\u0275elementStart"](2,"div",14),x["\u0275\u0275elementStart"](3,"div",36),x["\u0275\u0275text"](4),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",37),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",38),x["\u0275\u0275text"](8),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"div",37),x["\u0275\u0275element"](10,"app-currency",39),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](11,"div",40),x["\u0275\u0275elementStart"](12,"app-input-search",41),x["\u0275\u0275listener"]("change",(function(){x["\u0275\u0275restoreView"](e);const n=t.index;return x["\u0275\u0275nextContext"](3).changeTargetEntity(n)})),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](13,"div",40),x["\u0275\u0275element"](14,"app-input-search",23),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](15,"div",40),x["\u0275\u0275elementStart"](16,"mat-form-field",42),x["\u0275\u0275elementStart"](17,"mat-label"),x["\u0275\u0275text"](18,"Description"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](19,"input",43),x["\u0275\u0275listener"]("keydown.enter",(function(){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"](3).moveToNextStep()})),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](20,"div",37),x["\u0275\u0275text"](21),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,r=x["\u0275\u0275nextContext"](3);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("formGroupName",n),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate1"](" #",e.get("invoiceId").value," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" #PR",e.get("p2pHeaderId").value," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.get("title").value?e.get("title").value:"-"," "),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.get("amount").value),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("list",r.legalEntityMasterData),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("list",r.getGlAccountList(n)),x["\u0275\u0275advance"](7),x["\u0275\u0275textInterpolate1"](" ",e.get("cost_center_name").value," ")}}function L(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",14),x["\u0275\u0275elementStart"](2,"form",15),x["\u0275\u0275elementStart"](3,"div",16),x["\u0275\u0275elementStart"](4,"div",17),x["\u0275\u0275elementStart"](5,"div",18),x["\u0275\u0275elementStart"](6,"div",19),x["\u0275\u0275text"](7," Please choose the ledger "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](8,"div",20),x["\u0275\u0275text"](9," Note: This ledger will be auto populated for the below selected entries. "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",14),x["\u0275\u0275elementStart"](11,"div",21),x["\u0275\u0275element"](12,"app-input-search",22),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](13,"div",21),x["\u0275\u0275element"](14,"app-input-search",23),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](15,"div",21),x["\u0275\u0275element"](16,"app-input-search",24),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](17,"div",25),x["\u0275\u0275elementStart"](18,"button",26),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"](2).applyEntries()})),x["\u0275\u0275text"](19," Apply "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](20,"div",27),x["\u0275\u0275elementStart"](21,"div",28),x["\u0275\u0275elementStart"](22,"div",29),x["\u0275\u0275text"](23,"Invoice ID"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](24,"div",29),x["\u0275\u0275text"](25,"PR ID"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](26,"div",29),x["\u0275\u0275text"](27,"Title"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](28,"div",29),x["\u0275\u0275text"](29,"amount"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](30,"div",30),x["\u0275\u0275text"](31,"Target entity"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](32,"div",30),x["\u0275\u0275text"](33,"GL account"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](34,"div",30),x["\u0275\u0275text"](35,"Description"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](36,"div",30),x["\u0275\u0275text"](37,"Cost center"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](38,A,22,9,"div",31),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](39,"div",14),x["\u0275\u0275element"](40,"div",32),x["\u0275\u0275elementStart"](41,"div",25),x["\u0275\u0275elementStart"](42,"button",33),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"](),x["\u0275\u0275reference"](2).next()})),x["\u0275\u0275elementStart"](43,"mat-icon"),x["\u0275\u0275text"](44,"navigate_next"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("formGroup",e.newProvisionalEntryFormGroup),x["\u0275\u0275advance"](10),x["\u0275\u0275property"]("list",e.legalEntityMasterData),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("list",e.glAccountMasterData),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("list",e.targetLedgerMasterData),x["\u0275\u0275advance"](22),x["\u0275\u0275property"]("ngForOf",e.getNewProvisionalEntryArr.controls)}}function D(e,t){1&e&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",44),x["\u0275\u0275element"](2,"img",45),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](3,"div",46),x["\u0275\u0275elementStart"](4,"p",47),x["\u0275\u0275text"](5," No new provisional entries were selected ! "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"div",44),x["\u0275\u0275elementStart"](7,"p",48),x["\u0275\u0275text"](8," Navigate to vendor invoice tab and select the invoices to appear here. "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]())}function G(e,t){1&e&&x["\u0275\u0275text"](0,"Old Provisional")}function M(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",58),x["\u0275\u0275elementStart"](1,"div",59),x["\u0275\u0275elementStart"](2,"div",14),x["\u0275\u0275elementStart"](3,"div",36),x["\u0275\u0275text"](4),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",37),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",37),x["\u0275\u0275element"](8,"app-currency",39),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"div",60),x["\u0275\u0275text"](10),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](11,"div",60),x["\u0275\u0275text"](12),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](13,"div",61),x["\u0275\u0275text"](14),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](15,"div",60),x["\u0275\u0275text"](16),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("formGroupName",n),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate1"](" #",e.get("invoiceId").value," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" #PR",e.get("p2pHeaderId").value," "),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.get("amount").value),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.get("glAccount").value," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.get("targetLedger").value," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.get("targetEntity").value," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.get("tallyDescription").value?e.get("tallyDescription").value:"-"," ")}}function N(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"mat-icon",62),x["\u0275\u0275text"](1,"done_all"),x["\u0275\u0275elementEnd"]())}function O(e,t){1&e&&x["\u0275\u0275element"](0,"mat-spinner",63)}function F(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",49),x["\u0275\u0275elementStart"](2,"div",18),x["\u0275\u0275elementStart"](3,"div",19),x["\u0275\u0275text"](4," Old provisional payments "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",20),x["\u0275\u0275elementStart"](6,"p"),x["\u0275\u0275text"](7,"Note: On\xa0"),x["\u0275\u0275elementStart"](8,"b"),x["\u0275\u0275text"](9,"create provision action"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275text"](10,", a new provisional entry will be created from the last provisional entry date."),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](11,"div",14),x["\u0275\u0275elementStart"](12,"form",50),x["\u0275\u0275elementStart"](13,"div",51),x["\u0275\u0275elementStart"](14,"div",28),x["\u0275\u0275elementStart"](15,"div",29),x["\u0275\u0275text"](16,"Invoice ID"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](17,"div",29),x["\u0275\u0275text"](18,"PR ID"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](19,"div",29),x["\u0275\u0275text"](20,"Amount"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](21,"div",30),x["\u0275\u0275text"](22,"Target entity"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](23,"div",30),x["\u0275\u0275text"](24,"GL account"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](25,"div",52),x["\u0275\u0275text"](26,"Target ledger"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](27,"div",30),x["\u0275\u0275text"](28,"Description"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](29,M,17,9,"div",53),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](30,"div",54),x["\u0275\u0275element"](31,"div",32),x["\u0275\u0275elementStart"](32,"div",25),x["\u0275\u0275elementStart"](33,"button",55),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"](2).createProvisionalRequest()})),x["\u0275\u0275template"](34,N,2,0,"mat-icon",56),x["\u0275\u0275template"](35,O,1,0,"mat-spinner",57),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](12),x["\u0275\u0275property"]("formGroup",e.oldProvisionalEntryFormGroup),x["\u0275\u0275advance"](17),x["\u0275\u0275property"]("ngForOf",e.getOldProvisionalEntryArr.controls),x["\u0275\u0275advance"](4),x["\u0275\u0275property"]("ngClass",e.isLoading?"create-btn-loading":"create-btn"),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!e.isLoading),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",e.isLoading)}}function k(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"mat-icon",62),x["\u0275\u0275text"](1,"done_all"),x["\u0275\u0275elementEnd"]())}function T(e,t){1&e&&x["\u0275\u0275element"](0,"mat-spinner",63)}function j(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",44),x["\u0275\u0275element"](2,"img",45),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](3,"div",46),x["\u0275\u0275elementStart"](4,"p",47),x["\u0275\u0275text"](5," No old provisional entries were selected ! "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"div",54),x["\u0275\u0275element"](7,"div",32),x["\u0275\u0275elementStart"](8,"div",25),x["\u0275\u0275elementStart"](9,"button",55),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"](2).createProvisionalRequest()})),x["\u0275\u0275template"](10,k,2,0,"mat-icon",56),x["\u0275\u0275template"](11,T,1,0,"mat-spinner",57),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](9),x["\u0275\u0275property"]("ngClass",e.isLoading?"create-btn-loading":"create-btn"),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!e.isLoading),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",e.isLoading)}}function q(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",10),x["\u0275\u0275elementStart"](1,"mat-horizontal-stepper",null,11),x["\u0275\u0275elementStart"](3,"mat-step"),x["\u0275\u0275template"](4,I,1,0,"ng-template",12),x["\u0275\u0275template"](5,L,45,5,"ng-container",13),x["\u0275\u0275template"](6,D,9,0,"ng-container",13),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"mat-step"),x["\u0275\u0275template"](8,G,1,0,"ng-template",12),x["\u0275\u0275template"](9,F,36,5,"ng-container",13),x["\u0275\u0275template"](10,j,12,3,"ng-container",13),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf",e.provisionalEntryDetails.length>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",0==e.provisionalEntryDetails.length),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("ngIf",e.oldProvisionalEntries.length>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",0==e.oldProvisionalEntries.length)}}let V=(()=>{class e{constructor(e,t,n,r,i){this._toaster=e,this.dialogRef=t,this.inData=n,this.fb=r,this._p2pGeneralService=i,this.newProvisionalEntryFormGroup=this.fb.group({targetEntity:["",o.e.required],glAccount:["",o.e.required],targetLedger:["",o.e.required],newProvisionalEntryArr:this.fb.array([])}),this.oldProvisionalEntryFormGroup=this.fb.group({oldProvisionalEntryArr:this.fb.array([])}),this.subs=new a.a,this.provisionalEntryDetails=[],this.legalEntityMasterData=[],this.glAccountMasterData=[],this.targetLedgerMasterData=[],this.isLoading=!1,this.isModalLoading=!1,this.oldProvisionalEntries=[],n.modalParams&&(this.provisionalEntryDetails=n.modalParams.provisionalEntry)}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.isModalLoading=!0,this.oldProvisionalEntries=yield this.fetchProvisionalEntries(),this.removeOldProvisionalEntryInvoices(),this.legalEntityMasterData=yield this.getLegalEntity(),this.targetLedgerMasterData=yield this.getTallyLedgerMasterData(),this.constructOldProvisionalEntryArr(),this.isModalLoading=!1,this.targetEntityValueChangeListener()}))}get getNewProvisionalEntryArr(){return this.newProvisionalEntryFormGroup.get("newProvisionalEntryArr")}get getOldProvisionalEntryArr(){return this.oldProvisionalEntryFormGroup.get("oldProvisionalEntryArr")}getGlAccountList(e){return this.newProvisionalEntryFormGroup.get("newProvisionalEntryArr").controls[e].get("glAccountMasterData").value}getLegalEntity(){return new Promise((e,t)=>{this.subs.sink=this._p2pGeneralService.getLegalEntityMasterData().subscribe(t=>{"S"==t.messType&&e(t.data)},e=>{t(e)})})}getGlAccount(e){return new Promise((t,n)=>{this.subs.sink=this._p2pGeneralService.getPRTypeLedgerMaster(e).subscribe(e=>{"S"==e.messType&&t(e.data)},e=>{n(e)})})}getTallyLedgerMasterData(){return new Promise((e,t)=>{this.subs.sink=this._p2pGeneralService.getTallyLedgerMasterData().subscribe(n=>{n.err?t(!1):e(n.data)},e=>{t(e),console.log(e)})})}fetchProvisionalEntries(){return new Promise((e,t)=>{let n=s.pluck(this.provisionalEntryDetails,"id");this.subs.sink=this._p2pGeneralService.fetchProvisionalEntries(n).subscribe(n=>{n.err?t(n):e(n.data)},e=>{t(e)})})}targetEntityValueChangeListener(){this.newProvisionalEntryFormGroup.get("targetEntity").valueChanges.pipe(Object(l.a)(700)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&(this.glAccountMasterData=yield this.getGlAccount(e))})))}constructNewProvisionalEntryArr(){let e=this.newProvisionalEntryFormGroup.get("targetEntity").value,t=this.newProvisionalEntryFormGroup.get("glAccount").value,n=this.newProvisionalEntryFormGroup.get("targetLedger").value,r=this.newProvisionalEntryFormGroup.get("newProvisionalEntryArr");for(;r.length>0;)r.removeAt(0);console.log(this.provisionalEntryDetails),this.provisionalEntryDetails.forEach((i,a)=>{for(let l of i.ccSplitups)r.push(this.fb.group({invoiceId:i.id,p2pHeaderId:i.p2p_header_id,milestoneId:i.milestone_id,title:i.description,amount:[l.amount],invoiceDate:i.invoice_date,targetEntity:[e,o.e.required],cost_center_name:l.cost_center_name,glAccount:[t,o.e.required],glAccountMasterData:[this.glAccountMasterData],targetLedger:[n,o.e.required],tallyDescription:["",o.e.required]}))})}constructOldProvisionalEntryArr(){let e=this.oldProvisionalEntryFormGroup.get("oldProvisionalEntryArr");this.oldProvisionalEntries.forEach((t,n)=>{e.push(this.fb.group({invoiceId:t.invoice_id,p2pHeaderId:t.p2p_header_id,milestoneId:t.milestone_id,amount:[t.amount],lastProvisionalDate:t.provisional_on,glAccount:t.provisional_logs[0].ledger_name,targetLedger:t.provisional_logs[1].ledger_name,targetEntity:t.provisional_logs[0].tally_entity_name,tallyDescription:t.provisional_logs[0].narration,source_entity_id:t.provisional_logs[0].entity_id,invoiceDate:t.invoice_date}))})}applyEntries(){this.newProvisionalEntryFormGroup.get("targetEntity").valid&&this.newProvisionalEntryFormGroup.get("glAccount").valid&&this.newProvisionalEntryFormGroup.get("targetLedger").valid?this.constructNewProvisionalEntryArr():this._toaster.showError("Empty fields","Kindly choose the entries to proceed",2e3)}changeTargetEntity(e){return Object(r.c)(this,void 0,void 0,(function*(){let t=this.newProvisionalEntryFormGroup.get("newProvisionalEntryArr");t.controls[e].get("glAccount").patchValue("");let n=t.controls[e].get("targetEntity").value,r=yield this.getGlAccount(n);t.controls[e].get("glAccountMasterData").patchValue(r)}))}constructProvisionalPayload(){let e=[];return this.newProvisionalEntryFormGroup.get("newProvisionalEntryArr").value.forEach((t,n)=>{let r=s.findWhere(this.legalEntityMasterData,{id:t.targetEntity}),i=s.findWhere(t.glAccountMasterData,{id:t.glAccount});i=i.name;let o=s.findWhere(this.targetLedgerMasterData,{id:t.targetLedger});o=o.name,e.push({pr_id:t.p2pHeaderId,milestone_id:t.milestoneId,invoice_id:t.invoiceId,invoice_date:t.invoiceDate,amount:t.amount,source_entity_id:t.targetEntity,source_tally_entity_name:r.tally_entity_name?r.tally_entity_name:null,sub_group_name:i,description:t.tallyDescription,target_ledger:o,cost_center_name:t.cost_center_name})}),this.oldProvisionalEntryFormGroup.get("oldProvisionalEntryArr").value.forEach((t,n)=>{e.push({pr_id:t.p2pHeaderId,milestone_id:t.milestoneId,invoice_id:t.invoiceId,invoice_date:t.invoiceDate,amount:t.amount,source_entity_id:t.source_entity_id,source_tally_entity_name:t.targetEntity,sub_group_name:t.glAccount,description:t.tallyDescription,target_ledger:t.targetLedger})}),e}createProvisionalRequest(){if(this.newProvisionalEntryFormGroup.get("newProvisionalEntryArr").value.length>0){let e=this.constructProvisionalPayload();console.log(e);let t={tallyEntryArr:e};this.isLoading=!0,this.subs.sink=this._p2pGeneralService.createProvisionalEntry(t).subscribe(e=>{e.err?this._toaster.showError("Creation failed","Failed to create provisional entry !",3e3):(this._toaster.showSuccess("Provisional created","Expense entry updated successfully !",2e3),this.closeDialog(e)),this.isLoading=!1},e=>{this._toaster.showError("Creation failed","Failed to create provisional entry !",3e3),console.log(e),this.isLoading=!1})}else this._toaster.showError("No entries found !","Please choose the entries to proceed !",3e3)}removeOldProvisionalEntryInvoices(){let e=s.pluck(this.oldProvisionalEntries,"invoice_id");this.provisionalEntryDetails=s.filter(this.provisionalEntryDetails,t=>!s.contains(e,t.id))}closeDialog(e){this.dialogRef.close(e)}ngOnDestroy(){this.subs.unsubscribe()}moveToNextStep(){this.stepper.selectedIndex<this.stepper._steps.length-1&&this.stepper.next()}}return e.\u0275fac=function(t){return new(t||e)(x["\u0275\u0275directiveInject"](S.a),x["\u0275\u0275directiveInject"](i.h),x["\u0275\u0275directiveInject"](i.a),x["\u0275\u0275directiveInject"](m.i),x["\u0275\u0275directiveInject"](b.a))},e.\u0275cmp=x["\u0275\u0275defineComponent"]({type:e,selectors:[["provisional-entry-modal"]],viewQuery:function(e,t){if(1&e&&x["\u0275\u0275viewQuery"](_,!0),2&e){let e;x["\u0275\u0275queryRefresh"](e=x["\u0275\u0275loadQuery"]())&&(t.stepper=e.first)}},decls:14,vars:2,consts:[[1,"container-fluid","provisional-entry-modal"],[1,"d-flex","py-1",2,"font-size","16px","border-bottom","1px solid #80808073"],[1,"px-2","my-auto"],[1,"provisional-icon"],[1,"px-2","flex-grow-1","my-auto"],["mat-icon-button","",3,"click"],["class","d-flex justify-content-center mt-3",4,"ngIf"],["class","row mt-1",4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Loading...","diameter","30"],[1,"row","mt-1"],["stepper",""],["matStepLabel",""],[4,"ngIf"],[1,"row"],[2,"width","89vw",3,"formGroup"],[1,"px-4","py-1","edit-area"],[1,"row","my-2"],[1,"col-12"],[1,"row",2,"font-weight","500"],[1,"row",2,"color","#808080","font-size","13px"],[1,"col-3"],["placeholder","Target entity","formControlName","targetEntity",3,"list"],["placeholder","Gl Account","formControlName","glAccount",3,"list"],["placeholder","Target Ledger","formControlName","targetLedger",3,"list"],[1,"col-2"],["mat-raised-button","",1,"apply-btn",3,"click"],[1,"mt-4"],[1,"row","px-0"],[1,"col-1","field-title"],[1,"col-2","field-title"],["class","row my-2","style","border-bottom: 1px solid #8080802e","formArrayName","newProvisionalEntryArr",4,"ngFor","ngForOf"],[1,"col-10"],["mat-mini-fab","","matTooltip","Next",2,"background-color","#cf0001","color","white",3,"click"],["formArrayName","newProvisionalEntryArr",1,"row","my-2",2,"border-bottom","1px solid #8080802e"],[1,"col-12","px-0",2,"min-height","9vh",3,"formGroupName"],[1,"col-1","my-auto",2,"color","#4e4c4c"],[1,"col-1","my-auto",2,"color","#cf0001","font-weight","500"],[1,"col-1","my-auto",2,"color","#4e4c4c","font-weight","500"],["type","small",3,"showActualAmount","currencyList"],[1,"col-2","mt-2"],["placeholder","Target entity","formControlName","targetEntity",3,"list","change"],["appearance","outline",1,"create-account-field"],["matInput","","formControlName","tallyDescription",3,"keydown.enter"],[1,"d-flex","justify-content-center"],["src","https://assets.kebs.app/images/timeline.png","height","225","width","225",1,"mt-2","mb-2"],[1,"d-flex","justify-content-center","mt-2"],[1,"m-0",2,"font-weight","500","font-size","16px"],[2,"color","#808080","font-size","13px"],[1,"row","p-2",2,"background-color","#fafafa","border-radius","5px"],[3,"formGroup"],[1,"mt-4",2,"width","89vw"],[1,"col-3","field-title"],["class","row my-2","style","border-bottom: 1px solid #8080802e","formArrayName","oldProvisionalEntryArr",4,"ngFor","ngForOf"],[1,"row",2,"margin-top","3rem"],["mat-mini-fab","",1,"create-pr-btn",3,"ngClass","click"],["matTooltip","Create provision",4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","20",4,"ngIf"],["formArrayName","oldProvisionalEntryArr",1,"row","my-2",2,"border-bottom","1px solid #8080802e"],[1,"col-12","px-0",2,"min-height","5vh",3,"formGroupName"],[1,"col-2","my-auto"],[1,"col-3","my-auto"],["matTooltip","Create provision"],["matTooltip","Please wait...","diameter","20",1,"spinner-align"]],template:function(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275elementStart"](1,"div",1),x["\u0275\u0275elementStart"](2,"div",2),x["\u0275\u0275elementStart"](3,"mat-icon",3),x["\u0275\u0275text"](4,"add_card"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",4),x["\u0275\u0275elementStart"](6,"strong"),x["\u0275\u0275text"](7,"Provisional Entry"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](8,"div",2),x["\u0275\u0275elementStart"](9,"button",5),x["\u0275\u0275listener"]("click",(function(){return t.closeDialog(null)})),x["\u0275\u0275elementStart"](10,"mat-icon"),x["\u0275\u0275text"](11,"close"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](12,C,2,0,"div",6),x["\u0275\u0275template"](13,q,11,4,"div",7),x["\u0275\u0275elementEnd"]()),2&e&&(x["\u0275\u0275advance"](12),x["\u0275\u0275property"]("ngIf",t.isModalLoading),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!t.isModalLoading))},directives:[p.a,v.a,d.NgIf,g.c,h.a,E.a,E.b,E.c,m.J,m.w,m.n,w.a,m.v,m.l,d.NgForOf,m.h,m.o,P.a,c.c,c.g,y.b,m.e,d.NgClass],styles:[".provisional-entry-modal[_ngcontent-%COMP%]   .provisional-icon[_ngcontent-%COMP%]{color:#fff;background:#cf0001;border-radius:50%;width:35px;height:35px;padding-top:7px;padding-left:7px;font-size:20px;box-shadow:0 1px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.provisional-entry-modal[_ngcontent-%COMP%]   .edit-area[_ngcontent-%COMP%]{border-radius:5px;background-color:#fafafa}.provisional-entry-modal[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:11px;font-weight:500}.provisional-entry-modal[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%], .provisional-entry-modal[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.provisional-entry-modal[_ngcontent-%COMP%]   .create-btn-loading[_ngcontent-%COMP%]{background-color:#fff}"]}),e})(),R=(()=>{class e{}return e.\u0275mod=x["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=x["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[],imports:[[d.CommonModule,i.g,c.e,m.E,m.p,p.b,v.b,u.a,g.b,h.b,f.b,y.c,E.f]]}),e})()},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var r=n("fXoL"),i=n("3Pt+"),o=n("jtHE"),a=n("XNiG"),l=n("NJ67"),s=n("1G5W"),d=n("kmnG"),c=n("ofXK"),m=n("d3UM"),p=n("FKr1"),v=n("WJ5W"),u=n("Qu3c");function g(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-label"),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.placeholder)}}function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("value",null),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function f(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-option",8),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"]().emitChanges(n)})),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends l.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new i.j,this.fieldFilterCtrl=new i.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new r.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new o.a,this.change=new r.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new a.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.Renderer2))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:i.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-form-field",0),r["\u0275\u0275template"](1,g,2,1,"mat-label",1),r["\u0275\u0275elementStart"](2,"mat-select",2,3),r["\u0275\u0275elementStart"](4,"mat-option"),r["\u0275\u0275element"](5,"ngx-mat-select-search",4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](6,h,2,2,"mat-option",5),r["\u0275\u0275template"](7,f,2,3,"mat-option",6),r["\u0275\u0275pipe"](8,"async"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!t.hideMatLabel),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.hasNoneOption),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",r["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,c.NgIf,m.c,i.v,i.k,i.F,p.p,v.a,c.NgForOf,d.g,u.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()}}]);