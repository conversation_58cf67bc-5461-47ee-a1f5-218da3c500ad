.expense-landing-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #333;
    font-weight: 500;
  }
}

.action-buttons {
  display: flex;
  gap: 12px;

  button {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.summary-section {
  margin-bottom: 20px;
}

.summary-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .summary-content {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .summary-item {
    text-align: center;

    .label {
      display: block;
      font-size: 0.9em;
      opacity: 0.9;
      margin-bottom: 4px;
    }

    .value {
      display: block;
      font-size: 1.4em;
      font-weight: 600;
    }

    .total-amount {
      color: #ffeb3b;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;

  p {
    margin-top: 16px;
    color: #666;
    font-size: 1.1em;
  }
}

.table-container {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .empty-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }

  h3 {
    color: #666;
    margin-bottom: 8px;
  }

  p {
    color: #999;
    max-width: 400px;
  }
}

// Status badges
.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.85em;
  font-weight: 500;
  text-transform: uppercase;

  &.status-open {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #4caf50;
  }

  &.status-billing {
    background-color: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ff9800;
  }

  &.status-sow {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #2196f3;
  }

  &.clickable {
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
  }
}

// DevExtreme grid customizations
::ng-deep {
  .dx-datagrid {
    border: none;

    .dx-datagrid-headers {
      background-color: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
    }

    .dx-header-row {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
    }

    .dx-row-alt {
      background-color: #f8f9fa;
    }

    .dx-selection {
      background-color: #e3f2fd !important;
    }

    .dx-datagrid-rowsview .dx-row:hover {
      background-color: #f5f5f5;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .expense-landing-container {
    padding: 16px;
  }

  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    h2 {
      font-size: 1.5em;
    }
  }

  .summary-content {
    flex-direction: column;
    gap: 16px;
  }

  .action-buttons {
    width: 100%;

    button {
      flex: 1;
    }
  }
}