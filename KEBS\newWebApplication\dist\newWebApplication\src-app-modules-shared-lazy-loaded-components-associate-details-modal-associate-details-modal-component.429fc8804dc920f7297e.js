(window.webpackJsonp=window.webpackJsonp||[]).push([[982,987,990,991],{kkcx:function(e,t,n){"use strict";n.r(t),n.d(t,"AssociateDetailsModalComponent",(function(){return T})),n.d(t,"AssociateDetailModule",(function(){return R}));var i=n("mrSG"),r=n("0IaG"),a=n("1G5W"),o=n("XNiG"),s=n("wd/R"),l=n("ofXK"),c=n("bTqV"),d=n("NFeN"),p=n("Qu3c"),m=n("Xi0T"),h=n("Wp6s"),u=n("jaxi"),g=n("fXoL"),v=n("LcQX"),f=n("JLuW"),E=n("XXEo"),S=n("me71");function P(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"span",42),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](3,"br"),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](e.totalProjects)}}function x(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"span",42),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](3,"br"),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"]("0",e.totalProjects,"")}}function y(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"span",42),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"span",43),g["\u0275\u0275text"](4,"Years"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](5,"br"),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"]("",e.years,"\xa0")}}function D(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"span",42),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"span",43),g["\u0275\u0275text"](4,"Years"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](5,"br"),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"]("0",e.years,"\xa0")}}function b(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",46),g["\u0275\u0275elementStart"](1,"button",47),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("disableRipple",!0),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e," ")}}function w(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275template"](1,b,3,2,"div",45),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0!=e)}}function I(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",46),g["\u0275\u0275elementStart"](1,"button",48),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("disableRipple",!0),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e," ")}}function C(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275template"](1,I,3,2,"div",45),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0!=e)}}function j(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",19),g["\u0275\u0275template"](1,w,2,1,"div",44),g["\u0275\u0275template"](2,C,2,1,"div",44),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.primarySkill),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.secondarySkill)}}function O(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"h4",49),g["\u0275\u0275text"](1," No Skills Found ! "),g["\u0275\u0275elementEnd"]())}function _(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",50),g["\u0275\u0275elementStart"](1,"div",51),g["\u0275\u0275text"](2,"Name of Certification"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",41),g["\u0275\u0275text"](4,"Date"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",40),g["\u0275\u0275text"](6,"Organisation"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function M(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"h4",52),g["\u0275\u0275text"](2," No Certifications Found ! "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",53),g["\u0275\u0275element"](4,"img",54),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function k(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",19),g["\u0275\u0275elementStart"](1,"div",56),g["\u0275\u0275elementStart"](2,"div",57),g["\u0275\u0275elementStart"](3,"div",58),g["\u0275\u0275elementStart"](4,"div",59),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",60),g["\u0275\u0275text"](7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"div",61),g["\u0275\u0275text"](9),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](10,"div",62),g["\u0275\u0275text"](11),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",e.name,""),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.startDate.format("DD/MM/YYYY"),""),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.endDate.format("DD/MM/YYYY"),""),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.projectManager,"")}}function A(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275template"](1,k,12,4,"div",55),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.tempCurrent)}}function N(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",19),g["\u0275\u0275elementStart"](1,"div",56),g["\u0275\u0275elementStart"](2,"div",57),g["\u0275\u0275elementStart"](3,"div",58),g["\u0275\u0275elementStart"](4,"div",59),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",60),g["\u0275\u0275text"](7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"div",61),g["\u0275\u0275text"](9),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](10,"div",62),g["\u0275\u0275text"](11),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",e.name,""),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.startDate.format("DD/MM/YYYY"),""),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.endDate.format("DD/MM/YYYY"),""),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.projectManager,"")}}function L(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275template"](1,N,12,4,"div",55),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.tempPrevious)}}let T=(()=>{class e{constructor(e,t,n,r,a){this.utilityService=e,this.sharedLazyLoadedComponentsService=t,this.dialogRef=n,this.inData=r,this.loginService=a,this._onDestroy=new o.b,this.CurrentProject=[],this.PreviousProject=[],this.tempCurrent=[],this.tempPrevious=[],this.startDate=[],this.endDate=[],this.doj=new Date,this.projectName=[],this.totalProjects=0,this.years=0,this.approver="",this.approverOid="",this.phone=0,this.name="",this.email="",this.location="",this.primarySkill=[],this.secondarySkill=[],this.designation="",this.roleName="",this.org="",this.applicationId=0,this.certificates=[],this.text="CurrentProject",this.user={},this.currentDate=s(),this.initDetails=()=>Object(i.c)(this,void 0,void 0,(function*(){this.modalParams=this.inData.modalParams,this.applicationId=0,this.modalParams?(this.applicationId=this.modalParams.applicationId,this.user.oid=this.modalParams.objectId.resourceOid):this.user=this.loginService.getProfile().profile}))}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.initDetails(),yield this.getAssociateDetails(),yield this.getProjectDetails()}))}closeAssociateDetails(){this.dialogRef.close({event:"Close"})}getAssociateDetails(){return Object(i.c)(this,void 0,void 0,(function*(){this.sharedLazyLoadedComponentsService.getAssociateDetails(this.user.oid).pipe(Object(a.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==e.messType){this.userDetails=e.data,this.name=this.userDetails[0].display_name,this.email=this.userDetails[0].email,this.designation=this.userDetails[0].desgn,this.roleName=this.userDetails[0].role,this.org=this.userDetails[0].org_name,this.doj=this.userDetails[0].doj,this.primarySkill=this.userDetails[0].primary_skill.length>0?this.userDetails[0].primary_skill.split(","):this.userDetails[0].primary_skill,this.secondarySkill=this.userDetails[0].secondary_skill.length>0?this.userDetails[0].secondary_skill.split(","):this.userDetails[0].secondary_skill;let t=isNaN(this.currentDate.diff(this.doj,"year"));this.years=1==t?0:this.currentDate.diff(this.doj,"year"),this.phone=null==this.userDetails[0].phone?0:this.userDetails[0].phone}else this.utilityService.showMessage("Error in retrieving Associate Data !!","Dismiss",3e3)})),e=>{this.utilityService.showMessage("Error in Submitting AssociateDetails request","Dismiss",3e3)})}))}getProjectDetails(){return Object(i.c)(this,void 0,void 0,(function*(){this.sharedLazyLoadedComponentsService.getProjectDetails(this.user.oid).pipe(Object(a.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this.projectDetails=e.data,this.getApproverDetails()):this.utilityService.showMessage("Error in retrieving ProjectDetails !!","Dismiss",3e3)},e=>{this.utilityService.showMessage("Error in Submitting ProjectDetails request","Dismiss",3e3)})}))}getApproverDetails(){this.sharedLazyLoadedComponentsService.getApproverDetails(this.user.oid).pipe(Object(a.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.approverDetails=e.data,this.approver=this.approverDetails[0].approver,this.approverOid=this.approverDetails[0].oid,this.splitProjects()):this.utilityService.showMessage("Error in retrieving ApproverDetails !!","Dismiss",3e3)})),e=>{this.utilityService.showMessage("Error in Submitting ApproverDetails request","Dismiss",3e3)})}splitProjects(){var e,t=this.projectDetails.length;for(e=0;e<t;e++)this.startDate=this.projectDetails[e].startDate.split("T")[0],this.endDate=this.projectDetails[e].endDate.split("T")[0],this.projectName=this.projectDetails[e].objectName,this.location=this.projectDetails[e].locationName,s(this.endDate).isBefore(this.currentDate)||s(this.endDate).isSame(this.currentDate)?this.PreviousProject.push({name:this.projectName,startDate:s(this.startDate),endDate:s(this.endDate),projectManager:this.approver,location:this.location}):this.CurrentProject.push({name:this.projectName,startDate:s(this.startDate),endDate:s(this.endDate),projectManager:this.approver,location:this.location});this.totalProjects=this.PreviousProject.length,this.getLocation()}getLocation(){this.CurrentProject.length>1&&(this.CurrentProject=this.CurrentProject.sort((function(e,t){return t.endDate-e.endDate}))),this.PreviousProject.length>1&&(this.PreviousProject=this.PreviousProject.sort((function(e,t){return t.endDate-e.endDate}))),this.toggleClicked("CurrentProject"),this.location=this.CurrentProject.length>0?this.CurrentProject[0].location:this.PreviousProject.length>0?this.PreviousProject[0].location:"No Data"}toggleClicked(e){this.text=e,this.tempCurrent=this.CurrentProject,this.tempPrevious=this.PreviousProject,"CurrentProject"==this.text&&(this.tempPrevious=[]),"PreviousProject"==this.text&&(this.tempCurrent=[])}}return e.\u0275fac=function(t){return new(t||e)(g["\u0275\u0275directiveInject"](v.a),g["\u0275\u0275directiveInject"](f.a),g["\u0275\u0275directiveInject"](r.h),g["\u0275\u0275directiveInject"](r.a),g["\u0275\u0275directiveInject"](E.a))},e.\u0275cmp=g["\u0275\u0275defineComponent"]({type:e,selectors:[["app-associate-details-modal"]],decls:92,vars:21,consts:[[1,"container-fluid","associate-details-styles"],[1,"row","col-12","pl-3","pr-3","pt-2","pb-3",2,"border-bottom","solid 1px #cacaca"],[1,"mt-2","pt-1"],["imgWidth","110px","imgHeight","110px",3,"id"],[1,"col-3","mt-4","mr-0","mb-2","pl-4","pr-0"],[1,"title_name"],[1,"title","pt-2"],[1,"p-1","mr-3","mt-3","mb-3","card","card-inv"],[1,"pt-3","pl-2"],[1,"title_main",2,"font-size","15px","font-weight","550"],[1,"card-content"],[4,"ngIf"],[1,"col","mt-2","mb-3","pt-2","pl-3","pr-0",2,"background-color","#f5f5f5"],[1,"pl-1","pt-2",2,"font-size","12px"],[1,"col","pl-0","pt-2","pr-1"],["imgWidth","35px","imgHeight","35px",3,"id"],[2,"font-size","13px","font-weight","500","margin-left","2px","color","#000000"],["matTooltip","Close","mat-icon-button","",1,"ml-auto","close-button","ml-3",3,"click"],[1,"close-Icon"],[1,"row"],[1,"col-6","pl-0","pr-2",2,"border-right","solid 1px #cacaca"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-12","p-1","pt-3"],[1,"side-heading"],[1,"row","pb-1","pt-2"],[1,"pt-1","pl-0","side-subhead"],[1,"pt-1","pl-3","side-subhead-body"],[1,"pt-1","pl-4","ml-1","side-subhead-body"],[1,"row","mb-1","pt-2"],[1,"col-12","p-1"],["class","row",4,"ngIf","ngIfElse"],["noSkill",""],[1,"row",2,"border-top","solid 1px #cacaca"],["class","row pb-1 pl-3 pr-3 pt-1",4,"ngIf"],[1,"col-6","pl-2","pr-0","mt-2"],[3,"value","valueChange"],["group","matButtonToggleGroup"],["aria-label","Text align justify","value","CurrentProject",3,"click"],["aria-label","Text align justify","value","PreviousProject",3,"click"],[1,"row","pb-2","pl-2","pt-3"],[1,"col-4","smallSubtleText","p-0","ta-l"],[1,"col-2","smallSubtleText","p-0","ta-l"],[1,"title_main"],[1,"p-0","card-content"],[4,"ngFor","ngForOf"],["class","row p-2",4,"ngIf"],[1,"row","p-2"],["mat-raised-button","",1,"pb-0","mt-1","mb-1","raised-button",3,"disableRipple"],["mat-raised-button","",1,"pb-0","mt-1","mb-1","raised-button-new",3,"disableRipple"],[1,"p-1","mb-0"],[1,"row","pb-1","pl-3","pr-3","pt-1"],[1,"col-6","smallSubtleText","p-0","ta-l"],[1,"d-flex","justify-content-center","align-items-center","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","140","width","160"],["class","row",4,"ngFor","ngForOf"],[1,"col-12","p-0","card","listcard"],[1,"card-body","p-2"],[1,"row","card-details","p-0"],[1,"col-4","pl-1","pr-2","value13Bold","ta-l",2,"color","#cf0001 !important"],["content-type","template","max-width","300","placement","top",1,"col-2","p-0","value13Bold","ta-l"],["content-type","template","max-width","300","placement","top",1,"col-2","p-0","pl-1","value13Bold","ta-l"],["content-type","template","max-width","300","placement","top",1,"col-4","p-0","pl-2","value13Bold","ta-l"]],template:function(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",0),g["\u0275\u0275elementStart"](1,"div",1),g["\u0275\u0275elementStart"](2,"div",2),g["\u0275\u0275element"](3,"app-user-image",3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",4),g["\u0275\u0275elementStart"](5,"span",5),g["\u0275\u0275text"](6),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](7,"br"),g["\u0275\u0275elementStart"](8,"span",6),g["\u0275\u0275text"](9),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](10,"br"),g["\u0275\u0275elementStart"](11,"span",6),g["\u0275\u0275text"](12),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](13,"br"),g["\u0275\u0275elementStart"](14,"span",6),g["\u0275\u0275text"](15),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](16,"div",7),g["\u0275\u0275elementStart"](17,"div",8),g["\u0275\u0275elementStart"](18,"span",9),g["\u0275\u0275text"](19),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](20,"br"),g["\u0275\u0275elementStart"](21,"div",10),g["\u0275\u0275text"](22,"Location"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](23,"div",7),g["\u0275\u0275elementStart"](24,"div",8),g["\u0275\u0275template"](25,P,4,1,"div",11),g["\u0275\u0275template"](26,x,4,1,"div",11),g["\u0275\u0275elementStart"](27,"div",10),g["\u0275\u0275text"](28,"Projects Worked"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](29,"div",7),g["\u0275\u0275elementStart"](30,"div",8),g["\u0275\u0275template"](31,y,6,1,"div",11),g["\u0275\u0275template"](32,D,6,1,"div",11),g["\u0275\u0275elementStart"](33,"div",10),g["\u0275\u0275text"](34,"In Kaar"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](35,"div",12),g["\u0275\u0275elementStart"](36,"span",13),g["\u0275\u0275text"](37,"Reporting to"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](38,"div",14),g["\u0275\u0275element"](39,"app-user-image",15),g["\u0275\u0275elementStart"](40,"span",16),g["\u0275\u0275text"](41),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](42,"button",17),g["\u0275\u0275listener"]("click",(function(){return t.closeAssociateDetails()})),g["\u0275\u0275elementStart"](43,"mat-icon",18),g["\u0275\u0275text"](44,"close"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](45,"div",19),g["\u0275\u0275elementStart"](46,"div",20),g["\u0275\u0275elementStart"](47,"div",21),g["\u0275\u0275elementStart"](48,"div",22),g["\u0275\u0275elementStart"](49,"span",23),g["\u0275\u0275text"](50,"Contact Info"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](51,"div",24),g["\u0275\u0275elementStart"](52,"div",25),g["\u0275\u0275text"](53,"Phone"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](54,"div",26),g["\u0275\u0275text"](55),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](56,"div",24),g["\u0275\u0275elementStart"](57,"div",25),g["\u0275\u0275text"](58,"Mail"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](59,"div",27),g["\u0275\u0275text"](60),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](61,"div",28),g["\u0275\u0275elementStart"](62,"div",29),g["\u0275\u0275elementStart"](63,"span",23),g["\u0275\u0275text"](64,"Skills"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](65,j,3,2,"div",30),g["\u0275\u0275template"](66,O,2,0,"ng-template",null,31,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](68,"div",32),g["\u0275\u0275elementStart"](69,"div",29),g["\u0275\u0275elementStart"](70,"span",23),g["\u0275\u0275text"](71,"Certifications"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](72,_,7,0,"div",33),g["\u0275\u0275template"](73,M,5,0,"div",11),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](74,"div",34),g["\u0275\u0275elementStart"](75,"mat-button-toggle-group",35,36),g["\u0275\u0275listener"]("valueChange",(function(e){return t.text=e})),g["\u0275\u0275elementStart"](77,"mat-button-toggle",37),g["\u0275\u0275listener"]("click",(function(){return t.toggleClicked("CurrentProject")})),g["\u0275\u0275text"](78," Current Projects "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](79,"mat-button-toggle",38),g["\u0275\u0275listener"]("click",(function(){return t.toggleClicked("PreviousProject")})),g["\u0275\u0275text"](80," Previous Projects "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](81,"div",39),g["\u0275\u0275elementStart"](82,"div",40),g["\u0275\u0275text"](83,"Project Name"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](84,"div",41),g["\u0275\u0275text"](85,"Start Date"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](86,"div",41),g["\u0275\u0275text"](87,"End Date"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](88,"div",40),g["\u0275\u0275text"](89,"Project Manager"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](90,A,2,1,"div",11),g["\u0275\u0275template"](91,L,2,1,"div",11),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275reference"](67);g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("id",t.user.oid),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate"](t.name),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate"](t.roleName),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate"](t.designation),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate"](t.org),g["\u0275\u0275advance"](4),g["\u0275\u0275textInterpolate"](t.location),g["\u0275\u0275advance"](6),g["\u0275\u0275property"]("ngIf",t.totalProjects>=10),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",10>t.totalProjects),g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("ngIf",t.years>=10),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",10>t.years),g["\u0275\u0275advance"](7),g["\u0275\u0275property"]("id",t.approverOid),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",t.approver," "),g["\u0275\u0275advance"](14),g["\u0275\u0275textInterpolate"](t.phone),g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate"](t.email),g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("ngIf",0!=t.primarySkill.length&&0!=t.secondarySkill.length||0==t.primarySkill.length&&0!=t.secondarySkill.length||0!=t.primarySkill.length&&0==t.secondarySkill.length)("ngIfElse",e),g["\u0275\u0275advance"](7),g["\u0275\u0275property"]("ngIf",0!=t.certificates.length),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==t.certificates.length),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("value",t.text),g["\u0275\u0275advance"](15),g["\u0275\u0275property"]("ngIf",t.tempCurrent.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.tempPrevious.length>0)}},directives:[S.a,l.NgIf,c.a,p.a,d.a,u.b,u.a,l.NgForOf],styles:[".associate-details-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.associate-details-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.associate-details-styles[_ngcontent-%COMP%]   .card-inv[_ngcontent-%COMP%]{width:8rem!important;border-radius:1rem}.associate-details-styles[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{padding-top:10px;font-size:13px;color:#545352;font-weight:500}.associate-details-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#000;font-size:12px}.associate-details-styles[_ngcontent-%COMP%]   .title_main[_ngcontent-%COMP%]{font-weight:500;font-size:24px;color:#cf0001}.associate-details-styles[_ngcontent-%COMP%]   .title_name[_ngcontent-%COMP%]{font-weight:900;font-size:18px;color:#cf0001}.associate-details-styles[_ngcontent-%COMP%]   .raised-button[_ngcontent-%COMP%]{width:6.75rem;height:2rem;background-color:#cf0001;color:#fff;text-align:center;line-height:normal;box-shadow:none}.associate-details-styles[_ngcontent-%COMP%]   .raised-button-new[_ngcontent-%COMP%]{min-width:6.75rem;height:2rem;color:#cf0001;border:1px solid #cf0001;line-height:normal;box-shadow:none}.associate-details-styles[_ngcontent-%COMP%]   .side-heading[_ngcontent-%COMP%]{color:rgba(207,0,0,.984313725490196);font-weight:400;font-size:13px}.associate-details-styles[_ngcontent-%COMP%]   .mat-button-toggle-checked[_ngcontent-%COMP%]{border-radius:2px!important;background-color:#cf0001!important;color:#fff}.associate-details-styles[_ngcontent-%COMP%]   .mat-button-toggle-group[_ngcontent-%COMP%]{margin-top:2px}.associate-details-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.associate-details-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:15px}.associate-details-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.associate-details-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#000;font-size:13px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.associate-details-styles[_ngcontent-%COMP%]   .p-2[_ngcontent-%COMP%]{padding:.25rem!important}.associate-details-styles[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.associate-details-styles[_ngcontent-%COMP%]   .side-subhead[_ngcontent-%COMP%]{font-weight:400;color:#545352;font-size:14px}.associate-details-styles[_ngcontent-%COMP%]   .side-subhead-body[_ngcontent-%COMP%]{font-weight:500;color:#000;font-size:14px}"]}),e})(),R=(()=>{class e{}return e.\u0275mod=g["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.CommonModule,p.b,c.b,d.b,m.a,h.d,u.c]]}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("mrSG"),r=n("xG9w"),a=n("fXoL"),o=n("tk/3"),s=n("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],a=r.keys(t["cc"+n]);for(let r=0;r<a.length;r++)for(let o=0;o<t["cc"+n][a[r]].length;o++){let s={name:t["cc"+n][a[r]][o].DELEGATE_NAME,oid:t["cc"+n][a[r]][o].DELEGATE_OID,level:r+1,designation:t["cc"+n][a[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][a[r]][o].IS_DELEGATED,role:t["cc"+n][a[r]][o].DELEGATE_ROLE_NAME};if(1==t["cc"+n][a[r]][o].IS_DELEGATED&&(s.delegated_by={name:t["cc"+n][a[r]][o].APPROVER_NAME,oid:t["cc"+n][a[r]][o].APPROVER_OID,level:r+1,designation:t["cc"+n][a[r]][o].APPROVER_DESIGNATION_NAME}),i.push(s),n==e.length-1&&r==a.length-1&&o==t["cc"+n][a[r]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=r.keys(e["cc"+t]);for(let r=0;r<i.length;r++)for(let a=0;a<e["cc"+t][i[r]].length;a++){let o={name:e["cc"+t][i[r]][a].DELEGATE_NAME,oid:e["cc"+t][i[r]][a].DELEGATE_OID,level:e["cc"+t][i[r]][a].APPROVAL_ORDER,designation:e["cc"+t][i[r]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[r]][a].IS_DELEGATED};if(1==e["cc"+t][i[r]][a].IS_DELEGATED&&(o.delegated_by={name:e["cc"+t][i[r]][a].APPROVER_NAME,oid:e["cc"+t][i[r]][a].APPROVER_OID,level:e["cc"+t][i[r]][a].APPROVAL_ORDER,designation:e["cc"+t][i[r]][a].APPROVER_DESIGNATION_NAME}),n.push(o),r==i.length-1&&a==e["cc"+t][i[r]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](o.c),a["\u0275\u0275inject"](s.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);