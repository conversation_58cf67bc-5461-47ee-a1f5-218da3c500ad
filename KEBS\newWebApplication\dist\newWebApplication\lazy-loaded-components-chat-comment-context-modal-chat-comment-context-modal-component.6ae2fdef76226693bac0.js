(window.webpackJsonp=window.webpackJsonp||[]).push([[745],{"UN/B":function(t,e,n){"use strict";n.r(e),n.d(e,"ChatCommentContextModalComponent",(function(){return J})),n.d(e,"ChatCommentContextModalModule",(function(){return Q}));var o=n("mrSG"),i=n("wd/R"),a=n("xG9w"),l=n("0IaG"),s=n("PSD3"),c=n.n(s),m=n("kmnG"),r=n("qFsG"),d=n("NFeN"),p=n("bTqV"),h=n("Qu3c"),u=n("3Pt+"),x=n("iadO"),g=n("Xi0T"),f=n("lVl8"),C=n("Xa2L"),b=n("7pIB"),v=n("gvOY"),y=n("d3UM"),_=n("9mMp"),P=n("pA3K"),O=n("dNgK"),E=n("JqCM"),S=n("8hBH"),M=n("5+WD"),w=n("ofXK"),I=n("STbY"),k=n("z3KA"),D=n("fXoL"),L=n("XXEo"),F=n("LcQX"),B=n("JLuW"),N=n("me71");const T=["scrollFrame"];function z(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",37),D["\u0275\u0275elementStart"](1,"div",33),D["\u0275\u0275elementStart"](2,"span",38),D["\u0275\u0275text"](3),D["\u0275\u0275elementStart"](4,"span",39),D["\u0275\u0275text"](5,":"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"div",40),D["\u0275\u0275elementStart"](7,"span",38),D["\u0275\u0275text"](8),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=D["\u0275\u0275nextContext"](3).$implicit;D["\u0275\u0275advance"](3),D["\u0275\u0275textInterpolate1"]("",t," "),D["\u0275\u0275advance"](5),D["\u0275\u0275textInterpolate1"](" ",n.context[t],"")}}function j(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",30),D["\u0275\u0275elementStart"](1,"div",31),D["\u0275\u0275elementStart"](2,"div",32),D["\u0275\u0275elementStart"](3,"div",33),D["\u0275\u0275elementStart"](4,"span",34),D["\u0275\u0275text"](5," Context "),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"div",35),D["\u0275\u0275template"](7,z,9,2,"div",36),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){const t=D["\u0275\u0275nextContext"](2).$implicit,e=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](7),D["\u0275\u0275property"]("ngForOf",e.getContextKeys(t.context))}}function A(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"button",41),D["\u0275\u0275elementStart"](1,"mat-icon",42),D["\u0275\u0275text"](2,"more_vert"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){D["\u0275\u0275nextContext"]();const t=D["\u0275\u0275reference"](9);D["\u0275\u0275property"]("matMenuTriggerFor",t)}}function V(t,e){if(1&t){const t=D["\u0275\u0275getCurrentView"]();D["\u0275\u0275elementStart"](0,"div",19),D["\u0275\u0275elementStart"](1,"div",20),D["\u0275\u0275template"](2,j,8,1,"div",21),D["\u0275\u0275elementStart"](3,"div",22),D["\u0275\u0275elementStart"](4,"div",23),D["\u0275\u0275elementStart"](5,"span",24),D["\u0275\u0275text"](6),D["\u0275\u0275elementEnd"](),D["\u0275\u0275template"](7,A,3,1,"button",25),D["\u0275\u0275elementStart"](8,"mat-menu",null,26),D["\u0275\u0275elementStart"](10,"button",27),D["\u0275\u0275listener"]("click",(function(){D["\u0275\u0275restoreView"](t);const e=D["\u0275\u0275nextContext"]().index;return D["\u0275\u0275nextContext"]().deleteComment(e)})),D["\u0275\u0275text"](11," Delete Comment "),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](12,"span",28),D["\u0275\u0275text"](13),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](14,"app-user-image",29),D["\u0275\u0275elementEnd"]()}if(2&t){const t=D["\u0275\u0275nextContext"]().$implicit,e=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("ngIf",t.context&&e.getContextKeys(t.context).length>0),D["\u0275\u0275advance"](4),D["\u0275\u0275textInterpolate"](e.getDateFormat(t.time)),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("ngIf",!e.isReadOnly),D["\u0275\u0275advance"](6),D["\u0275\u0275textInterpolate"](t.comment),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("id",t.commentor_oid)}}function q(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",50),D["\u0275\u0275elementStart"](1,"div",33),D["\u0275\u0275elementStart"](2,"span",38),D["\u0275\u0275text"](3),D["\u0275\u0275elementStart"](4,"span",39),D["\u0275\u0275text"](5,":"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"div",40),D["\u0275\u0275elementStart"](7,"span",38),D["\u0275\u0275text"](8),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=D["\u0275\u0275nextContext"](3).$implicit;D["\u0275\u0275advance"](3),D["\u0275\u0275textInterpolate1"]("",t," "),D["\u0275\u0275advance"](5),D["\u0275\u0275textInterpolate"](n.context[t])}}function K(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",30),D["\u0275\u0275elementStart"](1,"div",31),D["\u0275\u0275elementStart"](2,"div",32),D["\u0275\u0275elementStart"](3,"div",33),D["\u0275\u0275elementStart"](4,"span",34),D["\u0275\u0275text"](5," Context - "),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"div",35),D["\u0275\u0275template"](7,q,9,2,"div",49),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){const t=D["\u0275\u0275nextContext"](2).$implicit,e=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](7),D["\u0275\u0275property"]("ngForOf",e.getContextKeys(t.context))}}function R(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",43),D["\u0275\u0275element"](1,"app-user-image",44),D["\u0275\u0275elementStart"](2,"div",45),D["\u0275\u0275template"](3,K,8,1,"div",21),D["\u0275\u0275elementStart"](4,"div",22),D["\u0275\u0275elementStart"](5,"span",46),D["\u0275\u0275text"](6),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](7,"span",47),D["\u0275\u0275text"](8),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](9,"div",48),D["\u0275\u0275text"](10),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){const t=D["\u0275\u0275nextContext"]().$implicit,e=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("id",t.commentor_oid),D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("ngIf",t.context&&e.getContextKeys(t.context).length>0),D["\u0275\u0275advance"](3),D["\u0275\u0275textInterpolate1"](" ",t.commentor_name," "),D["\u0275\u0275advance"](2),D["\u0275\u0275textInterpolate1"](" ",e.getDateFormat(t.time),""),D["\u0275\u0275advance"](2),D["\u0275\u0275textInterpolate1"](" ",t.comment," ")}}function H(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",16),D["\u0275\u0275template"](1,V,15,5,"div",17),D["\u0275\u0275template"](2,R,11,5,"div",18),D["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("ngIf",t.commentor_oid==n.currentUser.oid&&n.showOwnComments),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("ngIf",t.commentor_oid!=n.currentUser.oid&&n.showOthersComments)}}function U(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",50),D["\u0275\u0275elementStart"](1,"div",33),D["\u0275\u0275elementStart"](2,"span",38),D["\u0275\u0275text"](3),D["\u0275\u0275elementStart"](4,"span",39),D["\u0275\u0275text"](5,":"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"div",40),D["\u0275\u0275elementStart"](7,"span",52),D["\u0275\u0275text"](8),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=D["\u0275\u0275nextContext"](2);D["\u0275\u0275advance"](3),D["\u0275\u0275textInterpolate1"]("",t," "),D["\u0275\u0275advance"](4),D["\u0275\u0275property"]("matTooltip",n.modalParams.context[t]),D["\u0275\u0275advance"](1),D["\u0275\u0275textInterpolate"](n.modalParams.context[t])}}function $(t,e){if(1&t&&(D["\u0275\u0275elementStart"](0,"div",51),D["\u0275\u0275elementStart"](1,"div",31),D["\u0275\u0275elementStart"](2,"div",32),D["\u0275\u0275elementStart"](3,"div",33),D["\u0275\u0275elementStart"](4,"span",34),D["\u0275\u0275text"](5," Context - "),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"div",35),D["\u0275\u0275template"](7,U,9,3,"div",49),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&t){const t=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](7),D["\u0275\u0275property"]("ngForOf",t.contextKeys)}}const G=function(t,e){return{"trend-button-active":t,"trend-button-inactive":e}};function X(t,e){if(1&t){const t=D["\u0275\u0275getCurrentView"]();D["\u0275\u0275elementStart"](0,"div",53),D["\u0275\u0275elementStart"](1,"div",54),D["\u0275\u0275element"](2,"app-user-image",55),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](3,"div",56),D["\u0275\u0275elementStart"](4,"mat-form-field",57),D["\u0275\u0275elementStart"](5,"input",58,59),D["\u0275\u0275listener"]("keydown.enter",(function(){D["\u0275\u0275restoreView"](t);const e=D["\u0275\u0275reference"](6),n=D["\u0275\u0275nextContext"]();return n.enterComment(e.value,n.comments.length+1),e.value=""}))("itemSelected",(function(e){return D["\u0275\u0275restoreView"](t),D["\u0275\u0275nextContext"]().selectMention(e)})),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](7,"button",60),D["\u0275\u0275listener"]("click",(function(){return D["\u0275\u0275restoreView"](t),D["\u0275\u0275nextContext"]().toggleContext()})),D["\u0275\u0275elementStart"](8,"mat-icon",4),D["\u0275\u0275text"](9,"list"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](10,"div",61),D["\u0275\u0275elementStart"](11,"button",62),D["\u0275\u0275listener"]("click",(function(e){D["\u0275\u0275restoreView"](t);const n=D["\u0275\u0275nextContext"]();return n.enterComment(e.target.value,n.comments.length+1)})),D["\u0275\u0275elementStart"](12,"mat-icon",63),D["\u0275\u0275text"](13,"send"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()}if(2&t){const t=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("id",t.currentUser.oid),D["\u0275\u0275advance"](3),D["\u0275\u0275property"]("mentionConfig",t.mentionConfig),D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("ngClass",D["\u0275\u0275pureFunction2"](3,G,t.isContextEnabled,!t.isContextEnabled))}}const Y=function(t){return{height:t}},W=function(t){return{"min-height":t}};let J=(()=>{class t{constructor(t,e,n,i,a){this.authhelper=t,this.utilityService=e,this.sharedLazyLoadedComponentsService=n,this.dialogRef=i,this.inData=a,this.comments=[],this.contextKeys=[],this.isContextEnabled=!0,this.TABLE_NAME="",this.COMMENT_ID="",this.selectedMentions=[],this.title="",this.APPLICATION_NAME="",this.is_comments_mail_notif=0,this.isReadOnly=!1,this.showOwnComments=!0,this.showOthersComments=!0,this.hasEnteredComment=!1,this.deleteComment=t=>Object(o.c)(this,void 0,void 0,(function*(){this.isReadOnly||(console.log(t),(yield this.confirmSweetAlert("Confirm Deletion","Do you want to delete this comment?")).value&&(this.comments.splice(t,1),yield this.sharedLazyLoadedComponentsService.deleteComment(this.COMMENT_ID,this.TABLE_NAME,t),this.utilityService.showMessage("The comment has been successfully deleted","Dismiss")))}))}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.initDetails()}))}ngOnChanges(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.initDetails()}))}initDetails(){var t,e,n,i,l;return Object(o.c)(this,void 0,void 0,(function*(){this.modalParams=this.inData.modalParams,console.log(this.modalParams),this.isReadOnly=(null===(t=this.modalParams)||void 0===t?void 0:t.isReadOnly)||!1,this.showOwnComments=void 0===(null===(e=this.modalParams)||void 0===e?void 0:e.showOwnComments)||(null===(n=this.modalParams)||void 0===n?void 0:n.showOwnComments),this.showOthersComments=void 0===(null===(i=this.modalParams)||void 0===i?void 0:i.showOthersComments)||(null===(l=this.modalParams)||void 0===l?void 0:l.showOthersComments),this.currentUser=this.authhelper.getProfile().profile,this.userSuggestions=yield this.sharedLazyLoadedComponentsService.getEmployeesForSearch(),this.mentionConfig={mentions:[{items:this.userSuggestions,triggerChar:"@",maxItems:10,labelKey:"displayName",dropUp:!0,allowSpace:!0}]},setTimeout(()=>{this.scrollToBottom()},2e3),this.modalParams.context&&(this.contextKeys=a.keys(this.modalParams.context)),yield this.initCommentData()}))}ngAfterViewInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.scrollContainer=this.scrollFrame.nativeElement}))}initCommentData(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.modalParams.inputData&&this.modalParams.inputData.application_id&&this.modalParams.inputData.unique_id_1){this.title=this.modalParams.inputData.title?this.modalParams.inputData.title:"KEBS",this.APPLICATION_NAME=this.modalParams.inputData.application_name?this.modalParams.inputData.application_name:"application id : "+this.modalParams.inputData.application_id;let t=yield this.sharedLazyLoadedComponentsService.getComments(this.modalParams.inputData);return"S"==t.messType?(this.COMMENT_ID=t.result.id,this.comments="string"==typeof t.result.comments?JSON.parse(t.result.comments):t.result.comments,console.log("this.comments"),console.log(this.comments),this.TABLE_NAME=t.result.table_name,void(this.is_comments_mail_notif=t.result.is_comments_mail_notif)):(console.log(t),void this.utilityService.showMessage("Error while retreiving Comments. Please contact KEBS Team","Dismiss"))}this.utilityService.showMessage("Error while getting application id and input data. Please contact KEBS Team","Dismiss")}))}getDateFormat(t){return i(t).format("DD-MMM-YYYY")}enterComment(t,e){return Object(o.c)(this,void 0,void 0,(function*(){if(" "!=document.getElementById("comment").value&&""!=document.getElementById("comment").value&&!this.isReadOnly){let t=this.authhelper.getProfile().profile;console.log("user"),console.log(t),this.comments.push({context:this.isContextEnabled?this.modalParams.context:{},sequence_number:e,time:i(new Date).format("LLL"),commentor_oid:t.oid,commentor_name:t.name,comment:document.getElementById("comment").value}),console.log("this.comments2"),console.log(this.comments);let n={context:this.isContextEnabled?this.modalParams.context:{},sequence_number:e,time:new Date,commentor_oid:t.oid,commentor_name:t.name,comment:document.getElementById("comment").value,url:this.modalParams.url?this.modalParams.url:""};document.getElementById("comment").value="";let o={application_id:this.modalParams.inputData.application_id,unique_id_1:this.modalParams.inputData.unique_id_1,unique_id_2:this.modalParams.inputData.unique_id_2?this.modalParams.inputData.unique_id_2:"",table_name:this.TABLE_NAME,comment:n};if(yield this.sharedLazyLoadedComponentsService.writeComment(o),console.log(this.selectedMentions),n.comment="<b><i>"+n.comment+"</i></b>"+(""!=this.modalParams.unique_id?this.modalParams.unique_id:""),this.selectedMentions.length>0){let t=yield this.mentionSavePreFormatter(n);console.log(t);for(let e=0;e<t.length;e++){console.log(t[e]);let n=yield this.sharedLazyLoadedComponentsService.notifyIndividual(t[e]);console.log(n),e==t.length-1&&(this.selectedMentions=[])}}this.isContextEnabled=!1,this.scrollToBottom(),this.hasEnteredComment=!0}}))}onKeydown(t){}getCommentBoxHeight(){return this.modalParams.commentBoxHeight?this.modalParams.commentBoxHeight:"70vh"}getCommentScrollHeight(){return this.modalParams.commentBoxScrollHeight?this.modalParams.commentBoxScrollHeight:"93%"}toggleContext(){this.isContextEnabled=!this.isContextEnabled}getContextKeys(t){return a.keys(t)}selectMention(t){this.selectedMentions.push(t)}mentionSavePreFormatter(t){return Object(o.c)(this,void 0,void 0,(function*(){let e={application_id:this.modalParams.inputData.application_id,unique_id_1:this.modalParams.inputData.unique_id_1,unique_id_2:this.modalParams.inputData.unique_id_2?this.modalParams.inputData.unique_id_2:"",table_name:this.TABLE_NAME};return this.selectedMentions=a.reject(this.selectedMentions,n=>(n.message=this.currentUser.name+" has commented "+t.comment+" in "+this.title+" under "+this.APPLICATION_NAME,n.context=t,n.link=t.url?t.url:"",n.insert_meta_data=e,n.is_comments_mail_notif=this.is_comments_mail_notif,t.comment.split("@"+n.displayName).length<2)),this.selectedMentions}))}scrollToBottom(){this.scrollContainer.scroll({top:this.scrollContainer.scrollHeight,left:0,behavior:"smooth"})}closeComments(){this.dialogRef.close({event:"Close",hasEnteredComment:this.hasEnteredComment})}confirmSweetAlert(t,e){return c.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class",cancelButton:"confirm-button-class"},title:t,text:e,icon:"warning",showConfirmButton:!0,showCancelButton:!0})}ngOnDestroy(){this.modalParams.context={},this.contextKeys=[]}}return t.\u0275fac=function(e){return new(e||t)(D["\u0275\u0275directiveInject"](L.a),D["\u0275\u0275directiveInject"](F.a),D["\u0275\u0275directiveInject"](B.a),D["\u0275\u0275directiveInject"](l.h),D["\u0275\u0275directiveInject"](l.a))},t.\u0275cmp=D["\u0275\u0275defineComponent"]({type:t,selectors:[["app-chat-comment-context-modal"]],viewQuery:function(t,e){if(1&t&&D["\u0275\u0275viewQuery"](T,!0),2&t){let t;D["\u0275\u0275queryRefresh"](t=D["\u0275\u0275loadQuery"]())&&(e.scrollFrame=t.first)}},features:[D["\u0275\u0275NgOnChangesFeature"]],decls:20,vars:10,consts:[[1,"container-fluid","chat-context-styles","d-flex","flex-column","p-0",3,"ngStyle"],[1,"row","pt-0",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-1","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1","ml-1"],[1,"iconButton"],[1,"name","my-auto","ml-3"],[1,"normal-font","my-auto","ml-2"],[1,"col-1","d-flex"],["mat-icon-button","","matTooltip","Close",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[1,"overflow-scroll","pb-2",3,"ngStyle"],["scrollFrame",""],["class","row mt-3","style","min-width: 90%",4,"ngFor","ngForOf"],[2,"position","relative"],["class","col-12 p-0 card context-card",4,"ngIf"],["class","row mx-0 pt-2","style","border-top: 1px solid #c1bfbf; z-index: 1; background-color: white",4,"ngIf"],[1,"row","mt-3",2,"min-width","90%"],["class","col-12 pl-1 d-flex",4,"ngIf"],["class","col-12 pl-3 d-flex",4,"ngIf"],[1,"col-12","pl-1","d-flex"],[1,"chat-outgoing","p-0","pb-1"],["class","col-12 p-0 card context-card-comments",4,"ngIf"],[1,"row","mt-2",2,"height","21px !important"],[1,"col-12","pl-2","pr-2","d-flex"],[1,"comment-date","pl-3","ml-auto","mr-1"],["mat-icon-button","","aria-label","Triple dot menu","class","cmnt-menu",3,"matMenuTriggerFor",4,"ngIf"],["cmntMenu","matMenu"],["mat-menu-item","",3,"click"],[1,"mx-3","my-2","chatBox"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1","ml-1",3,"id"],[1,"col-12","p-0","card","context-card-comments"],[1,"card-body","p-1"],[1,"row","pb-1"],[1,"col-6","pl-1","pr-1"],[1,"context-title"],[1,"row"],["class","col-6 pr-1 pb-1 pl-0 d-flex",4,"ngFor","ngForOf"],[1,"col-6","pr-1","pb-1","pl-0","d-flex"],[1,"name"],[2,"float","right"],[1,"col-6","pl-0","pr-0","overflow-hidden"],["mat-icon-button","","aria-label","Triple dot menu",1,"cmnt-menu",3,"matMenuTriggerFor"],[1,"cmnt-menu-icon"],[1,"col-12","pl-3","d-flex"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1",3,"id"],[1,"col-11","chat-incoming","p-0","pb-1"],[1,"comment-user-name","pl-3","mr-3"],[1,"comment-date","mr-3"],[1,"row","px-3","chatBox"],["class","col-6 pb-1 pr-1 pl-0 d-flex",4,"ngFor","ngForOf"],[1,"col-6","pb-1","pr-1","pl-0","d-flex"],[1,"col-12","p-0","card","context-card"],[1,"name",3,"matTooltip"],[1,"row","mx-0","pt-2",2,"border-top","1px solid #c1bfbf","z-index","1","background-color","white"],[1,"col-1",2,"padding-top","9px"],["imgWidth","30px","imgHeight","30px",1,"my-auto","pr-1",3,"id"],[1,"col-10","pl-0","pr-2","d-flex"],[1,"comment-box"],["type","text","matInput","","placeholder","Enter @ to mention employee within the organization","id","comment",3,"mentionConfig","keydown.enter","itemSelected"],["comment",""],["mat-icon-button","",1,"ml-auto","mt-1",3,"ngClass","click"],[1,"col-1"],["mat-icon-button","",1,"mt-1","send-button",3,"click"],[2,"color","white","font-size","20px"]],template:function(t,e){1&t&&(D["\u0275\u0275elementStart"](0,"div",0),D["\u0275\u0275elementStart"](1,"div",1),D["\u0275\u0275elementStart"](2,"div",2),D["\u0275\u0275elementStart"](3,"div",3),D["\u0275\u0275elementStart"](4,"mat-icon",4),D["\u0275\u0275text"](5,"forum"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"span",5),D["\u0275\u0275text"](7,"Comments in"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](8,"span",6),D["\u0275\u0275text"](9),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](10,"div",7),D["\u0275\u0275elementStart"](11,"button",8),D["\u0275\u0275listener"]("click",(function(){return e.closeComments()})),D["\u0275\u0275elementStart"](12,"mat-icon",9),D["\u0275\u0275text"](13,"close"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](14,"div",10,11),D["\u0275\u0275template"](16,H,3,2,"div",12),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](17,"div",13),D["\u0275\u0275template"](18,$,8,1,"div",14),D["\u0275\u0275elementEnd"](),D["\u0275\u0275template"](19,X,14,6,"div",15),D["\u0275\u0275elementEnd"]()),2&t&&(D["\u0275\u0275property"]("ngStyle",D["\u0275\u0275pureFunction1"](6,Y,e.getCommentBoxHeight())),D["\u0275\u0275advance"](9),D["\u0275\u0275textInterpolate"](e.title),D["\u0275\u0275advance"](5),D["\u0275\u0275property"]("ngStyle",D["\u0275\u0275pureFunction1"](8,W,e.getCommentScrollHeight())),D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("ngForOf",e.comments),D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("ngIf",e.modalParams.context&&e.isContextEnabled),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("ngIf",!e.isReadOnly))},directives:[w.NgStyle,d.a,p.a,h.a,w.NgForOf,w.NgIf,I.g,I.d,N.a,I.f,m.c,r.b,k.a,w.NgClass],styles:[".chat-context-styles[_ngcontent-%COMP%]   .comment-row[_ngcontent-%COMP%]{height:70vh}.chat-context-styles[_ngcontent-%COMP%]   .cmnt-menu[_ngcontent-%COMP%]{height:15px;width:15px;line-height:15px}.chat-context-styles[_ngcontent-%COMP%]   .cmnt-menu[_ngcontent-%COMP%]   .cmnt-menu-icon[_ngcontent-%COMP%]{font-size:18px}.chat-context-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.chat-context-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#79ba44;font-size:18px;margin-top:3px!important;margin-left:4px!important}.chat-context-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;color:#1a1a1a}.chat-context-styles[_ngcontent-%COMP%]   .context-title[_ngcontent-%COMP%]{color:#545352;font-size:14px;font-weight:700}.chat-context-styles[_ngcontent-%COMP%]   .normal-font[_ngcontent-%COMP%]{color:#79ba44;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px;font-weight:500}.chat-context-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.chat-context-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.chat-context-styles[_ngcontent-%COMP%]   .footer-bar[_ngcontent-%COMP%]{position:absolute;bottom:0!important}.chat-context-styles[_ngcontent-%COMP%]   .comment-box[_ngcontent-%COMP%]{width:100%;font-size:14px}.chat-context-styles[_ngcontent-%COMP%]   .comment-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:30px;padding-bottom:9px}.chat-context-styles[_ngcontent-%COMP%]   .comment-box[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.chat-context-styles[_ngcontent-%COMP%]   .comment-date[_ngcontent-%COMP%]{font-size:11px;color:#464646}.chat-context-styles[_ngcontent-%COMP%]   .comment-user-name[_ngcontent-%COMP%]{padding-right:10px;font-size:11px;font-weight:400;color:#464646}.chat-context-styles[_ngcontent-%COMP%]   .comment-user-name-left[_ngcontent-%COMP%]{padding-left:20px;font-size:11px;font-weight:400;color:#252424}.chat-context-styles[_ngcontent-%COMP%]   .chat-outgoing[_ngcontent-%COMP%]{margin-left:auto!important;background:#f3f3f3;padding:5px 22px;border-radius:4px;width:900px}.chat-context-styles[_ngcontent-%COMP%]   .chat-incoming[_ngcontent-%COMP%], .chat-context-styles[_ngcontent-%COMP%]   .chat-outgoing[_ngcontent-%COMP%]{display:inline-block;font-size:14px;color:#1b1b1b;text-align:left;position:relative}.chat-context-styles[_ngcontent-%COMP%]   .chat-incoming[_ngcontent-%COMP%]{margin-right:auto!important;background:#ececec;padding:5px 15px;border-radius:7px;max-width:85%}.chat-context-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#66615b!important}.chat-context-styles[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]{width:32px;height:32px;line-height:32px;background-color:#79ba44;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.chat-context-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{background:#f5f5f5;line-height:32px;width:32px;height:32px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.chat-context-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.chat-context-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]{background-color:#79ba44;line-height:32px;width:32px;height:32px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.chat-context-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.chat-context-styles[_ngcontent-%COMP%]   .chatBox[_ngcontent-%COMP%]{word-wrap:break-word;display:block;overflow:hidden}.chat-context-styles[_ngcontent-%COMP%]   .context-card[_ngcontent-%COMP%]{max-width:85%;position:absolute;margin-left:15px;margin-right:15px;bottom:-5px;animation:slide-in-top .3s cubic-bezier(.25,.46,.45,.94) both}.chat-context-styles[_ngcontent-%COMP%]   .context-card-comments[_ngcontent-%COMP%]{text-align:left;box-shadow:none!important}.chat-context-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(50px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})(),Q=(()=>{class t{}return t.\u0275mod=D["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=D["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[w.CommonModule,m.e,r.c,d.b,p.b,h.b,C.b,b.c,f.b,u.E,g.a,u.p,x.h,y.d,v.b,P.a,_.a,O.b,E.b,S.c,M.g,I.e,k.b]]}),t})()},gvOY:function(t,e,n){"use strict";n.d(e,"a",(function(){return P})),n.d(e,"b",(function(){return O})),n.d(e,"c",(function(){return _}));var o=n("fXoL"),i=n("XNiG"),a=n("mrSG"),l=n("tk/3"),s=n("3Pt+"),c=n("1G5W"),m=n("Kj3r"),r=n("ofXK"),d=n("bTqV"),p=n("NFeN"),h=n("kmnG"),u=n("qFsG"),x=n("d3UM"),g=n("/1cH"),f=n("WJ5W"),C=n("FKr1");function b(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275elementStart"](1,"div",7),o["\u0275\u0275element"](2,"div"),o["\u0275\u0275element"](3,"div"),o["\u0275\u0275element"](4,"div"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function v(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"]("",n[t],"\xa0")}}function y(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"mat-option",8),o["\u0275\u0275template"](1,v,2,1,"span",9),o["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("value",t),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",n.optionLabel)}}let _=(()=>{class t{constructor(){this.msg=new i.b,this.removeOption=t=>{this.msg.next(t)},this.getRemoveIndex=()=>this.msg.asObservable()}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=Object(o["\u0275\u0275defineInjectable"])({factory:function(){return new t},token:t,providedIn:"root"}),t})(),P=(()=>{class t{constructor(t,e){this._http=t,this._mulL=e,this.selectedValues=new o.EventEmitter,this.list=[],this.searchCtrl=new s.j,this.selectedValCtrl=new s.j,this._onDestroy=new i.b,this.isLoading=!1,this.fetchData=()=>{const t={headers:new l.f({"Content-Type":"application/json",Authorization:"Bearer "+this.token})};return this.isLoading=!0,new Promise((e,n)=>{this._http.post(this.API_URL?this.API_URL:"",{searchText:this.searchCtrl.value},t).subscribe(t=>{e(t),this.isLoading=!1},t=>{n(t),this.isLoading=!1,console.error(t)})})}}ngOnInit(){this.searchCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy),Object(m.a)(700)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){null!=t&&""!=t&&(this.list=yield this.fetchData())}))),this.selectedValCtrl.valueChanges.subscribe(t=>{this.selectedValues.emit(t)}),this._mulL.getRemoveIndex().subscribe(t=>{this.removeSelectedOption(t)})}removeSelectedOption(t){let e=[];e=this.selectedValCtrl.value,e.splice(t,1),this.selectedValCtrl.patchValue([...e]),this.selectedValues.emit(this.selectedValCtrl.value)}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](l.c),o["\u0275\u0275directiveInject"](_))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["kebs-mul-sel-search"]],inputs:{label:"label",placeholder:"placeholder",appearance:"appearance",optionLabel:"optionLabel",API_URL:"API_URL",bodyParams:"bodyParams",token:"token"},outputs:{selectedValues:"selectedValues"},decls:10,vars:8,consts:[["appearance","outline",1,"full-width"],["multiple","",3,"placeholder","formControl"],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel","hideClearSearchButton"],["class","is-loading",4,"ngIf"],[3,"click"],[3,"value",4,"ngFor","ngForOf"],[1,"is-loading"],[1,"lds-facebook"],[3,"value"],[4,"ngFor","ngForOf"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1),o["\u0275\u0275elementStart"](4,"mat-option"),o["\u0275\u0275element"](5,"ngx-mat-select-search",2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](6,b,5,0,"mat-option",3),o["\u0275\u0275elementStart"](7,"mat-option",4),o["\u0275\u0275listener"]("click",(function(){return e.selectedValCtrl.reset()})),o["\u0275\u0275text"](8,"None"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](9,y,2,2,"mat-option",5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.label),o["\u0275\u0275advance"](1),o["\u0275\u0275propertyInterpolate"]("placeholder",e.placeholder),o["\u0275\u0275property"]("formControl",e.selectedValCtrl),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("formControl",e.searchCtrl)("placeholderLabel",e.placeholder?e.placeholder:"")("hideClearSearchButton",!1),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.isLoading),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngForOf",e.list))},directives:[h.c,h.g,x.c,s.v,s.k,C.p,f.a,r.NgIf,r.NgForOf],styles:[".full-width[_ngcontent-%COMP%]{width:100%}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;-webkit-animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;-webkit-animation-delay:-.24s;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;-webkit-animation-delay:-.12s;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;-webkit-animation-delay:0;animation-delay:0}@-webkit-keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),t})(),O=(()=>{class t{}return t.\u0275mod=o["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[r.CommonModule,d.b,p.b,h.e,u.c,x.d,g.c,l.d,s.E,f.b]]}),t})()}}]);