(window.webpackJsonp=window.webpackJsonp||[]).push([[818],{kWxL:function(e,t,n){"use strict";n.r(t),n.d(t,"EdTimeModule",(function(){return S}));var o=n("ofXK"),i=n("tyNb"),l=n("fXoL"),s=n("33Jv"),a=n("xG9w"),c=n("jAlA"),r=n("1A3m"),d=n("jaxi");const g=["contentContainer"],m=function(e){return{"btn-toggle-selected":e}},p=function(e){return{display:e}};function h(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-button-toggle",9),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("value",e.name)("ngClass",l["\u0275\u0275pureFunction1"](4,m,n.selectedToggle==e.name))("ngStyle",l["\u0275\u0275pureFunction1"](6,p,e.isVisible?"revert":"none")),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.name,"")}}const u=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i){this._router=e,this._edService=t,this._route=n,this._toaster=o,this._compiler=i,this.subs=new s.a,this.sectionList=[{id:1,name:"Time Details",isSelected:!1,isVisible:!1,objectId:188},{id:2,name:"Leave Details",isSelected:!1,isVisible:!1,objectId:190},{id:2,name:"Leave Balance Details",isSelected:!1,isVisible:!1,objectId:192}]}ngOnInit(){this.applicationRoleAccessList=this._edService.getAllRoleAccess(),this.getEmployeeId(),this.checkTabVisibility(),this.selectedToggle=this.sectionList[0].isVisible?this.sectionList[0].name:this.sectionList[1].name,this.loadSectionTab({value:this.selectedToggle})}checkTabVisibility(){this.sectionList.forEach(e=>{e.isVisible||(e.isVisible=a.where(this.applicationRoleAccessList,{object_id:e.objectId}).length>0)})}getEmployeeId(){this.subs.sink=this._edService.getEmployeeId().subscribe(e=>{console.log(e),this.associateId=e})}handleSectionSelect(e){this.sectionList.forEach((t,n)=>{t.isSelected=t.id==e.id}),this.loadSectionTab(e)}loadSectionTab(e){switch(this.selectedToggle=e.value,this.contentContainerRef&&this.contentContainerRef.clear(),this.selectedToggle){case"Time Details":this.loadTimeDetailsContainer();break;case"Leave Details":this.loadLeaveDetailsContainer();break;case"Leave Balance Details":this.loadLeaveBalanceDetailsContainer()}}loadTimeDetailsContainer(){Promise.all([n.e(0),n.e(670)]).then(n.bind(null,"dmRi")).then(e=>{const t=this._compiler.compileModuleSync(e.TimeDetailsModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.TimeDetailsComponent);this.contentContainerRef.createComponent(t).instance.associateId=this.associateId})}loadLeaveDetailsContainer(){console.log("Managerial Detail Load"),Promise.all([n.e(20),n.e(25),n.e(510)]).then(n.bind(null,"ysjT")).then(e=>{const t=this._compiler.compileModuleSync(e.LeaveDetailsModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.LeaveDetailsComponent);this.contentContainerRef.createComponent(t).instance.associateId=this.associateId})}loadLeaveBalanceDetailsContainer(){console.log("Managerial Detail Load"),Promise.all([n.e(20),n.e(25),n.e(509)]).then(n.bind(null,"ylMW")).then(e=>{const t=this._compiler.compileModuleSync(e.LeaveBalanceDetailsModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.LeaveBalanceDetailsComponent);this.contentContainerRef.createComponent(t).instance.associateId=this.associateId})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](i.g),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](r.a),l["\u0275\u0275directiveInject"](l.Compiler))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-time-landing-page"]],viewQuery:function(e,t){if(1&e&&l["\u0275\u0275viewQuery"](g,!0,l.ViewContainerRef),2&e){let e;l["\u0275\u0275queryRefresh"](e=l["\u0275\u0275loadQuery"]())&&(t.contentContainerRef=e.first)}},decls:11,vars:2,consts:[[1,"container-fluid","ed-org-detail-item-styles"],[1,"row","pt-3"],[1,"col-9","pl-0","pr-0"],[1,"row"],[1,"col-10","pl-0","pr-0"],[3,"value","change"],["class","toggle-btn",3,"value","ngClass","ngStyle",4,"ngFor","ngForOf"],[1,"col-12","p-0"],["contentContainer",""],[1,"toggle-btn",3,"value","ngClass","ngStyle"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"div",3),l["\u0275\u0275elementStart"](4,"div",4),l["\u0275\u0275elementStart"](5,"mat-button-toggle-group",5),l["\u0275\u0275listener"]("change",(function(e){return t.handleSectionSelect(e)})),l["\u0275\u0275template"](6,h,2,8,"mat-button-toggle",6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",3),l["\u0275\u0275elementStart"](8,"div",7),l["\u0275\u0275elementContainer"](9,null,8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("value",t.selectedToggle),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.sectionList))},directives:[d.b,o.NgForOf,d.a,o.NgClass,o.NgStyle],styles:[".ed-org-detail-item-styles[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem}.ed-org-detail-item-styles[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#ee4961!important;color:#fff}.ed-org-detail-item-styles[_ngcontent-%COMP%]     .mat-button-toggle-label-content{font-size:12px!important}.ed-org-detail-item-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.ed-org-detail-item-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.ed-org-detail-item-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ed-org-detail-item-styles[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%]{max-height:8rem;overflow:scroll;overflow-x:hidden;overflow-y:auto}.ed-org-detail-item-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]{background-color:#f1f1f1;line-height:8px;width:26px;height:26px;margin-right:15px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.ed-org-detail-item-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}"]}),e})()}];let f=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(u)],i.k]}),e})();var v=n("bTqV"),b=n("NFeN"),C=n("Qu3c"),y=n("STbY"),w=n("MutI"),M=n("0IaG");let S=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,f,b.b,v.b,C.b,d.c,y.e,w.d,M.g]]}),e})()}}]);