(window.webpackJsonp=window.webpackJsonp||[]).push([[772],{"1oGA":function(e,t,n){"use strict";n.r(t),n.d(t,"TsSubmissionQuickfillModalComponent",(function(){return O}));var l=n("wd/R"),o=n("0IaG"),a=n("ofXK"),i=n("bTqV"),s=(n("Qu3c"),n("3Pt+")),r=n("NFeN"),d=(n("kmnG"),n("bSwM")),m=n("fXoL");function c(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",24),m["\u0275\u0275elementStart"](1,"mat-checkbox",15),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.allWorkingDaysInWeek=e})),m["\u0275\u0275text"](2," Fill all working days in "),m["\u0275\u0275elementStart"](3,"span",25),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngModel",e.allWorkingDaysInWeek)("disabled",e.areWorkingDaysNonEditable),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](e.week)}}function h(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",24),m["\u0275\u0275elementStart"](1,"mat-checkbox",15),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.allWeekOffsInWeek=e})),m["\u0275\u0275text"](2," Fill all week offs in "),m["\u0275\u0275elementStart"](3,"span",25),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngModel",e.allWeekOffsInWeek)("disabled",e.areWeekOffsNonEditable),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](e.week)}}function p(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",13),m["\u0275\u0275text"](1,"Flexible Holidays (FH) :"),m["\u0275\u0275elementEnd"]())}function f(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",13),m["\u0275\u0275text"](1,"Holidays (H) :"),m["\u0275\u0275elementEnd"]())}function g(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"p",32),m["\u0275\u0275text"](1,"No flexible holidays in the current month !"),m["\u0275\u0275elementEnd"]())}function u(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"p",32),m["\u0275\u0275text"](1,"No holidays in the current month !"),m["\u0275\u0275elementEnd"]())}function x(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",30),m["\u0275\u0275template"](1,g,2,0,"p",31),m["\u0275\u0275template"](2,u,2,0,"p",31),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!e.rfidDisplay),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.rfidDisplay)}}function b(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"mat-checkbox",15),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).allFHsInMonth=t}))("ngModelChange",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).fillAllFHsInMonth()})),m["\u0275\u0275text"](1," Fill all flexible holidays in the current month "),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](3);m["\u0275\u0275property"]("ngModel",e.allFHsInMonth)("disabled",e.isMonthNonEditable)}}function y(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"mat-checkbox",15),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).allFHsInMonth=t}))("ngModelChange",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).fillAllFHsInMonth()})),m["\u0275\u0275text"](1," Fill all holidays in the current month "),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](3);m["\u0275\u0275property"]("ngModel",e.allFHsInMonth)("disabled",e.isMonthNonEditable)}}function M(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",30),m["\u0275\u0275template"](1,b,2,2,"mat-checkbox",33),m["\u0275\u0275template"](2,y,2,2,"mat-checkbox",33),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!e.rfidDisplay),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.rfidDisplay)}}function k(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",30),m["\u0275\u0275elementStart"](1,"mat-checkbox",15),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.isTaken=e})),m["\u0275\u0275text"](2," Fill "),m["\u0275\u0275elementStart"](3,"span",25),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngModel",e.isTaken)("disabled",e.isNonEditable),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate2"]("",e.day," : ",e.date,"")}}function v(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",9),m["\u0275\u0275elementStart"](1,"div",26),m["\u0275\u0275elementStart"](2,"div",11),m["\u0275\u0275elementStart"](3,"span"),m["\u0275\u0275element"](4,"div",12),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,p,2,0,"span",27),m["\u0275\u0275template"](6,f,2,0,"span",27),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](7,x,3,2,"div",28),m["\u0275\u0275template"](8,M,3,2,"div",28),m["\u0275\u0275template"](9,k,5,4,"div",29),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",!e.rfidDisplay),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.rfidDisplay),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",0==e.flexibleHolidays.length),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.flexibleHolidays.length>0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.flexibleHolidays)}}function I(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",34),m["\u0275\u0275elementStart"](1,"span"),m["\u0275\u0275element"](2,"div",12),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"span",13),m["\u0275\u0275text"](4,"Replicate From Previous Month :"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function C(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",35),m["\u0275\u0275elementStart"](1,"div",36),m["\u0275\u0275elementStart"](2,"mat-icon",37),m["\u0275\u0275text"](3,"info"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"span",38),m["\u0275\u0275text"](5," If you replicate from previous month, it will replace all the entries in the current month "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function E(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",42),m["\u0275\u0275elementStart"](1,"div",43),m["\u0275\u0275elementStart"](2,"div",44),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](2).cardClicked(n)})),m["\u0275\u0275elementStart"](3,"span",45),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngClass",e.clicked?"month-card-clicked":e.isDisabled?"month-card-disabled":""),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](e.monthLabel)}}function w(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",39),m["\u0275\u0275elementStart"](1,"div",40),m["\u0275\u0275elementStart"](2,"div",30),m["\u0275\u0275template"](3,E,5,2,"div",41),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngForOf",e.monthData)}}let O=(()=>{class e{constructor(e,t){this.dialogRef=e,this.inData=t,this.daily_hour_quota=0,this.isMonthNonEditable=!1,this.allWorkingDaysInMonth=!1,this.weeksInMonth=[],this.allWeekOffsInMonth=!1,this.allFHsInMonth=!1,this.flexibleHolidays=[],this.monthData=[],this.selectedMonth="",this.rfidDisplay=!1,this.hideHolidayUi=!1}ngOnInit(){this.initDetails()}ngOnChanges(){this.initDetails()}initDetails(){this.modalParams=this.inData.modalParams,this.daily_hour_quota=this.modalParams.daily_hour_quota,this.isMonthNonEditable=this.modalParams.isMonthNonEditable,this.allWorkingDaysInMonth=this.modalParams.allWorkingDaysInMonth,this.weeksInMonth=this.modalParams.weeksInMonth,this.allWeekOffsInMonth=this.modalParams.allWeekOffsInMonth,this.allFHsInMonth=this.modalParams.allFHsInMonth,this.flexibleHolidays=this.modalParams.flexibleHolidays,this.rfidDisplay=this.modalParams.rfidDisplay,this.hideHolidayUi=this.modalParams.hideHolidayUi,this.generateMonthData()}generateMonthData(){this.monthData=[],this.selectedMonth="";let e,t,n,o=l();for(o.month()>2?(e=l().year(o.year()).month(3),t=l().year(o.year()+1).month(2)):(e=l().year(o.year()-1).month(3),t=l().year(o.year()).month(2)),n=e;l(n).isSameOrBefore(t);)this.monthData.push({monthLabel:n.format("MMM"),monthValue:n.format("YYYY-MM-DD"),isDisabled:n>=l().date(1),clicked:!1}),n.add(1,"month")}cardClicked(e){if(!this.monthData[e].isDisabled){for(let t=0;t<this.monthData.length;t++)this.monthData[t].isDisabled||(this.monthData[t].clicked=t==e);this.selectedMonth=this.monthData[e].monthValue}}fillAllWorkingDaysInMonth(){for(let e=0;e<this.weeksInMonth.length;e++)this.weeksInMonth[e].allWorkingDaysInWeek=this.allWorkingDaysInMonth,this.weeksInMonth[e].areWorkingDaysNonEditable=this.allWorkingDaysInMonth}fillAllWeekOffsInMonth(){for(let e=0;e<this.weeksInMonth.length;e++)this.weeksInMonth[e].allWeekOffsInWeek=this.allWeekOffsInMonth,this.weeksInMonth[e].areWeekOffsNonEditable=this.allWeekOffsInMonth}fillAllFHsInMonth(){for(let e=0;e<this.flexibleHolidays.length;e++)this.flexibleHolidays[e].isTaken=this.allFHsInMonth,this.flexibleHolidays[e].isNonEditable=this.allFHsInMonth}saveQuickFillOptions(){this.modalParams.allWorkingDaysInMonth=this.allWorkingDaysInMonth,this.modalParams.weeksInMonth=this.weeksInMonth,this.modalParams.allWeekOffsInMonth=this.allWeekOffsInMonth,this.modalParams.allFHsInMonth=this.allFHsInMonth,this.modalParams.flexibleHolidays=this.flexibleHolidays,this.modalParams.selectedMonth=this.selectedMonth,this.dialogRef.close({event:"Submit",data:{quickfillResponse:this.modalParams}})}closeModal(){this.dialogRef.close({event:"Close"})}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](o.h),m["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["ts-submission-quickfill-modal"]],features:[m["\u0275\u0275NgOnChangesFeature"]],decls:43,vars:11,consts:[[1,"container-fluid","submission-summary-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-2","pb-2","d-flex","pl-2"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","my-auto","ml-3"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-6"],[1,"row","pb-2"],[1,"status-circular"],[1,"ml-3","headingBold"],[1,"row","mt-2"],[1,"checkbox-text",3,"ngModel","disabled","ngModelChange"],["class","row pt-1",4,"ngFor","ngForOf"],["class","row pt-2 pb-2","style","border-bottom: solid 1px #cacaca",4,"ngIf"],["class","row col-8 pt-2",4,"ngIf"],["class","row pt-2",4,"ngIf"],["class","row pt-3",4,"ngIf"],[1,"row","d-flex","pt-3","pb-3"],[1,"col-4","d-flex"],["mat-mini-fab","",1,"ml-auto","my-auto","mini-tick","mb-1","mr-5",3,"click"],[1,"row","pt-1"],[2,"color","#cf0001 !important"],[1,"col-8"],["class","ml-3 headingBold",4,"ngIf"],["class","row",4,"ngIf"],["class","row",4,"ngFor","ngForOf"],[1,"row"],["class","checkbox-text",4,"ngIf"],[1,"checkbox-text"],["class","checkbox-text",3,"ngModel","disabled","ngModelChange",4,"ngIf"],[1,"row","col-8","pt-2"],[1,"row","pt-2"],[1,"col-9","d-flex",2,"background-color","#ffe8c7"],[2,"color","#b94141","font-size","20px"],[1,"ml-2","info-text"],[1,"row","pt-3"],[1,"col-9","pl-0"],["class","col-2 pb-3 pr-0",4,"ngFor","ngForOf"],[1,"col-2","pb-3","pr-0"],[1,"card","month-card","slide-in-right",3,"ngClass"],[1,"card-body","p-0","d-flex","justify-content-center",3,"click"],[1,"my-auto"]],template:function(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",3),m["\u0275\u0275elementStart"](4,"mat-icon",4),m["\u0275\u0275text"](5,"flash_on"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"span",5),m["\u0275\u0275text"](7," Quick Fill "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",6),m["\u0275\u0275elementStart"](9,"button",7),m["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),m["\u0275\u0275elementStart"](10,"mat-icon",8),m["\u0275\u0275text"](11,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](12,"div",9),m["\u0275\u0275elementStart"](13,"div",10),m["\u0275\u0275elementStart"](14,"div",11),m["\u0275\u0275elementStart"](15,"span"),m["\u0275\u0275element"](16,"div",12),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](17,"span",13),m["\u0275\u0275text"](18),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](19,"div",14),m["\u0275\u0275elementStart"](20,"mat-checkbox",15),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.allWorkingDaysInMonth=e}))("ngModelChange",(function(){return t.fillAllWorkingDaysInMonth()})),m["\u0275\u0275text"](21," Fill all working days in current month"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](22,c,5,3,"div",16),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](23,"div",10),m["\u0275\u0275elementStart"](24,"div",11),m["\u0275\u0275elementStart"](25,"span"),m["\u0275\u0275element"](26,"div",12),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](27,"span",13),m["\u0275\u0275text"](28,"Week Offs (WO) :"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](29,"div",14),m["\u0275\u0275elementStart"](30,"mat-checkbox",15),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.allWeekOffsInMonth=e}))("ngModelChange",(function(){return t.fillAllWeekOffsInMonth()})),m["\u0275\u0275text"](31," Fill all week offs in the current month "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](32,h,5,3,"div",16),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](33,v,10,5,"div",17),m["\u0275\u0275template"](34,I,5,0,"div",18),m["\u0275\u0275template"](35,C,6,0,"div",19),m["\u0275\u0275template"](36,w,4,1,"div",20),m["\u0275\u0275elementStart"](37,"div",21),m["\u0275\u0275element"](38,"div",10),m["\u0275\u0275elementStart"](39,"div",22),m["\u0275\u0275elementStart"](40,"button",23),m["\u0275\u0275listener"]("click",(function(){return t.saveQuickFillOptions()})),m["\u0275\u0275elementStart"](41,"mat-icon"),m["\u0275\u0275text"](42,"done_all"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e&&(m["\u0275\u0275advance"](18),m["\u0275\u0275textInterpolate1"]("Working Days (",t.daily_hour_quota," Hours) :"),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngModel",t.allWorkingDaysInMonth)("disabled",t.isMonthNonEditable),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",t.weeksInMonth),m["\u0275\u0275advance"](8),m["\u0275\u0275property"]("ngModel",t.allWeekOffsInMonth)("disabled",t.isMonthNonEditable),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",t.weeksInMonth),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!t.hideHolidayUi),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!t.hideHolidayUi),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!t.hideHolidayUi),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!t.hideHolidayUi))},directives:[r.a,i.a,d.a,s.v,s.y,a.NgForOf,a.NgIf,a.NgClass],styles:[".submission-summary-styles[_ngcontent-%COMP%]{background-image:url(ts_quickfill.012f14e0a63d783a08e0.png);background-size:280px 245px;background-repeat:no-repeat;overflow-y:hidden!important;min-height:95vh;background-position:104% 105%}.submission-summary-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.submission-summary-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.submission-summary-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.submission-summary-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.submission-summary-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.submission-summary-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;background-color:#cf0001;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.submission-summary-styles[_ngcontent-%COMP%]   .checkbox-text[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .checkbox-text[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%]{padding-top:3px!important}.submission-summary-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.submission-summary-styles[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%], .submission-summary-styles[_ngcontent-%COMP%]   .month-card[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .month-card[_ngcontent-%COMP%]{min-height:35px;transition:all .3s linear}.submission-summary-styles[_ngcontent-%COMP%]   .month-card[_ngcontent-%COMP%]:hover{cursor:pointer}.submission-summary-styles[_ngcontent-%COMP%]   .month-card-clicked[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff2f2;font-weight:400;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .month-card-disabled[_ngcontent-%COMP%]{background-color:#d3d3d3;pointer-events:none}.submission-summary-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}"]}),e})()}}]);