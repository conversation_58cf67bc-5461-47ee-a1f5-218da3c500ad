(window.webpackJsonp=window.webpackJsonp||[]).push([[992],{QCtt:function(e,t,n){"use strict";n.r(t),n.d(t,"QuickTaskComponent",(function(){return Ie})),n.d(t,"QuickTaskModule",(function(){return _e}));var a=n("7pIB"),r=n("wd/R"),i=n("0IaG"),o=n("ofXK"),l=n("Xi0T"),d=n("1yaQ"),s=n("FKr1"),c=n("NFeN"),m=n("kmnG"),u=n("qFsG"),p=n("iadO"),g=n("bTqV"),h=n("Qu3c"),f=n("lVl8"),v=n("3Pt+"),y=n("4/q7"),k=n("1jcm"),x=n("MutI"),S=n("QibW"),C=n("d3UM"),T=n("STbY"),E=n("gvOY"),b=n("fXoL"),D=n("XXEo"),M=n("XNiG"),w=n("tk/3");let F=(()=>{class e{constructor(e){this._http=e,this.msg=new M.b,this.getTaskTypes=()=>this._http.post("/api/okr/task/getTaskTypes",{}),this.createTask=e=>this._http.post("/api/okr/task/createTask",e),this.ping=e=>{this.msg.next(e)},this.listen=()=>this.msg.asObservable(),this.getInitativeDataForTaskCreation=e=>this._http.post("/api/okr/initiative/getInitativeDataForTaskCreation",{init_id:e})}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275inject"](w.c))},e.\u0275prov=b["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var I=n("LcQX"),_=n("BVzC"),q=n("tyNb"),O=n("6t9p"),V=n("8SgF"),R=n("me71"),L=n("mS9j");const Y=["dayList1"],A=["dayList2"];function P(e,t){}const N=function(){return{errorMsg:"This field is required."}},j=function(e){return{$implicit:e}};function B(e,t){if(1&e&&b["\u0275\u0275template"](0,P,0,0,"ng-template",58),2&e){b["\u0275\u0275nextContext"]();const e=b["\u0275\u0275reference"](117);b["\u0275\u0275property"]("ngTemplateOutlet",e)("ngTemplateOutletContext",b["\u0275\u0275pureFunction1"](3,j,b["\u0275\u0275pureFunction0"](2,N)))}}function Q(e,t){}function U(e,t){if(1&e&&b["\u0275\u0275template"](0,Q,0,0,"ng-template",58),2&e){b["\u0275\u0275nextContext"]();const e=b["\u0275\u0275reference"](117);b["\u0275\u0275property"]("ngTemplateOutlet",e)("ngTemplateOutletContext",b["\u0275\u0275pureFunction1"](3,j,b["\u0275\u0275pureFunction0"](2,N)))}}function z(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",59),b["\u0275\u0275elementStart"](1,"div",60),b["\u0275\u0275element"](2,"app-user-image",61),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](3,"div",62),b["\u0275\u0275elementStart"](4,"div",0),b["\u0275\u0275elementStart"](5,"div",63),b["\u0275\u0275text"](6),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"div",0),b["\u0275\u0275elementStart"](8,"div",64),b["\u0275\u0275element"](9,"app-user-profile",65),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"div",66),b["\u0275\u0275elementStart"](11,"button",67),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"]().removeEmp(n)})),b["\u0275\u0275elementStart"](12,"mat-icon"),b["\u0275\u0275text"](13,"cancel"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("id",e.id),b["\u0275\u0275advance"](3),b["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.displayName," "),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("oid",e.id)("type","designation")}}function $(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-form-field",10),b["\u0275\u0275elementStart"](1,"mat-label"),b["\u0275\u0275text"](2,"Start Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](3,"input",68),b["\u0275\u0275element"](4,"mat-datepicker-toggle",69),b["\u0275\u0275element"](5,"mat-datepicker",null,70),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275reference"](6);b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("matDatepicker",e),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",e)}}function H(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-form-field",10),b["\u0275\u0275elementStart"](1,"mat-label"),b["\u0275\u0275text"](2,"Start Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](3,"input",71),b["\u0275\u0275element"](4,"mat-datepicker-toggle",69),b["\u0275\u0275element"](5,"mat-datepicker",null,70),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275reference"](6),t=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("matDatepicker",e)("min",t.okr_initiative_data[0].start_date)("max",t.okr_initiative_data[0].end_date),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",e)}}function W(e,t){}function G(e,t){if(1&e&&b["\u0275\u0275template"](0,W,0,0,"ng-template",58),2&e){b["\u0275\u0275nextContext"]();const e=b["\u0275\u0275reference"](117);b["\u0275\u0275property"]("ngTemplateOutlet",e)("ngTemplateOutletContext",b["\u0275\u0275pureFunction1"](3,j,b["\u0275\u0275pureFunction0"](2,N)))}}function J(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"button",72),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"]().startDatesClicked(n)})),b["\u0275\u0275text"](2),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](1),b["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.duration,"\n",e.displayValue,""),b["\u0275\u0275property"]("ngClass",e.clicked?"btn-active":"btn-not-active"),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function X(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-form-field",10),b["\u0275\u0275elementStart"](1,"mat-label"),b["\u0275\u0275text"](2,"End Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](3,"input",73),b["\u0275\u0275element"](4,"mat-datepicker-toggle",69),b["\u0275\u0275element"](5,"mat-datepicker",null,74),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275reference"](6);b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("matDatepicker",e),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",e)}}function K(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-form-field",10),b["\u0275\u0275elementStart"](1,"mat-label"),b["\u0275\u0275text"](2,"End Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](3,"input",75),b["\u0275\u0275element"](4,"mat-datepicker-toggle",69),b["\u0275\u0275element"](5,"mat-datepicker",null,74),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275reference"](6),t=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("matDatepicker",e)("min",t.okr_initiative_data[0].start_date)("max",t.okr_initiative_data[0].end_date),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",e)}}function Z(e,t){}function ee(e,t){if(1&e&&b["\u0275\u0275template"](0,Z,0,0,"ng-template",58),2&e){b["\u0275\u0275nextContext"]();const e=b["\u0275\u0275reference"](117);b["\u0275\u0275property"]("ngTemplateOutlet",e)("ngTemplateOutletContext",b["\u0275\u0275pureFunction1"](3,j,b["\u0275\u0275pureFunction0"](2,N)))}}function te(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"button",72),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"]().endDatesClicked(n)})),b["\u0275\u0275text"](2),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](1),b["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.duration,"\n",e.displayValue,""),b["\u0275\u0275property"]("ngClass",e.clicked?"btn-active":"btn-not-active"),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function ne(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",76),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.type_name," ")}}function ae(e,t){}function re(e,t){if(1&e&&b["\u0275\u0275template"](0,ae,0,0,"ng-template",58),2&e){b["\u0275\u0275nextContext"]();const e=b["\u0275\u0275reference"](117);b["\u0275\u0275property"]("ngTemplateOutlet",e)("ngTemplateOutletContext",b["\u0275\u0275pureFunction1"](3,j,b["\u0275\u0275pureFunction0"](2,N)))}}const ie=function(e){return{"btn-outline-danger-active":e}};function oe(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",8),b["\u0275\u0275elementStart"](1,"div",9),b["\u0275\u0275elementStart"](2,"button",77),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeRecurringType("daily")})),b["\u0275\u0275text"](3," Daily "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"button",77),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeRecurringType("weekly")})),b["\u0275\u0275text"](5," Weekly "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"button",77),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeRecurringType("biweekly")})),b["\u0275\u0275text"](7," Biweekly "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](8,"button",77),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeRecurringType("monthly")})),b["\u0275\u0275text"](9," Monthly "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"button",77),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeRecurringType("yearly")})),b["\u0275\u0275text"](11," Yearly "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](5,ie,"daily"==e.quickTaskForm.value.recurringType)),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](7,ie,"weekly"==e.quickTaskForm.value.recurringType)),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](9,ie,"biweekly"==e.quickTaskForm.value.recurringType)),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](11,ie,"monthly"==e.quickTaskForm.value.recurringType)),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](13,ie,"yearly"==e.quickTaskForm.value.recurringType))}}function le(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-list-option",81),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e," ")}}function de(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-list-option",81),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e," ")}}function se(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",8),b["\u0275\u0275elementStart"](1,"div",78),b["\u0275\u0275elementStart"](2,"mat-selection-list",79),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().dList1Selected=t}))("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().onFilterChange(t,"dlist1")})),b["\u0275\u0275template"](3,le,2,2,"mat-list-option",80),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",78),b["\u0275\u0275elementStart"](5,"mat-selection-list",79),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().dList2Selected=t}))("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().onFilterChange(t,"dlist2")})),b["\u0275\u0275template"](6,de,2,2,"mat-list-option",80),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngModel",e.dList1Selected),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.daysList1),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngModel",e.dList2Selected),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.daysList2)}}function ce(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-list-option",81),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e," ")}}function me(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-list-option",81),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e," ")}}function ue(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",8),b["\u0275\u0275elementStart"](1,"div",78),b["\u0275\u0275elementStart"](2,"mat-selection-list",79),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().dList1Selected=t}))("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().onFilterChange(t,"dlist1")})),b["\u0275\u0275template"](3,ce,2,2,"mat-list-option",80),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",78),b["\u0275\u0275elementStart"](5,"mat-selection-list",79),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().dList2Selected=t}))("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().onFilterChange(t,"dlist2")})),b["\u0275\u0275template"](6,me,2,2,"mat-list-option",80),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngModel",e.dList1Selected),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.daysList1),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngModel",e.dList2Selected),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.daysList2)}}function pe(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",8),b["\u0275\u0275elementStart"](1,"div",82),b["\u0275\u0275elementStart"](2,"mat-radio-group",83),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().monthlyRecurringDate=t}))("change",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeMonthlyRecurringDate(t)})),b["\u0275\u0275elementStart"](3,"mat-radio-button",84),b["\u0275\u0275text"](4),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"mat-radio-button",84),b["\u0275\u0275text"](6," First day of the Month "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"mat-radio-button",84),b["\u0275\u0275text"](8," Last day of the Month "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"div",82),b["\u0275\u0275elementStart"](10,"mat-radio-group",83),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().monthlyRecurringDate=t}))("change",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeMonthlyRecurringDateToCustom(t)})),b["\u0275\u0275elementStart"](11,"mat-radio-button",85),b["\u0275\u0275text"](12," Custom "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngModel",e.monthlyRecurringDate),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("value",e.todayDate),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" Repeat every ",e.todayDate,"th of the month "),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("value",e.startOftheMonthDate),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("value",e.lastDateOfMonth),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngModel",e.monthlyRecurringDate)}}function ge(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",76),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e)}}function he(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"div",86),b["\u0275\u0275text"](2," Kindly Choose the Date "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](3,"div",82),b["\u0275\u0275elementStart"](4,"mat-form-field",10),b["\u0275\u0275elementStart"](5,"mat-label"),b["\u0275\u0275text"](6,"Choose a date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"mat-select",87),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().monthlyRecurringCustomDate=t}))("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeInCustomMonthlyDate(t)})),b["\u0275\u0275template"](8,ge,2,2,"mat-option",41),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("ngModel",e.monthlyRecurringCustomDate),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.daysDropDownArr)}}function fe(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",8),b["\u0275\u0275elementStart"](1,"div",82),b["\u0275\u0275elementStart"](2,"mat-radio-group",83),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().yearlyRecurringDate=t}))("change",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeYearlyRecurringDate(t)})),b["\u0275\u0275elementStart"](3,"mat-radio-button",84),b["\u0275\u0275text"](4),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"div",82),b["\u0275\u0275elementStart"](6,"mat-radio-group",83),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().yearlyRecurringDate=t}))("change",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeYearlyRecurringDateToCustom(t)})),b["\u0275\u0275elementStart"](7,"mat-radio-button",85),b["\u0275\u0275text"](8," Custom "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngModel",e.yearlyRecurringDate),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("value",e.thisDaythisMonth),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate2"](" Repeat every ",e.todayDate,"th of ",e.currentMonthName," of the Year "),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngModel",e.yearlyRecurringDate)}}function ve(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",88),b["\u0275\u0275elementStart"](1,"div",86),b["\u0275\u0275text"](2," Kindly Choose the Date "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](3,"div",82),b["\u0275\u0275elementStart"](4,"mat-form-field",10),b["\u0275\u0275elementStart"](5,"mat-label"),b["\u0275\u0275text"](6,"Choose a date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"input",89),b["\u0275\u0275listener"]("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().yearlyRecurringCustomDate=t}))("ngModelChange",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().changeInCustomYearlyDate(t)})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](8,"mat-datepicker-toggle",69),b["\u0275\u0275element"](9,"mat-datepicker",null,90),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275reference"](10),t=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("matDatepicker",e)("ngModel",t.yearlyRecurringCustomDate),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",e)}}function ye(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"span"),b["\u0275\u0275elementStart"](1,"span",91),b["\u0275\u0275text"](2," done "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]())}function ke(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"span"),b["\u0275\u0275elementStart"](1,"span",91),b["\u0275\u0275text"](2," close "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]())}function xe(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"span"),b["\u0275\u0275elementStart"](1,"span",91),b["\u0275\u0275text"](2," error "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]())}function Se(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"strong"),b["\u0275\u0275text"](2),b["\u0275\u0275elementEnd"](),b["\u0275\u0275text"](3),b["\u0275\u0275pipe"](4,"number"),b["\u0275\u0275template"](5,ye,3,0,"span",14),b["\u0275\u0275template"](6,ke,3,0,"span",14),b["\u0275\u0275template"](7,xe,3,0,"span",14),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null==e||null==e.file?null:e.file.name),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" (",b["\u0275\u0275pipeBind2"](4,5,(null==e||null==e.file?null:e.file.size)/1024/1024,".2")," MB) "),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",e.isSuccess),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",e.isCancel),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",e.isError)}}function Ce(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"span",92),b["\u0275\u0275text"](1," Drag and Drop your attachments here! "),b["\u0275\u0275elementEnd"]())}function Te(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",93),b["\u0275\u0275elementStart"](1,"div",35),b["\u0275\u0275elementStart"](2,"button",94),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().uploader.uploadAll()})),b["\u0275\u0275text"](3,"Upload"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"button",95),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().uploader.uploadAll()}))("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().uploader.clearQueue()})),b["\u0275\u0275text"](5,"Clear"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"strong"),b["\u0275\u0275text"](7),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("disabled",!e.uploader.getNotUploadedItems().length),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("disabled",!e.uploader.queue.length),b["\u0275\u0275advance"](3),b["\u0275\u0275textInterpolate"](e.uploader.progress+"%")}}function Ee(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"mat-icon"),b["\u0275\u0275text"](1,"done_all"),b["\u0275\u0275elementEnd"]())}function be(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",96),b["\u0275\u0275elementStart"](1,"span",97),b["\u0275\u0275text"](2,"Loading..."),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]())}function De(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"mat-icon",98),b["\u0275\u0275text"](2," error_outline "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](3,"span",99),b["\u0275\u0275text"](4),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275advance"](4),b["\u0275\u0275textInterpolate2"](" ",e.errorMsg," ",e.count," ")}}const Me=function(){return[!1,1,2,3,4,5]},we=function(){return["displayName","associate_id"]},Fe=function(e){return{"nv-file-over":e}};let Ie=(()=>{class e{constructor(e,t,n,i,o,l,d,s,c,m,u){this.dialogRef=e,this._formBuilder=t,this._auth=n,this._qTask=i,this.data=o,this._dialog=l,this._util=d,this._ErrorService=s,this._mulSel=c,this._loginService=m,this.router=u,this.is_okr_intiative_task=!1,this.valueType="html",this.selectedCoOwnerId=[],this.finalAttachments=[],this.isCreatingTask=!1,this.employeeSearchUrl=window.location.origin+"/api/okr/objective/searchEmployee",this.selectedMemberList=[],this.daysList1=["Monday","Tuesday","Wednesday","Thursday"],this.daysList2=["Friday","Saturday","Sunday"],this.dfinalList=[],this.dList1Selected=[],this.dList2Selected=[],this.today=new Date,this.todayDate=this.today.getDate(),this.startOftheMonthDate=new Date(this.today.getFullYear(),this.today.getMonth(),1).getDate(),this.lastDateOfMonth=new Date(this.today.getFullYear(),this.today.getMonth()+1,0).getDate(),this.daysDropDownArr=[],this.monthNames=["January","February","March","April","May","June","July","August","September","October","November","December"],this.startDateTypes=[{label:"Today",value:r(),duration:"0 days",displayValue:"Due on : "+r().format("DD-MMM-YY"),clicked:!1},{label:"1 D",value:r().add(1,"d"),duration:"1 Day",displayValue:"Due on : "+r().add(1,"d").format("DD-MMM-YY"),clicked:!1},{label:"2 D",value:r().add(2,"d"),duration:"2 Days",displayValue:"Due on : "+r().add(2,"d").format("DD-MMM-YY"),clicked:!1},{label:"1 W",value:r().add(1,"w"),duration:"1 Week",displayValue:"Due on : "+r().add(1,"w").format("DD-MMM-YY"),clicked:!1}],this.endDateTypes=[{label:"Today",value:r(),duration:"0 days",displayValue:"Due on : "+r().format("DD-MMM-YY"),clicked:!1},{label:"1 D",value:r().add(1,"d"),duration:"1 Day",displayValue:"Due on : "+r().add(1,"d").format("DD-MMM-YY"),clicked:!1},{label:"2 D",value:r().add(2,"d"),duration:"2 Days",displayValue:"Due on : "+r().add(2,"d").format("DD-MMM-YY"),clicked:!1},{label:"1 W",value:r().add(1,"w"),duration:"1 Week",displayValue:"Due on : "+r().add(1,"w").format("DD-MMM-YY"),clicked:!1}],this.currentMonthName=this.monthNames[this.today.getMonth()].slice(0,3),this.thisDaythisMonth=this.convertDate(new Date),this.taskTypes=[],this.UPLOAD_URL="/api/appraisal/configuration/uploadAppraisalAttachment",this.maxFileSize=10485760,this.getTaskTypes=()=>{this._qTask.getTaskTypes().subscribe(e=>{console.log(e),this.taskTypes=e},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.startDatesClicked=e=>{for(let t=0;t<this.startDateTypes.length;t++)this.startDateTypes[t].clicked=t===e;this.quickTaskForm.patchValue({startDate:new Date(this.startDateTypes[e].value)})},this.endDatesClicked=e=>{for(let t=0;t<this.endDateTypes.length;t++)this.endDateTypes[t].clicked=t===e;this.quickTaskForm.patchValue({endDate:new Date(this.endDateTypes[e].value)})},this.ValueChange=e=>{this.quickTaskForm.get("taskDesc").patchValue(e)},this.uploader=new a.d({url:this.UPLOAD_URL,authToken:"Bearer "+this._auth.getToken(),disableMultipart:!1,maxFileSize:this.maxFileSize}),this.hasBaseDropZoneOver=!1;for(let a=1;a<=31;a++)this.daysDropDownArr.push(a);this.quickTaskForm=this._formBuilder.group({taskName:[null,v.H.required],taskDesc:[null],assignTo:[null,v.H.required],coOwner:[""],startDate:[null,v.H.required],endDate:[null,v.H.required],taskType:[null,v.H.required],isRecurring:[!1,v.H.required],recurringType:[null],recurringTypeAddonData:[null],attachments:[null]}),this.detectUploadChanges(),this.getTaskTypes(),this.quickTaskForm.get("isRecurring").valueChanges.subscribe(e=>{0==e&&this.quickTaskForm.get("recurringType").patchValue(null)})}recurringTaskChange(e){this.quickTaskForm.value.isRecurring&&this.changeRecurringType("daily")}fileOverBase(e){this.hasBaseDropZoneOver=e}ngOnInit(){let e=this.router.url;if(e.includes("initiative/")){let t=e.split("initiative/")[1].split("/")[0];console.log("Initiative ID : ",t),t&&(this.is_okr_intiative_task=!0,this._qTask.getInitativeDataForTaskCreation(t).subscribe(e=>{console.log(e),this.okr_initiative_data=e.data},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in getting InitiatveData",e.error.errMessage)}))}}detectUploadChanges(){this.uploader.onProgressItem=e=>{console.log("in Progress")},this.uploader.onCompleteItem=(e,t,n,a)=>{this.finalAttachments.push(t)},this.uploader.onWhenAddingFileFailed=()=>{console.log("adding Failed!")}}onFilterChange(e,t){this.dfinalList=this.dList1Selected.concat(this.dList2Selected),this.quickTaskForm.get("recurringTypeAddonData").patchValue({data:this.dfinalList})}changeRecurringType(e){this.monthlyRecurringDate=void 0,this.monthlyRecurringCustomDate=void 0,this.yearlyRecurringDate=void 0,this.yearlyRecurringCustomDate=void 0,this.quickTaskForm.get("recurringTypeAddonData").reset(),this.quickTaskForm.get("recurringType").patchValue(e)}changeMonthlyRecurringDate(e){this.quickTaskForm.get("recurringTypeAddonData").patchValue({data:[e.value]})}changeYearlyRecurringDate(e){this.yearlyRecurringCustomDate=void 0,this.quickTaskForm.get("recurringTypeAddonData").patchValue({data:[e.value]})}changeYearlyRecurringDateToCustom(e){this.quickTaskForm.get("recurringTypeAddonData").reset()}changeMonthlyRecurringDateToCustom(e){this.quickTaskForm.get("recurringTypeAddonData").reset()}changeInCustomMonthlyDate(e){this.quickTaskForm.get("recurringTypeAddonData").patchValue({data:e})}changeInCustomYearlyDate(e){this.quickTaskForm.get("recurringTypeAddonData").patchValue({data:[this.convertDate(e)]})}convertDate(e){var t=new Date(e),n=("0"+(t.getMonth()+1)).slice(-2),a=("0"+t.getDate()).slice(-2);return t.getFullYear(),[a,n].join("-")}removeEmp(e){this._mulSel.removeOption(e)}getOwner(e,t){this.selectedMemberList=e,this.selectedCoOwnerId=[];for(let n=0;n<this.selectedMemberList.length;n++)this.selectedCoOwnerId.push(this.selectedMemberList[n].id);this.quickTaskForm.get("coOwner").patchValue(this.selectedCoOwnerId),console.log("Selected MemberList : ",this.selectedMemberList)}saveTask(){this.quickTaskForm.valid?r(this.quickTaskForm.value.endDate)>=r(this.quickTaskForm.value.startDate)?(this.isCreatingTask=!0,this.quickTaskForm.get("attachments").patchValue(this.finalAttachments),console.log(this.quickTaskForm.value),this._qTask.createTask({application_id:this.data.appId,sub_application_id:this.data.subAppId,mapped_under_id:this.data.mapped_under_id,collection_name:this.data.collection_name,db_name:this.data.db_name,task_detail:this.quickTaskForm.value}).subscribe(e=>{this.isCreatingTask=!1,this.closeDialog("Task Created"),this._util.showMessage("Task Created Successfully!","Dismiss"),this._qTask.ping("reload")},e=>{console.error(e),this.isCreatingTask=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})):this._util.showMessage("Invalid Date","Dismiss"):this._util.showMessage("Kindly fill all details!","Dismiss")}closeDialog(e){this.dialogRef.close(e)}get token(){return this._loginService.getToken()}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](i.h),b["\u0275\u0275directiveInject"](v.i),b["\u0275\u0275directiveInject"](D.a),b["\u0275\u0275directiveInject"](F),b["\u0275\u0275directiveInject"](i.a),b["\u0275\u0275directiveInject"](i.b),b["\u0275\u0275directiveInject"](I.a),b["\u0275\u0275directiveInject"](_.a),b["\u0275\u0275directiveInject"](E.c),b["\u0275\u0275directiveInject"](D.a),b["\u0275\u0275directiveInject"](q.g))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["ng-component"]],viewQuery:function(e,t){if(1&e&&(b["\u0275\u0275viewQuery"](Y,!0),b["\u0275\u0275viewQuery"](A,!0)),2&e){let e;b["\u0275\u0275queryRefresh"](e=b["\u0275\u0275loadQuery"]())&&(t.dList1=e.first),b["\u0275\u0275queryRefresh"](e=b["\u0275\u0275loadQuery"]())&&(t.dList2=e.first)}},features:[b["\u0275\u0275ProvidersFeature"]([{provide:s.c,useClass:d.c,deps:[s.f,d.a]},{provide:s.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YY",monthYearLabel:"MMM YYYY"}}}])],decls:118,vars:41,consts:[[1,"row"],[1,"col-12","border-bottom","solid"],[1,"col-11","title"],[1,"col-1"],["mat-icon-button","",3,"click"],[1,"col-12","pl-3","pt-0","pb-0","pr-0"],[3,"formGroup"],[1,"col-10","p-0"],[1,"row","mt-3"],[1,"col-12"],["appearance","outline",1,"custom-class-formfield"],[1,"required-star"],["matInput","","formControlName","taskName"],[1,"row","mb-2"],[4,"ngIf"],[1,"dev-extreme-styles",3,"valueType","valueChange"],["name","bold"],["name","italic"],["name","strike"],["name","underline"],["name","separator"],["name","alignLeft"],["name","alignCenter"],["name","alignRight"],["name","alignJustify"],["name","header",3,"acceptedValues"],[1,"col-6"],["label","Assign To","formControlName","assignTo","required","true",3,"isAutocomplete"],[1,"col-4"],["label","Search Co Owner",3,"token","optionLabel","API_URL","selectedValues"],[1,"col-2"],["mat-icon-button","",3,"matMenuTriggerFor"],["yPosition","below"],["menu","matMenu"],[1,"row",2,"height","20rem","width","15rem",3,"click"],[1,"col-12","p-0"],[1,"d-flex","justify-content-center","mt-3","mb-3","menu-title"],["class","row mt-2 mb-2 pb-2 border-bottom solid",4,"ngFor","ngForOf"],["appearance","outline","class","custom-class-formfield",4,"ngIf"],[4,"ngFor","ngForOf"],["formControlName","taskType"],[3,"value",4,"ngFor","ngForOf"],["labelPosition","before","color","#cf0001","formControlName","isRecurring",3,"change"],["class","row mt-3",4,"ngIf"],["class","row",4,"ngIf"],["class","row pt-2",4,"ngIf"],["ng2FileDrop","",1,"col-8","ml-3","upload-box",3,"ngClass","uploader","fileOver"],[1,"col-9","p-0"],["style","font-size:12px;color:gray",4,"ngIf"],[1,"col-3","p-0"],[1,"material-icons","upload-icon",3,"click"],["type","file","ng2FileSelect","","multiple","",2,"display","none",3,"uploader"],["fileInput",""],["class","row mt-3 mb-3",4,"ngIf"],[1,"col-3","mt-5","pl-5"],["mat-mini-fab","",1,"save-task-btn",3,"disabled","click"],["class","spinner-border text-danger","role","status",4,"ngIf"],["errorCard",""],[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"row","mt-2","mb-2","pb-2","border-bottom","solid"],[1,"col-3","pr-0"],[3,"id"],[1,"col-7","p-0"],[1,"col-12","p-0","overflow-ctrl",3,"matTooltip"],[1,"col-12","p-0","overflow-ctrl"],[3,"oid","type"],[1,"col-2","p-0"],["mat-icon-button","","matTooltip","remove",2,"color","gray",3,"click"],["matInput","","required","","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker1",""],["matInput","","required","","formControlName","startDate",3,"matDatepicker","min","max"],["mat-raised-button","","matTooltipPosition","above","matTooltipClass","my-tooltip-multi-line",1,"ml-2",3,"matTooltip","ngClass","click"],["matInput","","required","","formControlName","endDate",3,"matDatepicker"],["picker2",""],["matInput","","required","","formControlName","endDate",3,"matDatepicker","min","max"],[3,"value"],["type","button",1,"btn","btn-outline-danger","mr-3","reminder-btn",3,"ngClass","click"],[1,"col-4","p-0"],[3,"ngModel","ngModelChange"],["checkboxPosition","before","style","height: 29px",3,"value",4,"ngFor","ngForOf"],["checkboxPosition","before",2,"height","29px",3,"value"],[1,"col-5"],[1,"example-radio-group",3,"ngModel","ngModelChange","change"],[1,"example-radio-button",3,"value"],["value","custom",1,"example-radio-button"],[1,"col-5","pt-2","mt-1"],["multiple","",3,"ngModel","ngModelChange"],[1,"row","pt-2"],["matInput","",3,"matDatepicker","ngModel","ngModelChange"],["picker4",""],[1,"material-icons"],[2,"font-size","12px","color","gray"],[1,"row","mt-3","mb-3"],["mat-raised-button","",2,"color","#cf0001",3,"disabled","click"],["mat-raised-button","",1,"ml-3","mr-3",2,"color","#cf0001",3,"disabled","click"],["role","status",1,"spinner-border","text-danger"],[1,"sr-only"],[2,"color","#cf0001","font-size","21px"],[2,"color","#cf0001","font-weight","500"]],template:function(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",0),b["\u0275\u0275elementStart"](3,"div",2),b["\u0275\u0275text"](4," Create Task "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"div",3),b["\u0275\u0275elementStart"](6,"button",4),b["\u0275\u0275listener"]("click",(function(){return t.closeDialog("")})),b["\u0275\u0275elementStart"](7,"mat-icon"),b["\u0275\u0275text"](8,"close"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"div",0),b["\u0275\u0275elementStart"](10,"div",5),b["\u0275\u0275elementStart"](11,"form",6),b["\u0275\u0275elementStart"](12,"div",0),b["\u0275\u0275elementStart"](13,"div",7),b["\u0275\u0275elementStart"](14,"div",8),b["\u0275\u0275elementStart"](15,"div",9),b["\u0275\u0275elementStart"](16,"mat-form-field",10),b["\u0275\u0275elementStart"](17,"mat-label"),b["\u0275\u0275text"](18,"Task Name "),b["\u0275\u0275elementStart"](19,"span",11),b["\u0275\u0275text"](20," \xa0*"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](21,"input",12),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"div",13),b["\u0275\u0275template"](23,B,1,5,void 0,14),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](24,"div",8),b["\u0275\u0275elementStart"](25,"div",9),b["\u0275\u0275elementStart"](26,"dx-html-editor",15),b["\u0275\u0275listener"]("valueType",(function(){return t.valueType}))("valueChange",(function(e){return t.ValueChange(e)})),b["\u0275\u0275elementStart"](27,"dxo-toolbar"),b["\u0275\u0275element"](28,"dxi-item",16),b["\u0275\u0275element"](29,"dxi-item",17),b["\u0275\u0275element"](30,"dxi-item",18),b["\u0275\u0275element"](31,"dxi-item",19),b["\u0275\u0275element"](32,"dxi-item",20),b["\u0275\u0275element"](33,"dxi-item",21),b["\u0275\u0275element"](34,"dxi-item",22),b["\u0275\u0275element"](35,"dxi-item",23),b["\u0275\u0275element"](36,"dxi-item",24),b["\u0275\u0275element"](37,"dxi-item",20),b["\u0275\u0275element"](38,"dxi-item",25),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](39,"div",8),b["\u0275\u0275elementStart"](40,"div",26),b["\u0275\u0275element"](41,"app-search-user",27),b["\u0275\u0275elementStart"](42,"div",13),b["\u0275\u0275template"](43,U,1,5,void 0,14),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](44,"div",28),b["\u0275\u0275elementStart"](45,"kebs-mul-sel-search",29),b["\u0275\u0275listener"]("selectedValues",(function(e){return t.getOwner(e,"coOwner")})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](46,"div",30),b["\u0275\u0275elementStart"](47,"button",31),b["\u0275\u0275elementStart"](48,"mat-icon"),b["\u0275\u0275text"](49,"supervisor_account"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](50,"strong"),b["\u0275\u0275text"](51),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](52,"mat-menu",32,33),b["\u0275\u0275elementStart"](54,"div",34),b["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),b["\u0275\u0275elementStart"](55,"div",35),b["\u0275\u0275elementStart"](56,"div",36),b["\u0275\u0275text"](57," Selected Co-Owners "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](58,z,14,5,"div",37),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](59,"div",8),b["\u0275\u0275elementStart"](60,"div",26),b["\u0275\u0275template"](61,$,7,2,"mat-form-field",38),b["\u0275\u0275template"](62,H,7,4,"mat-form-field",38),b["\u0275\u0275elementStart"](63,"div",13),b["\u0275\u0275template"](64,G,1,5,void 0,14),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](65,"div",26),b["\u0275\u0275elementStart"](66,"div",0),b["\u0275\u0275template"](67,J,3,4,"div",39),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](68,"div",8),b["\u0275\u0275elementStart"](69,"div",26),b["\u0275\u0275template"](70,X,7,2,"mat-form-field",38),b["\u0275\u0275template"](71,K,7,4,"mat-form-field",38),b["\u0275\u0275elementStart"](72,"div",13),b["\u0275\u0275template"](73,ee,1,5,void 0,14),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](74,"div",26),b["\u0275\u0275elementStart"](75,"div",0),b["\u0275\u0275template"](76,te,3,4,"div",39),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](77,"div",8),b["\u0275\u0275elementStart"](78,"div",9),b["\u0275\u0275elementStart"](79,"mat-form-field",10),b["\u0275\u0275elementStart"](80,"mat-label"),b["\u0275\u0275text"](81,"Task Type "),b["\u0275\u0275elementStart"](82,"span",11),b["\u0275\u0275text"](83," \xa0*"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](84,"mat-select",40),b["\u0275\u0275template"](85,ne,2,2,"mat-option",41),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](86,"div",13),b["\u0275\u0275template"](87,re,1,5,void 0,14),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](88,"div",8),b["\u0275\u0275elementStart"](89,"div",9),b["\u0275\u0275elementStart"](90,"mat-slide-toggle",42),b["\u0275\u0275listener"]("change",(function(e){return t.recurringTaskChange(e)})),b["\u0275\u0275text"](91," Recurring Task \xa0\xa0\xa0\xa0 "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](92,"div",30),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](93,oe,12,15,"div",43),b["\u0275\u0275template"](94,se,7,4,"div",43),b["\u0275\u0275template"](95,ue,7,4,"div",43),b["\u0275\u0275template"](96,pe,13,6,"div",43),b["\u0275\u0275template"](97,he,9,2,"div",44),b["\u0275\u0275template"](98,fe,9,5,"div",43),b["\u0275\u0275template"](99,ve,11,3,"div",45),b["\u0275\u0275elementStart"](100,"div",8),b["\u0275\u0275elementStart"](101,"div",46),b["\u0275\u0275listener"]("fileOver",(function(e){return t.fileOverBase(e)})),b["\u0275\u0275elementStart"](102,"div",8),b["\u0275\u0275elementStart"](103,"div",47),b["\u0275\u0275template"](104,Se,8,8,"div",39),b["\u0275\u0275template"](105,Ce,2,0,"span",48),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](106,"div",49),b["\u0275\u0275elementStart"](107,"span",50),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](110).click()})),b["\u0275\u0275text"](108," cloud_upload "),b["\u0275\u0275element"](109,"input",51,52),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](111,Te,8,3,"div",53),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](112,"div",54),b["\u0275\u0275elementStart"](113,"button",55),b["\u0275\u0275listener"]("click",(function(){return t.saveTask()})),b["\u0275\u0275template"](114,Ee,2,0,"mat-icon",14),b["\u0275\u0275template"](115,be,3,0,"div",56),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](116,De,5,2,"ng-template",null,57,b["\u0275\u0275templateRefExtractor"]),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275reference"](53);b["\u0275\u0275advance"](11),b["\u0275\u0275property"]("formGroup",t.quickTaskForm),b["\u0275\u0275advance"](12),b["\u0275\u0275property"]("ngIf",t.quickTaskForm.get("taskName").hasError("required")&&(t.quickTaskForm.get("taskName").dirty||t.quickTaskForm.get("taskName").touched)),b["\u0275\u0275advance"](15),b["\u0275\u0275property"]("acceptedValues",b["\u0275\u0275pureFunction0"](37,Me)),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("isAutocomplete",!0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",t.quickTaskForm.get("assignTo").hasError("required")&&(t.quickTaskForm.get("assignTo").dirty||t.quickTaskForm.get("assignTo").touched)),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("token",t.token)("optionLabel",b["\u0275\u0275pureFunction0"](38,we))("API_URL",t.employeeSearchUrl),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("matMenuTriggerFor",e),b["\u0275\u0275advance"](4),b["\u0275\u0275textInterpolate1"]("+",t.selectedMemberList.length,""),b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("ngForOf",t.selectedMemberList),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngIf",!t.is_okr_intiative_task),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.is_okr_intiative_task),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",t.quickTaskForm.get("startDate").hasError("required")&&(t.quickTaskForm.get("startDate").dirty||t.quickTaskForm.get("startDate").touched)),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngForOf",t.startDateTypes),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngIf",!t.is_okr_intiative_task),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.is_okr_intiative_task),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",t.quickTaskForm.get("endDate").hasError("required")&&(t.quickTaskForm.get("endDate").dirty||t.quickTaskForm.get("endDate").touched)),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngForOf",t.endDateTypes),b["\u0275\u0275advance"](9),b["\u0275\u0275property"]("ngForOf",t.taskTypes),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",t.quickTaskForm.get("taskType").hasError("required")&&(t.quickTaskForm.get("taskType").dirty||t.quickTaskForm.get("taskType").touched)),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngIf",t.quickTaskForm.value.isRecurring),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","weekly"==t.quickTaskForm.value.recurringType),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","biweekly"==t.quickTaskForm.value.recurringType),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","monthly"==t.quickTaskForm.value.recurringType),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","custom"==t.monthlyRecurringDate),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","yearly"==t.quickTaskForm.value.recurringType),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","custom"==t.yearlyRecurringDate),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](39,Fe,t.hasBaseDropZoneOver))("uploader",t.uploader),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngForOf",t.uploader.queue),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",0==t.uploader.queue.length),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("uploader",t.uploader),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",t.uploader.queue.length>0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("disabled",t.isCreatingTask),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",!t.isCreatingTask),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.isCreatingTask)}},directives:[g.a,c.a,v.J,v.w,v.n,m.c,m.g,u.b,v.e,v.v,v.l,o.NgIf,y.a,O.Ge,O.o,V.a,v.F,E.a,T.f,T.g,o.NgForOf,C.c,k.a,a.a,o.NgClass,a.b,o.NgTemplateOutlet,R.a,h.a,L.a,p.g,p.i,m.i,p.f,s.p,x.h,v.y,x.e,S.b,S.a],pipes:[o.DecimalPipe],styles:[".title[_ngcontent-%COMP%]{font-size:15px;font-weight:400}.required-star[_ngcontent-%COMP%], .title[_ngcontent-%COMP%]{color:#cf0001}.custom-class-formfield[_ngcontent-%COMP%]{width:100%}  .dx-htmleditor-toolbar-wrapper{height:48px;padding:0}.dev-extreme-styles[_ngcontent-%COMP%]{height:161px;border-radius:7px}  .mat-form-field-flex{height:41px!important}.btn-outline-danger[_ngcontent-%COMP%]:hover{color:#fff;background-color:#cf0001;border-color:#cf0001}.btn-outline-danger-active[_ngcontent-%COMP%]{color:#fff!important;background-color:#cf0001!important;border-color:#cf0001!important}.reminder-btn[_ngcontent-%COMP%]{width:8rem}.example-radio-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin:15px 0}.upload-box[_ngcontent-%COMP%]{min-height:128px;border:solid;border-color:#d6d6d6;border-width:1px}.my-drop-zone[_ngcontent-%COMP%]{border:3px dotted #d3d3d3}.nv-file-over[_ngcontent-%COMP%]{border:3px dotted red}.upload-icon[_ngcontent-%COMP%]{position:absolute;opacity:.1;cursor:pointer;font-size:6rem}.save-task-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.btn-active[_ngcontent-%COMP%], .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.overflow-ctrl[_ngcontent-%COMP%]{max-width:85%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})(),_e=(()=>{class e{}return e.\u0275mod=b["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=b["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[{provide:p.b,useValue:{}}],imports:[[o.CommonModule,g.b,v.p,C.d,v.E,h.b,f.b,c.b,y.b,p.h,a.c,x.d,S.c,m.e,u.c,l.a,i.g,k.b,E.b,T.e]]}),e})()}}]);