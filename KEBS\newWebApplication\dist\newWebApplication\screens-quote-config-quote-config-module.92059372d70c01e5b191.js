(window.webpackJsonp=window.webpackJsonp||[]).push([[874,765,985],{"A+V/":function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var o=n("mrSG"),i=n("XNiG"),a=n("xG9w"),r=n("fXoL"),s=n("tk/3"),l=n("flaP");let d=(()=>{class e{constructor(e,t){this._http=e,this.roleService=t,this.tabLoadingSubject=new i.b,this.getTabLoadingObservable=this.tabLoadingSubject.asObservable(),this.isActivityApproval=e=>new Promise((t,n)=>{this._http.post("/api/opportunity/isActivityAppStarted",{oppId:e}).subscribe(e=>t(e),e=>n(e))}),this.colorConfigUrl="/api/opportunity/tabs",this.tabColor=null}setTabLoadingObservable(e){this.tabLoadingSubject.next(e)}getOpportunityTeamDetails(e){return new Promise((t,n)=>{this._http.post("/api/opportunity/getOpportunityTeamDetails",{opportunityId:e}).subscribe(e=>t(e),e=>n(e))})}updateInternalStakeholderAsPrimary(e,t,n,o,i){return new Promise((a,r)=>{this._http.post("/api/opportunity/updateInternalStakeholderAsPrimary",{stakeholderId:e,ownerType:t,opportunityId:n,practice_id:o,member:i}).subscribe(e=>a(e),e=>r(e))})}addTeamMember(e,t){return new Promise((n,o)=>{this._http.post("/api/opportunity/addOpportunityTeamMember",{opportunityId:e,member:t}).subscribe(e=>n(e),e=>o(e))})}removeMember(e){return new Promise((t,n)=>{this._http.post("/api/opportunity/removeMember",{stakeholderId:e}).subscribe(e=>t(e),e=>n(e))})}getPastStakeholders(e){return new Promise((t,n)=>{this._http.post("/api/opportunity/getPastMembers",{opportunityId:e}).subscribe(e=>t(e),e=>n(e))})}getOpportunityStakeholderMaster(e){return new Promise((t,n)=>{this._http.post("api/opportunity/getOpportunityStakeholderMaster",{stakeholderType:e}).subscribe(e=>t(e),e=>n(e))})}getNotifyFormFieldCollection(e){return this._http.post("/api/activity/getNotifyFormFieldCollection",{application_id:e})}updateNotificationStakeholeder(e){return new Promise((t,n)=>{this._http.post("api/opportunity/updateNotificationStakeholeder",{updateNotificationParams:e}).subscribe(e=>t(e),e=>n(e))})}checkEditAcessISH(){a.where(this.roleService.roles,{application_id:36,role_id:1});let e=a.where(this.roleService.roles,{application_id:36,object_id:6,operation:"Read"});return console.log("accessList ISH",e),!(e.length>0)}getTheme(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.tabColor&&null!=this.loaderConfig)return Promise.resolve({tabColor:this.tabColor,loaderConfig:this.loaderConfig});try{const e=yield this._http.get(this.colorConfigUrl).toPromise();return this.tabColor=e.tabColor||"#defaultColor",this.loaderConfig=null==e?void 0:e.loaderConfig,{tabColor:this.tabColor,loaderConfig:e.loaderConfig||!1}}catch(e){return console.error(e),{tabColor:"#79ba44"}}}))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](s.c),r["\u0275\u0275inject"](l.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},R3G1:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var o=n("xG9w"),i=n("fXoL");let a=(()=>{class e{transform(e,t,n){let i=o.findWhere(t,{field_name:e,type:n});return!!i&&!!i.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},"S/m2":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return r}));var o=n("W2II"),i=n("AmyY"),a=function(e,t,n){var i=Object(o.a)(e),a=n?i.scrollLeft():i.scrollTop(),r=n?"Width":"Height",s=i.prop("scroll".concat(r))-i.prop("client".concat(r))-a;return(0!==a||0!==s)&&(!!(0===a&&t>=0||0===s&&t<=0||a>0&&s>0)||void 0)},r=function(e,t){var n=Object(o.a)(e);return{validate:function(r){if(Object(i.j)(r)&&(s=r.target,!t||Object(o.a)(s).is(e)))return!!a(n,-r.delta,r.shiftKey)&&(r._needSkipEvent=!0,!0);var s}}}},WUSH:function(e,t,n){"use strict";n.r(t),n.d(t,"QuoteConfigModule",(function(){return Ye}));var o=n("ofXK"),i=n("bTqV"),a=n("NFeN"),r=n("Qu3c"),s=n("kmnG"),l=n("qFsG"),d=n("3Pt+"),c=n("Xa2L"),p=n("Wp6s"),u=n("d3UM"),g=n("ZzPI"),m=n("WYlB"),h=n("4Ewp"),f=n("28g/"),w=n("xcIV"),v=n("1jcm"),S=n("tyNb"),y=n("fXoL"),E=n("flaP");function C(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementStart"](0,"div",7),y["\u0275\u0275listener"]("click",(function(){y["\u0275\u0275restoreView"](e);const n=t.$implicit;return y["\u0275\u0275nextContext"](2).selectedSettings(n.link)})),y["\u0275\u0275elementStart"](1,"div",8),y["\u0275\u0275elementStart"](2,"mat-icon",9),y["\u0275\u0275text"](3),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",10),y["\u0275\u0275text"](5),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275property"]("ngClass",e.link===n.selectedItem?"sr-nav-item":"r-nav-item slide-from-down")("routerLink",e.link),y["\u0275\u0275advance"](3),y["\u0275\u0275textInterpolate1"](" ",e.icon," "),y["\u0275\u0275advance"](2),y["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function b(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div",3),y["\u0275\u0275elementStart"](1,"div",4),y["\u0275\u0275element"](2,"router-outlet"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"mat-card",5),y["\u0275\u0275template"](4,C,6,4,"div",6),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](4),y["\u0275\u0275property"]("ngForOf",e.settingsList)}}function x(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",11),y["\u0275\u0275elementStart"](1,"div",12),y["\u0275\u0275elementStart"](2,"span",13),y["\u0275\u0275text"](3,"You don't have access to any Quote Configuration ! "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",14),y["\u0275\u0275element"](5,"img",15),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]())}let _=(()=>{class e{constructor(e,t){this.route=e,this._roles=t,this.settingsList=[{label:"General Settings",icon:"description",link:"general",objectId:91001},{label:"Field Settings",icon:"nfc",link:"field",objectId:91002},{label:"UOM Settings",icon:"ad_units",link:"uom",objectId:91003},{label:"Opportunity Integration",icon:"integration_instructions",link:"opp-integration",objectId:91004},{label:"Discount, Tax Master",icon:"dataset",link:"discount-tax",objectId:91005},{label:"Position Master",icon:"dataset",link:"position",objectId:91006},{label:"Data Mapping",icon:"cached",link:"mapping",objectId:91007},{label:"Financial Document Settings",icon:"description",link:"document-settings",objectId:91008},{label:"Quote Activation Approval Settings",icon:"format_quote",link:"quote-activation-settings",objectId:91009}],this.selectedItem=null}ngOnInit(){if(this._roles.roles&&this._roles.roles.length){const e=this._roles.roles.filter(e=>910===e.application_id);if(e.length)for(let t=this.settingsList.length-1;t>=0;t--)e.find(e=>e.object_id===this.settingsList[t].objectId)||this.settingsList.splice(t,1);else this.settingsList=[]}this.route&&this.route.children.length&&(this.routeSubscription=this.route.children[0].url.subscribe(e=>{e.length&&(this.selectedItem=e[0].path)}))}selectedSettings(e){this.selectedItem=e}ngOnDestroy(){this.routeSubscription&&this.routeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](S.a),y["\u0275\u0275directiveInject"](E.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],decls:3,vars:2,consts:[[1,"container-fluid","quote-config-style"],["class","pt-4 layout-wrapper",4,"ngIf"],["style","margin-top: 30vh;",4,"ngIf"],[1,"pt-4","layout-wrapper"],[1,"edit-main"],[1,"edit-nav"],["class","py-3 px-3 slide-from-down row",3,"ngClass","routerLink","click",4,"ngFor","ngForOf"],[1,"py-3","px-3","slide-from-down","row",3,"ngClass","routerLink","click"],[1,"mt-1","pl-0","col-2","icon"],[2,"font-size","25px !important","color","#000000ad !important"],[1,"col-8","px-2","my-auto"],[2,"margin-top","30vh"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","18px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-3","pb-2"],["src","https://assets.kebs.app/images/under_png.png","height","250","width","400",1,"mt-2","mb-2"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,b,5,1,"div",1),y["\u0275\u0275template"](2,x,6,0,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.settingsList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",0==t.settingsList.length))},directives:[o.NgIf,S.l,p.a,o.NgForOf,o.NgClass,S.h,a.a],styles:[".quote-config-style[_ngcontent-%COMP%]   .layout-wrapper[_ngcontent-%COMP%]{display:flex;height:90vh}.quote-config-style[_ngcontent-%COMP%]   .edit-nav[_ngcontent-%COMP%]{width:20vw;overflow-y:scroll;background-color:#fff;color:#000}.quote-config-style[_ngcontent-%COMP%]   .edit-main[_ngcontent-%COMP%]{width:75vw;padding:5px;overflow-y:scroll!important}.quote-config-style[_ngcontent-%COMP%]   .sr-nav-item[_ngcontent-%COMP%]{background-color:#f5f5f5!important;font-weight:600;border-left:solid #545352;border-radius:2px;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.quote-config-style[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]{font-weight:400;cursor:pointer}.quote-config-style[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]:hover{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:box-shadow .3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.quote-config-style[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.quote-config-style[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})();var O=n("XNiG"),R=n("1G5W"),L=n("jr6c"),D=n("0BBf"),I=n("1A3m"),M=n("TmG/"),T=n("FKr1");function P(e,t){if(1&e&&y["\u0275\u0275element"](0,"app-input-search",12),2&e){const e=y["\u0275\u0275nextContext"]().$implicit;y["\u0275\u0275property"]("list",e.masterData)("hideMatLabel",!0)("hasNoneOption",!1)("placeholder","Search "+e.label)("formControl",e.configValue)}}function k(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"mat-option",16),y["\u0275\u0275text"](1),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;y["\u0275\u0275property"]("value",e.id),y["\u0275\u0275advance"](1),y["\u0275\u0275textInterpolate"](e.name)}}function q(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"mat-form-field",13),y["\u0275\u0275elementStart"](1,"mat-select",14),y["\u0275\u0275template"](2,k,2,2,"mat-option",15),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=y["\u0275\u0275nextContext"]().$implicit;y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("formControl",e.configValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngForOf",e.masterData)}}function A(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"mat-option",16),y["\u0275\u0275text"](1),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;y["\u0275\u0275property"]("value",e.id),y["\u0275\u0275advance"](1),y["\u0275\u0275textInterpolate"](e.name)}}function F(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"mat-form-field",13),y["\u0275\u0275elementStart"](1,"mat-select",17),y["\u0275\u0275template"](2,A,2,2,"mat-option",15),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=y["\u0275\u0275nextContext"]().$implicit;y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("formControl",e.configValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngForOf",e.masterData)}}function j(e,t){1&e&&(y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275text"](1," Save "),y["\u0275\u0275elementContainerEnd"]())}function V(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",18),y["\u0275\u0275element"](1,"mat-spinner",19),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}function U(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275elementStart"](2,"div",4),y["\u0275\u0275text"](3),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",5),y["\u0275\u0275template"](5,P,1,5,"app-input-search",6),y["\u0275\u0275template"](6,q,3,2,"mat-form-field",7),y["\u0275\u0275template"](7,F,3,2,"mat-form-field",7),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](8,"div",8),y["\u0275\u0275elementStart"](9,"button",9),y["\u0275\u0275listener"]("click",(function(){y["\u0275\u0275restoreView"](e);const n=t.$implicit;return y["\u0275\u0275nextContext"]().saveConfig(n)})),y["\u0275\u0275template"](10,j,2,0,"ng-container",10),y["\u0275\u0275template"](11,V,2,1,"ng-template",null,11,y["\u0275\u0275templateRefExtractor"]),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=y["\u0275\u0275reference"](12);y["\u0275\u0275advance"](3),y["\u0275\u0275textInterpolate1"](" ",e.label," "),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("ngIf",e.isSearchRequired&&!e.isMultiple),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",!e.isSearchRequired&&!e.isMultiple),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",e.isMultiple),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("disabled",e.isDataBeingSaved)("ngClass",e.isDataBeingSaved?"preview-btn":"save-btn"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",!e.isDataBeingSaved)("ngIfElse",n)}}function N(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",20),y["\u0275\u0275element"](1,"mat-spinner",19),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}let B=(()=>{class e{constructor(e,t,n){this._quoteService=e,this._quoteMasterDataService=t,this._toaster=n,this._onDestroy=new O.b,this.configObject={label:null,configName:null,configValue:null,masterData:[],isMultiple:!1,isSearchRequired:!0,isDataBeingSaved:!1},this.configList=[],this.quoteConfigurationList=[],this.currencyList=[],this.calendarList=[],this.workScheduleList=[],this.booleanList=[{id:!0,name:"Yes"},{id:!1,name:"No"}],this.conversionTypeList=[],this.helpTopicList=[],this.isConfigLoading=!0,this.getQuoteConfiguration=()=>{this.isConfigLoading=!0,this._quoteService.getQuoteConfiguration(["quote_enabled","quote_currency","quote_calendar_id","quote_work_schedule_id","quote_currency_conversion_type_id","quote_help_topic_id","quote_service_division_mapping_enabled","quote_work_location_entity_mapping_enabled"]).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data&&e.data.length?(this.quoteConfigurationList=e.data,this.initializeMasterData()):this._toaster.showError("Error","Error in getting Quote Configuration",3e3)},e=>{this.isConfigLoading=!1,console.log(e),this._toaster.showError("Error","Error in getting Quote configuration",3e3)})},this.initializeMasterData=()=>{Promise.all([this.getCurrencyList(),this.getCalendarList(),this.getWorkScheduleList(),this.getConversionTypeList(),this.getHelpTopicList()]).then(e=>{for(const t of this.quoteConfigurationList)t&&t.quote_config_name&&t.hasOwnProperty("quote_config_value")&&this.patchConfigInList(t.quote_config_name,t.quote_config_value);this.isConfigLoading=!1})},this.patchConfigInList=(e,t)=>{const n=Object.assign({},this.configObject);switch(n.configValue=new d.j(null),e){case"quote_currency":n.label="Quote Rate Currency",n.configName=e,n.configValue.patchValue(t||null),n.masterData=this.currencyList;break;case"quote_calendar_id":n.label="Quote Calendar",n.configName=e,n.configValue.patchValue(t||null),n.masterData=this.calendarList;break;case"quote_work_schedule_id":n.label="Quote Work Schedule",n.configName=e,n.configValue.patchValue(t||null),n.masterData=this.workScheduleList;break;case"quote_currency_conversion_type_id":n.label="Quote Currency Conversion Type",n.configName=e,n.configValue.patchValue(t||null),n.masterData=this.conversionTypeList;break;case"quote_help_topic_id":n.label="Quote Help Topic",n.configName=e,n.configValue.patchValue(t||null),n.masterData=this.helpTopicList;break;case"quote_enabled":n.label="Quote Enabled",n.configName=e,n.configValue.patchValue(t||!1),n.masterData=this.booleanList,n.isSearchRequired=!1;break;case"quote_service_division_mapping_enabled":n.label="Quote Service <-> Division Mapping Enabled",n.configName=e,n.configValue.patchValue(t||!1),n.masterData=this.booleanList,n.isSearchRequired=!1;break;case"quote_work_location_entity_mapping_enabled":n.label="Quote Work Location <-> Entity Mapping Enabled",n.configName=e,n.configValue.patchValue(t||!1),n.masterData=this.booleanList,n.isSearchRequired=!1}n.configName&&this.configList.push(n)},this.getCurrencyList=()=>new Promise((e,t)=>{this._quoteMasterDataService.currency.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.currencyList=t,e(!0)})}),this.getCalendarList=()=>new Promise((e,t)=>{this._quoteMasterDataService.calendar.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.calendarList=t,e(!0)})}),this.getWorkScheduleList=()=>new Promise((e,t)=>{this._quoteMasterDataService.workSchedule.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.workScheduleList=t,e(!0)})}),this.getConversionTypeList=()=>new Promise((e,t)=>{this._quoteMasterDataService.conversionType.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.conversionTypeList=t,e(!0)})}),this.getHelpTopicList=()=>new Promise((e,t)=>{this._quoteMasterDataService.helpTopic.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.helpTopicList=t,e(!0)})}),this.saveConfig=e=>{const t=e.configValue.value;if(null===t||""===t||t instanceof Array&&0==t.length)return this._toaster.showError("Error","Kindly Enter Valid Config !",2e3);e.isDataBeingSaved=!0,this._quoteService.updateQuoteConfiguration([{config_name:e.configName,config_value:t}]).pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType?this._toaster.showSuccess("Updated",e.label+" Configuration updated Successfully !",2e3):this._toaster.showError("Error","Error in updating "+e.label,3e3),e.isDataBeingSaved=!1},t=>{console.log(t),e.isDataBeingSaved=!1,this._toaster.showError("Error","Error in updating Quote configuration",3e3)})}}ngOnInit(){this.getQuoteConfiguration()}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](D.a),y["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-general-config"]],decls:3,vars:2,consts:[[1,"container-fluid","quote-general-settings-style"],[4,"ngFor","ngForOf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"row","d-flex","align-items-baseline","pb-2"],[1,"col-4","label"],[1,"col-4"],["class","form-field-class",3,"list","hideMatLabel","hasNoneOption","placeholder","formControl",4,"ngIf"],["class","form-field-class","appearance","outline",4,"ngIf"],[1,"col-2","d-flex","justify-content-center"],["mat-stroked-button","",1,"ml-2","mt-2",3,"disabled","ngClass","click"],[4,"ngIf","ngIfElse"],["showSpinner",""],[1,"form-field-class",3,"list","hideMatLabel","hasNoneOption","placeholder","formControl"],["appearance","outline",1,"form-field-class"],[3,"formControl"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["multiple","",3,"formControl"],["matTooltip","Saving Data",1,"d-flex","justify-content-center"],[2,"color","#cf0000",3,"diameter"],[1,"row","w-100","justify-content-center","mt-5"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,U,13,8,"ng-container",1),y["\u0275\u0275template"](2,N,2,1,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngForOf",t.configList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgForOf,o.NgIf,i.a,o.NgClass,M.a,d.v,d.k,s.c,u.c,T.p,r.a,c.c],styles:[".quote-general-settings-style[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{width:90%;font-size:12px}.quote-general-settings-style[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{width:90%;font-size:12px!important}.quote-general-settings-style[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{color:#ee4961;font-weight:500}.quote-general-settings-style[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;font-size:12px!important;line-height:30px;border-radius:4px;height:30px}.quote-general-settings-style[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:30px;border-radius:4px;height:30px}"]}),e})();function Q(e,t){1&e&&(y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275text"](1," Save "),y["\u0275\u0275elementContainerEnd"]())}function z(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",18),y["\u0275\u0275element"](1,"mat-spinner",19),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}function W(e,t){1&e&&(y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"mat-form-field",22),y["\u0275\u0275element"](2,"input",30),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]())}function G(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",31),y["\u0275\u0275text"](1,"-"),y["\u0275\u0275elementEnd"]())}function K(e,t){if(1&e&&(y["\u0275\u0275elementContainerStart"](0,20),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275elementStart"](2,"div",21),y["\u0275\u0275text"](3),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",12),y["\u0275\u0275elementStart"](5,"mat-form-field",22),y["\u0275\u0275element"](6,"input",23),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](7,"div",12),y["\u0275\u0275elementStart"](8,"mat-form-field",22),y["\u0275\u0275element"](9,"input",24),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](10,"div",12),y["\u0275\u0275elementStart"](11,"mat-form-field",22),y["\u0275\u0275element"](12,"input",25),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](13,"div",12),y["\u0275\u0275template"](14,W,3,0,"ng-container",7),y["\u0275\u0275template"](15,G,2,0,"ng-template",null,26,y["\u0275\u0275templateRefExtractor"]),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](17,"div",27),y["\u0275\u0275element"](18,"mat-slide-toggle",28),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](19,"div",27),y["\u0275\u0275element"](20,"mat-slide-toggle",29),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=y["\u0275\u0275reference"](16);y["\u0275\u0275property"]("formGroup",e),y["\u0275\u0275advance"](3),y["\u0275\u0275textInterpolate1"](" ",e.get("key").value," "),y["\u0275\u0275advance"](11),y["\u0275\u0275property"]("ngIf","number"===e.get("fieldType").value||"display"===e.get("fieldType").value)("ngIfElse",n)}}function $(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275elementStart"](2,"div",4),y["\u0275\u0275text"](3," Field Configuration "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",5),y["\u0275\u0275elementStart"](5,"button",6),y["\u0275\u0275listener"]("click",(function(){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().saveFieldConfig()})),y["\u0275\u0275template"](6,Q,2,0,"ng-container",7),y["\u0275\u0275template"](7,z,2,1,"ng-template",null,8,y["\u0275\u0275templateRefExtractor"]),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](9,"div",9),y["\u0275\u0275elementStart"](10,"div",10),y["\u0275\u0275elementStart"](11,"div",11),y["\u0275\u0275elementStart"](12,"div",12),y["\u0275\u0275text"](13," Field Key "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](14,"div",12),y["\u0275\u0275text"](15," Field Label "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](16,"div",12),y["\u0275\u0275text"](17," Column Width "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](18,"div",12),y["\u0275\u0275text"](19," Position "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](20,"div",13),y["\u0275\u0275elementStart"](21,"span"),y["\u0275\u0275text"](22,"Decimal Part"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](23,"mat-icon",14),y["\u0275\u0275text"](24,"info"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](25,"div",15),y["\u0275\u0275text"](26," Visible "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](27,"div",15),y["\u0275\u0275text"](28," Active "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](29,"div",16),y["\u0275\u0275template"](30,K,21,4,"ng-container",17),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275reference"](8),t=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](5),y["\u0275\u0275property"]("disabled",t.isFieldDataBeingSaved)("ngClass",t.isFieldDataBeingSaved?"preview-btn":"save-btn"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",!t.isFieldDataBeingSaved)("ngIfElse",e),y["\u0275\u0275advance"](24),y["\u0275\u0275property"]("ngForOf",t.quoteFieldArr.controls)}}function H(e,t){1&e&&(y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275text"](1," Save "),y["\u0275\u0275elementContainerEnd"]())}function X(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",18),y["\u0275\u0275element"](1,"mat-spinner",19),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}function J(e,t){if(1&e&&(y["\u0275\u0275elementContainerStart"](0,20),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275elementStart"](2,"div",21),y["\u0275\u0275text"](3),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",32),y["\u0275\u0275elementStart"](5,"mat-form-field",22),y["\u0275\u0275element"](6,"input",23),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](7,"div",13),y["\u0275\u0275element"](8,"mat-slide-toggle",29),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;y["\u0275\u0275property"]("formGroup",e),y["\u0275\u0275advance"](3),y["\u0275\u0275textInterpolate1"](" ",e.get("key").value," ")}}function Y(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275elementStart"](2,"div",4),y["\u0275\u0275text"](3," Action Button Configuration "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](4,"div",5),y["\u0275\u0275elementStart"](5,"button",6),y["\u0275\u0275listener"]("click",(function(){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().saveActionBtnConfig()})),y["\u0275\u0275template"](6,H,2,0,"ng-container",7),y["\u0275\u0275template"](7,X,2,1,"ng-template",null,8,y["\u0275\u0275templateRefExtractor"]),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](9,"div",9),y["\u0275\u0275elementStart"](10,"div",10),y["\u0275\u0275elementStart"](11,"div",11),y["\u0275\u0275elementStart"](12,"div",12),y["\u0275\u0275text"](13," Button Key "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](14,"div",32),y["\u0275\u0275text"](15," Button Label "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](16,"div",12),y["\u0275\u0275text"](17," Active "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](18,"div",33),y["\u0275\u0275template"](19,J,9,2,"ng-container",17),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275reference"](8),t=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](5),y["\u0275\u0275property"]("disabled",t.isBtnDataBeingSaved)("ngClass",t.isBtnDataBeingSaved?"preview-btn":"save-btn"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",!t.isBtnDataBeingSaved)("ngIfElse",e),y["\u0275\u0275advance"](13),y["\u0275\u0275property"]("ngForOf",t.quoteActionBtnArr.controls)}}function Z(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",34),y["\u0275\u0275element"](1,"mat-spinner",19),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}let ee=(()=>{class e{constructor(e,t,n){this._quoteService=e,this._toaster=t,this.fb=n,this._onDestroy=new O.b,this.isConfigLoading=!0,this.isFieldDataBeingSaved=!1,this.isBtnDataBeingSaved=!1,this.fieldConfigName="quote_field_config",this.actionBtnConfigName="quote_action_button_config",this.quoteFieldArr=new d.g([]),this.quoteActionBtnArr=new d.g([]),this.getQuoteConfiguration=()=>{const e=[this.fieldConfigName,this.actionBtnConfigName];this.isConfigLoading=!0,this._quoteService.getQuoteConfiguration(e).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{if("S"==e.messType&&e.data&&e.data.length){for(const t of e.data)if(t&&t.quote_config_name&&t.hasOwnProperty("quote_config_value"))if(t.quote_config_name===this.fieldConfigName){const e=t.quote_config_value;for(const t of e)this.quoteFieldArr.push(this.fb.group({key:[t.key],columnName:[t.columnName],label:[t.label,d.H.required],fieldType:[t.fieldType],position:[t.position],col:[t.col],isForManpowerOnly:[t.isForManpowerOnly],isMandatory:[t.isMandatory],isVisible:[t.isVisible],isActive:[t.isActive],enableVariableName:[t.enableVariableName],decimalPart:[t.decimalPart],role_access_restriction:[t.role_access_restriction],map_key:[t.map_key],map_value:[t.map_value],migration_column_name:[t.migration_column_name],select_column_name:[t.select_column_name],table_name:[t.table_name]}))}else if(t.quote_config_name===this.actionBtnConfigName){const e=t.quote_config_value;for(const t of e)this.quoteActionBtnArr.push(this.fb.group({id:[t.id],key:[t.key],label:[t.label,d.H.required],isActive:[t.isActive]}))}for(const e of this.quoteFieldArr.controls)if("dropdown"===e.get("fieldType").value&&e.get("decimalPart").disable(),"positionId"===e.get("key").value){e.get("position").disable(),e.get("isVisible").disable(),e.get("isActive").disable();break}}else this._toaster.showError("Error","Error in getting Quote Configuration",3e3);this.isConfigLoading=!1},e=>{this.isConfigLoading=!1,console.log(e),this._toaster.showError("Error","Error in getting Quote configuration",3e3)})},this.saveFieldConfig=()=>{this.isFieldDataBeingSaved=!0;const e=[{config_name:this.fieldConfigName,config_value:this.quoteFieldArr.getRawValue()}];this.updateQuoteConfig(e)},this.saveActionBtnConfig=()=>{this.isBtnDataBeingSaved=!0,this.updateQuoteConfig([{config_name:this.actionBtnConfigName,config_value:this.quoteActionBtnArr.value}])},this.updateQuoteConfig=e=>{this._quoteService.updateQuoteConfiguration(e).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Updated","Configuration updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Configuration",3e3),this.isFieldDataBeingSaved=!1,this.isBtnDataBeingSaved=!1},e=>{console.log(e),this.isFieldDataBeingSaved=!1,this.isBtnDataBeingSaved=!1,this._toaster.showError("Error","Error in updating Quote Configuration",3e3)})}}ngOnInit(){this.getQuoteConfiguration()}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a),y["\u0275\u0275directiveInject"](d.i))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-field-config"]],decls:4,vars:3,consts:[[1,"container-fluid","quote-field-settings-style"],[4,"ngIf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"row","pt-2","pb-2"],[1,"col-10","d-flex","justify-content-center","align-items-center"],[1,"col-2","d-flex","justify-content-center"],["matTooltip","Save Field Config","mat-stroked-button","",1,"ml-2","mt-2",3,"disabled","ngClass","click"],[4,"ngIf","ngIfElse"],["showSpinner",""],[1,"card"],[1,"card-body","p-2"],[1,"row","pt-2","pb-2","header-class"],[1,"col-2"],[1,"col-2","d-flex","align-items-center"],["matTooltip","If Decimal Part entered 0 then decimal won't be allowed for the particular field!",1,"ml-1","d-flex","align-items-center","justify-content-center",2,"font-size","15px"],[1,"col-1"],[2,"height","65vh","overflow-x","hidden"],[3,"formGroup",4,"ngFor","ngForOf"],["matTooltip","Saving Data",1,"d-flex","justify-content-center"],[2,"color","#cf0000",3,"diameter"],[3,"formGroup"],[1,"col-2","d-flex","align-items-center","key-class"],["appearance","outline",1,"form-field-class"],["matInput","","placeholder","Enter label","formControlName","label"],["type","number","min","1","max","12","matInput","","placeholder","Enter Columns","formControlName","col"],["type","number","min","0","max","14","matInput","","placeholder","Enter Position","formControlName","position"],["showDash",""],[1,"col-1","d-flex","align-items-center"],["formControlName","isVisible"],["formControlName","isActive"],["type","number","min","0","max","16","matInput","","placeholder","Enter Decimal Part","formControlName","decimalPart"],[1,"dash-class"],[1,"col-3"],[2,"height","45vh","overflow-x","hidden"],[1,"row","w-100","justify-content-center","mt-5"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,$,31,5,"ng-container",1),y["\u0275\u0275template"](2,Y,20,5,"ng-container",1),y["\u0275\u0275template"](3,Z,2,1,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",null==t.quoteFieldArr?null:t.quoteFieldArr.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",null==t.quoteActionBtnArr?null:t.quoteActionBtnArr.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgIf,i.a,r.a,o.NgClass,a.a,o.NgForOf,c.c,d.w,d.n,s.c,l.b,d.e,d.v,d.l,d.A,v.a],styles:[".quote-field-settings-style[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;font-size:12px!important;line-height:30px;border-radius:4px;height:30px}.quote-field-settings-style[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:30px;border-radius:4px;height:30px}.quote-field-settings-style[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]{overflow:hidden;color:#5f6c81;text-overflow:ellipsis;white-space:nowrap;font-size:12px;font-weight:600;padding:8px 0}.quote-field-settings-style[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{width:80%;font-size:12px}.quote-field-settings-style[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{width:90%;font-size:13px!important}.quote-field-settings-style[_ngcontent-%COMP%]   .key-class[_ngcontent-%COMP%]{color:#ef4a61;font-weight:500;font-size:13px}.quote-field-settings-style[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:10px!important;height:10px!important;transform:translate(50%,50%)}.quote-field-settings-style[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:16px!important}.quote-field-settings-style[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-2px!important}.quote-field-settings-style[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:#f27a6c}.quote-field-settings-style[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}"]}),e})();var te=n("6t9p");function ne(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275text"](2," Quote Unit of Measurement "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowInserting",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateUnitRow(t,!0)}))("onRowUpdating",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateUnitRow(t)}))("onRowInserted",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().unitRowInserted(t)}))("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().unitRowUpdated(t)})),y["\u0275\u0275element"](4,"dxo-paging",5),y["\u0275\u0275element"](5,"dxo-editing",6),y["\u0275\u0275element"](6,"dxo-sorting",7),y["\u0275\u0275element"](7,"dxo-filter-row",8),y["\u0275\u0275element"](8,"dxo-header-filter",9),y["\u0275\u0275element"](9,"dxo-scrolling",10),y["\u0275\u0275element"](10,"dxi-column",11),y["\u0275\u0275element"](11,"dxi-column",12),y["\u0275\u0275element"](12,"dxi-column",13),y["\u0275\u0275elementStart"](13,"dxi-column",14),y["\u0275\u0275element"](14,"dxo-header-filter",15),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](15,"dxi-column",16),y["\u0275\u0275element"](16,"dxo-lookup",17),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](17,"dxi-column",18),y["\u0275\u0275element"](18,"dxo-lookup",17),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.unitList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",e.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",7)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",180)("allowSorting",!0)("setCellValue",e.setUnitValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.customizeHeaderFilterData),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",120)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList)}}const oe=function(){return{min:0,max:100}};function ie(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",19),y["\u0275\u0275text"](2," Custom Percentage "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowInserting",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateCustomPerRow(t,!0)}))("onRowUpdating",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateCustomPerRow(t)}))("onRowInserted",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().customPerRowInserted(t)}))("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().customPerRowUpdated(t)})),y["\u0275\u0275element"](4,"dxo-paging",5),y["\u0275\u0275element"](5,"dxo-editing",6),y["\u0275\u0275element"](6,"dxo-sorting",7),y["\u0275\u0275element"](7,"dxo-filter-row",8),y["\u0275\u0275element"](8,"dxo-header-filter",9),y["\u0275\u0275element"](9,"dxo-scrolling",10),y["\u0275\u0275element"](10,"dxi-column",20),y["\u0275\u0275element"](11,"dxi-column",21),y["\u0275\u0275element"](12,"dxi-column",22),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.customPerList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",e.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",5)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0)("setCellValue",e.setPercentageValue)("editorOptions",y["\u0275\u0275pureFunction0"](24,oe))}}function ae(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",23),y["\u0275\u0275element"](1,"mat-spinner",24),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}let re=(()=>{class e{constructor(e,t){this._quoteService=e,this._toaster=t,this._onDestroy=new O.b,this.isConfigLoading=!1,this.unitList=[],this.customPerList=[],this.booleanList=[{id:1,name:"Yes"},{id:0,name:"No"}],this.columnResizingMode="widget",this.getUOM=(e=!1)=>{this.isConfigLoading=!0,this._quoteService.getUOM().pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&t.data&&t.data.length?(this.unitList=t.data,e?this.isConfigLoading=!1:this.getCustomPercentage()):this._toaster.showError("Error","Error in getting Quote UOM",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote UOM",3e3)})},this.getCustomPercentage=()=>{this._quoteService.getCustomPercentage().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data&&e.data.length?this.customPerList=e.data:this._toaster.showError("Error","Error in getting Quote Custom % List",3e3),this.isConfigLoading=!1},e=>{this.isConfigLoading=!1,console.log(e),this._toaster.showError("Error","Error in getting Quote Custom % List",3e3)})},this.validateUnitRow=(e,t=!1)=>{const n=t?e.data:Object.assign(Object.assign({},e.oldData),e.newData);return n.name?n.unit_value_in_hrs?void(e.cancel=!1):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Unit Value in Hours !",2e3)):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Unit Name !",2e3))},this.unitRowInserted=e=>{this._quoteService.insertQuoteUOM(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Quote Unit configuration added Successfully !",2e3),this.getUOM(!0)):this._toaster.showError("Error","Error in inserting Quote Unit data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in inserting Quote Unit data",3e3)})},this.unitRowUpdated=e=>{this._quoteService.updateQuoteUOM(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Unit configuration updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Quote Unit data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Unit data",3e3)})},this.validateCustomPerRow=(e,t=!1)=>{const n=t?e.data:Object.assign(Object.assign({},e.oldData),e.newData);return n.label?n.value?void(e.cancel=!1):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Percentage Value !",2e3)):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Percentage label !",2e3))},this.customPerRowInserted=e=>{this._quoteService.insertCustomPercentage(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Custom Percentage configuration added Successfully !",2e3),this.getCustomPercentage()):this._toaster.showError("Error","Error in inserting Custom Percentage data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in inserting Custom Percentage data",3e3)})},this.customPerRowUpdated=e=>{this._quoteService.updateCustomPercentage(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Custom Percentage configuration updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Quote Custom Percentage data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Custom Percentage data",3e3)})}}ngOnInit(){this.getUOM()}setPercentageValue(e,t,n){e.value=t<0||""===t||null===t?0:parseInt(t)}customizeHeaderFilterData(e){e.dataSource.postProcess=e=>e.reduce((e,t)=>(e.find(e=>e.text.toLowerCase()==t.text.toLowerCase())||e.push(t),e),[])}setUnitValue(e,t,n){e.unit_value_in_hrs=t<0||""===t||null===t?1:parseInt(t)}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-uom-config"]],decls:4,vars:3,consts:[[1,"container-fluid","quote-uom-styles"],[4,"ngIf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"row","col-12","label"],["id","gridContainer","keyExpr","id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserting","onRowUpdating","onRowInserted","onRowUpdated"],[3,"pageSize","pageIndex"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible","applyFilter"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","id","caption","Unit Id",3,"width","allowSorting","allowEditing"],["dataField","name","caption","Unit name",3,"width","allowSorting"],["dataField","unit_value_in_hrs","dataType","number","caption","Unit Value(in Hours)",3,"width","allowSorting","setCellValue"],["dataField","unit_suffix","caption","Unit Suffix",3,"width","allowSorting"],[3,"dataSource"],["dataField","is_for_position","caption","Unit for Position",3,"width","allowSorting"],["displayExpr","name","valueExpr","id",3,"dataSource"],["dataField","is_value_fixed","caption","Unit Fixed",3,"width","allowSorting"],[1,"row","col-12","pt-4","label"],["dataField","id","caption","Id",3,"width","allowSorting","allowEditing"],["dataField","label","caption","Percentage label",3,"width","allowSorting"],["dataField","value","dataType","number","caption","Percentage Value",3,"width","allowSorting","setCellValue","editorOptions"],[1,"row","w-100","justify-content-center","mt-5"],[2,"color","#cf0000",3,"diameter"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,ne,19,32,"ng-container",1),y["\u0275\u0275template"](2,ie,13,25,"ng-container",1),y["\u0275\u0275template"](3,ae,2,1,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.unitList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.customPerList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgIf,g.a,te.od,te.Qb,te.ae,te.dc,te.Cc,te.Jd,te.g,te.Wc,c.c],styles:[".quote-uom-styles[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#f27a6c;font-size:14px;line-height:24px;letter-spacing:.02em;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}"]}),e})();function se(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275text"](2," Quote Opportunity Status Configuration "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().statusRowUpdated(t)}))("onEditorPreparing",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onRowPrepared",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onRowPrepared(t)})),y["\u0275\u0275element"](4,"dxo-editing",5),y["\u0275\u0275element"](5,"dxo-sorting",6),y["\u0275\u0275element"](6,"dxo-filter-row",7),y["\u0275\u0275element"](7,"dxo-header-filter",8),y["\u0275\u0275element"](8,"dxo-scrolling",9),y["\u0275\u0275element"](9,"dxi-column",10),y["\u0275\u0275element"](10,"dxi-column",11),y["\u0275\u0275elementStart"](11,"dxi-column",12),y["\u0275\u0275element"](12,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](13,"dxi-column",14),y["\u0275\u0275element"](14,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](15,"dxi-column",15),y["\u0275\u0275element"](16,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](17,"dxi-column",16),y["\u0275\u0275element"](18,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](19,"dxi-column",17),y["\u0275\u0275element"](20,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](21,"dxi-column",18),y["\u0275\u0275element"](22,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](23,"dxi-column",19),y["\u0275\u0275element"](24,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](25,"dxi-column",20),y["\u0275\u0275element"](26,"dxi-button",21),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.statusConfigList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode","widget"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!1),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",e.shouldShowEditButton)}}function le(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",22),y["\u0275\u0275element"](1,"mat-spinner",23),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}let de=(()=>{class e{constructor(e,t){this._quoteService=e,this._toaster=t,this._onDestroy=new O.b,this.isConfigLoading=!1,this.statusIdsNoEdit=[21],this.statusConfigList=[],this.booleanList=[{id:1,name:"Yes"},{id:0,name:"No"}],this.getOppStatusConfig=()=>{this.isConfigLoading=!0,this._quoteService.getOppStatusConfig().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data&&e.data.length?this.statusConfigList=e.data:this._toaster.showError("Error","Error in getting Quote Opportunity Status Config",3e3),this.isConfigLoading=!1},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Opportunity Status Config",3e3)})},this.statusRowUpdated=e=>{this._quoteService.updateOppStatusConfig(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Opportunity Status Config updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",3e3)})},this.shouldShowEditButton=e=>this.isEditableRow(e.row.data)}ngOnInit(){this.getNonEditableStatus(),this.getOppStatusConfig()}onEditorPreparing(e){console.log(e)}isEditableRow(e){return console.log(e),!this.statusIdsNoEdit.includes(e.status_id)}onEditingStart(e){this.isEditableRow(e.data)||(e.cancel=!0)}onRowPrepared(e){"data"===e.rowType&&this.isEditableRow(e.data)&&e.rowElement.classList.add("editable-row")}getNonEditableStatus(){this._quoteService.getNonEditableStatus().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data&&e.data.length?this.statusIdsNoEdit=e.data:this._toaster.showError("Error","Error in getting Quote Opportunity Status Config",3e3),this.isConfigLoading=!1},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Opportunity Status Config",3e3)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-opp-status-config"]],decls:3,vars:2,consts:[[1,"container-fluid","quote-opp-status-config-styles"],[4,"ngIf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"row","col-12","label"],["id","gridContainer","keyExpr","status_id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowUpdated","onEditorPreparing","onRowPrepared"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible","applyFilter"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","status_id","caption","Status Id",3,"width","allowSorting","allowEditing"],["dataField","status_name","caption","Status name",3,"width","allowSorting","allowEditing"],["dataField","is_quote_mand","caption","Quote Mandatory",3,"width","allowSorting"],["displayExpr","name","valueExpr","id",3,"dataSource"],["dataField","show_msg","caption","Quote Warning",3,"width","allowSorting"],["dataField","is_opp_value_editable","caption","Opportunity Value Editable",3,"width","allowSorting"],["dataField","update_quote_val_to_opp","caption","Update Quote Value to Opportunity",3,"width","allowSorting"],["dataField","confirm_before_update","caption","Ask Confirmation Before Update",3,"width","allowSorting"],["dataField","update_opp_value_btn_in_quote","caption","Enable Value Sync Button(Opportunity & Quote Value)",3,"width","allowSorting"],["dataField","is_quote_editable","caption","Quote Editable",3,"width","allowSorting"],["type","buttons"],["name","edit",3,"visible"],[1,"row","w-100","justify-content-center","mt-5"],[2,"color","#cf0000",3,"diameter"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,se,27,41,"ng-container",1),y["\u0275\u0275template"](2,le,2,1,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.statusConfigList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgIf,g.a,te.Qb,te.ae,te.dc,te.Cc,te.Jd,te.g,te.Wc,te.d,c.c],styles:[".quote-opp-status-config-styles[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#f27a6c;font-size:14px;line-height:24px;letter-spacing:.02em;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}"]}),e})();const ce=function(){return{min:0,max:100}};function pe(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275text"](2," Discount Data "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowInserting",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateDiscountRow(t,!0)}))("onRowUpdating",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateDiscountRow(t)}))("onRowInserted",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().discountRowInserted(t)}))("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().discountRowUpdated(t)})),y["\u0275\u0275element"](4,"dxo-paging",5),y["\u0275\u0275element"](5,"dxo-editing",6),y["\u0275\u0275element"](6,"dxo-sorting",7),y["\u0275\u0275element"](7,"dxo-filter-row",8),y["\u0275\u0275element"](8,"dxo-header-filter",9),y["\u0275\u0275element"](9,"dxo-scrolling",10),y["\u0275\u0275element"](10,"dxi-column",11),y["\u0275\u0275element"](11,"dxi-column",12),y["\u0275\u0275element"](12,"dxi-column",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.discountList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",e.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",7)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0)("setCellValue",e.setDiscountPercentage)("editorOptions",y["\u0275\u0275pureFunction0"](24,ce))}}function ue(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",14),y["\u0275\u0275text"](2," Tax Data "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowInserting",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateTaxRow(t,!0)}))("onRowUpdating",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().validateTaxRow(t)}))("onRowInserted",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().taxRowInserted(t)}))("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().taxRowUpdated(t)})),y["\u0275\u0275element"](4,"dxo-paging",5),y["\u0275\u0275element"](5,"dxo-editing",6),y["\u0275\u0275element"](6,"dxo-sorting",7),y["\u0275\u0275element"](7,"dxo-filter-row",8),y["\u0275\u0275element"](8,"dxo-header-filter",9),y["\u0275\u0275element"](9,"dxo-scrolling",10),y["\u0275\u0275element"](10,"dxi-column",15),y["\u0275\u0275element"](11,"dxi-column",16),y["\u0275\u0275element"](12,"dxi-column",17),y["\u0275\u0275element"](13,"dxi-column",18),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.taxList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",e.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",5)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0)("setCellValue",e.setTaxPercentage)("editorOptions",y["\u0275\u0275pureFunction0"](26,ce))}}function ge(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",19),y["\u0275\u0275element"](1,"mat-spinner",20),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}let me=(()=>{class e{constructor(e,t){this._quoteService=e,this._toaster=t,this._onDestroy=new O.b,this.isConfigLoading=!1,this.discountList=[],this.taxList=[],this.columnResizingMode="widget",this.getDiscountDetails=(e=!1)=>{this.isConfigLoading=!0,this._quoteService.getDiscountDetails().pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&t.data&&t.data.length?(this.discountList=t.data,e?this.isConfigLoading=!1:this.getTaxDetails()):this._toaster.showError("Error","Error in getting Discount details",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Discount details",3e3)})},this.getTaxDetails=()=>{this._quoteService.getTaxDetails().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data&&e.data.length?this.taxList=e.data:this._toaster.showError("Error","Error in getting tax details",3e3),this.isConfigLoading=!1},e=>{this.isConfigLoading=!1,console.log(e),this._toaster.showError("Error","Error in getting tax details",3e3)})},this.validateDiscountRow=(e,t=!1)=>{const n=t?e.data:Object.assign(Object.assign({},e.oldData),e.newData);return n.name?n.percentage?void(e.cancel=!1):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Discount Percentage !",2e3)):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Discount Name !",2e3))},this.discountRowInserted=e=>{this._quoteService.insertDiscount(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Discount Configuration added Successfully !",2e3),this.getDiscountDetails(!0)):this._toaster.showError("Error","Error in inserting Discount data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in inserting Quote Unit data",3e3)})},this.discountRowUpdated=e=>{this._quoteService.updateDiscount(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Discount configuration updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Discount data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Discount data",3e3)})},this.validateTaxRow=(e,t=!1)=>{const n=t?e.data:Object.assign(Object.assign({},e.oldData),e.newData);return n.tax_code?n.name?n.tax_percentage?void(e.cancel=!1):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Tax Percentage !",2e3)):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Tax Description !",2e3)):(e.cancel=!0,this._toaster.showError("Error","Kindly enter Tax Code !",2e3))},this.taxRowInserted=e=>{this._quoteService.insertTax(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Tax configuration added Successfully !",2e3),this.getTaxDetails()):this._toaster.showError("Error","Error in inserting tax data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in inserting tax data",3e3)})},this.taxRowUpdated=e=>{this._quoteService.updateTax(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Tax configuration updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Tax data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Tax data",3e3)})}}ngOnInit(){this.getDiscountDetails()}setDiscountPercentage(e,t,n){e.percentage=t<0||""===t||null===t?0:parseFloat(t.toFixed(2))}setTaxPercentage(e,t,n){e.tax_percentage=t<0||""===t||null===t?0:parseFloat(t.toFixed(2))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-discount-tax-config"]],decls:4,vars:3,consts:[[1,"container-fluid","quote-discount-tax-config-styles"],[4,"ngIf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"row","col-12","label"],["id","gridContainer","keyExpr","id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserting","onRowUpdating","onRowInserted","onRowUpdated"],[3,"pageSize","pageIndex"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible","applyFilter"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","id","caption","Discount Id",3,"width","allowSorting","allowEditing"],["dataField","name","caption","Discount name",3,"width","allowSorting"],["dataField","percentage","dataType","number","caption","Discount Percentage",3,"width","allowSorting","setCellValue","editorOptions"],[1,"row","col-12","pt-4","label"],["dataField","id","caption","Id",3,"width","allowSorting","allowEditing"],["dataField","tax_code","caption","Tax Code",3,"width","allowSorting"],["dataField","name","caption","Tax Description",3,"width","allowSorting"],["dataField","tax_percentage","dataType","number","caption","Tax Percentage",3,"width","allowSorting","setCellValue","editorOptions"],[1,"row","w-100","justify-content-center","mt-5"],[2,"color","#cf0000",3,"diameter"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,pe,13,25,"ng-container",1),y["\u0275\u0275template"](2,ue,14,27,"ng-container",1),y["\u0275\u0275template"](3,ge,2,1,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.discountList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.taxList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgIf,g.a,te.od,te.Qb,te.ae,te.dc,te.Cc,te.Jd,te.g,c.c],styles:[".quote-discount-tax-config-styles[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#f27a6c;font-size:14px;line-height:24px;letter-spacing:.02em;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}"]}),e})();function he(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275text"](2," Quote Positions "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowInserted",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().positionRowInserted(t)}))("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().positionRowUpdated(t)}))("onEditorPreparing",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onInitNewRow",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onInitNewRow(t)})),y["\u0275\u0275element"](4,"dxo-paging",5),y["\u0275\u0275element"](5,"dxo-editing",6),y["\u0275\u0275element"](6,"dxo-sorting",7),y["\u0275\u0275element"](7,"dxo-filter-row",8),y["\u0275\u0275element"](8,"dxo-header-filter",9),y["\u0275\u0275element"](9,"dxo-scrolling",10),y["\u0275\u0275element"](10,"dxi-column",11),y["\u0275\u0275element"](11,"dxi-column",12),y["\u0275\u0275elementStart"](12,"dxi-column",13),y["\u0275\u0275element"](13,"dxo-lookup",14),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.positionList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",e.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",15)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",300)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.booleanList)}}function fe(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",15),y["\u0275\u0275element"](1,"mat-spinner",16),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}let we=(()=>{class e{constructor(e,t){this._quoteService=e,this._toaster=t,this._onDestroy=new O.b,this.isConfigLoading=!1,this.positionList=[],this.booleanList=[{id:1,name:"Yes"},{id:0,name:"No"}],this.columnResizingMode="widget",this.getPositionList=()=>{this.isConfigLoading=!0,this._quoteService.getQuotePosition().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data&&e.data.length?(this.positionList=e.data,this.isConfigLoading=!1):this._toaster.showError("Error","Error in getting Quote Position List",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Position List",3e3)})},this.positionRowInserted=e=>{this._quoteService.insertQuotePosition(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Quote Position added Successfully !",2e3),this.getPositionList()):this._toaster.showError("Error","Error in inserting Quote Position data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in inserting Quote Position data",3e3)})},this.positionRowUpdated=e=>{this._quoteService.updateQuotePosition(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Quote Position updated Successfully !",2e3),this.getPositionList()):this._toaster.showError("Error","Error in updating Quote Position data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Position data",3e3)})}}ngOnInit(){this.getPositionList()}onEditorPreparing(e){"dataRow"===e.parentType&&"is_for_quote"===e.dataField&&e.row.isNewRow&&(e.editorOptions.disabled=!0)}onInitNewRow(e){e.data.is_for_quote=1}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-position-config"]],decls:3,vars:2,consts:[[1,"container-fluid","quote-position-config-styles"],[4,"ngIf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"row","col-12","label"],["id","gridContainer","keyExpr","id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserted","onRowUpdated","onEditorPreparing","onInitNewRow"],[3,"pageSize","pageIndex"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible","applyFilter"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","id","caption","Position Id",3,"width","allowSorting","allowEditing"],["dataField","name","caption","Position name",3,"width","allowSorting"],["dataField","is_for_quote","caption","Position for Quote",3,"width","allowSorting"],["displayExpr","name","valueExpr","id",3,"dataSource"],[1,"row","w-100","justify-content-center","mt-5"],[2,"color","#cf0000",3,"diameter"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,he,14,23,"ng-container",1),y["\u0275\u0275template"](2,fe,2,1,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.positionList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgIf,g.a,te.od,te.Qb,te.ae,te.dc,te.Cc,te.Jd,te.g,te.Wc,c.c],styles:[".quote-position-config-styles[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#f27a6c;font-size:14px;line-height:24px;letter-spacing:.02em;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}"]}),e})();var ve=n("WGBV");function Se(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",28),y["\u0275\u0275element"](1,"mat-spinner",29),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",20))}let ye=(()=>{class e{constructor(e,t,n,o){this._quoteService=e,this._toaster=t,this._masterDataService=n,this.accountService=o,this._onDestroy=new O.b,this.isConfigLoading=!1,this.workLocationList=[],this.entityList=[],this.serviceList=[],this.divisionList=[],this.revenueRegionList=[],this.workLocationEntityMappingList=[],this.revenueRegionEntitySalesMappingList=[],this.serviceDivisionMappingList=[],this.salesRegionList=[],this.booleanList=[{id:1,name:"Yes"},{id:0,name:"No"}],this.columnResizingMode="widget",this.getWorkLocation=()=>new Promise((e,t)=>{this._masterDataService.workLocation.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.workLocationList=t,e(!0)})}),this.getEntity=()=>new Promise((e,t)=>{this._masterDataService.entity.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.entityList=t,e(!0)})}),this.getGeographicalRegion=()=>new Promise((e,t)=>{this._masterDataService.geographicalRegion.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.revenueRegionList=t,e(!0)})}),this.getDivisionList=()=>new Promise((e,t)=>{this._masterDataService.division.pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.divisionList=t,e(!0)},e=>{console.log(e),t(e)})}),this.getRevenueRegionEntitySalesMappingList=()=>{this.isConfigLoading=!0,this._quoteService.getRevenueRegionEntitySalesMappingList().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this.revenueRegionEntitySalesMappingList=e.data,this.revenueRegionMappingEnabled=e.mappingEnabled):this._toaster.showError("Error","Error in getting Revenue Region, Sales region, Entity Mapping List",3e3)},e=>{this.isConfigLoading=!1,console.log(e),this._toaster.showError("Error","Error in getting Revenue Region, Sales region, Entity Mapping List",3e3)})},this.getWorkLocationEntityMappingList=()=>{this.isConfigLoading=!0,this._quoteService.getQuoteWorkLocationEntityMapping().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this.workLocationEntityMappingList=e.data:this._toaster.showError("Error","Error in getting Work Location Entity Mapping List",3e3),this.getServiceDivisionMappingList()},e=>{this.isConfigLoading=!1,console.log(e),this._toaster.showError("Error","Error in getting Work Location Entity Mapping List",3e3)})},this.getServiceDivisionMappingList=()=>{this.isConfigLoading=!0,this._quoteService.getQuoteServiceDivisionMapping().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this.serviceDivisionMappingList=e.data:this._toaster.showError("Error","Error in getting Service Division Mapping List",3e3),this.isConfigLoading=!1},e=>{this.isConfigLoading=!1,console.log(e),this._toaster.showError("Error","Error in getting Service Division List",3e3)})},this.wERowInserted=e=>{this._quoteService.insertQuoteWorkLocationEntityMapping(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Work Location Entity Mapping data added !",2e3):this._toaster.showError("Error",e.messText||"Error in inserting Quote Work Location Entity data",3e3),this.getWorkLocationEntityMappingList()},e=>{console.log(e),this._toaster.showError("Error","Error in inserting Quote Work Location Entity data",3e3)})},this.wERowUpdated=e=>{this._quoteService.updateQuoteWorkLocationEntityMapping(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Work Location Entity Mapping data updated Successfully !",2e3):this._toaster.showError("Error",e.messText||"Error in updating Quote Work Location Entity Mapping data",3e3),this.getWorkLocationEntityMappingList()},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Work Location Entity Mapping data",3e3)})},this.sDRowInserted=e=>{this._quoteService.insertQuoteServiceDivisionMapping(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Service Division Mapping data added !",2e3):this._toaster.showError("Error",e.messText||"Error in inserting Quote Service Division data",3e3),this.getServiceDivisionMappingList()},e=>{console.log(e),this._toaster.showError("Error","Error in inserting Quote Service Division data",3e3)})},this.sDRowUpdated=e=>{this._quoteService.updateQuoteServiceDivisionMapping(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Service Division Mapping data updated Successfully !",2e3):this._toaster.showError("Error",e.messText||"Error in updating Quote Service Division Mapping data",3e3),this.getServiceDivisionMappingList()},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Service Division Mapping data",3e3)})},this.rSRowInserted=e=>{this._quoteService.insertQuoteEntitySalesRegionRevenueRegionMapping(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Work Location Entity Mapping data added !",2e3):this._toaster.showError("Error",e.messText||"Error in inserting Quote Entity, Sales region, Revenue/ Geographical Region Mapping",3e3),this.getRevenueRegionEntitySalesMappingList()},e=>{console.log(e),this._toaster.showError("Error","Error in inserting Quote Entity, Sales region, Revenue/ Geographical Region Mapping",3e3)})},this.rSRowUpdated=e=>{this._quoteService.updateQuoteEntitySalesRegionRevenueRegionMapping(e.data).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Error in updating Quote Entity, Sales region, Revenue/ Geographical Region Mapping",2e3):this._toaster.showError("Error",e.messText||"Error in updating Quote Entity, Sales region, Revenue/ Geographical Region Mapping",3e3),this.getRevenueRegionEntitySalesMappingList()},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Work Location Entity Mapping data",3e3)})},this.validateWERow=(e,t=!1)=>{const n=t?e.data:Object.assign(Object.assign({},e.oldData),e.newData);return n.work_location_id?n.entity_id?void(e.cancel=!1):(e.cancel=!0,this._toaster.showError("Error","Kindly Select Entity !",2e3)):(e.cancel=!0,this._toaster.showError("Error","Kindly Select Work Location !",2e3))},this.validateSDRow=(e,t=!1)=>{const n=t?e.data:Object.assign(Object.assign({},e.oldData),e.newData);return n.service_id?n.division_id?void(e.cancel=!1):(e.cancel=!0,this._toaster.showError("Error","Kindly Select Division !",2e3)):(e.cancel=!0,this._toaster.showError("Error","Kindly Select Service !",2e3))},this.getSalesRegionMaster=()=>{this.accountService.getSalesRegionMaster().subscribe(e=>{"S"==e.messType&&e.data&&(this.salesRegionList=e.data)},e=>this._toaster.showError("Error","Kindly Select Division !",2e3))}}ngOnInit(){this.isConfigLoading=!0,Promise.all([this.getWorkLocation(),this.getEntity(),this.getServices(),this.getDivisionList(),this.getGeographicalRegion(),this.getSalesRegionMaster()]).then(()=>{this.getWorkLocationEntityMappingList(),this.getRevenueRegionEntitySalesMappingList()})}getServices(){return new Promise((e,t)=>{this._quoteService.getServices(!0).pipe(Object(R.a)(this._onDestroy)).subscribe(t=>{this.serviceList=t.data,e(!0)},e=>{console.log(e),t(e)})})}onEditorPreparing(e){"dataRow"===e.parentType&&"is_active"===e.dataField&&e.row.isNewRow&&(e.editorOptions.disabled=!0),"dataRow"===e.parentType&&"work_location_id"===e.dataField&&(e.row.isNewRow||(e.editorOptions.disabled=!0)),"dataRow"===e.parentType&&"service_id"===e.dataField&&(e.row.isNewRow||(e.editorOptions.disabled=!0))}onInitNewRow(e){e.data.is_active=1}calculateSortValue(e){const t=this,n=t.calculateCellValue(e);return""===n||null===n?n:t.hasOwnProperty("lookup")?t.lookup.calculateCellValue(n):n}toggleRevenueRegionMapping(){this.revenueRegionMappingEnabled=!this.revenueRegionMappingEnabled,this._quoteService.updateQuoteConfiguration([{config_name:"quote_geograpical_revenue_region_mapping_enable",config_value:this.revenueRegionMappingEnabled}]).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success",`Geographical/Revenue Region Mapping ${this.revenueRegionMappingEnabled?"Enabled":"Disabled"} Successfully !`,2e3):this._toaster.showError("Error",e.messText||"Error in updating Revenue Region Mapping",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Revenue Region Mapping",3e3)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a),y["\u0275\u0275directiveInject"](D.a),y["\u0275\u0275directiveInject"](ve.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-data-mapping-config"]],decls:62,vars:94,consts:[[1,"container-fluid","quote-data-mapping-config-styles"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"row","pt-2","pb-2","col-12","label"],["matTooltip","A Work Location can be mapped to only one Entity",1,"info-icon"],["id","gridContainer","keyExpr","id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserting","onRowUpdating","onRowInserted","onRowUpdated","onEditorPreparing","onInitNewRow"],[3,"pageSize","pageIndex"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible","applyFilter"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","id","caption","Id",3,"width","allowSorting","allowEditing"],["dataField","work_location_id","caption","Work Location",3,"width","allowSorting","calculateSortValue"],["displayExpr","name","valueExpr","id",3,"dataSource"],["dataField","entity_id","caption","Entity",3,"width","allowSorting","calculateSortValue"],["dataField","is_active","caption","Active",3,"width","allowSorting"],["matTooltip","A Service can be mapped to only one Division",1,"info-icon"],["dataField","service_id","caption","Service Line",3,"width","allowSorting","calculateSortValue"],["dataField","division_id","caption","Division",3,"width","allowSorting","calculateSortValue"],[1,"row","pt-2","pb-2","col-12","label","mt-3","d-flex","justify-content-between","align-items-center"],[1,"d-flex","justify-content-end","align-items-center"],["matTooltip","Mapping of sales region with the quote position entity to geographical region",1,"info-icon"],["color","primary",1,"toggle",2,"margin","0px",3,"checked","matTooltip","change"],["id","gridContainer","keyExpr","combination_id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserted","onRowUpdated","onEditorPreparing","onInitNewRow"],["dataField","combination_id","caption","Id",3,"width","allowSorting","allowEditing"],["dataField","sales_region_id","caption","Sales Region",3,"width","allowSorting","calculateSortValue"],["dataField","legal_entity_id","caption","Entity",3,"width","allowSorting","calculateSortValue"],["dataField","revenue_region_id","caption","Geographical Region",3,"width","allowSorting","calculateSortValue"],[1,"row","w-100","justify-content-center","mt-5"],[2,"color","#cf0000",3,"diameter"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,Se,2,1,"div",1),y["\u0275\u0275elementStart"](2,"div",2),y["\u0275\u0275text"](3," Quote Work Location <-> Entity Mapping "),y["\u0275\u0275elementStart"](4,"mat-icon",3),y["\u0275\u0275text"](5,"info"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](6,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowInserting",(function(e){return t.validateWERow(e,!0)}))("onRowUpdating",(function(e){return t.validateWERow(e)}))("onRowInserted",(function(e){return t.wERowInserted(e)}))("onRowUpdated",(function(e){return t.wERowUpdated(e)}))("onEditorPreparing",(function(e){return t.onEditorPreparing(e)}))("onInitNewRow",(function(e){return t.onInitNewRow(e)})),y["\u0275\u0275element"](7,"dxo-paging",5),y["\u0275\u0275element"](8,"dxo-editing",6),y["\u0275\u0275element"](9,"dxo-sorting",7),y["\u0275\u0275element"](10,"dxo-filter-row",8),y["\u0275\u0275element"](11,"dxo-header-filter",9),y["\u0275\u0275element"](12,"dxo-scrolling",10),y["\u0275\u0275element"](13,"dxi-column",11),y["\u0275\u0275elementStart"](14,"dxi-column",12),y["\u0275\u0275element"](15,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](16,"dxi-column",14),y["\u0275\u0275element"](17,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](18,"dxi-column",15),y["\u0275\u0275element"](19,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](20,"div",2),y["\u0275\u0275text"](21," Quote Service <-> Division Mapping "),y["\u0275\u0275elementStart"](22,"mat-icon",16),y["\u0275\u0275text"](23,"info"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](24,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowInserting",(function(e){return t.validateSDRow(e,!0)}))("onRowUpdating",(function(e){return t.validateSDRow(e)}))("onRowInserted",(function(e){return t.sDRowInserted(e)}))("onRowUpdated",(function(e){return t.sDRowUpdated(e)}))("onEditorPreparing",(function(e){return t.onEditorPreparing(e)}))("onInitNewRow",(function(e){return t.onInitNewRow(e)})),y["\u0275\u0275element"](25,"dxo-paging",5),y["\u0275\u0275element"](26,"dxo-editing",6),y["\u0275\u0275element"](27,"dxo-sorting",7),y["\u0275\u0275element"](28,"dxo-filter-row",8),y["\u0275\u0275element"](29,"dxo-header-filter",9),y["\u0275\u0275element"](30,"dxo-scrolling",10),y["\u0275\u0275element"](31,"dxi-column",11),y["\u0275\u0275elementStart"](32,"dxi-column",17),y["\u0275\u0275element"](33,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](34,"dxi-column",18),y["\u0275\u0275element"](35,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](36,"dxi-column",15),y["\u0275\u0275element"](37,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](38,"div",19),y["\u0275\u0275elementStart"](39,"div",20),y["\u0275\u0275elementStart"](40,"span"),y["\u0275\u0275text"](41," Quote Entity & Sales Region <-> Geographical Region Mapping "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](42,"mat-icon",21),y["\u0275\u0275text"](43,"info"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](44,"div"),y["\u0275\u0275elementStart"](45,"mat-slide-toggle",22),y["\u0275\u0275listener"]("change",(function(){return t.toggleRevenueRegionMapping()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](46,"dx-data-grid",23),y["\u0275\u0275listener"]("onRowInserted",(function(e){return t.rSRowInserted(e)}))("onRowUpdated",(function(e){return t.rSRowUpdated(e)}))("onEditorPreparing",(function(e){return t.onEditorPreparing(e)}))("onInitNewRow",(function(e){return t.onInitNewRow(e)})),y["\u0275\u0275element"](47,"dxo-paging",5),y["\u0275\u0275element"](48,"dxo-editing",6),y["\u0275\u0275element"](49,"dxo-sorting",7),y["\u0275\u0275element"](50,"dxo-filter-row",8),y["\u0275\u0275element"](51,"dxo-header-filter",9),y["\u0275\u0275element"](52,"dxo-scrolling",10),y["\u0275\u0275element"](53,"dxi-column",24),y["\u0275\u0275elementStart"](54,"dxi-column",25),y["\u0275\u0275element"](55,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](56,"dxi-column",26),y["\u0275\u0275element"](57,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](58,"dxi-column",27),y["\u0275\u0275element"](59,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](60,"dxi-column",15),y["\u0275\u0275element"](61,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading),y["\u0275\u0275advance"](5),y["\u0275\u0275property"]("dataSource",t.workLocationEntityMappingList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",t.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",15)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",300)("allowSorting",!0)("calculateSortValue",t.calculateSortValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.workLocationList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",300)("allowSorting",!0)("calculateSortValue",t.calculateSortValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.entityList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.booleanList),y["\u0275\u0275advance"](5),y["\u0275\u0275property"]("dataSource",t.serviceDivisionMappingList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",t.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",15)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",300)("allowSorting",!0)("calculateSortValue",t.calculateSortValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.serviceList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",300)("allowSorting",!0)("calculateSortValue",t.calculateSortValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.divisionList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",150)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.booleanList),y["\u0275\u0275advance"](8),y["\u0275\u0275property"]("checked",t.revenueRegionMappingEnabled)("matTooltip",t.revenueRegionMappingEnabled?"Disable Mapping":"Enable Mapping"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.revenueRegionEntitySalesMappingList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",t.columnResizingMode),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("pageSize",15)("pageIndex",0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0)("applyFilter",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",250)("allowSorting",!0)("calculateSortValue",t.calculateSortValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.salesRegionList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",250)("allowSorting",!0)("calculateSortValue",t.calculateSortValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.entityList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",250)("allowSorting",!0)("calculateSortValue",t.calculateSortValue),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.revenueRegionList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",100)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.booleanList))},directives:[o.NgIf,a.a,r.a,g.a,te.od,te.Qb,te.ae,te.dc,te.Cc,te.Jd,te.g,te.Wc,v.a,c.c],styles:[".quote-data-mapping-config-styles[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#f27a6c;font-size:14px;line-height:24px;letter-spacing:.02em;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.quote-data-mapping-config-styles[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{font-size:18px;display:flex;align-items:center;margin:1px 5px 0}.quote-data-mapping-config-styles[_ngcontent-%COMP%]   .toggle[_ngcontent-%COMP%]{color:var(--blue-grey-80,#5f6c81);font-family:Roboto;font-size:12px;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.24px;text-transform:capitalize}.quote-data-mapping-config-styles[_ngcontent-%COMP%]   .mat-slide-toggle[_ngcontent-%COMP%]{transform:scale(.8)}.quote-data-mapping-config-styles[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-bar{width:25px}.quote-data-mapping-config-styles[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-thumb{background-color:#fff;width:10px;height:10px;margin-left:4px;margin-top:5px}.quote-data-mapping-config-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-bar{background-color:rgba(208,12,12,.54);width:25px}.quote-data-mapping-config-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb{background-color:#fff;width:10px;height:10px;margin-left:-4px;margin-top:5px}"]}),e})();var Ee=n("mrSG"),Ce=n("Kj3r"),be=n("A+V/"),xe=n("Yd6m"),_e=n("PVOt");const Oe=function(){return{"aria-label":"Name"}};function Re(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",15),y["\u0275\u0275listener"]("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.accountStakeholderList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",3)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](7,Oe))("searchEnabled",!0)}}function Le(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",15),y["\u0275\u0275listener"]("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.opportunityStakeholderList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",4)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](7,Oe))("searchEnabled",!0)}}function De(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",16),y["\u0275\u0275listener"]("onScroll",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"](2).onScroll(t)}))("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()}))("onInput",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"](2).searchSubject.next(t.target.value)})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.approverList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",4)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](8,Oe))("virtualModeEnabled",!0)("searchEnabled",!0)}}function Ie(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275text"](2," Quote Activation Approver Settings "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().updateQuoteApproverConfig(t)}))("onEditorPreparing",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onRowPrepared",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onRowPrepared(t)})),y["\u0275\u0275element"](4,"dxo-editing",5),y["\u0275\u0275element"](5,"dxo-sorting",6),y["\u0275\u0275element"](6,"dxo-header-filter",7),y["\u0275\u0275element"](7,"dxo-scrolling",8),y["\u0275\u0275elementStart"](8,"dxi-column",9),y["\u0275\u0275element"](9,"dxo-lookup",10),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](10,Re,2,8,"div",11),y["\u0275\u0275elementStart"](11,"dxi-column",12),y["\u0275\u0275element"](12,"dxo-lookup",10),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](13,Le,2,8,"div",11),y["\u0275\u0275elementStart"](14,"dxi-column",13),y["\u0275\u0275element"](15,"dxo-lookup",14),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](16,De,2,9,"div",11),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.approverConfigList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode","widget"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!1),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",250)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.accountStakeholderList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dxTemplateOf","accountsTagBoxEditor"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",250)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.opportunityStakeholderList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dxTemplateOf","opportunityTagBoxEditor"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",250)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.approverList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dxTemplateOf","customTagBoxEditor")}}function Me(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",23),y["\u0275\u0275listener"]("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.approverList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",4)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](7,Oe))("searchEnabled",!0)}}function Te(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",17),y["\u0275\u0275text"](2,"Quote Sales Org Approver Settings"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",18),y["\u0275\u0275listener"]("onRowInserted",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().financialDocSalesOrgConigUpdate(t,"INSERT")}))("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().financialDocSalesOrgConigUpdate(t,"UPDATE")}))("onEditorPreparing",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onRowPrepared",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onRowPrepared(t)})),y["\u0275\u0275element"](4,"dxo-editing",5),y["\u0275\u0275element"](5,"dxo-sorting",6),y["\u0275\u0275element"](6,"dxo-header-filter",7),y["\u0275\u0275element"](7,"dxo-scrolling",8),y["\u0275\u0275elementStart"](8,"dxi-column",19),y["\u0275\u0275element"](9,"dxo-lookup",20),y["\u0275\u0275element"](10,"dxi-validation-rule",21),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](11,"dxi-column",22),y["\u0275\u0275element"](12,"dxo-lookup",14),y["\u0275\u0275element"](13,"dxi-validation-rule",21),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](14,Me,2,8,"div",11),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.approverSalesOrgConfigList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode","widget"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.salesOrgList),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",200)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.approverList),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("dxTemplateOf","salesTagBoxEditor")}}function Pe(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",24),y["\u0275\u0275element"](1,"mat-spinner",25),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",25)("matTooltip","Getting Approver Settings"))}let ke=(()=>{class e{constructor(e,t,n,o){this._quoteService=e,this._toaster=t,this._stakeholderService=n,this._accstakeholderService=o,this._onDestroy=new O.b,this.searchSubject=new O.b,this.isConfigLoading=!1,this.isAddingNewRow=!1,this.statusIdsNoEdit=[21],this.approverList=[],this.documentTypes=[],this.opportunityStakeholderList=[],this.accountStakeholderList=[],this.salesOrgList=[],this.fullApproverList=[],this.applicationData=[],this.getQuoteActivationApproverConfig=()=>{this._quoteService.getQuoteActivationApproverConfig().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"===e.messType&&e.data?(e.data.unique_id="QUOTE_ACTIVATION_CONFIG",this.approverConfigList=[e.data]):this._toaster.showError("Error","Error in getting Approver Config",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Approver Config",3e3)})},this.updateQuoteApproverConfig=e=>{let t=this.constructNdReplace(this.approverConfigList,e.data);this._quoteService.updateQuoteActivationConfig(t[0]).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Opportunity Status Config updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",3e3)})},this.shouldShowEditButton=e=>this.isEditableRow(e.row.data),this.getFinancialDocumentSalesOrgConfig=()=>{this._quoteService.getFinancialDocumentSalesOrgConfig().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{var t,n;if("S"===e.messType&&e.data){this.accountConfig=(null===(t=e.data)||void 0===t?void 0:t.accountConfig)?e.data.accountConfig.map((e,t)=>Object.assign(Object.assign({},e),{application_id:33,application_name:"Account",unique_id:"account_"+t})):[];const o=(null===(n=e.data)||void 0===n?void 0:n.opportunityConfig)?e.data.opportunityConfig.map((e,t)=>Object.assign(Object.assign({},e),{application_id:36,application_name:"Opportunity",unique_id:"opportunity_"+t})):[];this.approverSalesOrgConfigList=[...o]}else this._toaster.showError("Error","Error in getting Sales Org Approver Config",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Sales Org Approver Config",3e3)})},this.financialDocSalesOrgConigUpdate=(e,t)=>{let n,o,i=e.data;i.application_id=36,o=[...this.accountConfig,...this.approverSalesOrgConfigList],"INSERT"===t&&(n=o),"UPDATE"===t&&(n=this.constructNdReplace(o,i));let a={accountConfig:[],opportunityConfig:[]};for(const r of n)33===r.application_id?a.accountConfig.push(r):36===r.application_id&&a.opportunityConfig.push(r);this._quoteService.updateFinancialDocSalesOrgApproverConfig(a).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Sales Org Config updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Quote Sales Org Config data",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Sales Org Config data",3e3)})}}ngOnInit(){return Object(Ee.c)(this,void 0,void 0,(function*(){this.isConfigLoading=!0,this.opportunityStakeholderList=yield this._stakeholderService.getOpportunityStakeholderMaster("internal"),this.accountStakeholderList=yield this._accstakeholderService.getAccountStakeholderMaster("internal"),yield this.getAllMembers(),yield this.getSalesRegionMaster(),this.getQuoteActivationApproverConfig(),this.getFinancialDocumentSalesOrgConfig(),this.isConfigLoading=!1,this.searchSubject.pipe(Object(Ce.a)(300)).subscribe(e=>{this.approverList=this.fullApproverList.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())).slice(0,50)})}))}cellTemplate(e,t){const n=(Array.isArray(t.value)?t.value:[t.value]).map(e=>t.column.lookup.calculateCellValue(e)).join(", ");e.textContent=n||"\xa0",e.title=n}calculateFilterExpression(e,t,n){return"search"===n&&"string"==typeof e?[this.dataField,"contains",e]:function(t){return-1!==(t.AssignedEmployee||[]).indexOf(e)}}constructNdReplace(e,t){for(let n=0;n<e.length;n++){const o=e[n];if(o.document_type===t.document_type&&o.application_id===t.application_id){let i=!0;for(const e in o)if(o[e]!==t[e]){i=!1;break}i?console.log("same values"):e[n]=t;break}}return e}onEditorPreparing(e){"dataRow"===e.parentType&&(this.isAddingNewRow=e.row&&e.row.isNewRow)}isEditableRow(e){return console.log(e),!this.statusIdsNoEdit.includes(e.status_id)}onEditingStart(e){this.isEditableRow(e.data)||(e.cancel=!0)}onRowPrepared(e){"data"===e.rowType&&this.isEditableRow(e.data)&&e.rowElement.classList.add("editable-row")}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}getAllMembers(){return Object(Ee.c)(this,void 0,void 0,(function*(){try{const e=yield this._quoteService.getAllMembers().toPromise();e?(this.fullApproverList=e,this.approverList=this.fullApproverList.slice(0,5e3)):(console.warn("No members received."),this.approverList=[])}catch(e){console.error("Failed to retrieve members:",e)}}))}getSalesRegionMaster(){return Object(Ee.c)(this,void 0,void 0,(function*(){try{const e=yield this._quoteService.getSalesRegionMaster().toPromise();e?this.salesOrgList=e.data:(console.warn("No members received."),this.approverList=[])}catch(e){console.error("Failed to retrieve members:",e)}}))}onScroll(e){const t=e.component.getScrollable();if(t.scrollHeight()-t.scrollTop()-t.clientHeight()<50){const e=this.approverList.length,t=this.fullApproverList.slice(e,e+50);this.approverList=[...this.approverList,...t]}}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a),y["\u0275\u0275directiveInject"](be.a),y["\u0275\u0275directiveInject"](xe.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-activation-approver-config"]],decls:4,vars:3,consts:[[1,"container-fluid","qb-activation-config-styles"],[4,"ngIf"],["class","loader",4,"ngIf"],[1,"row","col-12","label","mb-2"],["id","gridContainer","keyExpr","unique_id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowUpdated","onEditorPreparing","onRowPrepared"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","ACCSTAKEHOLDER","caption","Account Stakeholder Role","editCellTemplate","accountsTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["valueExpr","owner_type","displayExpr","name",3,"dataSource"],[4,"dxTemplate","dxTemplateOf"],["dataField","OPPSTAKEHOLDERS","caption","Opportunity Stakeholder Role","editCellTemplate","opportunityTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["dataField","MEMBER","caption","Custom Approver","editCellTemplate","customTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["displayExpr","name","valueExpr","id",3,"dataSource"],["valueExpr","owner_type","displayExpr","name","applyValueMode","useButtons",3,"dataSource","value","showSelectionControls","maxDisplayedTags","showMultiTagOnly","inputAttr","searchEnabled","onValueChanged","onSelectionChanged"],["displayExpr","name","valueExpr","id","applyValueMode","useButtons",3,"dataSource","value","showSelectionControls","maxDisplayedTags","showMultiTagOnly","inputAttr","virtualModeEnabled","searchEnabled","onScroll","onValueChanged","onSelectionChanged","onInput"],[1,"row","col-12","mt-5","mb-0","label"],["id","gridContainer","keyExpr","unique_id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserted","onRowUpdated","onEditorPreparing","onRowPrepared"],["dataField","sales_org","caption","Sales Org",3,"width","allowSorting"],["displayExpr","name","valueExpr","pl_id",3,"dataSource"],["type","required"],["dataField","approver","caption","Sales Org Approver","editCellTemplate","salesTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["displayExpr","name","valueExpr","id","applyValueMode","useButtons",3,"dataSource","value","showSelectionControls","maxDisplayedTags","showMultiTagOnly","inputAttr","searchEnabled","onValueChanged","onSelectionChanged"],[1,"loader"],[2,"color","#cf0000",3,"diameter","matTooltip"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,Ie,17,29,"ng-container",1),y["\u0275\u0275template"](2,Te,15,20,"ng-container",1),y["\u0275\u0275template"](3,Pe,2,2,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.approverConfigList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.approverSalesOrgConfigList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgIf,g.a,te.Qb,te.ae,te.Cc,te.Jd,te.g,te.Wc,_e.d,w.a,te.P,c.c,r.a],styles:[".qb-activation-config-styles[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#f27a6c;font-size:14px;line-height:24px;letter-spacing:.02em;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.qb-activation-config-styles[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{height:70vh!important;width:-webkit-fill-available!important;display:flex;align-items:center;justify-content:center;overflow:hidden}"]}),e})();var qe=n("pgif");const Ae=function(){return{"aria-label":"Name"}};function Fe(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",20),y["\u0275\u0275listener"]("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.accountStakeholderList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",3)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](7,Ae))("searchEnabled",!0)}}const je=function(){return[]};function Ve(e,t){1&e&&(y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275element"](1,"dx-tag-box",21),y["\u0275\u0275elementContainerEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",y["\u0275\u0275pureFunction0"](2,je))("value",y["\u0275\u0275pureFunction0"](3,je)))}function Ue(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"dx-tag-box",20),y["\u0275\u0275listener"]("onValueChanged",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().$implicit.setValue(t.value)}))("onSelectionChanged",(function(){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().$implicit.component.updateDimensions()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]().$implicit,t=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",t.opportunityStakeholderList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",4)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](7,Ae))("searchEnabled",!0)}}function Ne(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275template"](1,Ve,2,4,"ng-container",1),y["\u0275\u0275template"](2,Ue,2,8,"ng-container",1),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",33===e.data.application_id),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",33!==e.data.application_id)}}function Be(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",22),y["\u0275\u0275listener"]("onScroll",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"](2).onScroll(t)}))("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()}))("onInput",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"](2).searchSubject.next(t.target.value)})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.approverList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",4)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](8,Ae))("virtualModeEnabled",!0)("searchEnabled",!0)}}function Qe(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",3),y["\u0275\u0275text"](2,"Financial Documents Approver Settings"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",4),y["\u0275\u0275listener"]("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().financialDocConfigUpdate(t)}))("onEditorPreparing",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onRowPrepared",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onRowPrepared(t)})),y["\u0275\u0275element"](4,"dxo-editing",5),y["\u0275\u0275element"](5,"dxo-sorting",6),y["\u0275\u0275element"](6,"dxo-header-filter",7),y["\u0275\u0275element"](7,"dxo-scrolling",8),y["\u0275\u0275elementStart"](8,"dxi-column",9),y["\u0275\u0275element"](9,"dxo-lookup",10),y["\u0275\u0275element"](10,"dxi-validation-rule",11),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](11,"dxi-column",12),y["\u0275\u0275element"](12,"dxo-lookup",13),y["\u0275\u0275element"](13,"dxi-validation-rule",11),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](14,"dxi-column",14),y["\u0275\u0275element"](15,"dxo-lookup",15),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](16,Fe,2,8,"div",16),y["\u0275\u0275elementStart"](17,"dxi-column",17),y["\u0275\u0275element"](18,"dxo-lookup",15),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](19,Ne,3,2,"div",16),y["\u0275\u0275elementStart"](20,"dxi-column",18),y["\u0275\u0275element"](21,"dxo-lookup",19),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](22,Be,2,9,"div",16),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.approverConfigList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode","widget"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!1),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",170)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.applicationData),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.documentTypes),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",200)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.accountStakeholderList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dxTemplateOf","accountsTagBoxEditor"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.opportunityStakeholderList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dxTemplateOf","opportunityTagBoxEditor"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.approverList),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dxTemplateOf","customTagBoxEditor")}}function ze(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",28),y["\u0275\u0275listener"]("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.approverList)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",4)("showMultiTagOnly",!1)("inputAttr",y["\u0275\u0275pureFunction0"](7,Ae))("searchEnabled",!0)}}function We(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",23),y["\u0275\u0275text"](2,"Financial Documents Sales Org Approver Settings"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",24),y["\u0275\u0275listener"]("onRowInserted",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().financialDocSalesOrgConigUpdate(t,"INSERT")}))("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().financialDocSalesOrgConigUpdate(t,"UPDATE")}))("onEditorPreparing",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onRowPrepared",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onRowPrepared(t)})),y["\u0275\u0275element"](4,"dxo-editing",5),y["\u0275\u0275element"](5,"dxo-sorting",6),y["\u0275\u0275element"](6,"dxo-header-filter",7),y["\u0275\u0275element"](7,"dxo-scrolling",8),y["\u0275\u0275elementStart"](8,"dxi-column",9),y["\u0275\u0275element"](9,"dxo-lookup",10),y["\u0275\u0275element"](10,"dxi-validation-rule",11),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](11,"dxi-column",25),y["\u0275\u0275element"](12,"dxo-lookup",26),y["\u0275\u0275element"](13,"dxi-validation-rule",11),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](14,"dxi-column",27),y["\u0275\u0275element"](15,"dxo-lookup",19),y["\u0275\u0275element"](16,"dxi-validation-rule",11),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](17,ze,2,8,"div",16),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.approverSalesOrgConfigList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode","widget"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",170)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.salesOrgApplicationData),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",200)("allowSorting",!0),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.salesOrgList),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",200)("allowSorting",!1)("cellTemplate",e.cellTemplate)("calculateFilterExpression",e.calculateFilterExpression),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.approverList),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("dxTemplateOf","salesTagBoxEditor")}}function Ge(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275elementStart"](1,"dx-tag-box",33),y["\u0275\u0275listener"]("onValueChanged",(function(e){return t.$implicit.setValue(e.value)}))("onSelectionChanged",(function(){return t.$implicit.component.updateDimensions()})),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=y["\u0275\u0275nextContext"](2);y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",n.documentTypesForOppStage)("value",e.value)("showSelectionControls",!0)("maxDisplayedTags",3)("showMultiTagOnly",!1)("searchEnabled",!0)}}function Ke(e,t){if(1&e){const e=y["\u0275\u0275getCurrentView"]();y["\u0275\u0275elementContainerStart"](0),y["\u0275\u0275elementStart"](1,"div",23),y["\u0275\u0275text"](2," Financial Documents - Opportunity Stage Wise Document Mandate Config "),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](3,"dx-data-grid",29),y["\u0275\u0275listener"]("onRowUpdated",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().stageWiseDocUploadUpdate(t)}))("onEditorPreparing",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onRowPrepared",(function(t){return y["\u0275\u0275restoreView"](e),y["\u0275\u0275nextContext"]().onRowPrepared(t)})),y["\u0275\u0275element"](4,"dxo-editing",5),y["\u0275\u0275element"](5,"dxo-sorting",6),y["\u0275\u0275element"](6,"dxo-header-filter",7),y["\u0275\u0275element"](7,"dxo-scrolling",8),y["\u0275\u0275element"](8,"dxi-column",30),y["\u0275\u0275element"](9,"dxi-column",31),y["\u0275\u0275elementStart"](10,"dxi-column",32),y["\u0275\u0275element"](11,"dxo-lookup",13),y["\u0275\u0275elementEnd"](),y["\u0275\u0275template"](12,Ge,2,6,"div",16),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](3),y["\u0275\u0275property"]("dataSource",e.opportunityStatusDocConfig)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode","widget"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!1),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("visible",!0),y["\u0275\u0275advance"](2),y["\u0275\u0275property"]("width",170)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",200)("allowSorting",!0)("allowEditing",!1),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("width",250)("allowSorting",!0)("allowFiltering",!0)("cellTemplate",e.cellTemplate)("editCellTemplate","docTypeTagBoxEditor"),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dataSource",e.documentTypesForOppStage),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("dxTemplateOf","docTypeTagBoxEditor")}}function $e(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",34),y["\u0275\u0275element"](1,"mat-spinner",35),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("diameter",25)("matTooltip","Getting Approver Settings"))}const He=[{path:"",component:_,children:[{path:"",redirectTo:"general",pathMatch:"full"},{path:"general",component:B},{path:"field",component:ee},{path:"uom",component:re},{path:"opp-integration",component:de},{path:"discount-tax",component:me},{path:"position",component:we},{path:"mapping",component:ye},{path:"quote-activation-settings",component:ke},{path:"document-settings",component:(()=>{class e{constructor(e,t,n,o,i){this._quoteService=e,this._toaster=t,this._stakeholderService=n,this._accstakeholderService=o,this.opportunityService=i,this._onDestroy=new O.b,this.searchSubject=new O.b,this.isConfigLoading=!1,this.isAddingNewRow=!1,this.approverSalesOrgConfigList=[],this.statusIdsNoEdit=[21],this.approverConfigList=[],this.approverList=[],this.documentTypes=[],this.opportunityStakeholderList=[],this.accountStakeholderList=[],this.salesOrgList=[],this.fullApproverList=[],this.salesOrgApplicationData=[],this.applicationData=[],this.opportunityStatusDocConfig=[],this.documentTypesForOppStage=[],this.getFinDashApproverConfig=()=>{this._quoteService.getFinancialDocumentConfig().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{var t,n,o;if("S"===e.messType&&e.data){const i=e=>e.length>0&&{OPPSTAKEHOLDERS:1,ACCSTAKEHOLDER:2,SALESORG:3,MEMBER:4}[e[0]]||null,a=(null===(t=e.data)||void 0===t?void 0:t.accountConfig)?e.data.accountConfig.map((e,t)=>Object.assign(Object.assign({},e),{application_id:33,application_name:"Account",unique_id:"account_"+t,approvalTypeId:i(e.type),ACCSTAKEHOLDER:e.ACCSTAKEHOLDER||[],MEMBER:e.MEMBER||[],OPPSTAKEHOLDERS:e.OPPSTAKEHOLDERS||[]})):[],r=(null===(n=e.data)||void 0===n?void 0:n.opportunityConfig)?null===(o=e.data)||void 0===o?void 0:o.opportunityConfig.map((e,t)=>Object.assign(Object.assign({},e),{application_id:36,application_name:"Opportunity",unique_id:"opportunity_"+t,approvalTypeId:i(e.type),ACCSTAKEHOLDER:e.ACCSTAKEHOLDER||[],MEMBER:e.MEMBER||[],OPPSTAKEHOLDERS:e.OPPSTAKEHOLDERS||[]})):[];a.length>0&&this.applicationData.push({application_id:33,application_name:"Account"}),r.length>0&&this.applicationData.push({application_id:36,application_name:"Opportunity"}),this.approverConfigList=[...a,...r]}else this._toaster.showError("Error","Error in getting Financial Document Approver Config",this.opportunityService.mediumInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Financial Document Approver Config",this.opportunityService.mediumInterval)})},this.getFinancialDocumentSalesOrgConfig=()=>{this._quoteService.getFinancialDocumentSalesOrgConfig().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{var t,n;if("S"===e.messType&&e.data){const o=(null===(t=e.data)||void 0===t?void 0:t.accountConfig)?e.data.accountConfig.map((e,t)=>Object.assign(Object.assign({},e),{application_id:33,application_name:"Account",unique_id:"account_"+t})):[],i=(null===(n=e.data)||void 0===n?void 0:n.opportunityConfig)?e.data.opportunityConfig.map((e,t)=>Object.assign(Object.assign({},e),{application_id:36,application_name:"Opportunity",unique_id:"opportunity_"+t})):[];o.length>0&&this.salesOrgApplicationData.push({application_id:33,application_name:"Account"}),i.length>0&&this.salesOrgApplicationData.push({application_id:36,application_name:"Opportunity"}),this.approverSalesOrgConfigList=[...o,...i]}else this._toaster.showError("Error","Error in getting Financial Document Sales Org Approver Config",this.opportunityService.mediumInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote Financial Document Sales Org Approver Config",this.opportunityService.mediumInterval)})},this.getDocUploadStageWiseConfig=()=>{this._quoteService.getDocUploadStageWiseConfig().pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"===e.messType&&e.data?(this.opportunityStatusDocConfig=e.data,this.opportunityStatusDocConfig.forEach(e=>{if(e.document_type_upload_required_config)try{const t=JSON.parse(e.document_type_upload_required_config);e.document_types=t.value.doc_upload_config.filter(e=>e.is_active).map(e=>e.document_type)}catch(t){console.error("Failed to parse document config:",t),e.document_types=[]}else e.document_types=[]})):this._toaster.showError("Error","Error in getting Financial Document Upload Opportunity Stage wise Config",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in getting Financial Document Upload Opportunity Stage wise Config",3e3)})},this.stageWiseDocUploadUpdate=e=>{let t,n=e.data,o=n.document_types||[];t=o.some(e=>null==e)?{value:{doc_upload_config:{document_type:null,is_active:!1},is_doc_upload_mandatory:!1}}:{value:{doc_upload_config:o.map(e=>({document_type:e,is_active:!!e})),is_doc_upload_mandatory:!o.some(e=>null==e)}},t.value.is_doc_upload_mandatory||(e.data.document_types=[null],this._toaster.showWarning("None was selected","Upload Mandatory will be turned off!"));const i=JSON.stringify(t);this._quoteService.stageWiseDocUploadUpdate({status_id:n.status_id,document_type_upload_required_config:i}).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"===e.messType?this._toaster.showSuccess("Success","Financial Document Upload Opportunity Stage wise Config updated Successfully!",2e3):this._toaster.showError("Error","Error in updating Financial Document Upload Opportunity Stage wise Config",3e3)},e=>{console.error(e),this._toaster.showError("Error","Error in updating Financial Document Upload Opportunity Stage wise Config",3e3)})},this.financialDocConfigUpdate=e=>{let t=this.constructNdReplace(this.approverConfigList,e.data),n={accountConfig:[],opportunityConfig:[]};for(const o of t)33===o.application_id?n.accountConfig.push(o):36===o.application_id&&n.opportunityConfig.push(o);this._quoteService.updateFinancialDocApproverConfig(n).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Opportunity Status Config updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",this.opportunityService.mediumInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",this.opportunityService.mediumInterval)})},this.financialDocSalesOrgConigUpdate=(e,t)=>{let n;"INSERT"===t&&(n=this.approverSalesOrgConfigList),"UPDATE"===t&&(n=this.constructNdReplace(this.approverSalesOrgConfigList,e.data));let o={accountConfig:[],opportunityConfig:[]};for(const i of n)33===i.application_id?o.accountConfig.push(i):36===i.application_id&&o.opportunityConfig.push(i);this._quoteService.updateFinancialDocSalesOrgApproverConfig(o).pipe(Object(R.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess("Success","Quote Opportunity Status Config updated Successfully !",2e3):this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",this.opportunityService.mediumInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote Opportunity Status Config data",this.opportunityService.mediumInterval)})},this.shouldShowEditButton=e=>this.isEditableRow(e.row.data)}ngOnInit(){return Object(Ee.c)(this,void 0,void 0,(function*(){this.isConfigLoading=!0,this.opportunityStakeholderList=yield this._stakeholderService.getOpportunityStakeholderMaster("internal"),this.accountStakeholderList=yield this._accstakeholderService.getAccountStakeholderMaster("internal"),yield this.getDocumentTypes(),yield this.getAllMembers(),yield this.getSalesRegionMaster(),this.getFinDashApproverConfig(),this.getFinancialDocumentSalesOrgConfig(),this.getDocUploadStageWiseConfig(),this.isConfigLoading=!1,this.searchSubject.pipe(Object(Ce.a)(300)).subscribe(e=>{this.approverList=this.fullApproverList.filter(t=>t.name.toLowerCase().includes(e.toLowerCase())).slice(0,50)})}))}cellTemplate(e,t){const n=(Array.isArray(t.value)?t.value:[t.value]).map(e=>t.column.lookup.calculateCellValue(e)).join(", ");e.textContent=n||"\xa0",e.title=n}calculateFilterExpression(e,t,n){return"search"===n&&"string"==typeof e?[this.dataField,"contains",e]:function(t){return-1!==(t.AssignedEmployee||[]).indexOf(e)}}constructNdReplace(e,t){for(let n=0;n<e.length;n++){const o=e[n];if(o.document_type===t.document_type&&o.application_id===t.application_id){let i=!0;for(const e in o)if(o[e]!==t[e]){i=!1;break}i?console.log("same values"):e[n]=t;break}}return e}onEditorPreparing(e){"dataRow"===e.parentType&&(this.isAddingNewRow=e.row&&e.row.isNewRow)}isEditableRow(e){return console.log(e),!this.statusIdsNoEdit.includes(e.status_id)}onEditingStart(e){this.isEditableRow(e.data)||(e.cancel=!0)}onRowPrepared(e){"data"===e.rowType&&this.isEditableRow(e.data)&&e.rowElement.classList.add("editable-row")}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}getDocumentTypes(){return Object(Ee.c)(this,void 0,void 0,(function*(){yield this._quoteService.getOpportunityDocumentTypes().then(e=>{console.log(e),e&&e.data?(this.documentTypes=e.data,this.documentTypesForOppStage=e.data,this.documentTypesForOppStage.push({id:9999,document_type:null,document_label:"None",application_id:36,is_active:1,name:"None"})):(console.warn("No document types received."),this.documentTypes=[])}).catch(e=>{console.error("Failed to retrieve document types:",e)})}))}getAllMembers(){return Object(Ee.c)(this,void 0,void 0,(function*(){try{const e=yield this._quoteService.getAllMembers().toPromise();e?(this.fullApproverList=e,this.approverList=this.fullApproverList.slice(0,5e3)):(console.warn("No members received."),this.approverList=[])}catch(e){console.error("Failed to retrieve members:",e)}}))}getSalesRegionMaster(){return Object(Ee.c)(this,void 0,void 0,(function*(){try{const e=yield this._quoteService.getSalesRegionMaster().toPromise();e?this.salesOrgList=e.data:(console.warn("No members received."),this.approverList=[])}catch(e){console.error("Failed to retrieve members:",e)}}))}onScroll(e){const t=e.component.getScrollable();if(t.scrollHeight()-t.scrollTop()-t.clientHeight()<50){const e=this.approverList.length,t=this.fullApproverList.slice(e,e+50);this.approverList=[...this.approverList,...t]}}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](L.a),y["\u0275\u0275directiveInject"](I.a),y["\u0275\u0275directiveInject"](be.a),y["\u0275\u0275directiveInject"](xe.a),y["\u0275\u0275directiveInject"](qe.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-fin-doc-approver-config"]],decls:5,vars:4,consts:[[1,"container-fluid","fin-doc-config-styles"],[4,"ngIf"],["class","loader",4,"ngIf"],[1,"row","col-12","label","mb-2"],["id","gridContainer","keyExpr","unique_id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowUpdated","onEditorPreparing","onRowPrepared"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","application_id","caption","Application",3,"width","allowSorting"],["displayExpr","application_name","valueExpr","application_id",3,"dataSource"],["type","required"],["dataField","document_type","caption","Document Type",3,"width","allowSorting"],["displayExpr","document_label","valueExpr","document_type",3,"dataSource"],["dataField","ACCSTAKEHOLDER","caption","Account Stakeholder Role","editCellTemplate","accountsTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["valueExpr","owner_type","displayExpr","name",3,"dataSource"],[4,"dxTemplate","dxTemplateOf"],["dataField","OPPSTAKEHOLDERS","caption","Opportunity Stakeholder Role","editCellTemplate","opportunityTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["dataField","MEMBER","caption","Custom Approver","editCellTemplate","customTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["displayExpr","name","valueExpr","id",3,"dataSource"],["valueExpr","owner_type","displayExpr","name","applyValueMode","useButtons",3,"dataSource","value","showSelectionControls","maxDisplayedTags","showMultiTagOnly","inputAttr","searchEnabled","onValueChanged","onSelectionChanged"],["disabled","true","placeholder","Not applicable for Accounts",3,"dataSource","value"],["displayExpr","name","valueExpr","id","applyValueMode","useButtons",3,"dataSource","value","showSelectionControls","maxDisplayedTags","showMultiTagOnly","inputAttr","virtualModeEnabled","searchEnabled","onScroll","onValueChanged","onSelectionChanged","onInput"],[1,"row","col-12","mt-5","mb-2","label"],["id","gridContainer","keyExpr","unique_id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserted","onRowUpdated","onEditorPreparing","onRowPrepared"],["dataField","sales_org","caption","Sales Org",3,"width","allowSorting"],["displayExpr","name","valueExpr","pl_id",3,"dataSource"],["dataField","approver","caption","Sales Org Approver","editCellTemplate","salesTagBoxEditor",3,"width","allowSorting","cellTemplate","calculateFilterExpression"],["displayExpr","name","valueExpr","id","applyValueMode","useButtons",3,"dataSource","value","showSelectionControls","maxDisplayedTags","showMultiTagOnly","inputAttr","searchEnabled","onValueChanged","onSelectionChanged"],["id","gridContainer","keyExpr","status_id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowUpdated","onEditorPreparing","onRowPrepared"],["dataField","sequence_number","caption","Sequence Order",3,"width","allowSorting","allowEditing"],["dataField","status_name","caption","Opportunity Status",3,"width","allowSorting","allowEditing"],["dataField","document_types","caption","Document Type",3,"width","allowSorting","allowFiltering","cellTemplate","editCellTemplate"],["valueExpr","document_type","displayExpr","document_label","applyValueMode","useButtons",3,"dataSource","value","showSelectionControls","maxDisplayedTags","showMultiTagOnly","searchEnabled","onValueChanged","onSelectionChanged"],[1,"loader"],[2,"color","#cf0000",3,"diameter","matTooltip"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275template"](1,Qe,23,35,"ng-container",1),y["\u0275\u0275template"](2,We,18,23,"ng-container",1),y["\u0275\u0275template"](3,Ke,13,24,"ng-container",1),y["\u0275\u0275template"](4,$e,2,2,"div",2),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.approverConfigList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.approverSalesOrgConfigList.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.opportunityStatusDocConfig.length),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngIf",t.isConfigLoading))},directives:[o.NgIf,g.a,te.Qb,te.ae,te.Cc,te.Jd,te.g,te.Wc,te.P,_e.d,w.a,c.c,r.a],styles:[".fin-doc-config-styles[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:500;color:#f27a6c;font-size:14px;line-height:24px;letter-spacing:.02em;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.fin-doc-config-styles[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{height:70vh!important;width:-webkit-fill-available!important;display:flex;align-items:center;justify-content:center;overflow:hidden}"]}),e})()}]}];let Xe=(()=>{class e{}return e.\u0275mod=y["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=y["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[S.k.forChild(He)],S.k]}),e})();var Je=n("Xi0T");let Ye=(()=>{class e{}return e.\u0275mod=y["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=y["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,Xe,i.b,a.b,r.b,s.e,l.c,d.E,c.b,p.d,Je.a,u.d,g.b,m.b,v.b,h.a,f.a,w.b]]}),e})()},WYlB:function(e,t,n){"use strict";n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return d}));var o=n("jhN1"),i=n("fXoL"),a=n("B61+"),r=n("PVOt");const s=["*"];let l=(()=>{let e=class extends r.b{constructor(e,t,n,o,i,a,r){super(e,t,n,o,a,r),this._createEventEmitters([{subscribe:"click",emit:"onClick"},{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{emit:"accessKeyChange"},{emit:"activeStateEnabledChange"},{emit:"disabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"iconChange"},{emit:"rtlEnabledChange"},{emit:"stylingModeChange"},{emit:"tabIndexChange"},{emit:"templateChange"},{emit:"textChange"},{emit:"typeChange"},{emit:"useSubmitBehaviorChange"},{emit:"validationGroupChange"},{emit:"visibleChange"},{emit:"widthChange"}]),i.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get activeStateEnabled(){return this._getOption("activeStateEnabled")}set activeStateEnabled(e){this._setOption("activeStateEnabled",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get icon(){return this._getOption("icon")}set icon(e){this._setOption("icon",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get stylingMode(){return this._getOption("stylingMode")}set stylingMode(e){this._setOption("stylingMode",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get template(){return this._getOption("template")}set template(e){this._setOption("template",e)}get text(){return this._getOption("text")}set text(e){this._setOption("text",e)}get type(){return this._getOption("type")}set type(e){this._setOption("type",e)}get useSubmitBehavior(){return this._getOption("useSubmitBehavior")}set useSubmitBehavior(e){this._setOption("useSubmitBehavior",e)}get validationGroup(){return this._getOption("validationGroup")}set validationGroup(e){this._setOption("validationGroup",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}};return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.ElementRef),i["\u0275\u0275directiveInject"](i.NgZone),i["\u0275\u0275directiveInject"](r.e),i["\u0275\u0275directiveInject"](r.j),i["\u0275\u0275directiveInject"](r.i),i["\u0275\u0275directiveInject"](o.h),i["\u0275\u0275directiveInject"](i.PLATFORM_ID))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-button"]],inputs:{accessKey:"accessKey",activeStateEnabled:"activeStateEnabled",disabled:"disabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",icon:"icon",rtlEnabled:"rtlEnabled",stylingMode:"stylingMode",tabIndex:"tabIndex",template:"template",text:"text",type:"type",useSubmitBehavior:"useSubmitBehavior",validationGroup:"validationGroup",visible:"visible",width:"width"},outputs:{onClick:"onClick",onContentReady:"onContentReady",onDisposing:"onDisposing",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",accessKeyChange:"accessKeyChange",activeStateEnabledChange:"activeStateEnabledChange",disabledChange:"disabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",iconChange:"iconChange",rtlEnabledChange:"rtlEnabledChange",stylingModeChange:"stylingModeChange",tabIndexChange:"tabIndexChange",templateChange:"templateChange",textChange:"textChange",typeChange:"typeChange",useSubmitBehaviorChange:"useSubmitBehaviorChange",validationGroupChange:"validationGroupChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[i["\u0275\u0275ProvidersFeature"]([r.e,r.j,r.i]),i["\u0275\u0275InheritDefinitionFeature"]],ngContentSelectors:s,decls:1,vars:0,template:function(e,t){1&e&&(i["\u0275\u0275projectionDef"](),i["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.c,r.f,o.b],r.f]}),e})()},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var o=n("mrSG"),i=n("XNiG"),a=n("xG9w"),r=n("fXoL"),s=n("tk/3"),l=n("LcQX"),d=n("XXEo"),c=n("flaP");let p=(()=>{class e{constructor(e,t,n,o){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=o,this.msg=new i.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,o,i,a,r){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:o,skip:i,limit:a,filterConfig:r,orgIds:s})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,o,i,a,r){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:o,skip:i,limit:a,filterConfig:r,orgIds:s})}getRequestsForAwaitingApproval(e,t,n,o){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:o})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,o){let i=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:o,orgIds:i})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,i,r,s,l){return Object(o.c)(this,void 0,void 0,(function*(){let o;o=s&&s.length>1&&(yield this.getManpowerCostByOId(s,n,r,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,r,l));let d=yield this.getNonManpowerCost(t,n,i,r,2),c=yield this.getAllocatedCost(),p=0;p=(o?o.cost:0)+d.length>0?a.reduce(a.pluck(d,"cost"),(e,t)=>e+t,0):0;let u=c.length>0?a.reduce(a.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:p,currency:o&&o.currency_code?o.currency_code:"",manpowerCost:o,nonManpowerCost:d,allocatedCost:c,allocatedCostValue:p*(u/100)}}))}getManpowerCostBasedOnPosition(e,t,n,o,i){return new Promise((a,r)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:o,position:i}).subscribe(e=>a(e),e=>(console.log(e),r(e)))})}getNonManpowerCost(e,t,n,o,i){return new Promise((a,r)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:o,currency_id:i}).subscribe(e=>a(e),e=>(console.log(e),r(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,o){return new Promise((i,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:o}).subscribe(e=>i(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](s.c),r["\u0275\u0275inject"](l.a),r["\u0275\u0275inject"](d.a),r["\u0275\u0275inject"](c.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},os0P:function(e,t,n){"use strict";n.d(t,"a",(function(){return E}));var o=n("mrSG"),i=n("fXoL"),a=n("3Pt+"),r=n("jtHE"),s=n("XNiG"),l=n("NJ67"),d=n("1G5W"),c=n("xG9w"),p=n("t44d"),u=n("kmnG"),g=n("ofXK"),m=n("d3UM"),h=n("FKr1"),f=n("WJ5W"),w=n("Qu3c");const v=["singleSelect"];function S(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",6),i["\u0275\u0275text"](1,"Select one"),i["\u0275\u0275elementEnd"]()),2&e&&i["\u0275\u0275property"]("value",null)}function y(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),i["\u0275\u0275property"]("value",e.id),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let E=(()=>{class e extends l.a{constructor(e,t){super(),this.renderer=e,this.pmMasterService=t,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.disableColor="#E8E9EE",this.disableField=!1,this.showSelect=!0,this.uniqueList=[],this.filteredList=new r.a,this.change=new i.EventEmitter,this._onDestroy=new s.b,this.formConfig=[],this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=c.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--inputSearchFont",this.fontStyle),this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)}),this.updateFilteredList()}))}ngOnChanges(){this.updateFilteredList()}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value,t=this.list.slice();e&&(e=e.toLowerCase(),t=t.filter(t=>t.name.toLowerCase().indexOf(e)>-1)),this.uniqueList.length>0&&(t=t.filter(e=>this.uniqueList.includes(e))),this.filteredList.next(t)}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}isDisabled(){return this.disableField?{"background-color":null!=this.disableColor?this.disableColor:"#E8E9EE","pointer-events":"none",color:"#6E7B8F"}:{}}writeValue(e){this.fieldCtrl.setValue(e)}updateFilteredList(){this.uniqueList.length>0?this.filterBanks():this.filteredList.next(this.list.slice())}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2),i["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275viewQuery"](v,!0),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",disableColor:"disableColor",disableField:"disableField",showSelect:"showSelect",uniqueList:"uniqueList"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:11,consts:[["appearance","outline",1,"app-input-search",3,"ngStyle"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-select",1,2),i["\u0275\u0275elementStart"](3,"mat-option"),i["\u0275\u0275element"](4,"ngx-mat-select-search",3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,S,2,1,"mat-option",4),i["\u0275\u0275template"](6,y,2,3,"mat-option",5),i["\u0275\u0275pipe"](7,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275property"]("ngStyle",t.isDisabled()),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.showSelect),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](7,9,t.filteredList)))},directives:[u.c,g.NgStyle,m.c,a.v,a.k,a.F,h.p,f.a,g.NgIf,g.NgForOf,w.a],pipes:[g.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]:disabled{background-color:#0ff}.app-input-search[_ngcontent-%COMP%]     .mat-select-value{color:var(--blue-grey-80,#5f6c81);font-family:var(--inputSearchFont)!important;font-size:13px;font-style:normal;font-weight:400;line-height:16px}"]}),e})()}}]);