(window.webpackJsonp=window.webpackJsonp||[]).push([[769],{"bqX/":function(t,e,n){"use strict";n.r(e),n.d(e,"TsHistoryModalComponent",(function(){return p}));var o=n("0IaG"),a=n("wd/R"),r=n("ofXK"),l=n("bTqV"),i=(n("lVl8"),n("3Pt+"),n("NFeN")),s=(n("kmnG"),n("Xi0T"),n("fXoL")),c=n("me71");function d(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"div",11),s["\u0275\u0275elementStart"](1,"div",12),s["\u0275\u0275elementStart"](2,"p",13),s["\u0275\u0275text"](3,"Action By"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div",12),s["\u0275\u0275elementStart"](5,"p",13),s["\u0275\u0275text"](6,"Status"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"div",12),s["\u0275\u0275elementStart"](8,"p",13),s["\u0275\u0275text"](9,"Level"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",12),s["\u0275\u0275elementStart"](11,"p",13),s["\u0275\u0275text"](12,"Action On"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function m(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",11),s["\u0275\u0275elementStart"](1,"div",14),s["\u0275\u0275elementStart"](2,"div",15),s["\u0275\u0275elementStart"](3,"div",16),s["\u0275\u0275element"](4,"app-user-image",17),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",18),s["\u0275\u0275elementStart"](6,"p",19),s["\u0275\u0275text"](7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",12),s["\u0275\u0275element"](9,"div",20),s["\u0275\u0275elementStart"](10,"p",21),s["\u0275\u0275text"](11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"div",12),s["\u0275\u0275elementStart"](13,"p",22),s["\u0275\u0275text"](14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"div",12),s["\u0275\u0275elementStart"](16,"p",22),s["\u0275\u0275text"](17),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("id",t.oid),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.name),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngClass","Draft"==t.status||"Recalled"==t.status?"is-draft":"Submitted"==t.status||"Awaiting Approval"==t.status?"is-submitted":"Approved"==t.status||"Auto Approved"==t.status||"Escalated"==t.status?"is-approved":"Rejected"==t.status||"Cancelled"==t.status?"is-rejected":"is-draft"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](t.status),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.level),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](n.getLocalDate(t.date))}}let p=(()=>{class t{constructor(t,e){this.dialogRef=t,this.inData=e,this.currentCostCentreWeekHistory=[]}ngOnInit(){this.historyModalParams=this.inData.historyModalParams,this.currentCostCentreWeekHistory=this.historyModalParams.currentCostCentreWeekHistory}closeModal(){this.dialogRef.close({event:"Close"})}getLocalDate(t){return"-"==t?t:a(t,"DD - MMM - YYYY HH : mm : ss").utc(t).local().format("DD - MMM - YYYY HH : mm : ss")}getLength(t){return t.length}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](o.h),s["\u0275\u0275directiveInject"](o.a))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["ts-history-modal"]],decls:14,vars:2,consts:[[1,"container-fluid","history-modal-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-2","pb-2","d-flex","pl-2"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","my-auto","ml-3"],[1,"col-1","d-flex"],["mat-icon-button","","matTooltip","Close",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],["class","row pt-2 pb-2","style","border-bottom: solid 1px #cacaca",4,"ngIf"],["class","row pt-2 pb-2","style","border-bottom: solid 1px #cacaca",4,"ngFor","ngForOf"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-3","d-flex"],[1,"h13"],[1,"col-3"],[1,"row"],[1,"col-2","pl-0","pr-2"],["imgWidth","28px","imgHeight","28px",3,"id"],[1,"col-10","pl-0","pr-0"],[1,"name"],[1,"status-circular",3,"ngClass"],[1,"it14","mr-2","ml-2"],[1,"it14","mr-2"]],template:function(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"mat-icon",4),s["\u0275\u0275text"](5,"history"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"span",5),s["\u0275\u0275text"](7,"History"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",6),s["\u0275\u0275elementStart"](9,"button",7),s["\u0275\u0275listener"]("click",(function(){return e.closeModal()})),s["\u0275\u0275elementStart"](10,"mat-icon",8),s["\u0275\u0275text"](11,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](12,d,13,0,"div",9),s["\u0275\u0275template"](13,m,18,6,"div",10),s["\u0275\u0275elementEnd"]()),2&t&&(s["\u0275\u0275advance"](12),s["\u0275\u0275property"]("ngIf",e.currentCostCentreWeekHistory.length>0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.currentCostCentreWeekHistory))},directives:[i.a,l.a,r.NgIf,r.NgForOf,c.a,r.NgClass],styles:[".history-modal-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.history-modal-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.history-modal-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.history-modal-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.history-modal-styles[_ngcontent-%COMP%]   .long_desc[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%!important;font-size:14px}.history-modal-styles[_ngcontent-%COMP%]   .h13[_ngcontent-%COMP%]{font-size:13px;color:#000;font-weight:700;margin-bottom:0!important}.history-modal-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.history-modal-styles[_ngcontent-%COMP%]   .it14[_ngcontent-%COMP%]{font-size:14px;color:#000;font-weight:400;margin-bottom:0!important;overflow:hidden}.history-modal-styles[_ngcontent-%COMP%]   .date-sub[_ngcontent-%COMP%]{font-size:14px;color:#9a9a9a;font-weight:400}.history-modal-styles[_ngcontent-%COMP%]   .card-action-buttons[_ngcontent-%COMP%]{width:20px!important;height:20px!important;line-height:20px!important}.history-modal-styles[_ngcontent-%COMP%]   .card-action-buttons[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .history-modal-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.history-modal-styles[_ngcontent-%COMP%]   .mr5[_ngcontent-%COMP%]{margin-right:5px!important}.history-modal-styles[_ngcontent-%COMP%]   .modal-active-button[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 35px;border-radius:4px}.history-modal-styles[_ngcontent-%COMP%]   .modal-title[_ngcontent-%COMP%]{font-size:14px}.history-modal-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.history-modal-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.history-modal-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.history-modal-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.history-modal-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.history-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.history-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:16px;margin-top:4px!important;margin-left:5px!important}"]}),t})()}}]);