(window.webpackJsonp=window.webpackJsonp||[]).push([[899],{"0je2":function(t,e,n){"use strict";n.r(e),n.d(e,"TicketStatusHistoryComponent",(function(){return I}));var a=n("mrSG"),o=n("0IaG"),i=n("XNiG"),l=n("ofXK"),s=n("NFeN"),d=n("bTqV"),r=n("Qu3c"),c=(n("lVl8"),n("Xa2L"),n("xm0x"),n("fXoL")),m=n("yu80"),u=n("BVzC"),p=n("YGor"),_=n("Vtn0"),f=n("wd/R");let h=(()=>{class t{transform(t,e){return t&&e?f(e).diff(t,"hours"):t?f().diff(t,"hours"):0}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=c["\u0275\u0275definePipe"]({name:"calendarHours",type:t,pure:!0}),t})(),g=(()=>{class t{transform(t,e){if(t){let n=0,a=f(t).format("YYYY-MM-DD"),o=e?f(e).format("YYYY-MM-DD"):f().format("YYYY-MM-DD"),i=a;for(;i<o;)"Friday"!=f(i).format("dddd")&&"Saturday"!=f(i).format("dddd")&&(n+=8),i=f(i).add(1,"d").format("YYYY-MM-DD");return n}}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=c["\u0275\u0275definePipe"]({name:"businessHours",type:t,pure:!0}),t})();const x=function(t){return{background:t}};function y(t,e){if(1&t&&(c["\u0275\u0275element"](0,"span",27),c["\u0275\u0275pipe"](1,"statusColor"),c["\u0275\u0275pipe"](2,"lookupName")),2&t){const t=c["\u0275\u0275nextContext"]().$implicit,e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction1"](8,x,c["\u0275\u0275pipeBind2"](1,1,c["\u0275\u0275pipeBind3"](2,4,t.status_id,null==e.ticketItem?null:e.ticketItem.status_history_names,"status_name"),e.ticketStatusColors)))}}function v(t,e){1&t&&(c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275element"](1,"span",29),c["\u0275\u0275elementEnd"]())}function b(t,e){1&t&&(c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275element"](1,"span",29),c["\u0275\u0275elementEnd"]())}function k(t,e){if(1&t&&(c["\u0275\u0275elementStart"](0,"div",30),c["\u0275\u0275elementStart"](1,"div",11),c["\u0275\u0275elementStart"](2,"div",12),c["\u0275\u0275elementStart"](3,"div",31),c["\u0275\u0275elementStart"](4,"div",30),c["\u0275\u0275elementStart"](5,"div",32),c["\u0275\u0275text"](6," Calendar hours "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div",33),c["\u0275\u0275text"](8),c["\u0275\u0275pipe"](9,"calendarHours"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](10,"div",32),c["\u0275\u0275text"](11," Business hours "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](12,"div",33),c["\u0275\u0275text"](13),c["\u0275\u0275pipe"](14,"businessHours"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](8),c["\u0275\u0275textInterpolate1"](" ",c["\u0275\u0275pipeBind2"](9,2,null==t?null:t.start_date,null==t?null:t.end_date)," "),c["\u0275\u0275advance"](5),c["\u0275\u0275textInterpolate1"](" ",c["\u0275\u0275pipeBind2"](14,5,null==t?null:t.start_date,null==t?null:t.end_date)," ")}}function C(t,e){1&t&&(c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275element"](1,"span",29),c["\u0275\u0275elementEnd"]())}const M=function(t){return{"bold-font":t}};function S(t,e){if(1&t){const t=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",11),c["\u0275\u0275elementStart"](1,"div",19),c["\u0275\u0275elementStart"](2,"div",20),c["\u0275\u0275template"](3,y,3,10,"span",21),c["\u0275\u0275elementStart"](4,"mat-icon",22),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](t);const n=e.index;return c["\u0275\u0275nextContext"]().toggleDetailMode(n)})),c["\u0275\u0275text"](5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"span",23),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](t);const n=e.index;return c["\u0275\u0275nextContext"]().toggleDetailMode(n)})),c["\u0275\u0275text"](7),c["\u0275\u0275pipe"](8,"lookupName"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"div",20),c["\u0275\u0275elementStart"](10,"span",24),c["\u0275\u0275text"](11),c["\u0275\u0275pipe"](12,"date"),c["\u0275\u0275elementStart"](13,"small"),c["\u0275\u0275text"](14),c["\u0275\u0275pipe"](15,"date"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](16,v,2,0,"div",25),c["\u0275\u0275template"](17,b,2,0,"div",25),c["\u0275\u0275template"](18,k,15,8,"div",26),c["\u0275\u0275template"](19,C,2,0,"div",25),c["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=e.index,a=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngIf",a.ticketStatusColors.length),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.detail_mode_activated?"keyboard_arrow_down":"keyboard_arrow_right"," "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngClass",c["\u0275\u0275pureFunction1"](20,M,t.detail_mode_activated)),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",c["\u0275\u0275pipeBind3"](8,10,t.status_id,null==a.ticketItem?null:a.ticketItem.status_history_names,"status_name")," "),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"](" ",t.start_date?c["\u0275\u0275pipeBind2"](12,14,t.start_date,"dd-MMM-yy"):"-"," "),c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate"](t.start_date?c["\u0275\u0275pipeBind2"](15,17,t.start_date,"hh:mm a"):"-"),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",n<a.statusHistory.length-1),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",n==a.statusHistory.length-1&&t.detail_mode_activated),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.detail_mode_activated),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",n<a.statusHistory.length-1&&t.detail_mode_activated)}}let I=(()=>{class t{constructor(t,e,n,a){this.dialogRef=t,this.inData=e,this._ticket=n,this._ErrorService=a,this.ticketStatusColors=[],this._onDestroy=new i.b,this.statusHistory=[{status_id:"5fdb113aa453d714d93a5292",status_name:"Open",start_date:"2020-12-28",end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Respond",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Waiting for Input",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Waiting for IT Approval",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"IT Approved",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"IT Rejected",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Inprocess Fn",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Inprocess Tc",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"UAT",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Transition period",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"On Hold",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Cancelled",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Completed",start_date:null,end_date:null,actual_hours:180},{status_id:"5fdb113aa453d714d93a5292",status_name:"Closed",start_date:null,end_date:null,actual_hours:180}]}ngOnInit(){this.getStatusColor(),this.ticketItem=this.inData.ticketItem,this.statusHistory=this.ticketItem.status_history;for(let t of this.statusHistory)t.detail_mode_activated=!1}toggleDetailMode(t){this.statusHistory[t].detail_mode_activated=!this.statusHistory[t].detail_mode_activated}closeTicketHistoryModal(){this.dialogRef.close({event:"Close"})}getStatusColor(){return Object(a.c)(this,void 0,void 0,(function*(){let t=yield this._ticket.getStatusColor();if("S"==t.messType&&t.data)for(let e=0;e<t.data.length;e++)this.ticketStatusColors.push({statusName:t.data[e].status_name,statusColor:t.data[e].status_color})}))}}return t.\u0275fac=function(e){return new(e||t)(c["\u0275\u0275directiveInject"](o.h),c["\u0275\u0275directiveInject"](o.a),c["\u0275\u0275directiveInject"](m.a),c["\u0275\u0275directiveInject"](u.a))},t.\u0275cmp=c["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ticket-status-history"]],decls:36,vars:11,consts:[[1,"container-fluid","ticket-history-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","pl-2","pt-2",3,"matTooltip"],[1,"normalFont","pl-1","pt-2",3,"matTooltip"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],["matTooltip","Close",1,"close-Icon"],[1,"row","pt-1"],[1,"col-12"],[1,"card","header-card","slide-in-top"],[1,"card-body",2,"padding","2px !important"],[1,"header","pt-1","col-6"],[1,"pt-1","normalFont","bold-font","col-6"],[1,"row","pt-1","pb-1"],[1,"row","pt-3"],["class","col-12",4,"ngFor","ngForOf"],[1,"row","p-0",2,"height","18px"],[1,"col-6"],["class","status-dot",3,"ngStyle",4,"ngIf"],[2,"font-size","15px","color","#4d4d4b","vertical-align","sub","cursor","pointer",3,"click"],[1,"pl-2","normalFont","pt-1",2,"cursor","pointer",3,"ngClass","click"],[1,"pl-2","normalFont","pt-1"],["class","row line-height",4,"ngIf"],["class","row",4,"ngIf"],[1,"status-dot",3,"ngStyle"],[1,"row","line-height"],[2,"border-left","1px solid #c7c4c4"],[1,"row"],[1,"card-body","p-2"],[1,"col-3","header"],[1,"col-3","normalFont","bold-font"]],template:function(t,e){1&t&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"div",3),c["\u0275\u0275elementStart"](4,"mat-icon",4),c["\u0275\u0275text"](5,"timer"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"span",5),c["\u0275\u0275text"](7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"span",6),c["\u0275\u0275text"](9),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](10,"div",7),c["\u0275\u0275elementStart"](11,"button",8),c["\u0275\u0275listener"]("click",(function(){return e.closeTicketHistoryModal()})),c["\u0275\u0275elementStart"](12,"mat-icon",9),c["\u0275\u0275text"](13,"close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](14,"div",10),c["\u0275\u0275elementStart"](15,"div",11),c["\u0275\u0275elementStart"](16,"div",12),c["\u0275\u0275elementStart"](17,"div",13),c["\u0275\u0275elementStart"](18,"div",10),c["\u0275\u0275elementStart"](19,"div",14),c["\u0275\u0275text"](20," Estimated closure date "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](21,"div",15),c["\u0275\u0275text"](22),c["\u0275\u0275pipe"](23,"date"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](24,"div",10),c["\u0275\u0275elementStart"](25,"div",14),c["\u0275\u0275text"](26," Planned hours "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](27,"div",15),c["\u0275\u0275text"](28),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](29,"div",16),c["\u0275\u0275elementStart"](30,"div",14),c["\u0275\u0275text"](31," Actual hours "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](32,"div",15),c["\u0275\u0275text"](33),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](34,"div",17),c["\u0275\u0275template"](35,S,20,22,"div",18),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&t&&(c["\u0275\u0275advance"](6),c["\u0275\u0275property"]("matTooltip",null==e.ticketItem?null:e.ticketItem.project_item_name),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"]("",null==e.ticketItem?null:e.ticketItem.project_item_name," -"),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("matTooltip",null==e.ticketItem?null:e.ticketItem.ticket_title),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate"](null==e.ticketItem?null:e.ticketItem.ticket_title),c["\u0275\u0275advance"](13),c["\u0275\u0275textInterpolate1"](" ",e.ticketItem.estimated_closure_date?c["\u0275\u0275pipeBind2"](23,8,e.ticketItem.estimated_closure_date,"dd-MMM-yy"):"-"," "),c["\u0275\u0275advance"](6),c["\u0275\u0275textInterpolate1"](" ",e.ticketItem.estimated_hours?e.ticketItem.estimated_hours:"-"," "),c["\u0275\u0275advance"](5),c["\u0275\u0275textInterpolate1"](" ",e.ticketItem.actual_hours?e.ticketItem.actual_hours:"-"," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",e.statusHistory))},directives:[s.a,r.a,d.a,l.NgForOf,l.NgIf,l.NgClass,l.NgStyle],pipes:[l.DatePipe,p.a,_.a,h,g],styles:[".ticket-history-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.ticket-history-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.ticket-history-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.ticket-history-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.ticket-history-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.ticket-history-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%], .ticket-history-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ticket-history-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-weight:400}.ticket-history-styles[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:13px;width:13px;border-radius:50%;display:inline-block;vertical-align:middle}.ticket-history-styles[_ngcontent-%COMP%]   .line-height[_ngcontent-%COMP%]{height:15px;padding-left:20px}.ticket-history-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%]{font-weight:600}.ticket-history-styles[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{position:relative;height:2.5rem;width:100%}.ticket-history-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.ticket-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.ticket-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.ticket-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.ticket-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.ticket-history-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.ticket-history-styles[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.ticket-history-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.ticket-history-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})()}}]);