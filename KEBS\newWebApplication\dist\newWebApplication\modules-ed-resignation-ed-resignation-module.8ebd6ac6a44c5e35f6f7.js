(window.webpackJsonp=window.webpackJsonp||[]).push([[816],{"3ZXS":function(e,t,n){"use strict";n.r(t),n.d(t,"EdResignationModule",(function(){return so}));var o=n("ofXK"),i=n("tyNb"),r=n("mrSG"),a=n("0IaG"),l=n("wd/R"),s=n.n(l),c=n("33Jv"),d=n("fXoL"),m=n("bxdq"),p=n("1A3m"),g=n("XXEo"),f=n("Xa2L"),h=n("Qu3c"),u=n("Wp6s"),x=n("NFeN"),v=n("me71"),_=n("mS9j");function C(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"ng-contianer"),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function y(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",25),d["\u0275\u0275text"](1,"done"),d["\u0275\u0275elementEnd"]())}function b(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",25),d["\u0275\u0275text"](1,"clear"),d["\u0275\u0275elementEnd"]())}const O=function(e,t){return{"blurData step active":e,"step active":t}},M=function(e){return{background:e}},P=function(e){return{"background-color":e}};function S(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",13),d["\u0275\u0275elementStart"](2,"div",14),d["\u0275\u0275elementStart"](3,"a"),d["\u0275\u0275template"](4,y,2,0,"mat-icon",15),d["\u0275\u0275template"](5,b,2,0,"mat-icon",15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",16),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275elementStart"](8,"div",17),d["\u0275\u0275elementStart"](9,"div",18),d["\u0275\u0275element"](10,"app-user-image",19),d["\u0275\u0275text"](11,"\xa0\xa0 "),d["\u0275\u0275element"](12,"app-user-profile",20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",21),d["\u0275\u0275text"](14,"Completed on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",21),d["\u0275\u0275text"](16,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",17),d["\u0275\u0275elementStart"](18,"div",22),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",23),d["\u0275\u0275text"](21),d["\u0275\u0275pipe"](22,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"div",24),d["\u0275\u0275element"](24,"div",14),d["\u0275\u0275elementStart"](25,"div"),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction2"](13,O,o.blurFlag&&"reject"!=e.type,!o.blurFlag)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](16,M,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","reject"!=e.type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","reject"==e.type),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("id",e.approver_oid),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("oid",e.approver_oid),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",0==n?"Direct Manager":"Functional Head"," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.completed_on?d["\u0275\u0275pipeBind2"](22,10,e.completed_on,"dd-MMM-YYYY"):"--"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](18,P,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.status)}}function w(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"mat-card",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",10),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).close()})),d["\u0275\u0275elementStart"](6,"mat-icon",11),d["\u0275\u0275text"](7,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"br"),d["\u0275\u0275elementStart"](9,"main"),d["\u0275\u0275template"](10,S,27,20,"div",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.header),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data)}}function E(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",26),d["\u0275\u0275elementStart"](1,"div",27),d["\u0275\u0275elementStart"](2,"div",28),d["\u0275\u0275element"](3,"img",29),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",30),d["\u0275\u0275text"](6,"Approvers Not Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function k(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,w,11,2,"div",4),d["\u0275\u0275template"](2,E,7,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let I=(()=>{class e{constructor(e,t,n,o,i,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=o,this._loginService=i,this.data1=r,this.header=["Manager Approval"],this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date,this.blurFlag=!1}ngOnInit(){var e;return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchApprovers(),null===(e=this.approvalData)||void 0===e||e.data.forEach(e=>{"reject"==e.type&&(this.blurFlag=!0)}),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchApprovers(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"manager_acceptance",exit_employee_id:this.data1.emp_Id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchApprovers(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Manageral Approval !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-manager-status"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"loaDer"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[2,"width","100%","height","100vh"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[2,"height","13%","display","flex","margin-bottom","1rem",3,"ngClass"],[1,"circle",3,"ngStyle"],["class","done-icon",4,"ngIf"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],[1,"done-icon"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,C,3,0,"ng-contianer",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,k,3,2,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,u.a,x.a,o.NgForOf,o.NgClass,o.NgStyle,v.a,_.a],pipes:[o.DatePipe],styles:[".whole[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.whole[_ngcontent-%COMP%]   .blurData[_ngcontent-%COMP%]{opacity:.6;cursor:not-allowed;height:13%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.whole[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%;height:13vh}.whole[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin-top:24px;left:2%}.whole[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .done-icon[_ngcontent-%COMP%]{color:#fff;font-size:20px;margin-top:11%;margin-left:8%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:capitalize;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;gap:6%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;display:flex;justify-content:center;align-items:center}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]{height:120px;width:100%;background:#f7f9fb;border-radius:4px;padding:1rem}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;padding:2rem 0 0}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%]{border:1px solid #45546e;font-style:normal;font-weight:600;font-size:13px;letter-spacing:.01em;width:105px;height:40px;background:#f7f9fb;border-radius:8px;flex:none;color:#45546e;display:flex;justify-content:center;align-items:center}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:5px;padding:1rem;margin-left:0}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:8rem}.loaDer[_ngcontent-%COMP%]{height:100vh;margin-top:0!important;display:flex;justify-content:center;align-items:center}"]}),e})();function D(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"ng-contianer"),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}const F=function(e){return{background:e}},j=function(e){return{"background-color":e}};function z(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",15),d["\u0275\u0275elementStart"](2,"div",16),d["\u0275\u0275elementStart"](3,"a"),d["\u0275\u0275elementStart"](4,"mat-icon",17),d["\u0275\u0275text"](5,"done"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",18),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275elementStart"](8,"div",19),d["\u0275\u0275elementStart"](9,"div",20),d["\u0275\u0275element"](10,"app-user-image",21),d["\u0275\u0275text"](11,"\xa0\xa0 "),d["\u0275\u0275element"](12,"app-user-profile",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",23),d["\u0275\u0275text"](14,"Completed on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",23),d["\u0275\u0275text"](16,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",19),d["\u0275\u0275elementStart"](18,"div",24),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",25),d["\u0275\u0275text"](21),d["\u0275\u0275pipe"](22,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"div",26),d["\u0275\u0275element"](24,"div",16),d["\u0275\u0275elementStart"](25,"div"),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](10,F,e.color)),d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("id",e.approver_oid),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("oid",e.approver_oid),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",e.wf_desc," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.completed_on?d["\u0275\u0275pipeBind2"](22,7,e.completed_on,"dd-MMM-YYYY"):"--"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](12,j,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.status)}}function A(e,t){1&e&&d["\u0275\u0275element"](0,"div",36)}function R(e,t){1&e&&d["\u0275\u0275element"](0,"div",37)}function N(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",38),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](5);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," ")}}function q(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",39),d["\u0275\u0275elementStart"](1,"div",40),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("matTooltip",null==t.fieldConfig[e].options[t.adminDetails[0][e.key]-1]?null:t.fieldConfig[e].options[t.adminDetails[0][e.key]-1].name),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",null==t.fieldConfig[e].options[t.adminDetails[0][e]-1]?null:t.fieldConfig[e].options[t.adminDetails[0][e]-1].name," ")}}function Y(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",39),d["\u0275\u0275elementStart"](1,"div",40),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("matTooltip",t.adminDetails[0][e.key]),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",t.adminDetails[0][e]?t.adminDetails[0][e]:"--"," ")}}function V(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",29),d["\u0275\u0275elementStart"](2,"div",30),d["\u0275\u0275template"](3,A,1,0,"div",31),d["\u0275\u0275template"](4,R,1,0,"div",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,N,2,1,"div",33),d["\u0275\u0275elementStart"](6,"div",34),d["\u0275\u0275template"](7,q,3,2,"div",35),d["\u0275\u0275template"](8,Y,3,2,"div",35),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](5);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf","text"!=n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","toggle-button"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field)}}function T(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",28),d["\u0275\u0275template"](1,V,9,5,"div",12),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.sortedKeys(e.fieldConfig))}}function H(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,T,2,1,"div",27),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.fieldConfig&&e.adminDetails)}}function B(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",41),d["\u0275\u0275elementStart"](1,"div",42),d["\u0275\u0275text"](2," Approver not yet filled the form "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function $(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",10),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).close()})),d["\u0275\u0275elementStart"](6,"mat-icon",11),d["\u0275\u0275text"](7,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"br"),d["\u0275\u0275elementStart"](9,"main"),d["\u0275\u0275template"](10,z,27,14,"div",12),d["\u0275\u0275elementStart"](11,"div",13),d["\u0275\u0275template"](12,H,2,1,"div",1),d["\u0275\u0275template"](13,B,3,0,"div",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.header),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",(null==e.adminDetails?null:e.adminDetails.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==(null==e.adminDetails?null:e.adminDetails.length))}}function W(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",43),d["\u0275\u0275elementStart"](1,"div",44),d["\u0275\u0275elementStart"](2,"div",45),d["\u0275\u0275element"](3,"img",46),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",47),d["\u0275\u0275text"](6,"Approvers Not Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,$,14,4,"div",4),d["\u0275\u0275template"](2,W,7,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let L=(()=>{class e{constructor(e,t,n,o,i,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=o,this._loginService=i,this.data1=r,this.header=["Administrator"],this.subs=new c.a,this.displayCondition=!1,this.approvalData=[],this.adminDetails=[],this.currentDate=new Date,this.formfields=[],this.fieldConfig={}}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchApprovers(),this.adminDetails=yield this.fetchAdminFormDetails(),this.formfields=yield this.getAdminFormConfig(),yield this.handleFieldConfig(),yield this.getEmpExitID(),this.page_loader=!1}))}sortedKeys(e){return Object.keys(e).sort((t,n)=>e[t].sort_order-e[n].sort_order)}handleFieldConfig(){return Object(r.c)(this,void 0,void 0,(function*(){this.fieldConfig={};let e=this.formfields;if(console.log("fieldList",e),e.length>0){const t=new Map;e.forEach((e,n)=>{t.set(e.field_key,e)}),t.forEach((e,t)=>{this.fieldConfig[t]=e}),console.log(this.fieldConfig)}console.log("Field Config",this.fieldConfig)}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchApprovers(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"admin",exit_employee_id:this.data1.emp_Id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchApprovers(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}getAdminFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.getAdminFormConfig().subscribe(t=>{t.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,e(t.data))},e=>{this._toaster.showError("Error","Failed to retrive Finance Form!",2e3),console.log(e),t(e)})})}fetchAdminFormDetails(){console.log("API System Admin Called");let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.data1.emp_Id}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchAdminFormDetails(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-admin-status"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","100vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[1,"main"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[1,"noDues"],["class","pl-3",4,"ngIf"],[1,"step","active",2,"height","13vh"],[1,"circle",3,"ngStyle"],[2,"color","#fff","font-size","20px","margin-top","17%","margin-left","7%"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],["class","admin-description id-card",4,"ngIf"],[1,"admin-description","id-card"],[1,"row",2,"margin-bottom","5px","color","#26303e"],[2,"display","flex","justify-content","center","align-items","flex-start","margin-top","5px"],["style","\n                        width: 7px;\n                        height: 8px;\n                        border-radius: 50%;\n                        background-color: black;\n                      ",4,"ngIf"],["style","width: 7px; height: 8px",4,"ngIf"],["class","id-card-header col-7",4,"ngIf"],[1,"id-card-status","col-4"],["class","status-desc",3,"matTooltip",4,"ngIf"],[2,"width","7px","height","8px","border-radius","50%","background-color","black"],[2,"width","7px","height","8px"],[1,"id-card-header","col-7"],[1,"status-desc",3,"matTooltip"],[1,"id-card-status-tooltip"],[1,"pl-3"],[1,"no-data-from-approver"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,D,3,0,"ng-contianer",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,G,3,2,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,x.a,o.NgForOf,o.NgStyle,v.a,_.a],pipes:[o.DatePipe],styles:[".main[_ngcontent-%COMP%]{padding:1rem}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2% 4%}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:none;color:#000}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:none;color:#000;gap:6%}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;width:100%;padding:.5rem 1.5rem 1rem}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .id-card-status-tooltip[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .update-from-system-admin[_ngcontent-%COMP%]{padding:.5rem;font-weight:550;font-size:16px;line-height:24px;letter-spacing:.02em}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%], .main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .no-data-from-approver[_ngcontent-%COMP%]{border:1px solid #7d838b;border-radius:8px;opacity:.9;font-weight:400;font-size:14px;line-height:24px;letter-spacing:.02em;color:#5e5d5d;height:100px;padding:1rem}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .no-data-from-approver[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center}.step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin-top:26px;left:2%}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:4px;margin-left:0;padding:1rem 1rem 1rem 1.2rem;height:11vh}.red-text[_ngcontent-%COMP%]{color:#ee4961}.green-text[_ngcontent-%COMP%]{color:#009432}"]}),e})();function K(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"ng-contianer"),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}const J=function(e){return{background:e}},X=function(e){return{"background-color":e}};function U(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",15),d["\u0275\u0275elementStart"](2,"div",16),d["\u0275\u0275elementStart"](3,"a"),d["\u0275\u0275elementStart"](4,"mat-icon",17),d["\u0275\u0275text"](5,"done"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",18),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275elementStart"](8,"div",19),d["\u0275\u0275elementStart"](9,"div",20),d["\u0275\u0275element"](10,"app-user-image",21),d["\u0275\u0275text"](11,"\xa0\xa0 "),d["\u0275\u0275element"](12,"app-user-profile",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",23),d["\u0275\u0275text"](14,"Completed on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",23),d["\u0275\u0275text"](16,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",19),d["\u0275\u0275elementStart"](18,"div",24),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",25),d["\u0275\u0275text"](21),d["\u0275\u0275pipe"](22,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"div",26),d["\u0275\u0275element"](24,"div",16),d["\u0275\u0275elementStart"](25,"div"),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](10,J,e.color)),d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("id",e.approver_oid),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("oid",e.approver_oid),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",e.wf_desc," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.completed_on?d["\u0275\u0275pipeBind2"](22,7,e.completed_on,"dd-MMM-YYYY"):"--"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](12,X,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.status)}}function Z(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275elementStart"](1,"div",34),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",35),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",null==e.fieldConfig||null==e.fieldConfig.amount?null:e.fieldConfig.amount.field_label),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.amount?null:e.fieldConfig.amount.field_label," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",null==e.fieldConfig||null==e.fieldConfig.amount?null:e.fieldConfig.amount.options[e.financeDetails[0].amount-1].name),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.amount?null:e.fieldConfig.amount.options[e.financeDetails[0].amount-1].name," ")}}function Q(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275elementStart"](1,"div",36),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",37),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",null==e.fieldConfig||null==e.fieldConfig.amount_remark?null:e.fieldConfig.amount_remark.field_label),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.amount_remark?null:e.fieldConfig.amount_remark.field_label," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.financeDetails[0].amount_remark),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.financeDetails[0].amount_remark?e.financeDetails[0].amount_remark:"--"," ")}}function ee(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",38),d["\u0275\u0275elementStart"](1,"div",39),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",35),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275property"]("matTooltip",null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.field_label),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.field_label," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.options[e.financeDetails[0].settlement-1].name),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.options[e.financeDetails[0].settlement-1].name," ")}}function te(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275elementStart"](1,"div",36),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",37),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",null==e.fieldConfig||null==e.fieldConfig.settlement_remark?null:e.fieldConfig.settlement_remark.field_label),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.settlement_remark?null:e.fieldConfig.settlement_remark.field_label," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.financeDetails[0].settlement_remark),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.financeDetails[0].settlement_remark?e.financeDetails[0].settlement_remark:"--"," ")}}function ne(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",27),d["\u0275\u0275elementStart"](2,"div",28),d["\u0275\u0275elementStart"](3,"div",29),d["\u0275\u0275elementStart"](4,"div",30),d["\u0275\u0275template"](5,Z,5,4,"div",31),d["\u0275\u0275template"](6,Q,5,4,"div",31),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",30),d["\u0275\u0275template"](8,ee,5,4,"div",32),d["\u0275\u0275template"](9,te,5,4,"div",31),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.amount?null:e.fieldConfig.amount.is_active)&&(null==e.fieldConfig||null==e.fieldConfig.amount?null:e.fieldConfig.amount.is_active_field)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.amount_remark?null:e.fieldConfig.amount_remark.is_active)&&(null==e.fieldConfig||null==e.fieldConfig.amount_remark?null:e.fieldConfig.amount_remark.is_active_field)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.is_active)&&(null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.is_active_field)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.settlement_remark?null:e.fieldConfig.settlement_remark.is_active)&&(null==e.fieldConfig||null==e.fieldConfig.settlement_remark?null:e.fieldConfig.settlement_remark.is_active_field))}}function oe(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",40),d["\u0275\u0275elementStart"](1,"div",41),d["\u0275\u0275text"](2," Approver not yet filled the form "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",10),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).close()})),d["\u0275\u0275elementStart"](6,"mat-icon",11),d["\u0275\u0275text"](7,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"br"),d["\u0275\u0275elementStart"](9,"main"),d["\u0275\u0275template"](10,U,27,14,"div",12),d["\u0275\u0275elementStart"](11,"div",13),d["\u0275\u0275template"](12,ne,10,4,"div",1),d["\u0275\u0275template"](13,oe,3,0,"div",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.header),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",(null==e.financeDetails?null:e.financeDetails.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==(null==e.financeDetails?null:e.financeDetails.length))}}function re(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",42),d["\u0275\u0275elementStart"](1,"div",43),d["\u0275\u0275elementStart"](2,"div",44),d["\u0275\u0275element"](3,"img",45),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",46),d["\u0275\u0275text"](6,"Approvers Not Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function ae(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,ie,14,4,"div",4),d["\u0275\u0275template"](2,re,7,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let le=(()=>{class e{constructor(e,t,n,o,i,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=o,this._loginService=i,this.data1=r,this.header=["Finance Department"],this.subs=new c.a,this.displayCondition=!1,this.approvalData=[],this.financeDetails=[],this.fieldConfig={},this.formfields=[],this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchApprovers(),this.financeDetails=yield this.fetchFinanceFormDetails(),this.formfields=yield this.getFinanceFormConfig(),yield this.handleFieldConfig(),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}sortedKeys(e){return Object.keys(e).sort((t,n)=>e[t].sort_order-e[n].sort_order)}handleFieldConfig(){return Object(r.c)(this,void 0,void 0,(function*(){this.fieldConfig={};let e=this.formfields;if(console.log("fieldList",e),e.length>0){const t=new Map;e.forEach((e,n)=>{t.set(e.field_key,e)}),t.forEach((e,t)=>{this.fieldConfig[t]=e}),console.log(this.fieldConfig)}console.log("Field Config",this.fieldConfig)}))}fetchApprovers(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"finance",exit_employee_id:this.data1.emp_Id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchApprovers(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}fetchFinanceFormDetails(){console.log("API System Admin Called");let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.data1.emp_Id}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchFinanceFormDetails(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}getFinanceFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.getFinanceFormConfig().subscribe(t=>{t.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,e(t.data))},e=>{this._toaster.showError("Error","Failed to retrive Finance Form!",2e3),console.log(e),t(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-finance-status"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","100vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[1,"main"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[1,"noDues"],["class","pl-3",4,"ngIf"],[1,"step","active",2,"height","13vh"],[1,"circle",3,"ngStyle"],[1,"done-icon",2,"color","#fff","font-size","20px","margin-top","17%","margin-left","7%"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],[1,"description-from-system-admin"],[1,"finance-description"],[1,"decriptives","row"],[1,"row","col-12",2,"margin-bottom","10px"],["class","loan-type col-6",4,"ngIf"],["class","loan-type col-6",3,"matTooltip",4,"ngIf"],[1,"loan-type","col-6"],[1,"header","col-8",3,"matTooltip"],[1,"type","col-4",3,"matTooltip"],[1,"header","col-4",3,"matTooltip"],[1,"type","col-8",3,"matTooltip"],[1,"loan-type","col-6",3,"matTooltip"],[1,"header","col-8"],[1,"pl-3"],[1,"no-data-from-approver"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,K,3,0,"ng-contianer",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,ae,3,2,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,x.a,o.NgForOf,o.NgStyle,v.a,_.a],pipes:[o.DatePipe],styles:[".main[_ngcontent-%COMP%]{padding:1rem}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2% 4%}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000;text-overflow:ellipsis;overflow:hidden}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:none;color:#000}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:none;color:#000;gap:6%}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .done-icon[_ngcontent-%COMP%]{color:#fff;font-size:20px;margin-top:17%;margin-left:7%}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;width:100%;padding:.5rem 1.5rem 1rem}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .update-from-system-admin[_ngcontent-%COMP%]{padding:.5rem;font-weight:550;font-size:16px;line-height:24px;letter-spacing:.02em}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%]{border:1px solid #7d838b;border-radius:8px;opacity:.9;font-weight:400;font-size:14px;line-height:24px;letter-spacing:.02em;color:#5e5d5d;height:100px;padding:1rem}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%]   .finance-description[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%]   .finance-description[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:none;color:#000}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%]   .finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:6%;padding:1.5rem 3px 3px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%]   .finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]   .loan-type[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:5%;width:20%}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%]   .finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]   .loan-type[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.4;text-overflow:ellipsis;overflow:hidden}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%]   .finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]   .loan-type[_ngcontent-%COMP%]   .type[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:none;color:#000;text-overflow:ellipsis;overflow:hidden}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .no-data-from-approver[_ngcontent-%COMP%]{border:1px solid #7d838b;border-radius:8px;opacity:.9;font-weight:400;font-size:14px;line-height:24px;letter-spacing:.02em;color:#5e5d5d;height:100px;padding:1rem;display:flex;justify-content:flex-start;align-items:center}.step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin-top:26px;left:2%}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:4px;margin-left:0;padding:1rem 1rem 1rem 1.2rem;height:11vh}.green-text[_ngcontent-%COMP%]{color:#009432}.red-text[_ngcontent-%COMP%]{color:#ee4961}"]}),e})();var se=n("3Pt+"),ce=n("dNgK"),de=n("qFsG"),me=n("iadO"),pe=n("kmnG");function ge(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]())}function fe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",12),d["\u0275\u0275elementStart"](1,"h5",13),d["\u0275\u0275text"](2,"Take over Details"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",14),d["\u0275\u0275elementStart"](4,"div",15),d["\u0275\u0275elementStart"](5,"div",16),d["\u0275\u0275text"](6,"Taken over from "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",17),d["\u0275\u0275text"](8,"Name"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",18),d["\u0275\u0275text"](10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"div",15),d["\u0275\u0275elementStart"](12,"div",16),d["\u0275\u0275text"](13,"Taken over by"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"div",17),d["\u0275\u0275text"](15,"Name"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",18),d["\u0275\u0275text"](17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](18,"div",14),d["\u0275\u0275elementStart"](19,"div",15),d["\u0275\u0275elementStart"](20,"div",17),d["\u0275\u0275text"](21,"Designation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](22,"div",18),d["\u0275\u0275text"](23),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](24,"div",15),d["\u0275\u0275elementStart"](25,"div",17),d["\u0275\u0275text"](26,"Designation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](27,"div",18),d["\u0275\u0275text"](28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](10),d["\u0275\u0275textInterpolate1"](" ",e.taken_from_name," "),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",n.handoverToDetails[0].NAME," "),d["\u0275\u0275advance"](6),d["\u0275\u0275textInterpolate1"](" ",e.taken_from_designation," "),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",n.handoverToDetails[0].position," ")}}function he(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275template"](1,fe,29,4,"div",11),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.handoverTakenoverDetails)}}function ue(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"b"),d["\u0275\u0275elementStart"](1,"u"),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e)}}function xe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e)}}function ve(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,ue,3,1,"b",1),d["\u0275\u0275template"](2,xe,2,1,"span",1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Product & Business Support Services"==e),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Product & Business Support Services"!=e)}}function _e(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",38),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function Ce(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",34),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"div",35),d["\u0275\u0275template"](3,ve,3,2,"div",36),d["\u0275\u0275template"](4,_e,2,0,"span",37),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,o=d["\u0275\u0275nextContext"](2).index,i=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",i.getFormControlValue(t,"lable_name")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==i.params.exitSurveySection[o].lables[n].is_mandatory)}}function ye(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"b"),d["\u0275\u0275elementStart"](1,"u"),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e)}}function be(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e)}}function Oe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,ye,3,1,"b",1),d["\u0275\u0275template"](2,be,2,1,"span",1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Product & Business Support Services"==e),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Product & Business Support Services"!=e)}}function Me(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",38),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function Pe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",39),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"div",35),d["\u0275\u0275template"](3,Oe,3,2,"div",36),d["\u0275\u0275template"](4,Me,2,0,"span",37),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,o=d["\u0275\u0275nextContext"](2).index,i=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",i.getFormControlValue(t,"lable_name")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==i.params.exitSurveySection[o].lables[n].is_mandatory)}}function Se(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",41),d["\u0275\u0275elementStart"](2,"div",42),d["\u0275\u0275elementStart"](3,"input",43),d["\u0275\u0275listener"]("keydown",(function(e){return e.preventDefault()}))("dateChange",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"](2).index;return d["\u0275\u0275nextContext"](3).changeDateSelect(i,o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"mat-datepicker-toggle",44),d["\u0275\u0275element"](5,"mat-datepicker",null,45),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](7,"br"),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275reference"](6);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("matDatepicker",e),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("for",e)}}function we(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",41),d["\u0275\u0275elementStart"](2,"div",46),d["\u0275\u0275elementStart"](3,"input",47),d["\u0275\u0275listener"]("keydown.Tab",(function(e){return e.preventDefault()})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementEnd"]())}function Ee(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"label",51),d["\u0275\u0275text"](1,"Remarks"),d["\u0275\u0275elementEnd"]())}const ke=function(e,t){return{"textfield-for-project-input":e,"text-field":t}};function Ie(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",34),d["\u0275\u0275elementStart"](1,"div",48),d["\u0275\u0275template"](2,Ee,2,0,"label",49),d["\u0275\u0275elementStart"](3,"input",50),d["\u0275\u0275listener"]("keydown.Tab",(function(e){return e.preventDefault()}))("change",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"](2).index;return d["\u0275\u0275nextContext"](3).changeTextSelect(i,o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](4).$implicit,t=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","project_handover_takeover"!=t.getFormControlValue(e,"section_name")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction2"](2,ke,"project_handover_takeover"==t.getFormControlValue(e,"section_name"),"project_handover_takeover"!=t.getFormControlValue(e,"section_name")))}}function De(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"input",53),d["\u0275\u0275listener"]("keydown",(function(e){return e.preventDefault()})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](3,"mat-datepicker-toggle",44),d["\u0275\u0275element"](4,"mat-datepicker",null,45),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](6,"br"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275reference"](5);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matDatepicker",e),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("for",e)}}function Fe(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"label",51),d["\u0275\u0275text"](1,"Remarks"),d["\u0275\u0275elementEnd"]())}function je(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",52),d["\u0275\u0275elementStart"](1,"div",48),d["\u0275\u0275template"](2,De,7,2,"div",1),d["\u0275\u0275template"](3,Fe,2,0,"label",49),d["\u0275\u0275elementStart"](4,"input",50),d["\u0275\u0275listener"]("keydown.Tab",(function(e){return e.preventDefault()}))("change",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"](2).index;return d["\u0275\u0275nextContext"](3).changeTextSelect(i,o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](3).$implicit,n=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","date"==n.getFormControlValue(e,"field_type")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","project_handover_takeover"!=n.getFormControlValue(t,"section_name")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction2"](3,ke,"project_handover_takeover"==n.getFormControlValue(t,"section_name"),"project_handover_takeover"!=n.getFormControlValue(t,"section_name")))}}function ze(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",29),d["\u0275\u0275template"](1,Se,8,2,"div",1),d["\u0275\u0275template"](2,we,5,0,"div",1),d["\u0275\u0275template"](3,Ie,4,5,"div",31),d["\u0275\u0275template"](4,je,5,6,"div",40),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("formGroupName",n),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","date"==i.getFormControlValue(e,"field_type")&&!i.submitFlag),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","date"==i.getFormControlValue(e,"field_type")&&i.submitFlag),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","freeTextWithLabel"==i.getFormControlValue(e,"field_type")&&o%2!=0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","freeTextWithLabel"==i.getFormControlValue(e,"field_type")&&o%2==0)}}const Ae=function(e){return{"textfield-for-project":e}};function Re(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",29),d["\u0275\u0275elementStart"](1,"div",30),d["\u0275\u0275template"](2,Ce,5,2,"div",31),d["\u0275\u0275template"](3,Pe,5,2,"div",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",33),d["\u0275\u0275template"](5,ze,5,5,"div",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=d["\u0275\u0275nextContext"](2),o=n.$implicit,i=n.index,r=d["\u0275\u0275nextContext"](3);d["\u0275\u0275property"]("formGroupName",e),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e%2!=0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e%2==0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction1"](5,Ae,"project_handover_takeover"==r.getFormControlValue(o,"section_name"))),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",r.getOptionFormArray(i,e).controls)}}function Ne(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",27),d["\u0275\u0275template"](1,Re,6,7,"div",28),d["\u0275\u0275element"](2,"br"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().index,t=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",t.getLableFormArray(e).controls)}}function qe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"b"),d["\u0275\u0275elementStart"](1,"u"),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e)}}function Ye(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e)}}function Ve(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,qe,3,1,"b",1),d["\u0275\u0275template"](2,Ye,2,1,"span",1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Product & Business Support Services"==e),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","Product & Business Support Services"!=e)}}function Te(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",38),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function He(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",57),d["\u0275\u0275elementStart"](2,"input",58),d["\u0275\u0275listener"]("keydown.Tab",(function(e){return e.preventDefault()}))("change",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]().index,n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"](2).index;return d["\u0275\u0275nextContext"](3).changeSingleSelect(o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"label",59),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](5,"br"),d["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](),t=e.index,n=e.$implicit,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"](2).index,r=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("checked",r.params.exitSurveySection[i].lables[o].options[t].isChecked),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](r.getFormControlValue(n,"checkbox_name"))}}function Be(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"input",60),d["\u0275\u0275listener"]("keydown.Tab",(function(e){return e.preventDefault()}))("change",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]().index,n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"](2).index;return d["\u0275\u0275nextContext"](3).changeMultipleSelect(o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](2,"label",61),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](6);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](t.getFormControlValue(e,"checkbox_name"))}}function $e(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"input",53),d["\u0275\u0275listener"]("keydown",(function(e){return e.preventDefault()})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](3,"mat-datepicker-toggle",44),d["\u0275\u0275element"](4,"mat-datepicker",null,45),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](6,"br"),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275reference"](5);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matDatepicker",e),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("for",e)}}function We(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"label",51),d["\u0275\u0275text"](1,"Remarks"),d["\u0275\u0275elementEnd"]())}function Ge(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",48),d["\u0275\u0275template"](2,We,2,0,"label",49),d["\u0275\u0275elementStart"](3,"input",50),d["\u0275\u0275listener"]("keydown.Tab",(function(e){return e.preventDefault()}))("change",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"](2).index;return d["\u0275\u0275nextContext"](3).changeTextSelect(i,o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](4).$implicit,t=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","project_handover_takeover"!=t.getFormControlValue(e,"section_name")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction2"](2,ke,"project_handover_takeover"==t.getFormControlValue(e,"section_name"),"project_handover_takeover"!=t.getFormControlValue(e,"section_name")))}}function Le(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0,23),d["\u0275\u0275template"](1,He,6,2,"ng-container",1),d["\u0275\u0275template"](2,Be,5,1,"ng-container",1),d["\u0275\u0275template"](3,$e,7,2,"ng-container",1),d["\u0275\u0275template"](4,Ge,4,5,"ng-container",1),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275nextContext"](6);d["\u0275\u0275property"]("formGroupName",n),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","checkbox_single"==o.getFormControlValue(e,"field_type")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","checkbox_multiple"==o.getFormControlValue(e,"field_type")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","date"==o.getFormControlValue(e,"field_type")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","freeTextWithLabel"==o.getFormControlValue(e,"field_type"))}}function Ke(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0,23),d["\u0275\u0275elementContainerStart"](1),d["\u0275\u0275elementStart"](2,"mat-card",55),d["\u0275\u0275elementStart"](3,"div",35),d["\u0275\u0275template"](4,Ve,3,2,"div",36),d["\u0275\u0275template"](5,Te,2,0,"span",37),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementStart"](6,"div",56),d["\u0275\u0275template"](7,Le,5,5,"ng-container",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275nextContext"](2),i=o.index,r=o.$implicit,a=d["\u0275\u0275nextContext"](3);d["\u0275\u0275property"]("formGroupName",n),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngForOf",a.getFormControlValue(e,"lable_name")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==a.params.exitSurveySection[i].lables[n].is_mandatory),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction1"](5,Ae,"project_handover_takeover"==a.getFormControlValue(r,"section_name"))),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",a.getOptionFormArray(i,n).controls)}}function Je(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0,54),d["\u0275\u0275template"](1,Ke,8,7,"ng-container",22),d["\u0275\u0275element"](2,"br"),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().index,t=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",t.getLableFormArray(e).controls)}}function Xe(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0,23),d["\u0275\u0275elementStart"](1,"div",24),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,Ne,3,1,"div",25),d["\u0275\u0275template"](4,Je,3,1,"ng-container",26),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275nextContext"](3);d["\u0275\u0275property"]("formGroupName",n),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",o.getFormControlValue(e,"section_description")," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","project_handover_takeover"==o.getFormControlValue(e,"section_name")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","project_handover_takeover"!=o.getFormControlValue(e,"section_name"))}}const Ue=function(e){return{"pointer-events":e}};function Ze(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275elementStart"](1,"form",20),d["\u0275\u0275listener"]("keydown",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).onTabKey(t)})),d["\u0275\u0275elementContainerStart"](2,21),d["\u0275\u0275template"](3,Xe,5,4,"ng-container",22),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](3,Ue,e.submitFlag?"none":"auto")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",e.exitSurveyForm),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e.getSectionFormArray.controls)}}function Qe(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",62),d["\u0275\u0275elementStart"](1,"div",63),d["\u0275\u0275elementStart"](2,"div",64),d["\u0275\u0275element"](3,"img",65),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",66),d["\u0275\u0275elementStart"](6,"h4"),d["\u0275\u0275text"](7," No Handover Takeover form has been submitted by your manager "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"p",67),d["\u0275\u0275text"](9," We will let you know once the action has been taken "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function et(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",4),d["\u0275\u0275elementStart"](2,"div",5),d["\u0275\u0275elementStart"](3,"mat-card",6),d["\u0275\u0275template"](4,he,2,1,"div",7),d["\u0275\u0275template"](5,Ze,4,5,"div",8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](6,Qe,10,0,"div",9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",e.displayCondition&&e.handoverTakenoverDetails.length>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&e.exitResponse.length>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==e.exitResponse.length)}}let tt=(()=>{class e{constructor(e,t,n,o,i,r,a,l,s){this.dialogRef=e,this._router=t,this.route=n,this._snackBar=o,this.edreqService=i,this._toaster=r,this._loginService=a,this.fb=l,this.data1=s,this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date,this.exitSurvey=[],this.handoverTakenoverDetails=[],this.handoverToDetails=[],this.submitFlag=!0,this.optionsDisableFlag=!0,this.exitResponse=[],this.exitSurveyForm=this.fb.group({exitSurveySection:this.fb.array([])}),this.mandt_lables=[],this.selected_lables=[]}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.initFunc()}))}initFunc(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("data1",this.data1),this.params={},this.page_loader=!0,this.handoverTakenoverDetails=yield this.getHandOverTakeOverDetails(),this.handoverToDetails=yield this.getHandOverToDetails(),this.exitResponse=yield this.viewHandoverSurvey(),this.generateExitForm(),this.params=this.exitSurveyForm.value,this.page_loader=!1,console.log("Handiver Appr params",this.edreqService.handover_appr_params)}))}get getSectionFormArray(){return this.exitSurveyForm.get("exitSurveySection")}getLableFormArray(e){let t=this.exitSurveyForm.get("exitSurveySection"),n=t.at(e).get("lables");return this.project_ht_lables_length=n.length,t.at(e).get("lables")}getOptionFormArray(e,t){return this.exitSurveyForm.get("exitSurveySection").at(e).get("lables").at(t).get("options")}getFormControlValue(e,t){return e.get(t).value.split(/:|\|/)}changeSingleSelect(e,t,n){1==this.params.exitSurveySection[e].lables[t].options[n].isChecked?(this.params.exitSurveySection[e].lables[t].options[n].isChecked=0,this.selected_lables=this.selected_lables.filter(n=>n!==this.params.exitSurveySection[e].lables[t].lable_id)):(this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id));for(let o=0;o<this.params.exitSurveySection[e].lables[t].options.length;o++)o!=n&&(this.params.exitSurveySection[e].lables[t].options[o].isChecked=0)}changeMultipleSelect(e,t,n){if(1==this.params.exitSurveySection[e].lables[t].options[n].isChecked){this.params.exitSurveySection[e].lables[t].options[n].isChecked=0;let o=this.selected_lables.indexOf(this.params.exitSurveySection[e].lables[t].lable_id);-1!==o&&this.selected_lables.splice(o,1)}else this.params.exitSurveySection[e].lables[t].options[n].isChecked=1;this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id)}changeTextSelect(e,t,n,o){let i=o.target.value;this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.params.exitSurveySection[e].lables[t].options[n].textbox_lable_name=i,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id)}changeDateSelect(e,t,n,o){let i=s()(o.value).format("YYYY-MM-DD");this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.params.exitSurveySection[e].lables[t].options[n].textbox_lable_name=i,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id)}generateExitForm(){let e=this.exitResponse.map(e=>this.createSection(e));this.exitSurveyForm.setControl("exitSurveySection",this.fb.array(e))}createSection(e){return this.fb.group({section_id:[e.section_id,se.H.required],section_name:[e.section_name,se.H.required],section_description:[e.section_description,se.H.required],lables:this.fb.array(this.loadLablesArray(e.lables))})}loadLablesArray(e){return e.map(e=>this.createLable(e))}createLable(e){return 1==e.is_mandatory&&this.mandt_lables.push(e.lable_id),this.fb.group({lable_id:[e.lable_id,se.H.required],lable_name:[e.lable_name,se.H.required],is_mandatory:[e.is_mandatory,se.H.required],options:this.fb.array(this.loadOptionsArray(e.options))})}loadOptionsArray(e){return e.map(e=>this.createOption(e))}createOption(e){return this.fb.group({field_type:[e.field_type],checkbox_id:[e.checkbox_id],checkbox_name:[e.checkbox_name],textbox_lable_id:[e.textbox_lable_id],textbox_lable_name:[e.textbox_lable_name?e.textbox_lable_name:e.textbox_lable_value],isChecked:[e.isChecked]})}getHandoverTakenoverSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,form_name:"Handover_takeover",current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchEmployeeExitSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Handover Takeover Form !",2e3),console.log(e),n(e)})})}getHandOverTakeOverDetails(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.data1.emp_id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.viewHandoverTakenoverSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Handover Takeover Form !",2e3),console.log(e),n(e)})})}getHandOverToDetails(){let e={params:{exit_employee_id:this.data1.emp_id}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.getHandoverApproverData(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Handover To details !",2e3),console.log(e),n(e)})})}viewHandoverSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.data1.emp_id,form_name:"Handover_takeover",current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.viewHandoverSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Handover Takeover!",2e3),console.log(e),n(e)})})}backtoDetails(){this.exitSurveyForm.reset(),this.initFunc()}back(){window.history.back()}isSubset(e,t){return e.every(e=>t.includes(e))}submitBtn(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables);let e=this.isSubset(this.mandt_lables,this.selected_lables);if(console.log("Is mandatory fields are filled",e),e){let e={exit_employee_id:this.edreqService.empExId,exit_process_id:this.edreqService.processId,current_date:s()(this.currentDate).format("YYYY-MM-DD"),user_aid:this._loginService.getProfile().profile.aid,form_name:"Handover_takeover",form_values:this.params.exitSurveySection};this.onSubmit=yield this.saveExitForm(e),yield this.initiateApproval(),this.openSnackBar("Handover Takeover form has been submitted","Ok"),this.onSubmit.err||(this.submitFlag=!0,window.history.back())}else this._toaster.showWarning("Warning","Fill all mandatory fields")}))}openSnackBar(e,t){this._snackBar.open(e,t,{duration:3e3})}saveExitForm(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.saveExitForm(e).subscribe(e=>{t(e)},e=>{this._toaster.showError("Error","Failed to submit your Form!",2e3),console.log(e),n(e)})})}getRoundedValue(){return console.log("Round of value",Math.round(this.project_ht_lables_length/2)-1),Math.round(this.project_ht_lables_length/2)-1}onTabKey(e){this.submitFlag&&e.preventDefault()}initiateApproval(){return Object(r.c)(this,void 0,void 0,(function*(){try{let e=this.edreqService.handover_appr_params;console.log("initiate appr params",e),this.onSubmit=yield this.initiateApprove(e)}catch(e){this._toaster.showError("Error","Failed to submit your request!",2e3),console.log(e)}}))}initiateApprove(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.initiateApprove(e).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){t(e.data)})),e=>{this._toaster.showError("Error","Failed to submit your request!",2e3),console.log(e),n(e)})})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.g),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](ce.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](se.i),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-handover-dialog"]],inputs:{type:"type"},decls:3,vars:2,consts:[[1,"container-fluid-hand"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","91vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],[1,"whole-handover-takeover"],[1,"main-handover-takeover"],[1,"sub-card"],["class","firstrow",4,"ngIf"],["class","handover-form-start",3,"ngStyle",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"firstrow"],["class","takenover-details-box","style","padding: 1rem",4,"ngFor","ngForOf"],[1,"takenover-details-box",2,"padding","1rem"],[1,"header-takenover-details"],[2,"display","flex","justify-content","space-between","width","100%"],[1,"details-from-manager"],[1,"red-head"],[1,"takeover-head"],[1,"takeover-detail"],[1,"handover-form-start",3,"ngStyle"],[3,"formGroup","keydown"],["formArrayName","exitSurveySection"],[3,"formGroupName",4,"ngFor","ngForOf"],[3,"formGroupName"],[1,"sub-headers"],["class","row","style","margin-top: 10px;","formArrayName","lables",4,"ngIf"],["formArrayName","lables",4,"ngIf"],["formArrayName","lables",1,"row",2,"margin-top","10px"],["class","col-6","style","padding-left: 0px;",3,"formGroupName",4,"ngFor","ngForOf"],[1,"col-6",2,"padding-left","0px",3,"formGroupName"],[2,"display","flex"],["style","display: block",4,"ngIf"],["style","display: inline-block; margin-right: 20%",4,"ngIf"],["formArrayName","options",1,"all-options","row",3,"ngClass"],[2,"display","block"],[1,"all-sub-labels"],[4,"ngFor","ngForOf"],["class","required-star",4,"ngIf"],[1,"required-star"],[2,"display","inline-block","margin-right","20%"],["style","display: inline-block;margin-right: 15%;",4,"ngIf"],[1,"text-field-handover","dateFieldBox"],[1,"label-for-textfield",2,"width","100%"],["formControlName","textbox_lable_name","matInput","","placeholder","DD MM YYYY",2,"pointer-events","none",3,"matDatepicker","keydown","dateChange"],["matSuffix","",3,"for"],["picker",""],[1,"label-for-textfield"],["formControlName","textbox_lable_name","placeholder","DD MM YYYY",2,"border","none",3,"keydown.Tab"],[1,"text-field-handover"],["class","label-for-textfield","for","",4,"ngIf"],["type","text","formControlName","textbox_lable_name",1,"text-field",3,"ngClass","keydown.Tab","change"],["for","",1,"label-for-textfield"],[2,"display","inline-block","margin-right","15%"],["formControlName","textbox_lable_name","matInput","","placeholder","DD MM YYYY",3,"matDatepicker","keydown"],["formArrayName","lables"],[1,"card-outline"],["formArrayName","options",1,"all-options",2,"display","flex",3,"ngClass"],[1,"single-checkbox"],["type","checkbox","formControlName","isChecked",1,"single-input",3,"checked","keydown.Tab","change"],["for","vehicle1",1,"label-single"],["type","checkbox","formControlName","isChecked",3,"keydown.Tab","change"],["for","vehicle1"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","Manager not yet approved",2,"width","250px"],[1,"error-text"],[1,"d-flex","justify-content-center"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,ge,3,0,"ng-container",1),d["\u0275\u0275template"](2,et,7,3,"ng-container",1),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,u.a,o.NgForOf,o.NgStyle,se.J,se.w,se.n,se.h,se.o,o.NgClass,se.e,de.b,me.g,se.v,se.l,me.i,pe.i,me.f,se.b],styles:['.container-fluid-hand[_ngcontent-%COMP%]{height:91vh;width:100%}.container-fluid-hand[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#cf0001}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]{position:fixed;overflow:hidden}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked{background:#ee4961;color:#fff}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:0;background:#fff;height:15px;width:15px;border:1px solid;color:#d3d3d3;border-radius:4px}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:after{content:" ";position:relative;left:40%;top:12%;width:31%;height:64%;border:solid #fff;border-width:0 2px 2px 0;transform:rotate(50deg);display:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked:after{display:block}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .backtodetailes[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;padding:.5rem 1rem 0}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .backtodetailes[_ngcontent-%COMP%]   .backtodetail[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .backtodetailes[_ngcontent-%COMP%]   .backtodetail[_ngcontent-%COMP%]   .icontoBack[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:17px}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]{margin-top:.5vh;width:98%;margin-left:1%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]{width:76rem;padding:1rem;overflow-y:scroll;height:73vh}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .firstrow[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding:1rem;background:var(--blue-grey-10,#f6f6f6)}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .firstrow[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .header-takenover-details[_ngcontent-%COMP%]{color:var(--blue-grey-100,#45546e);font-size:16px;font-family:Roboto;font-weight:500;line-height:24px;text-transform:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .firstrow[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .details-from-manager[_ngcontent-%COMP%]{padding:.6rem;width:100%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .firstrow[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .details-from-manager[_ngcontent-%COMP%]   .red-head[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:24px;display:flex;align-items:center;text-transform:none;color:#ff3a46}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .firstrow[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .details-from-manager[_ngcontent-%COMP%]   .takeover-head[_ngcontent-%COMP%]{color:var(--blue-grey-70,#6e7b8f);font-size:14px;font-family:Roboto;font-weight:500;line-height:24px;letter-spacing:.28px;text-transform:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .firstrow[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .details-from-manager[_ngcontent-%COMP%]   .takeover-detail[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:24px;letter-spacing:.02em;color:#000;flex:none;order:0;flex-grow:0}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]{padding:1rem}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .sub-headers[_ngcontent-%COMP%]{color:var(--blue-grey-100,#45546e);font-size:16px;font-family:Roboto;font-style:normal;font-weight:500;line-height:24px;text-transform:none;padding:.2rem}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .sub-header-project[_ngcontent-%COMP%]{color:var(--blue-grey-70,#6e7b8f);font-size:14px;font-family:Roboto;font-style:normal;font-weight:500;line-height:24px;letter-spacing:.28px;text-transform:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .card-outline[_ngcontent-%COMP%]{border-top-right-radius:15px;border-top-left-radius:15px;border-bottom-left-radius:0;background-color:#f3f5fc;width:100%;box-shadow:none;display:flex;align-items:center;margin-top:1%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .card-outline[_ngcontent-%COMP%]   .all-sub-labels[_ngcontent-%COMP%]{color:var(--black-100,#000);font-size:13px;font-family:Roboto;font-style:normal;font-weight:400;line-height:24px;letter-spacing:.26px;text-transform:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-sub-labels-for-project[_ngcontent-%COMP%]{color:var(--blue-grey-70,#6e7b8f);font-size:14px;font-family:Roboto;font-style:normal;font-weight:500;line-height:24px;letter-spacing:.28px;text-transform:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]{height:60px;border:1px solid #ced4da;margin-left:0;width:100%;display:flex;justify-content:flex-start;align-items:center;gap:5%;padding:2%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .single-checkbox[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .single-checkbox[_ngcontent-%COMP%]   .single-input[_ngcontent-%COMP%]{height:17px;width:17px;margin-right:4px;margin-bottom:0!important}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .single-checkbox[_ngcontent-%COMP%]   .label-single[_ngcontent-%COMP%]{color:#000;font-size:14px;font-family:Roboto;font-style:normal;font-weight:400;line-height:24px;letter-spacing:.28px;margin-bottom:0!important}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .text-field-handover[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;gap:3%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .text-field-handover[_ngcontent-%COMP%]   .text-field[_ngcontent-%COMP%]{border-radius:6px;border:1px solid var(--mild-grey,#d0d5e5);width:556px;height:40px;display:flex;justify-content:center;align-items:center;padding:2%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .text-field-handover[_ngcontent-%COMP%]   .textfield-for-project-input[_ngcontent-%COMP%]{border-radius:6px;border:1px solid var(--mild-grey,#d0d5e5);width:350px;height:40px;display:flex;justify-content:center;align-items:center;padding:2%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .text-field-handover[_ngcontent-%COMP%]   .label-for-textfield[_ngcontent-%COMP%]{color:#000;font-size:14px;font-family:Roboto;font-style:normal;font-weight:400;line-height:24px;letter-spacing:.28px;display:flex;justify-content:center;align-items:center;margin-bottom:0!important}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .all-options[_ngcontent-%COMP%]   .dateFieldBox[_ngcontent-%COMP%]{border-radius:6px;border:1px solid var(--mild-grey,#d0d5e5);width:100%;height:40px;display:flex;padding:2%;margin-top:8px}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .textfield-for-project[_ngcontent-%COMP%]{border:none;padding:0}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .end-btns[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;padding:.6rem;gap:3%}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .end-btns[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]{cursor:pointer;color:var(--blue-grey-100,#45546e);font-size:14px;font-family:Roboto;font-weight:700;line-height:16px;letter-spacing:-.28px;text-transform:none;border-radius:4px;border:1px solid var(--blue-grey-100,#45546e);background-color:none;height:35px;width:65px}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .end-btns[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{cursor:pointer;border-radius:4px;background:var(--primary-maroon,#ee4961);color:var(--neutral-white,#fff);font-size:14px;font-family:Roboto;font-weight:700;line-height:16px;letter-spacing:-.28px;text-transform:none;height:35px;width:65px;border:none}.container-fluid-hand[_ngcontent-%COMP%]   .whole-handover-takeover[_ngcontent-%COMP%]   .main-handover-takeover[_ngcontent-%COMP%]   .sub-card[_ngcontent-%COMP%]   .handover-form-start[_ngcontent-%COMP%]   .end-btns[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:disabled{cursor:not-allowed}']}),e})();function nt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"ng-contianer"),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}const ot=function(e){return{background:e}},it=function(e){return{"background-color":e}};function rt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",18),d["\u0275\u0275elementStart"](2,"div",19),d["\u0275\u0275elementStart"](3,"a"),d["\u0275\u0275elementStart"](4,"mat-icon",20),d["\u0275\u0275text"](5,"done"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",21),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275elementStart"](8,"div",22),d["\u0275\u0275elementStart"](9,"div",23),d["\u0275\u0275element"](10,"app-user-image",24),d["\u0275\u0275text"](11,"\xa0\xa0 "),d["\u0275\u0275element"](12,"app-user-profile",25),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",26),d["\u0275\u0275text"](14,"Completed on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",26),d["\u0275\u0275text"](16,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",22),d["\u0275\u0275elementStart"](18,"div",27),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",28),d["\u0275\u0275text"](21),d["\u0275\u0275pipe"](22,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"div",29),d["\u0275\u0275element"](24,"div",19),d["\u0275\u0275elementStart"](25,"div"),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](10,ot,e.color)),d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("id",e.approver_oid),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("oid",e.approver_oid),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",e.wf_desc," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.completed_on?d["\u0275\u0275pipeBind2"](22,7,e.completed_on,"dd-MMM-YYYY"):"--"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](12,it,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.status)}}function at(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"mat-card",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",10),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).close()})),d["\u0275\u0275elementStart"](6,"mat-icon",11),d["\u0275\u0275text"](7,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"br"),d["\u0275\u0275elementStart"](9,"main"),d["\u0275\u0275template"](10,rt,27,14,"div",12),d["\u0275\u0275element"](11,"br"),d["\u0275\u0275elementStart"](12,"div",13),d["\u0275\u0275elementStart"](13,"div",14),d["\u0275\u0275elementStart"](14,"div",15),d["\u0275\u0275text"](15,"Handover Takeover Form"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",16),d["\u0275\u0275elementStart"](17,"button",17),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).viewHandover()})),d["\u0275\u0275text"](18,"View Form"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.header),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data)}}function lt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",30),d["\u0275\u0275elementStart"](1,"div",31),d["\u0275\u0275elementStart"](2,"div",32),d["\u0275\u0275element"](3,"img",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",34),d["\u0275\u0275text"](6,"Approvers Not Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function st(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,at,19,2,"div",4),d["\u0275\u0275template"](2,lt,7,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let ct=(()=>{class e{constructor(e,t,n,o,i,r,a){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=o,this.dialog=i,this._loginService=r,this.data1=a,this.header=["Handover/ Takeover Form"],this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchApprovers(),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchApprovers(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"handover_takeover",exit_employee_id:this.data1.emp_Id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchApprovers(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Handovers Approval !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}viewHandover(){this.dialog.open(tt,{height:"550px",width:"1300px",data:{emp_id:this.data1.emp_Id,aid:this.data1.associate_id}})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](a.b),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-handover-status"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","100vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[2,"width","100%","height","100vh"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[1,"view-form"],[1,"form-outime"],[1,"form-header"],[1,"view-form-btn"],[1,"view-btn",3,"click"],[1,"step","active",2,"height","13vh"],[1,"circle",3,"ngStyle"],[2,"color","#fff","font-size","20px","margin-top","11%","margin-left","8%"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,nt,3,0,"ng-contianer",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,st,3,2,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,u.a,x.a,o.NgForOf,o.NgStyle,v.a,_.a],pipes:[o.DatePipe],styles:[".whole[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.whole[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.whole[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin-top:26px;left:2%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:none;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:none;color:#000;gap:6%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;display:flex;justify-content:center;align-items:center;padding:.5rem 1.5rem}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]{height:120px;width:100%;background:#f7f9fb;border-radius:4px;padding:1rem}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;padding:2rem 0 0}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%]{border:1px solid #45546e;font-style:normal;font-weight:600;font-size:13px;letter-spacing:.01em;width:105px;height:40px;background:#f7f9fb;border-radius:8px;flex:none;color:#45546e;display:flex;justify-content:center;align-items:center}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:5px;padding:1rem;margin-left:0}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:8rem}"]}),e})();var dt=n("1n7x"),mt=n("jhN1");function pt(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]())}function gt(e,t){if(1&e&&d["\u0275\u0275element"](0,"div",21),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275property"]("innerHTML",e.dynamicContent,d["\u0275\u0275sanitizeHtml"])}}function ft(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",22),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.NAME," ")}}function ht(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"h2",9),d["\u0275\u0275text"](4," UNDERTAKING "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"h4",10),d["\u0275\u0275text"](6," TO WHOMSOEVER IT MAY CONCERN "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",11),d["\u0275\u0275template"](8,gt,1,1,"div",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](9,"br"),d["\u0275\u0275elementStart"](10,"div",13),d["\u0275\u0275elementStart"](11,"div",14),d["\u0275\u0275elementStart"](12,"div",15),d["\u0275\u0275text"](13," Signed at Chennai on this : "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"div",16),d["\u0275\u0275text"](15),d["\u0275\u0275pipe"](16,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",14),d["\u0275\u0275elementStart"](18,"div",17),d["\u0275\u0275text"](19," Name of the Employee : "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](20,ft,2,1,"div",18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"button",19),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).generatePdf()})),d["\u0275\u0275elementStart"](22,"mat-icon",20),d["\u0275\u0275text"](23,"download"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](24,"Download "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("ngIf",e.edit),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind2"](16,3,e.currentDate,"dd-MMM-YYYY")," "),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngForOf",e.employeeName)}}function ut(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",23),d["\u0275\u0275elementStart"](1,"div",24),d["\u0275\u0275element"](2,"img",25),d["\u0275\u0275text"](3," IP Signout Not yet submitted "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function xt(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,ht,25,6,"div",4),d["\u0275\u0275template"](2,ut,4,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.edit),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.edit)}}let vt=(()=>{class e{constructor(e,t,n,o,i,r,a){this.route=e,this.edreqService=t,this._toaster=n,this._loginService=o,this.sanitizer=i,this.dialogRef=r,this.inData=a,this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date,this.termsAndConditions=[],this.edit=!1}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExId=e}),this.termsAndConditions=yield this.fetchIpSignOutForm(),this.employeeName=yield this.fetchIpSignOutFormEmpDetails(),this.page_loader=!1}))}fetchIpSignOutForm(){console.log("Modal passing Data",this.inData);let e={params:{user_aid:this.inData.value.associate_id,user_oid:this._loginService.getProfile().profile.oid,exit_process_id:this.inData.value.process_Id,exit_employee_id:this.inData.value.emp_Id}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchIpSignOutForm(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,this.edit=1==e.form_filled,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}fetchIpSignOutFormEmpDetails(){let e={params:{user_aid:this.inData.value.associate_id,user_oid:this._loginService.getProfile().profile.oid,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchIpSignOutFormEmpDetails(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve employee name !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}get dynamicContent(){let e=JSON.parse(this.termsAndConditions[0].form_values),t=this.termsAndConditions[0].ip_out_sign_form;for(let n in e){const o=new RegExp("\\$\\{"+n+"\\}","g");t=t.replace(o,e[n])}return this.editTemplate=`<div>${t}</div>`,console.log(t),this.sanitizer.bypassSecurityTrustHtml(t)}generatePdf(){var e=document.getElementById("Ip-form");dt().from(e).set({filename:"Ip-Form.pdf",margin:[.5,.5,.5,.5],image:{type:"jpeg",quality:.98},jsPDF:{unit:"in",format:"a4",orientation:"portrait"}}).save()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](mt.c),d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-ip-dialog"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","550px","width","1000px"],["matTooltip","Please wait...","diameter","30"],["class","ip-signout",4,"ngIf"],["class","err",4,"ngIf"],[1,"ip-signout"],["id","Ip-form",1,"form-field"],[1,"ip-docs"],[1,"header-text-style","d-flex","justify-content-center","mt-3"],[1,"sub-header-text-style","d-flex","justify-content-center","mt-3"],[1,"conditions"],["class","one",3,"innerHTML",4,"ngIf"],[1,"empdetails","row"],[1,"col-6"],[1,"date","col-6",2,"display","inline-block"],[1,"date-ts","col-6",2,"display","inline-block"],[1,"empname","col-6",2,"display","inline-block"],["style","display: inline-block","class","empname-ts col-6",4,"ngFor","ngForOf"],[1,"ip-download",3,"click"],[1,"download-icon"],[1,"one",3,"innerHTML"],[1,"empname-ts","col-6",2,"display","inline-block"],[1,"err"],[1,"error-message"],["src","https://assets.kebs.app/images/nomilestone.png","height","220","width","250"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,pt,3,0,"ng-container",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,xt,3,2,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,o.NgForOf,x.a],pipes:[o.DatePipe],styles:[".ip-signout[_ngcontent-%COMP%]{font-family:Roboto;max-height:550px;overflow-y:scroll}.ip-signout[_ngcontent-%COMP%]   .ip-download[_ngcontent-%COMP%]{position:relative;overflow:hidden;border:1px solid #45546e;border-radius:5px;text-transform:capitalize;color:#45546e;background:#fff!important;width:150px;height:40px;display:flex;line-height:12px;font-size:12px;font-weight:600;padding:2px 10px;justify-content:center;align-items:center;margin-right:4px;margin-bottom:2rem;margin-left:4rem}.ip-signout[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{padding:1rem}.ip-signout[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]   .ip-docs[_ngcontent-%COMP%]{margin-top:0;margin-left:1%;margin-right:3%;padding:1rem}.ip-signout[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]   .ip-docs[_ngcontent-%COMP%]   .header-text-style[_ngcontent-%COMP%]{color:var(--black-60,#7d838b);font-size:18px;font-family:Roboto;font-style:normal;font-weight:500;line-height:normal;letter-spacing:.36px;text-transform:capitalize}.ip-signout[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]   .ip-docs[_ngcontent-%COMP%]   .sub-header-text-style[_ngcontent-%COMP%]{color:var(--black-80,#515965);font-size:14px;font-family:Roboto;font-style:normal;font-weight:500;line-height:normal;letter-spacing:.28px;text-transform:capitalize}.ip-signout[_ngcontent-%COMP%]   .conditions[_ngcontent-%COMP%]{color:#000;text-align:justify;font-size:14px;font-family:Roboto;font-style:normal;font-weight:400;line-height:22px;letter-spacing:.28px}.ip-signout[_ngcontent-%COMP%]   .conditions[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .ip-signout[_ngcontent-%COMP%]   .conditions[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{background-color:#f6f6f6!important;border-color:#f6f6f6!important}.ip-signout[_ngcontent-%COMP%]   .ip-consents[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.5}.ip-signout[_ngcontent-%COMP%]   .ip-checkbox[_ngcontent-%COMP%]{display:block;position:relative;padding-left:35px;margin-bottom:12px;cursor:pointer;margin-left:41px;color:#5e5d5d;order:1}.ip-signout[_ngcontent-%COMP%]   .ip-checkbox[_ngcontent-%COMP%], .ip-signout[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-size:12px;font-weight:400;line-height:16px;letter-spacing:.02em;flex:none;flex-grow:0}.ip-signout[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{-webkit-text-decoration-line:underline;text-decoration-line:underline;text-transform:capitalize;color:#f27a6c;order:2}.ip-signout[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.ip-signout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;display:flex;justify-content:space-between;padding:1rem}.ip-signout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .ip-signout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .empname[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:20px;letter-spacing:.02em;color:#000}.ip-signout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .date-ts[_ngcontent-%COMP%], .ip-signout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .empname-ts[_ngcontent-%COMP%]{display:flex;text-align:center;justify-content:flex-start;font-style:normal;font-weight:500;font-size:16px;line-height:20px;letter-spacing:.02em;color:#000}.ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]{display:flex;padding:10px;margin:left 100px}.ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]{position:relative;overflow:hidden;width:150px}.ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%], .ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]:hover{border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;height:40px;display:flex;line-height:12px;font-size:12px;font-weight:600;padding:2px 10px;justify-content:center;align-items:center}.ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{position:absolute;display:flex;justify-content:center;transform:translate(-50%,-50%);display:none;width:20px;height:20px;border:2px solid #fff;border-top-color:#000;border-radius:50%;animation:loader-rotate 1s linear infinite}@keyframes loader-rotate{0%{transform:translate(-50%,-50%) rotate(0deg)}to{transform:translate(-50%,-50%) rotate(1turn)}}.ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit.loading[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{display:block}.ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit.loading[_ngcontent-%COMP%]{pointer-events:none;opacity:.7}.ip-signout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]:disabled{opacity:.5}.err[_ngcontent-%COMP%]{height:550px;width:60rem;padding:30px}.err[_ngcontent-%COMP%], .err[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.err[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-family:Roboto;text-align:center;font-style:normal;flex-direction:column;text-transform:capitalize;font-size:18px;font-weight:500;color:#000}"]}),e})();function _t(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"ng-contianer"),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}const Ct=function(e){return{background:e}},yt=function(e){return{"background-color":e}};function bt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",18),d["\u0275\u0275elementStart"](2,"div",19),d["\u0275\u0275elementStart"](3,"a"),d["\u0275\u0275elementStart"](4,"mat-icon",20),d["\u0275\u0275text"](5,"done"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",21),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275elementStart"](8,"div",22),d["\u0275\u0275elementStart"](9,"div",23),d["\u0275\u0275element"](10,"app-user-image",24),d["\u0275\u0275text"](11,"\xa0\xa0 "),d["\u0275\u0275element"](12,"app-user-profile",25),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",26),d["\u0275\u0275text"](14,"Completed on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",26),d["\u0275\u0275text"](16,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",22),d["\u0275\u0275elementStart"](18,"div",27),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",28),d["\u0275\u0275text"](21),d["\u0275\u0275pipe"](22,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"div",29),d["\u0275\u0275element"](24,"div",19),d["\u0275\u0275elementStart"](25,"div"),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](10,Ct,e.color)),d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("id",e.approver_oid),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("oid",e.approver_oid),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",e.wf_desc," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.completed_on?d["\u0275\u0275pipeBind2"](22,7,e.completed_on,"dd-MMM-YYYY"):"--"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](12,yt,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.status)}}function Ot(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"mat-card",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",10),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).close()})),d["\u0275\u0275elementStart"](6,"mat-icon",11),d["\u0275\u0275text"](7,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"br"),d["\u0275\u0275elementStart"](9,"main"),d["\u0275\u0275template"](10,bt,27,14,"div",12),d["\u0275\u0275element"](11,"br"),d["\u0275\u0275elementStart"](12,"div",13),d["\u0275\u0275elementStart"](13,"div",14),d["\u0275\u0275elementStart"](14,"div",15),d["\u0275\u0275text"](15,"IP Signout Form"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",16),d["\u0275\u0275elementStart"](17,"button",17),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).viewIP()})),d["\u0275\u0275text"](18,"View Form"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.header),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data)}}function Mt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",30),d["\u0275\u0275elementStart"](1,"div",31),d["\u0275\u0275elementStart"](2,"div",32),d["\u0275\u0275element"](3,"img",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",34),d["\u0275\u0275text"](6,"Approvers Not Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Pt(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,Ot,19,2,"div",4),d["\u0275\u0275template"](2,Mt,7,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let St=(()=>{class e{constructor(e,t,n,o,i,r,a){this.route=e,this.dialog=t,this.edreqService=n,this._toaster=o,this._loginService=i,this.dialogRef=r,this.inData=a,this.header=["IP Signout"],this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchApprovers(),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchApprovers(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"ip_out_sign",exit_employee_id:this.inData.emp_Id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchApprovers(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve IP SignOut Approval !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}viewIP(){this.dialog.open(vt,{height:"550px",width:"1000px",data:{value:this.inData}})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](a.b),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ip-sign-status"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","100vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[2,"width","100%","height","100vh"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[1,"view-form"],[1,"form-outime"],[1,"form-header"],[1,"view-form-btn"],[1,"view-btn",3,"click"],[1,"step","active",2,"height","13%"],[1,"circle",3,"ngStyle"],[2,"color","#fff","font-size","20px","margin-top","11%","margin-left","8%"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,_t,3,0,"ng-contianer",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,Pt,3,2,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,u.a,x.a,o.NgForOf,o.NgStyle,v.a,_.a],pipes:[o.DatePipe],styles:['@charset "UTF-8";.whole[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.whole[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.whole[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin-top:26px;left:2%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:capitalize;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\u2022";color:#009432;font-weight:500;width:1.5em;display:inline-block;margin-left:-1em}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;gap:6%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:13px;height:13px;border-radius:50%;margin-top:0}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;display:flex;justify-content:center;align-items:center;padding:.5rem 1.5rem}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]{height:120px;width:100%;background:#f7f9fb;border-radius:4px;padding:1rem}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;padding:2rem 0 0}.whole[_ngcontent-%COMP%]   .view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%]{border:1px solid #45546e;font-style:normal;font-weight:600;font-size:13px;letter-spacing:.01em;width:105px;height:40px;background:#f7f9fb;border-radius:8px;flex:none;color:#45546e;display:flex;justify-content:center;align-items:center}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:5px;padding:1rem;margin-left:0}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:8rem}']}),e})();function wt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"ng-contianer"),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}const Et=function(e){return{background:e}},kt=function(e){return{"background-color":e}};function It(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",15),d["\u0275\u0275elementStart"](2,"div",16),d["\u0275\u0275elementStart"](3,"a"),d["\u0275\u0275elementStart"](4,"mat-icon",17),d["\u0275\u0275text"](5,"done"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",18),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275elementStart"](8,"div",19),d["\u0275\u0275elementStart"](9,"div",20),d["\u0275\u0275element"](10,"app-user-image",21),d["\u0275\u0275text"](11,"\xa0\xa0 "),d["\u0275\u0275element"](12,"app-user-profile",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",23),d["\u0275\u0275text"](14,"Completed on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",23),d["\u0275\u0275text"](16,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",19),d["\u0275\u0275elementStart"](18,"div",24),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",25),d["\u0275\u0275text"](21),d["\u0275\u0275pipe"](22,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"div",26),d["\u0275\u0275element"](24,"div",16),d["\u0275\u0275elementStart"](25,"div"),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](10,Et,e.color)),d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("id",e.approver_oid),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("oid",e.approver_oid),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",e.wf_desc," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.completed_on?d["\u0275\u0275pipeBind2"](22,7,e.completed_on,"dd-MMM-YYYY"):"--"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](12,kt,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.status)}}function Dt(e,t){1&e&&d["\u0275\u0275element"](0,"div",36)}function Ft(e,t){1&e&&d["\u0275\u0275element"](0,"div",37)}function jt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",38),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](5);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," ")}}function zt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",39),d["\u0275\u0275elementStart"](1,"div",40),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("matTooltip",null==t.fieldConfig[e].options[t.systemAdminDetails[0][e.key]-1]?null:t.fieldConfig[e].options[t.systemAdminDetails[0][e.key]-1].name),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",null==t.fieldConfig[e].options[t.systemAdminDetails[0][e]-1]?null:t.fieldConfig[e].options[t.systemAdminDetails[0][e]-1].name," ")}}function At(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",39),d["\u0275\u0275elementStart"](1,"div",40),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("matTooltip",t.systemAdminDetails[0][e.key]),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",t.systemAdminDetails[0][e]?t.systemAdminDetails[0][e]:"--"," ")}}function Rt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",29),d["\u0275\u0275elementStart"](2,"div",30),d["\u0275\u0275template"](3,Dt,1,0,"div",31),d["\u0275\u0275template"](4,Ft,1,0,"div",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,jt,2,1,"div",33),d["\u0275\u0275elementStart"](6,"div",34),d["\u0275\u0275template"](7,zt,3,2,"div",35),d["\u0275\u0275template"](8,At,3,2,"div",35),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](5);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf","text"!=n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","toggle-button"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field)}}function Nt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",28),d["\u0275\u0275template"](1,Rt,9,5,"div",12),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.sortedKeys(e.fieldConfig))}}function qt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,Nt,2,1,"div",27),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.fieldConfig&&e.systemAdminDetails)}}function Yt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",41),d["\u0275\u0275elementStart"](1,"div",42),d["\u0275\u0275text"](2," Approver not yet filled the form "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Vt(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",10),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).close()})),d["\u0275\u0275elementStart"](6,"mat-icon",11),d["\u0275\u0275text"](7,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"br"),d["\u0275\u0275elementStart"](9,"main"),d["\u0275\u0275template"](10,It,27,14,"div",12),d["\u0275\u0275elementStart"](11,"div",13),d["\u0275\u0275template"](12,qt,2,1,"div",1),d["\u0275\u0275template"](13,Yt,3,0,"div",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.header),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.systemAdminDetails.length>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==e.systemAdminDetails.length)}}function Tt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",43),d["\u0275\u0275elementStart"](1,"div",44),d["\u0275\u0275elementStart"](2,"div",45),d["\u0275\u0275element"](3,"img",46),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",47),d["\u0275\u0275text"](6,"Approvers Not Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Ht(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,Vt,14,4,"div",4),d["\u0275\u0275template"](2,Tt,7,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let Bt=(()=>{class e{constructor(e,t,n,o,i,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=o,this._loginService=i,this.data1=r,this.header=["System Adminstration"],this.subs=new c.a,this.displayCondition=!1,this.approvalData=[],this.systemAdminDetails=[],this.currentDate=new Date,this.formfields=[],this.fieldConfig={}}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchApprovers(),this.systemAdminDetails=yield this.fetchSystemAdminFormDetails(),this.formfields=yield this.getSystemAdminFormConfig(),yield this.handleFieldConfig(),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}sortedKeys(e){return Object.keys(e).sort((t,n)=>e[t].sort_order-e[n].sort_order)}handleFieldConfig(){return Object(r.c)(this,void 0,void 0,(function*(){this.fieldConfig={};let e=this.formfields;if(console.log("fieldList",e),e.length>0){const t=new Map;e.forEach((e,n)=>{t.set(e.field_key,e)}),t.forEach((e,t)=>{this.fieldConfig[t]=e}),console.log(this.fieldConfig)}console.log("Field Config",this.fieldConfig)}))}fetchApprovers(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"system_admin",exit_employee_id:this.data1.emp_Id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchApprovers(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}fetchSystemAdminFormDetails(){console.log("API System Admin Called");let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.data1.emp_Id}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchSystemAdminFormDetails(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}getSystemAdminFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.getSystemAdminFormConfig().subscribe(t=>{t.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,e(t.data))},e=>{this._toaster.showError("Error","Failed to retrive Finance Form!",2e3),console.log(e),t(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-system-admin-status"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","100vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[1,"main"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[1,"noDues"],["class","pl-3",4,"ngIf"],[1,"step","active",2,"height","13vh"],[1,"circle",3,"ngStyle"],[2,"color","#fff","font-size","20px","margin-top","17%","margin-left","7%"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],["class","admin-description id-card",4,"ngIf"],[1,"admin-description","id-card"],[1,"row",2,"margin-bottom","5px","color","#26303e"],[2,"display","flex","justify-content","center","align-items","flex-start","margin-top","5px"],["style","\n                        width: 7px;\n                        height: 8px;\n                        border-radius: 50%;\n                        background-color: black;\n                      ",4,"ngIf"],["style","width: 7px; height: 8px",4,"ngIf"],["class","id-card-header col-7",4,"ngIf"],[1,"id-card-status","col-4"],["class","status-desc",3,"matTooltip",4,"ngIf"],[2,"width","7px","height","8px","border-radius","50%","background-color","black"],[2,"width","7px","height","8px"],[1,"id-card-header","col-7"],[1,"status-desc",3,"matTooltip"],[1,"status-desc-tootip"],[1,"pl-3"],[1,"no-data-from-approver"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,wt,3,0,"ng-contianer",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,Ht,3,2,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,x.a,o.NgForOf,o.NgStyle,v.a,_.a],pipes:[o.DatePipe],styles:[".main[_ngcontent-%COMP%]{padding:1rem}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2% 4%}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.main[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:none;color:#000}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:none;color:#000;gap:6%}.main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;width:100%;padding:.5rem 1.5rem 1rem}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .status-desc-tootip[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .update-from-system-admin[_ngcontent-%COMP%]{padding:.5rem;font-weight:550;font-size:16px;line-height:24px;letter-spacing:.02em;display:flex;justify-content:center;align-items:flex-start}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .description-from-system-admin[_ngcontent-%COMP%], .main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .no-data-from-approver[_ngcontent-%COMP%]{border:1px solid #7d838b;border-radius:8px;opacity:.9;font-weight:400;font-size:14px;line-height:24px;letter-spacing:.02em;color:#5e5d5d;height:100px;padding:1rem}.main[_ngcontent-%COMP%]   .noDues[_ngcontent-%COMP%]   .no-data-from-approver[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center}.step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin-top:26px;left:2%}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:4px;margin-left:0;padding:1rem 1rem 1rem 1.2rem;height:11vh}"]}),e})();function $t(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]())}function Wt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",18),d["\u0275\u0275elementStart"](1,"div",19),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e)}}function Gt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275elementStart"](4,"div",10),d["\u0275\u0275text"](5,"Date of submission"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",11),d["\u0275\u0275text"](7),d["\u0275\u0275pipe"](8,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",12),d["\u0275\u0275elementStart"](10,"div",13),d["\u0275\u0275text"](11,"Reason For Resignation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](12,Wt,3,1,"div",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",15),d["\u0275\u0275elementStart"](14,"div",16),d["\u0275\u0275text"](15,"Notes"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",17),d["\u0275\u0275text"](17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind2"](8,3,e.resignaitonInfo[0].submitted_date,"dd-MMM-YYYY")," "),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngForOf",e.parseValue(e.resignaitonInfo[0].reason_for_resignation)),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate"](e.resignaitonInfo[0].notes)}}function Lt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",20),d["\u0275\u0275elementStart"](1,"div",21),d["\u0275\u0275elementStart"](2,"div",22),d["\u0275\u0275element"](3,"img",23),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"br"),d["\u0275\u0275elementStart"](5,"div",24),d["\u0275\u0275text"](6,"Approvers Not Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Kt(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,Gt,18,6,"div",4),d["\u0275\u0275template"](2,Lt,7,0,"div",5),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&e.resignaitonInfo.length>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==e.resignaitonInfo.length)}}let Jt=(()=>{class e{constructor(e,t,n,o,i,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=o,this._loginService=i,this.data1=r,this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.getEmpExitID(),this.page_loader=!0,this.resignaitonInfo=yield this.fetchResignationinfo(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchResignationinfo(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchResignationinfo(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Failed to retrive resignaiton info"):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Resignation Info!",2e3),console.log(e),n(e)})})}parseValue(e){return JSON.parse(e)}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-reason-for-resignation"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[1,"main"],[1,"body"],[1,"date-of-submission"],[1,"head-date"],[1,"date"],[1,"reason-for-resignation"],[1,"head-reason-resignaiton"],["class","reasons",4,"ngFor","ngForOf"],[1,"notes-from-employee"],[1,"head-notes"],[1,"notes-desc"],[1,"reasons"],[1,"reason"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,$t,3,0,"ng-container",1),d["\u0275\u0275template"](2,Kt,3,2,"ng-container",1),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,o.NgForOf],pipes:[o.DatePipe],styles:[".main[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]{height:77vh;width:100%;background-color:#fff;font-family:Roboto;font-style:normal}.main[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]   .date-of-submission[_ngcontent-%COMP%]{padding:1rem;margin-top:4%}.main[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]   .date-of-submission[_ngcontent-%COMP%]   .head-date[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.5;flex:none;order:0;flex-grow:0;padding:.5rem}.main[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%]   .date-of-submission[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;color:#000;padding:.5rem}.main[_ngcontent-%COMP%]   .reason-for-resignation[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding:1.5rem}.main[_ngcontent-%COMP%]   .reason-for-resignation[_ngcontent-%COMP%]   .head-reason-resignaiton[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.5;display:block}.main[_ngcontent-%COMP%]   .reason-for-resignation[_ngcontent-%COMP%]   .reasons[_ngcontent-%COMP%]{display:inline-block;gap:.5rem;padding:1rem .5rem .5rem}.main[_ngcontent-%COMP%]   .reason-for-resignation[_ngcontent-%COMP%]   .reasons[_ngcontent-%COMP%]   .reason[_ngcontent-%COMP%]{font-size:10px;font-weight:400;line-height:16px;letter-spacing:.02em;text-transform:capitalize;opacity:.7;width:170px;height:25px;margin-left:0;display:flex;justify-content:center;line-height:23px;border:1px solid #cf0001;border-radius:17px;color:#5e5d5d}.main[_ngcontent-%COMP%]   .notes-from-employee[_ngcontent-%COMP%]{padding:1.5rem}.main[_ngcontent-%COMP%]   .notes-from-employee[_ngcontent-%COMP%]   .head-notes[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:16px;letter-spacing:.02em;color:#45546e}.main[_ngcontent-%COMP%]   .notes-from-employee[_ngcontent-%COMP%]   .notes-desc[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:18px;letter-spacing:.02em;color:#7d838b;padding:.5rem}.main[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.main[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:8rem}"]}),e})();function Xt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",11),d["\u0275\u0275text"](1," This field is required. "),d["\u0275\u0275elementEnd"]())}function Ut(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275element"](1,"mat-spinner",12),d["\u0275\u0275elementEnd"]())}function Zt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275text"](1," Submit "),d["\u0275\u0275elementEnd"]())}let Qt=(()=>{class e{constructor(e,t,n,o,i,r,a,l){this._router=e,this.dialogRef=t,this.route=n,this.edreqService=o,this._toaster=i,this._loginService=r,this.formBuilder=a,this.data1=l,this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date,this.onSubmit=!1,this.disableSubmit=!1,this.selectedButton="",this.isExited=!1}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExId=e}),this.updateForm=this.formBuilder.group({update:["",se.H.required]})}))}submitMarkAsRetire(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.submitMarkAsRetire(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to Send your Update!",2e3),console.log(e),n(e)})})}exit(){this.selectedButton="exit",this.emitValue("exited")}absconded(){this.selectedButton="absconded",this.emitValue("absconded")}emitValue(e){}submit(){return Object(r.c)(this,void 0,void 0,(function*(){let e="exit"===this.selectedButton?"exited":"absconded"===this.selectedButton?"absconded":"",t={user_aid:this.data1,exit_type:e,exit_employee_id:this.edreqService.empExId,message:this.updateForm.get("update").value};console.log("params",t),this.updateForm.valid&&""!=e?(this.disableSubmit=!0,this.pleasewait_loader=!0,this.onSubmit=yield this.submitMarkAsRetire(t),this.pleasewait_loader=!1,this.disableSubmit=!1,this.dialogRef.close(),0==this.onSubmit.length?this._toaster.showWarning("Warning","Some Exit process are not yet completed"):(this._toaster.showSuccess("Success","Employee has been exited successfully !",2e3),this._router.navigateByUrl("/main/employee-central/employeeCentralHome"))):this._toaster.showWarning("Required",this.updateForm.valid&&""==e?"Kindly Choose the Exit Type":this.updateForm.valid||""!=e?"Kindly state your reason":"Kindly choose the Exit Type and state your reason")}))}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](i.g),d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](se.i),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mark-as-retire"]],decls:14,vars:9,consts:[[1,"mark-as-retire"],[1,"mark-as-retire-btn"],[1,"exit-btn",3,"click"],[1,"absconded-btn",3,"click"],[3,"formGroup"],[1,"text-field"],["formControlName","update","id","updateTextArea","placeholder","Your Text here!!","rows","3",1,"form-control",2,"font-size","14px"],["class","error-message",4,"ngIf"],[1,"submit-button"],[1,"submit-btn",3,"disabled","click"],[4,"ngIf"],[1,"error-message"],["matTooltip","Your update is being submitted!!","diameter","25"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"button",2),d["\u0275\u0275listener"]("click",(function(){return t.exit()})),d["\u0275\u0275text"](3," Exit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"button",3),d["\u0275\u0275listener"]("click",(function(){return t.absconded()})),d["\u0275\u0275text"](5," Absconded "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"form",4),d["\u0275\u0275elementStart"](7,"div",5),d["\u0275\u0275element"](8,"textarea",6),d["\u0275\u0275template"](9,Xt,2,0,"div",7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",8),d["\u0275\u0275elementStart"](11,"button",9),d["\u0275\u0275listener"]("click",(function(){return t.submit()})),d["\u0275\u0275template"](12,Ut,2,0,"span",10),d["\u0275\u0275template"](13,Zt,2,0,"span",10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](2),d["\u0275\u0275classProp"]("selected","exit"===t.selectedButton),d["\u0275\u0275advance"](2),d["\u0275\u0275classProp"]("selected","absconded"===t.selectedButton),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("formGroup",t.updateForm),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",t.updateForm.controls.update.invalid&&t.updateForm.controls.update.touched),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("disabled",t.disableSubmit),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.pleasewait_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.pleasewait_loader))},directives:[u.a,se.J,se.w,se.n,se.e,se.v,se.l,o.NgIf,f.c,h.a],styles:[".mark-as-retire[_ngcontent-%COMP%]{height:250px;font-family:Roboto}.mark-as-retire[_ngcontent-%COMP%]   .mark-as-retire-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;padding:.8rem;gap:15%}.mark-as-retire[_ngcontent-%COMP%]   .mark-as-retire-btn[_ngcontent-%COMP%]   .exit-btn[_ngcontent-%COMP%]{display:flex;align-items:center;border:1px solid #45546e;border-radius:5px;text-transform:capitalize;background:#fff;width:70px;height:35px;padding:2px 10px;justify-content:center;letter-spacing:-.02em;font-size:13px;line-height:16px;color:#45546e;font-style:normal;font-weight:600}.mark-as-retire[_ngcontent-%COMP%]   .mark-as-retire-btn[_ngcontent-%COMP%]   .exit-btn.selected[_ngcontent-%COMP%]{background:#ee4961!important;color:#fff!important;border:1px solid #ee4961}.mark-as-retire[_ngcontent-%COMP%]   .mark-as-retire-btn[_ngcontent-%COMP%]   .absconded-btn[_ngcontent-%COMP%]{display:flex;align-items:center;border:1px solid #45546e;border-radius:5px;text-transform:capitalize;background:#fff;width:11 0;height:35px;padding:2px 10px;justify-content:center;letter-spacing:-.02em;font-size:13px;line-height:16px;color:#45546e;font-style:normal;font-weight:600}.mark-as-retire[_ngcontent-%COMP%]   .mark-as-retire-btn[_ngcontent-%COMP%]   .absconded-btn.selected[_ngcontent-%COMP%]{background:#ee4961!important;color:#fff!important;border:1px solid #ee4961}.mark-as-retire[_ngcontent-%COMP%]   .text-field[_ngcontent-%COMP%]{margin-top:6%;height:150px;width:100px}.mark-as-retire[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-size:10px;color:#ef4a61}.mark-as-retire[_ngcontent-%COMP%]   textarea.form-control[_ngcontent-%COMP%]{height:auto;display:block;width:320px;padding:.375rem .75rem;font-size:1rem;font-weight:400;line-height:1.5;color:#495057;background-color:#fff;background-clip:padding-box;border:1px solid #ced4da;border-radius:5px;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out;resize:none!important;box-shadow:none!important}.mark-as-retire[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;margin-top:-16%}.mark-as-retire[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{border:1px solid #fff;font-style:normal;font-weight:600;font-size:13px;letter-spacing:.01em;width:76px;height:40px;background:#ee4961;border-radius:8px;flex:none;color:#fff;display:flex;justify-content:center;align-items:center}.mark-as-retire[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:hover{opacity:.7}.mark-as-retire[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:disabled{opacity:.8}"]}),e})();var en=n("nlo9");function tn(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]())}function nn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",17),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function on(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",22),d["\u0275\u0275elementContainerStart"](1),d["\u0275\u0275elementStart"](2,"input",23),d["\u0275\u0275listener"]("change",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]().index,n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().index;return d["\u0275\u0275nextContext"](3).changeSingleSelect(o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"label",24),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](5,"br"),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](),t=e.index,n=e.$implicit,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"]().index,r=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("checked",r.params.exitSurveySection[i].lables[o].options[t].isChecked),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](r.getFormControlValue(n,"checkbox_name"))}}function rn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",25),d["\u0275\u0275elementContainerStart"](1),d["\u0275\u0275elementStart"](2,"input",26),d["\u0275\u0275listener"]("change",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]().index,n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().index;return d["\u0275\u0275nextContext"](3).changeMultipleSelect(o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"label",27),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](5,"br"),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](5);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](t.getFormControlValue(e,"checkbox_name"))}}function an(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"input",28),d["\u0275\u0275listener"]("change",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"]().index;return d["\u0275\u0275nextContext"](3).changeTextSelect(i,o,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()}}function ln(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0,18),d["\u0275\u0275template"](1,on,6,2,"div",19),d["\u0275\u0275template"](2,rn,6,1,"div",20),d["\u0275\u0275elementStart"](3,"div",21),d["\u0275\u0275template"](4,an,2,0,"ng-container",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("formGroupName",n),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","checkbox_single"==o.getFormControlValue(e,"field_type")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","checkbox_multiple"==o.getFormControlValue(e,"field_type")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","freeTextWithLabel"==o.getFormControlValue(e,"field_type"))}}function sn(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0,10),d["\u0275\u0275elementStart"](1,"mat-card",12),d["\u0275\u0275elementStart"](2,"div",13),d["\u0275\u0275text"](3),d["\u0275\u0275template"](4,nn,2,0,"span",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div"),d["\u0275\u0275elementStart"](6,"div",15),d["\u0275\u0275template"](7,ln,5,4,"ng-container",16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275nextContext"]().index,i=d["\u0275\u0275nextContext"](3);d["\u0275\u0275property"]("formGroupName",n),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",i.getFormControlValue(e,"lable_name")," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==i.params.exitSurveySection[o].lables[n].is_mandatory),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",i.getOptionFormArray(o,n).controls)}}function cn(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0,10),d["\u0275\u0275elementContainerStart"](1,11),d["\u0275\u0275template"](2,sn,8,4,"ng-container",9),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.index,n=d["\u0275\u0275nextContext"](3);d["\u0275\u0275property"]("formGroupName",e),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",n.getLableFormArray(e).controls)}}function dn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"mat-card",5),d["\u0275\u0275elementStart"](2,"div",6),d["\u0275\u0275text"](3,"Exit Survey"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"form",7),d["\u0275\u0275elementContainerStart"](5,8),d["\u0275\u0275template"](6,cn,3,2,"ng-container",9),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("formGroup",e.exitSurveyForm),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e.getSectionFormArray.controls)}}function mn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",29),d["\u0275\u0275element"](1,"img",30),d["\u0275\u0275text"](2," Exit Survey is not yet submitted by an Employee "),d["\u0275\u0275elementEnd"]())}function pn(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,dn,7,2,"div",1),d["\u0275\u0275template"](2,mn,3,0,"div",4),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.displayCondition&&e.exitResponse.length>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==e.exitResponse.length)}}let gn=(()=>{class e{constructor(e,t,n,o,i,r,a){this.edreqService=e,this._loginService=t,this._toaster=n,this.fb=o,this._router=i,this.route=r,this._snackBar=a,this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date,this.exitSurvey=[],this.submitFlag=!0,this.optionsDisableFlag=!0,this.exitResponse=[],this.exitSurveyForm=this.fb.group({exitSurveySection:this.fb.array([])}),this.submit_loader=!1,this.mandt_lables=[],this.selected_lables=[]}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.exitResponse=yield this.viewFilledExitSurvey(),yield this.generateExitForm(),this.params=this.exitSurveyForm.value,this.page_loader=!1}))}viewFilledExitSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId,form_name:"exit_survey",current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.viewFilledExitSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Exit Process !",2e3),console.log(e),n(e)})})}get getSectionFormArray(){return this.exitSurveyForm.get("exitSurveySection")}getLableFormArray(e){return this.exitSurveyForm.get("exitSurveySection").at(e).get("lables")}getOptionFormArray(e,t){return this.exitSurveyForm.get("exitSurveySection").at(e).get("lables").at(t).get("options")}getFormControlValue(e,t){return e.get(t).value}changeSingleSelect(e,t,n){1==this.params.exitSurveySection[e].lables[t].options[n].isChecked?(this.params.exitSurveySection[e].lables[t].options[n].isChecked=0,this.selected_lables=this.selected_lables.filter(n=>n!==this.params.exitSurveySection[e].lables[t].lable_id)):(this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id),console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables));for(let o=0;o<this.params.exitSurveySection[e].lables[t].options.length;o++)o!=n&&(this.params.exitSurveySection[e].lables[t].options[o].isChecked=0)}changeMultipleSelect(e,t,n){if(1==this.params.exitSurveySection[e].lables[t].options[n].isChecked){this.params.exitSurveySection[e].lables[t].options[n].isChecked=0;let o=this.selected_lables.indexOf(this.params.exitSurveySection[e].lables[t].lable_id);-1!==o&&this.selected_lables.splice(o,1)}else this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id),console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables)}changeTextSelect(e,t,n,o){let i=o.target.value;console.log("Text value",i),this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.params.exitSurveySection[e].lables[t].options[n].textbox_lable_name=i,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id),console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables)}generateExitForm(){let e=this.exitResponse.map(e=>this.createSection(e));this.exitSurveyForm.setControl("exitSurveySection",this.fb.array(e))}createSection(e){return this.fb.group({section_id:[e.section_id,se.H.required],section_name:[e.section_name,se.H.required],section_description:[e.section_description,se.H.required],lables:this.fb.array(this.loadLablesArray(e.lables))})}loadLablesArray(e){return e.map(e=>this.createLable(e))}createLable(e){return 1==e.is_mandatory&&this.mandt_lables.push(e.lable_id),this.fb.group({lable_id:[e.lable_id,se.H.required],lable_name:[e.lable_name,se.H.required],is_mandatory:[e.is_mandatory,se.H.required],options:this.fb.array(this.loadOptionsArray(e.options))})}loadOptionsArray(e){return e.map(e=>this.createOption(e))}createOption(e){return this.fb.group({field_type:[e.field_type],checkbox_id:[e.checkbox_id],checkbox_name:[e.checkbox_name],textbox_lable_id:[e.textbox_lable_id],textbox_lable_name:[e.textbox_lable_name?e.textbox_lable_name:e.textbox_lable_value],isChecked:[e.isChecked]})}isSubset(e,t){return e.every(e=>t.includes(e))}openSnackBar(e,t){this._snackBar.open(e,t)}backtodetailed(){this._router.navigate(["../detail-resignation-page"],{relativeTo:this.route})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](se.i),d["\u0275\u0275directiveInject"](i.g),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](ce.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-exit-survey-dialog"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","77vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","error-message",4,"ngIf"],[1,"exit-survey-form-popup"],[1,"exit-survey-header"],[2,"padding-bottom","6%","pointer-events","none",3,"formGroup"],["formArrayName","exitSurveySection"],[3,"formGroupName",4,"ngFor","ngForOf"],[3,"formGroupName"],["formArrayName","lables"],[1,"label-outline"],[1,"label-name"],["class","required-star",4,"ngIf"],["formArrayName","options",1,"options-to-send"],["class","option-container",3,"formGroupName",4,"ngFor","ngForOf"],[1,"required-star"],[1,"option-container",3,"formGroupName"],["class","single-select-main",4,"ngIf"],["class","multi-select-main",4,"ngIf"],[1,"text-field-main"],[1,"single-select-main"],["type","checkbox","formControlName","isChecked",1,"single-checkbox-type",3,"checked","change"],[1,"single-check-label"],[1,"multi-select-main"],["type","checkbox","formControlName","isChecked",1,"multi-select-checkbox-type",3,"change"],[1,"mulit-select-label"],["type","text","formControlName","textbox_lable_name",1,"text-field-exit-survey",3,"change"],[1,"error-message"],["src","https://assets.kebs.app/images/nomilestone.png","height","220","width","250"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,tn,3,0,"ng-container",1),d["\u0275\u0275template"](2,pn,3,2,"ng-container",1),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,u.a,se.J,se.w,se.n,se.h,o.NgForOf,se.o,se.b,se.v,se.l,se.e],styles:['.exit-survey-form-popup[_ngcontent-%COMP%]{width:95%;margin-top:17px;margin-left:3%;height:77vh;font-family:Roboto}.exit-survey-form-popup[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked{background:#ee4961;color:#fff}.exit-survey-form-popup[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:0;background:#fff;height:15px;width:15px;border:1px solid;color:#d3d3d3;border-radius:4px}.exit-survey-form-popup[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:after{content:" ";position:relative;left:40%;top:20%;width:15%;height:40%;border:solid #fff;border-width:0 2px 2px 0;transform:rotate(50deg);display:none}.exit-survey-form-popup[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked:after{display:block}.exit-survey-form-popup[_ngcontent-%COMP%]   .exit-survey-header[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;color:var(--black-100,#000);text-align:center;font-size:14px;font-style:normal;font-weight:500;line-height:normal;letter-spacing:.28px;text-transform:none}.exit-survey-form-popup[_ngcontent-%COMP%]   .label-outline[_ngcontent-%COMP%]{border-top-right-radius:15px;border-top-left-radius:15px;border-bottom-left-radius:0;background-color:#f3f5fc;width:97%;margin-top:1%}.exit-survey-form-popup[_ngcontent-%COMP%]   .label-outline[_ngcontent-%COMP%]   .label-name[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:13px;line-height:24px;display:flex;align-items:center;letter-spacing:.02em;text-transform:none;color:#000}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]{width:97%;border:1px solid var(--blue-grey-40,#dadce2);min-height:50px;justify-content:flex-start;align-items:center;padding-left:2%}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .single-select-main[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;padding:5px;width:10rem}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .single-select-main[_ngcontent-%COMP%]   .single-select-checkbox-type[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:2%}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .single-select-main[_ngcontent-%COMP%]   .single-check-label[_ngcontent-%COMP%]{width:auto;margin-bottom:0!important;padding-left:4px}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .multi-select-main[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;margin-right:10px}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .multi-select-main[_ngcontent-%COMP%]   .multi-select-checkbox-type[_ngcontent-%COMP%]{height:14px;width:14px}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .multi-select-main[_ngcontent-%COMP%]   .mulit-select-label[_ngcontent-%COMP%]{margin-bottom:0!important;width:auto;padding-left:4px}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .text-field-main[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:none}.exit-survey-form-popup[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .text-field-main[_ngcontent-%COMP%]   .text-field-exit-survey[_ngcontent-%COMP%]{width:97%;margin-top:8px;justify-content:flex-start;align-items:center;border:1px solid var(--blue-grey-40,#dadce2);height:5vh;padding:0 10px}.exit-survey-form-popup[_ngcontent-%COMP%]   .option-container[_ngcontent-%COMP%]{width:calc(100% / 3);box-sizing:border-box;padding:.5rem}.exit-survey-form-popup[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background:#f1f3f8;border-radius:8px;border:1px solid var(--blue-grey-100,#45546e);color:var(--blue-grey-100,#45546e);font-size:14px;font-style:normal;font-weight:700;line-height:16px;text-transform:none;width:100px;height:40px}.exit-survey-form-popup[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   .doneall-icon[_ngcontent-%COMP%]{justify-content:flex-start;align-items:center;display:flex;font-size:16px}.exit-survey-form-popup[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:disabled{opacity:.6}.required-star[_ngcontent-%COMP%]{color:#cf0001}.error-message[_ngcontent-%COMP%]{font-family:Roboto;justify-content:center;text-align:center;padding:10rem;font-style:normal;text-transform:none;display:flex;align-items:center;font-size:18px;font-weight:500;color:#000}  .input{outline:none!important;box-shadow:none!important}  .mat-card:not([class*=mat-elevation-z]){box-shadow:none}.mat-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%],   .submit-button .mat-progress-spinner circle{stroke:var(--palette-100-green,#5f6c81)}']}),e})(),fn=(()=>{class e{constructor(e,t,n,o,i,r,a){this.dialogRef=e,this.dialog=t,this.route=n,this.edreqService=o,this._toaster=i,this._loginService=r,this.data1=a,this.header=["Exit Survey Status"],this.subs=new c.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}viewExitSurvey(){this.dialog.open(gn,{height:"550px",width:"1000px"})}fetchApprovers(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"employee_exit_survey",exit_employee_id:this.data1.emp_Id,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchApprovers(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Manageral Approval !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](a.h),d["\u0275\u0275directiveInject"](a.b),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-exit-survey-status"]],decls:7,vars:0,consts:[[1,"view-form"],[1,"form-outime"],[1,"form-header"],[1,"view-form-btn"],[1,"view-btn",3,"click"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275text"](3,"Exit Survey Form"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",3),d["\u0275\u0275elementStart"](5,"button",4),d["\u0275\u0275listener"]("click",(function(){return t.viewExitSurvey()})),d["\u0275\u0275text"](6,"View Form"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())},styles:[".view-form[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;display:flex;justify-content:center;align-items:center;padding:1.5rem 1.5rem .5rem}.view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]{height:120px;width:100%;background:#f7f9fb;border-radius:4px;padding:1rem}.view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .form-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;color:#000}.view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;padding:2rem 0 0}.view-form[_ngcontent-%COMP%]   .form-outime[_ngcontent-%COMP%]   .view-form-btn[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%]{border:1px solid #45546e;font-style:normal;font-weight:600;font-size:13px;letter-spacing:.01em;width:105px;height:40px;background:#f7f9fb;border-radius:8px;flex:none;color:#45546e;display:flex;justify-content:center;align-items:center}"]}),e})();var hn=n("bTqV"),un=n("STbY");function xn(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]())}function vn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275elementStart"](1,"div",34),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.lwd_change_reason)}}function _n(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",25),d["\u0275\u0275elementStart"](2,"div",14),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",26),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",27),d["\u0275\u0275text"](7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",14),d["\u0275\u0275text"](9),d["\u0275\u0275pipe"](10,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"div",28),d["\u0275\u0275elementStart"](12,"div",29),d["\u0275\u0275elementStart"](13,"div",30),d["\u0275\u0275text"](14),d["\u0275\u0275pipe"](15,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",31),d["\u0275\u0275text"](17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](18,vn,3,1,"div",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.associate_id),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.NAME),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.personal_email),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind2"](10,7,e.submitted_on,"dd-MMM-YYYY")," "),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind2"](15,10,e.last_working_day,"dd-MMM-YYYY")," "),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.days_left," Days Left "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==e.is_lwd_changed)}}const Cn=function(e){return{disabled:e}};function yn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"a",57),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](),t=e.index;d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction1"](2,Cn,"NotApplicable"==e.$implicit.status)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](t+1)}}function bn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",58),d["\u0275\u0275text"](1,"done"),d["\u0275\u0275elementEnd"]())}function On(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",58),d["\u0275\u0275text"](1,"clear"),d["\u0275\u0275elementEnd"]())}function Mn(e,t){1&e&&d["\u0275\u0275element"](0,"div",59)}const Pn=function(e){return{"background-color":e}},Sn=function(e,t){return{"background-color":e,"border-color":t,color:"white"}},wn=function(e){return{visibility:e}};function En(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",38),d["\u0275\u0275elementStart"](2,"div",39),d["\u0275\u0275template"](3,yn,2,4,"a",40),d["\u0275\u0275template"](4,bn,2,0,"mat-icon",41),d["\u0275\u0275template"](5,On,2,0,"mat-icon",41),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](6,Mn,1,0,"div",42),d["\u0275\u0275elementStart"](7,"mat-card",43),d["\u0275\u0275elementStart"](8,"div",44),d["\u0275\u0275elementStart"](9,"div",45),d["\u0275\u0275elementStart"](10,"div",46),d["\u0275\u0275text"](11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"div",47),d["\u0275\u0275text"](13,"Completed on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"div",47),d["\u0275\u0275text"](15,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](16,"br"),d["\u0275\u0275elementStart"](17,"div",45),d["\u0275\u0275elementStart"](18,"div",48),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",49),d["\u0275\u0275text"](21),d["\u0275\u0275pipe"](22,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"div",50),d["\u0275\u0275element"](24,"div",51),d["\u0275\u0275elementStart"](25,"div"),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](27,"div",52),d["\u0275\u0275elementStart"](28,"mat-icon",19),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit,o=t.index;return d["\u0275\u0275nextContext"](4).nextbtn(n,o)})),d["\u0275\u0275text"](29," chevron_right "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](30,"div",53),d["\u0275\u0275elementStart"](31,"button",54),d["\u0275\u0275elementStart"](32,"mat-icon"),d["\u0275\u0275text"](33,"more_vert"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](34,"mat-menu",null,55),d["\u0275\u0275elementStart"](36,"button",56),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.index,o=t.$implicit;return d["\u0275\u0275nextContext"](4).onNotApplicableClick(n,o)})),d["\u0275\u0275elementStart"](37,"span"),d["\u0275\u0275text"](38,"Not Applicable"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](39,"button",56),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.index,o=t.$implicit;return d["\u0275\u0275nextContext"](4).approveClick(n,o)})),d["\u0275\u0275elementStart"](40,"span"),d["\u0275\u0275text"](41,"Approve"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,o=d["\u0275\u0275reference"](35),i=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](21,Pn,e.color))("ngStyle",d["\u0275\u0275pureFunction2"](23,Sn,e.color,e.color))("ngClass",d["\u0275\u0275pureFunction1"](26,Cn,"NotApplicable"==e.status)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","inprogress"==e.status_type||"pending"==e.status_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","accept"==e.status_type||"submit"==e.status_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","reject"==e.status_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",n+1!=i.resignationSteps.length),d["\u0275\u0275advance"](1),d["\u0275\u0275classProp"]("disabled",i.isCardDisabled[n]),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction1"](28,Cn,"NotApplicable"==e.status)),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate1"](" ",e.process_display_name," "),d["\u0275\u0275advance"](8),d["\u0275\u0275textInterpolate1"](" ",e.process_desc," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.completed_on?d["\u0275\u0275pipeBind2"](22,18,e.completed_on,"dd-MMM-YYYY"):"--"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](30,Pn,e.color)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ","NotApplicable"===e.status?"Not Applicable":"InProgress"===e.status?"In-Progress":e.status," "),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](32,wn,"accept"==e.status_type||"notApplicable"==e.status_type||i.isCardDisabled[n]?"hidden":"visible")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matMenuTriggerFor",o)("matMenuTriggerDisabled",i.isMenuNotApplicable(n))}}function kn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",35),d["\u0275\u0275template"](1,En,42,34,"div",36),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.resignationSteps)}}function In(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",60),d["\u0275\u0275element"](2,"mat-spinner",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]())}function Dn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",86),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function Fn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",84),d["\u0275\u0275text"](1),d["\u0275\u0275template"](2,Dn,2,0,"span",85),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.field_label,""),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e?null:e.is_mandatory)}}function jn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",88),d["\u0275\u0275elementStart"](2,"input",89),d["\u0275\u0275listener"]("change",(function(n){d["\u0275\u0275restoreView"](e);const o=t.index,i=t.$implicit,r=d["\u0275\u0275nextContext"](2).$implicit;return d["\u0275\u0275nextContext"](7).getHrCheckBoxVal(o,i.id,r,n)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"label",90),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2).$implicit,o=d["\u0275\u0275nextContext"](7);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("checked",o.hrParams[n.field_key]==e.id),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function zn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",87),d["\u0275\u0275template"](1,jn,5,2,"div",20),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.options)}}function An(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",86),d["\u0275\u0275text"](1," \xa0*"),d["\u0275\u0275elementEnd"]())}function Rn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",91),d["\u0275\u0275text"](1),d["\u0275\u0275template"](2,An,2,0,"span",85),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.field_label,""),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e?null:e.is_mandatory)}}function Nn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",92),d["\u0275\u0275elementStart"](1,"input",93),d["\u0275\u0275listener"]("change",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275nextContext"]().$implicit,o=d["\u0275\u0275nextContext"](7);return o.getHrInputVal(o.i,n,t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formControlName",e.field_key)}}function qn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",79),d["\u0275\u0275template"](1,Fn,3,2,"div",80),d["\u0275\u0275template"](2,zn,2,1,"div",81),d["\u0275\u0275template"](3,Rn,3,2,"div",82),d["\u0275\u0275template"](4,Nn,2,1,"div",83),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",""!=(null==e?null:e.field_label)&&"checkbox"==e.field_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","checkbox"==e.field_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",""!=(null==e?null:e.field_label)&&"text"==e.field_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","text"==e.field_type)}}function Yn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275element"](1,"mat-spinner",94),d["\u0275\u0275elementEnd"]())}function Vn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275text"](1," save "),d["\u0275\u0275elementEnd"]())}const Tn=function(e){return{"pointer-events":e}};function Hn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",66),d["\u0275\u0275elementStart"](1,"form",67),d["\u0275\u0275elementStart"](2,"div",68),d["\u0275\u0275elementStart"](3,"div",69),d["\u0275\u0275template"](4,qn,5,4,"div",70),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",71),d["\u0275\u0275elementStart"](6,"div",72),d["\u0275\u0275elementStart"](7,"div",73),d["\u0275\u0275elementStart"](8,"p",74),d["\u0275\u0275text"](9,"Functional Head :"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"p",75),d["\u0275\u0275text"](11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"div",76),d["\u0275\u0275elementStart"](13,"p",74),d["\u0275\u0275text"](14,"Date :"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"p",75),d["\u0275\u0275text"](16),d["\u0275\u0275pipe"](17,"date"),d["\u0275\u0275pipe"](18,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"div",72),d["\u0275\u0275elementStart"](20,"div",73),d["\u0275\u0275elementStart"](21,"p",74),d["\u0275\u0275text"](22,"Signature :"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"p",75),d["\u0275\u0275text"](24),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](25,"div",77),d["\u0275\u0275elementStart"](26,"button",78),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](6).saveHrForm()})),d["\u0275\u0275template"](27,Yn,2,0,"span",1),d["\u0275\u0275template"](28,Vn,2,0,"span",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](6);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",e.hrFormGroup)("ngStyle",d["\u0275\u0275pureFunction1"](15,Tn,e.disableSubmit||"accept"==e.hr_status_type?"none":"auto")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.hrFormData),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate"](e.fh_data.length>0?e.fh_data[0].fm_name:"-"),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate"](e.formValues.length>0?d["\u0275\u0275pipeBind2"](17,9,e.formValues[0].created_on,"dd-MMM-YYYY"):d["\u0275\u0275pipeBind2"](18,12,e.currentDate,"dd-MMM-YYYY")),d["\u0275\u0275advance"](8),d["\u0275\u0275textInterpolate"](e.user_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("disabled",e.disableSubmit||"accept"==e.hr_status_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.pleasewait_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.pleasewait_loader)}}function Bn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",62),d["\u0275\u0275elementStart"](1,"div",63),d["\u0275\u0275elementStart"](2,"mat-icon",64),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](5).backtoDetails()})),d["\u0275\u0275text"](3,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](4,Hn,29,17,"div",65),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](5);d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",e.hrFormData.length>0)}}function $n(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,Bn,5,1,"div",61),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.showNextHrTemplate)}}function Wn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,In,3,0,"ng-container",1),d["\u0275\u0275template"](2,$n,2,1,"ng-container",1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.hr_page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.hr_page_loader)}}function Gn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275elementStart"](2,"div",6),d["\u0275\u0275elementStart"](3,"div",7),d["\u0275\u0275element"](4,"app-user-image",8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",9),d["\u0275\u0275text"](6,"Resignation Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275elementStart"](8,"button",10),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).markAsRetire()})),d["\u0275\u0275text"](9," Mark as Exit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",11),d["\u0275\u0275elementStart"](11,"div",12),d["\u0275\u0275elementStart"](12,"div",13),d["\u0275\u0275elementStart"](13,"div",14),d["\u0275\u0275text"](14,"Emp ID"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",14),d["\u0275\u0275text"](16,"Name"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",15),d["\u0275\u0275text"](18,"Personal Email"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"div",14),d["\u0275\u0275text"](20,"Submitted On"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"div",16),d["\u0275\u0275elementStart"](22,"div",17),d["\u0275\u0275text"](23," last Working Day "),d["\u0275\u0275elementStart"](24,"div",18),d["\u0275\u0275elementStart"](25,"mat-icon",19),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).openDatePicker()})),d["\u0275\u0275text"](26,"edit"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](27,_n,19,13,"div",20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](28,"div",21),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).viewReason()})),d["\u0275\u0275elementStart"](29,"div",22),d["\u0275\u0275text"](30,"View reason for Resignation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](31,"hr"),d["\u0275\u0275template"](32,kn,2,1,"div",23),d["\u0275\u0275template"](33,Wn,3,2,"div",24),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("id",e.employeeDetails.user_oid),d["\u0275\u0275advance"](23),d["\u0275\u0275property"]("ngForOf",e.employeeDetails),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngIf",e.mainContainer),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.showNextHrTemplate)}}function Ln(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"mat-card",4),d["\u0275\u0275template"](2,Gn,34,4,"div",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.displayCondition)}}function Kn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275elementStart"](1,"div",11),d["\u0275\u0275elementStart"](2,"div",12),d["\u0275\u0275text"](3,"Emp ID"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",13),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",11),d["\u0275\u0275elementStart"](7,"div",12),d["\u0275\u0275text"](8,"Name"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",13),d["\u0275\u0275text"](10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"div",11),d["\u0275\u0275elementStart"](12,"div",12),d["\u0275\u0275text"](13,"Personal Email"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"div",13),d["\u0275\u0275text"](15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",11),d["\u0275\u0275elementStart"](17,"div",12),d["\u0275\u0275text"](18,"Submitted On"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"div",13),d["\u0275\u0275text"](20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"div",11),d["\u0275\u0275elementStart"](22,"div",12),d["\u0275\u0275text"](23,"Last Working Date (Estimated)"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](24,"div",14),d["\u0275\u0275elementStart"](25,"div",13),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](27,"div",15),d["\u0275\u0275text"](28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",e.employeeID," "),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",e.employeeName," "),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",e.employeeEmail," "),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",e.employeeResignationAppliedDate," "),d["\u0275\u0275advance"](6),d["\u0275\u0275textInterpolate1"](" ",e.employeeLastWorkingDate," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.daysLeft," ")}}function Jn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"label",22),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.option)}}function Xn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275element"](2,"input",20),d["\u0275\u0275template"](3,Jn,2,1,"label",21),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275classMap"](e.class),d["\u0275\u0275propertyInterpolate"]("type",e.type),d["\u0275\u0275propertyInterpolate"]("placeholder",e.placeholder),d["\u0275\u0275propertyInterpolate"]("value",e.value),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isItrated)}}function Un(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",16),d["\u0275\u0275elementStart"](1,"div",17),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,Xn,4,7,"div",18),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.item),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",n.options)}}const Zn=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i,r){this._router=e,this.route=t,this.dialog=n,this._toaster=o,this._loginService=i,this.edreqService=r,this.subs=new c.a,this.displayCondition=!1,this.displayConditionHr=!1,this.page_loader=!1,this.hr_page_loader=!1,this.pleasewait_loader=!1,this.disableSubmit=!1,this.form="hr",this.currentDate=new Date,this.isCardDisabled=[],this.fh_data=[],this.showNextHrTemplate=!1,this.mainContainer=!1,this.hrFormData=[],this.hrParams={},this.hrFormGroup=new se.m({medical_card:new se.j,medical_card_remark:new se.j,no_objection:new se.j,no_objection_remark:new se.j,due:new se.j}),this.formValues={}}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.user_name=this._loginService.getProfile().profile.name,yield this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExId=e}),this.mainContainer=!0,this.resignationSteps=yield this.fetchResignationStepsResignationTab(),this.endRoute=this._router.url.split("/")[4],console.log(this._router.url.split("/"),this.endRoute),this.employeeDetails=yield this.fetchEmployeeDetaisForDetailResignationRequest(),this.page_loader=!1}))}nextbtn(e,t){const{exit_process_id:n,dept_id:o,exit_employee_id:i,id:r,type:a}=e;switch(this.edreqService.empExId=i,this.edreqService.processId=r,n){case 1:this.nextManager(this.employeeDetails[0].associate_id,n,i,a);break;case 2:this.nextipsignup(this.employeeDetails[0].associate_id,n,i,a);break;case 3:this.nexthandover(this.employeeDetails[0].associate_id,n,i,a);break;case 4:this.nextExitSurvey(this.employeeDetails[0].associate_id,n,i,a)}switch(o){case 1:this.nextsystemAdministration(this.employeeDetails[0].associate_id,o,i,a);break;case 2:this.nextAdministration(this.employeeDetails[0].associate_id,o,i,a);break;case 3:this.nextFinance(this.employeeDetails[0].associate_id,o,i,a);break;case 4:this.nextHr(e,t),this.wf_header_id=e.wf_header_id,this.wf_item_id=e.wf_item_id,this.exit_employee_id=e.exit_employee_id,this.exit_process_id=e.exit_process_id}}fetchEmployeeDetaisForDetailResignationRequest(){let e={params:{user_aid:this.endRoute,user_oid:this._loginService.getProfile().profile.oid,current_date:s()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchEmployeeDetaisForDetailResignationRequest(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Employee Details !",2e3),console.log(e),n(e)})})}fetchResignationStepsResignationTab(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId,user_oid:this._loginService.getProfile().profile.oid}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchResignationStepsResignationTab(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Exit Process !",2e3),console.log(e),n(e)})})}nextStep(e,t,n,o,i){this.edreqService.sendEmpExitId(o),this.dialog.open(e,{height:"100vh",width:"50%",position:{top:"0px",right:"0px"},data:{associate_id:t,process_Id:n,emp_Id:o,type:i}})}nextManager(e,t,n,o){this.nextStep(I,e,t,n,o)}nextipsignup(e,t,n,o){this.nextStep(St,e,t,n,o)}nexthandover(e,t,n,o){this.nextStep(ct,e,t,n,o)}nextExitSurvey(e,t,n,o){this.nextStep(fn,e,t,n,o)}nextsystemAdministration(e,t,n,o){this.nextStep(Bt,e,t,n,o)}nextAdministration(e,t,n,o){this.nextStep(L,e,t,n,o)}nextFinance(e,t,n,o){this.nextStep(le,e,t,n,o)}nextHr(e,t){return Object(r.c)(this,void 0,void 0,(function*(){this.hrIndex=t,this.hr_status_type=e.status_type,this.hrParams.exit_employee_id=this.edreqService.empExId,this.hrParams.user_aid=e.aid,this.mainContainer=!1,this.showNextHrTemplate=!0,this.fh_data=yield this.getFunctionalHead(),console.log("fh_data",this.fh_data),this.formValues=yield this.fetchHRFormValues(),console.log("formValues",this.formValues),this.hrFormData=yield this.fetchHRFormConfig(),console.log("hrFormData",this.hrFormData),yield this.handleHrFieldConfig()}))}handleHrFieldConfig(){return Object(r.c)(this,void 0,void 0,(function*(){let e=this.hrFormData;e.length>0&&e.forEach((e,t)=>{let n=this.hrFormGroup.get(e.field_key);this.formValues.length>0&&(this.hrFormGroup.get(e.field_key).setValue(this.formValues[0][e.field_key]),this.hrParams[null==e?void 0:e.field_key]=this.formValues[0][null==e?void 0:e.field_key]),n&&e.is_active_field&&(e.is_mandatory?n.setValidators([se.H.required]):(n.clearValidators(),n.updateValueAndValidity()))})}))}saveHrForm(e){return Object(r.c)(this,void 0,void 0,(function*(){if(console.log("Params",this.hrParams),console.log("Form group value",this.hrFormGroup.value),console.log("Form group status",this.hrFormGroup.status),"VALID"==this.hrFormGroup.status)return yield this.initiateApproval(),new Promise((e,t)=>{this.subs.sink=this.edreqService.insertHrFormDetails({params:this.hrParams}).subscribe(t=>{e(t),this.disableSubmit=!0,this.pleasewait_loader=!0,this._toaster.showSuccess("Success","HR Form Submitted!",2e3),this.pleasewait_loader=!1,this.backtoDetails(),console.log("Hr Data :",this.employeeDetails[this.hrIndex]),this.resignationSteps[this.hrIndex].color="#009432",this.resignationSteps[this.hrIndex].status_type="submit",this.resignationSteps[this.hrIndex].status="Submitted"},e=>{this._toaster.showError("Error","Failed to submit form!",2e3),console.log(e),t(e)})});this._toaster.showWarning("Warning","Fill mandatory fields")}))}initiateApproval(){return Object(r.c)(this,void 0,void 0,(function*(){let e={action_type:"A",wf_header_id:this.wf_header_id,exit_process_id:this.exit_process_id,exit_employee_id:this.exit_employee_id,user_oid:this._loginService.getProfile().profile.oid};this.disableSubmit=!0,this.onSubmit=yield this.initiateApprove(e)}))}initiateApprove(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.initiateApprove(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to submit your request!",2e3),console.log(e),n(e)})})}backtoDetails(){this.mainContainer=!0,this.showNextHrTemplate=!1}viewReason(){this.dialog.open(Jt,{height:"100vh",width:"42%",position:{top:"0px",right:"0px"}})}getHrInputVal(e,t,n){this.hrParams[t.field_key]=n.target.value}getHrInputNumberVal(e,t,n){this.hrParams[t.field_key]=parseInt(n.target.value)}getHrCheckBoxVal(e,t,n,o){this.hrParams[n.field_key]=t,o.target.checked?this.hrFormGroup.get(n.field_key).setValue(t):this.hrFormGroup.get(n.field_key).reset()}markAsRetire(){this.dialog.open(Qt,{height:"250px",width:"350px",data:this.employeeDetails[0].associate_id})}openDatePicker(){this.dialog.open(en.a,{width:"610px",height:"380px",data:s()(this.employeeDetails[0].submitted_on).format("YYYY-MM-DD")})}changeApprove(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.changeApprove(e).subscribe(e=>{t(e)},e=>{this._toaster.showError("Error","Failed to change to Inapplicable!",2e3),console.log(e),n(e)})})}fetchHRFormValues(){let e={params:{exit_employee_id:this.edreqService.empExId,user_aid:this._loginService.getProfile().profile.aid}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchHRForm(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to get HR form vlues",2e3),console.log(e),n(e)})})}fetchHRFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.fetchHRFormConfig().subscribe(t=>{e(t.data)},e=>{this._toaster.showError("Error","Failed to change to Inapplicable!",2e3),console.log(e),t(e)})})}getFunctionalHead(){let e={aid:this.employeeDetails[0].associate_id,form:this.form};return new Promise((t,n)=>{this.subs.sink=this.edreqService.getFunctionalHead(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to get functional head data",2e3),console.log(e),n(e)})})}changeIfApplicable(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.changeIfApplicable(e).subscribe(e=>{t(e)},e=>{this._toaster.showError("Error","Failed to change to Inapplicable!",2e3),console.log(e),n(e)})})}isMenuNotApplicable(e){return this.isCardDisabled&&e===this.disabledIndex}approveClick(e,t){return Object(r.c)(this,void 0,void 0,(function*(){if("handover_takeover"==t.type&&"inprogress"==t.status_type||"ip_out_sign"==t.type&&"inprogress"==t.status_type)this._toaster.showWarning("Approval denied",t.process_name+" form has not been submitted");else{this.isCardDisabled[e]=!1,this.resignationSteps[e].status_type="accept",this.resignationSteps[e].color="#009432",this.resignationSteps[e].status="Accepted";let n={exit_employee_id:this.edreqService.empExId,user_aid:this._loginService.getProfile().profile.aid,exit_process_id:t.exit_process_id,dept_id:t.dept_id};this.onSubmit=yield this.changeApprove(n),this._toaster.showInfo("Success","Changed",1e3)}}))}onNotApplicableClick(e,t){return Object(r.c)(this,void 0,void 0,(function*(){this.isCardDisabled[e]=!0,this.resignationSteps[e].status_type="notApplicable",this.resignationSteps[e].color="#d7d7d7",this.resignationSteps[e].status="NotApplicable";let n={exit_employee_id:this.edreqService.empExId,user_aid:this._loginService.getProfile().profile.aid,exit_process_id:t.exit_process_id,exit_dept_id:t.dept_id};this.onSubmit=yield this.changeIfApplicable(n),this._toaster.showInfo("Success","Changed",1e3)}))}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](i.g),d["\u0275\u0275directiveInject"](i.a),d["\u0275\u0275directiveInject"](a.b),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-resignation-landing-page"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"loaDer"],["matTooltip","Please wait...","diameter","30"],[1,"resign-req","pl-1","pr-1","mat-card",2,"height","100%"],[1,"top-stat"],[1,"pro-reg"],[1,"svg-resignation"],["imgWidth","28px","imgHeight","28px",2,"margin-right","3px","overflow","hidden","text-overflow","ellipsis",3,"id"],[1,"header-request"],[1,"retire-btn",3,"click"],[2,"display","flex","justify-content","space-between"],[1,"table-light","col-10"],[1,"table-content-head","row"],[1,"col-2"],[1,"col-3"],[1,"col-3",2,"display","flex","justify-content","center"],[1,"change-date"],[1,"change-date-option"],[3,"click"],[4,"ngFor","ngForOf"],[1,"reason-btn","col-2",3,"click"],[1,"emp-reason-btn"],["class","ScrollStyle",4,"ngIf"],["class","container-fluid RequestForm formpage",4,"ngIf"],[1,"table-content","row"],[1,"overflowText","col-2"],[1,"overflowText","col-3"],[1,"lat-working","col-3"],[1,"lst-row","row"],[1,"d-flex","align-items-center","col-7"],[1,"days-left","col-5"],["class","hidediv",4,"ngIf"],[1,"hidediv"],[1,"reason_title"],[1,"ScrollStyle"],["class","d-flex justify-content-around",4,"ngFor","ngForOf"],[1,"d-flex","justify-content-around"],[1,"step","active",2,"height","15%"],[1,"circle",3,"ngStyle","ngClass"],[3,"ngClass",4,"ngIf"],["class","circle_style",4,"ngIf"],["class","line",4,"ngIf"],[1,"resign-request-steps",3,"ngClass"],[2,"display","block","width","97%","padding","0.4rem"],[1,"content","row"],["mat-cell","",1,"head-content","col-8"],[1,"comp-stat","col-2"],["mat-cell","",1,"des-cont","col-8"],[1,"comp-date","col-2"],[1,"stat-approve","col-2"],[1,"circle-stat",3,"ngStyle"],[1,"next-btn"],[1,"option-btn",3,"ngStyle"],["mat-icon-button","",3,"matMenuTriggerFor","matMenuTriggerDisabled"],["menu","matMenu"],["mat-menu-item","",3,"click"],[3,"ngClass"],[1,"circle_style"],[1,"line"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","53vh","margin-top","0rem !important"],["class","hr-card",4,"ngIf"],[1,"hr-card"],[1,"d-flex","justify-content-end",2,"padding","1rem 1rem 0rem"],[1,"backtodetail",3,"click"],["class","main",4,"ngIf"],[1,"main"],[3,"formGroup","ngStyle"],[1,"pl-1","pr-1","container-fluid"],[1,"returnBack","row"],["class","returnBackItems col-6",4,"ngFor","ngForOf"],[1,"returnBack","row",2,"padding-left","2rem !important"],[1,"row","col-12"],[1,"col-6","data-box"],[1,"label-name"],[1,"label-value"],[1,"col-6","data-box",2,"justify-content","center"],[1,"pl-1"],["mat-raised-button","",1,"submit-btn",3,"disabled","click"],[1,"returnBackItems","col-6"],["class","returnBackItemsHeader col-7",4,"ngIf"],["class","checkbox-css col-4 form-check form-check-inline","style","margin-left: 2%",4,"ngIf"],["class","returnBackItemsHeader col-4",4,"ngIf"],["class","form-check form-check-inline col-4","style","margin-left: 2%",4,"ngIf"],[1,"returnBackItemsHeader","col-7"],["class","required-star",4,"ngIf"],[1,"required-star"],[1,"checkbox-css","col-4","form-check","form-check-inline",2,"margin-left","2%"],[2,"display","flex","justify-content","center","align-items","center","gap","15%"],["type","checkbox",1,"checkbox-field-hr",3,"checked","change"],["for","inlineCheckbox1",1,"form-check-label"],[1,"returnBackItemsHeader","col-4"],[1,"form-check","form-check-inline","col-4",2,"margin-left","2%"],[3,"formControlName","change"],["matTooltip","Your request is being submitted!!","diameter","25"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,xn,3,0,"ng-container",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](2,Ln,3,1,"ng-container",1)),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.page_loader),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[o.NgIf,f.c,h.a,u.a,v.a,x.a,o.NgForOf,o.NgStyle,o.NgClass,hn.a,un.f,un.g,un.d,se.J,se.w,se.n,se.e,se.v,se.l],pipes:[o.DatePipe],styles:[".resign-req[_ngcontent-%COMP%]{background:#fff;display:inline-block;margin-top:1rem;height:77vh}.resign-req[_ngcontent-%COMP%], .resign-request-steps[_ngcontent-%COMP%]{margin-left:2%;border-radius:6px;width:96%}.resign-request-steps[_ngcontent-%COMP%]{background-color:#f3f5fc;display:flex;justify-content:center;align-items:center;height:15%}.required-star[_ngcontent-%COMP%]{color:#cf0001}.overflowText[_ngcontent-%COMP%]{flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.lat-working[_ngcontent-%COMP%], .lat-working[_ngcontent-%COMP%]   .lst-row[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.lat-working[_ngcontent-%COMP%]   .lst-row[_ngcontent-%COMP%]{margin-left:-8%}.top-stat[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;flex-direction:row;justify-content:space-between;padding:.5rem 1rem}.top-stat[_ngcontent-%COMP%], .top-stat[_ngcontent-%COMP%]   .pro-reg[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.top-stat[_ngcontent-%COMP%]   .pro-reg[_ngcontent-%COMP%]{justify-content:flex-start;gap:1%}.top-stat[_ngcontent-%COMP%]   .svg-resignation[_ngcontent-%COMP%]{margin-left:0}.top-stat[_ngcontent-%COMP%]   .header-request[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;width:75%}.top-stat[_ngcontent-%COMP%]   .withdraw-btn[_ngcontent-%COMP%]{border:1px solid #fff;border-radius:5px;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;width:180px;height:37px;line-height:33px;padding:2px 10px;font-weight:500;letter-spacing:.01em}.top-stat[_ngcontent-%COMP%]   .retire-btn[_ngcontent-%COMP%], .top-stat[_ngcontent-%COMP%]   .withdraw-btn[_ngcontent-%COMP%]{text-transform:capitalize;display:flex;justify-content:center;font-size:13px}.top-stat[_ngcontent-%COMP%]   .retire-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:5px;background:#fff;width:120px;height:32px;line-height:16px;align-items:center;color:#45546e;font-style:normal;font-weight:601}.table-light[_ngcontent-%COMP%]{font-family:Roboto;font-size:12px;line-height:14px;letter-spacing:.02em;padding:1rem 0 1rem 1rem}.table-light[_ngcontent-%COMP%]   .table-content-head[_ngcontent-%COMP%]{font-weight:400;opacity:.4;padding:1px}.table-light[_ngcontent-%COMP%]   .table-content[_ngcontent-%COMP%]{font-weight:400;display:flex;justify-content:center;align-items:center;padding:1px}.table-light[_ngcontent-%COMP%]   .table-content[_ngcontent-%COMP%]   .days-left[_ngcontent-%COMP%]{font-weight:400;font-size:10px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#cf0001;opacity:.7;width:14rem;height:25px;margin-left:-18%;display:flex;justify-content:center;line-height:23px;border:1px solid #cf0001;border-radius:17px}.change-date[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;font-family:Roboto;font-style:normal}.change-date[_ngcontent-%COMP%]   .change-date-option[_ngcontent-%COMP%]{cursor:pointer;margin-top:-2px;color:#5e5d5d}.change-date[_ngcontent-%COMP%]   .change-date-option[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:14px}.change-date[_ngcontent-%COMP%]   input[hidden][_ngcontent-%COMP%]{position:absolute;top:-9999px;left:-9999px}.resign-req[_ngcontent-%COMP%]   .disabled[_ngcontent-%COMP%]{opacity:.5;pointer-events:none}.reason-btn[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;cursor:pointer;padding:1rem 3rem 1rem 0}.reason-btn[_ngcontent-%COMP%]   .emp-reason-btn[_ngcontent-%COMP%]{border-radius:5px;text-transform:capitalize;color:#45546e;width:200px;height:35px;display:flex;line-height:25px;padding:2px 10px;justify-content:center;font-weight:500;font-size:11px;letter-spacing:.01em}hr[_ngcontent-%COMP%]{margin-top:0;opacity:.1;border:.5px solid #000}.step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;justify-content:flex-start;width:98%;margin-left:-2%}.circle[_ngcontent-%COMP%]{border-radius:100%;width:38px;height:38px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;background:#d7d7d7;border:2px solid #d7d7d7;position:relative;z-index:2;margin-top:26px;left:3rem}.circle[_ngcontent-%COMP%]   a.disabled[_ngcontent-%COMP%]{pointer-events:none;opacity:.5}.circle[_ngcontent-%COMP%]   .disabled[_ngcontent-%COMP%]{pointer-events:none;background:#e8e8ea;border:2px solid #e8e8ea}.step.empty[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{visibility:hidden}.content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-weight:700;font-size:14px;line-height:16px;display:flex;align-items:center;color:#26303e}.content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{font-size:12px;letter-spacing:.02em;opacity:.5}.content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;font-weight:401;line-height:14px;text-transform:capitalize;color:#000}.content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{font-size:12px!important;align-items:center}.content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:401;font-size:12px;justify-content:center;line-height:16px;letter-spacing:.02em;text-transform:capitalize;justify-content:flex-start;gap:4%}.content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle-stat[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.next-btn[_ngcontent-%COMP%]{background:#f3f5fc;font-size:35px;opacity:.7}.next-btn[_ngcontent-%COMP%], .next-btn[_ngcontent-%COMP%]:hover{cursor:pointer;display:flex;justify-content:center;align-items:center;border-radius:100%;width:30px;height:30px;color:#a4a8ac}.next-btn[_ngcontent-%COMP%]:hover{background:#d7d7d7;opacity:1;transition:background-color .1s linear}.next-btn[_ngcontent-%COMP%]   .disabled-icon[_ngcontent-%COMP%]{cursor:not-allowed}.option-btn[_ngcontent-%COMP%]{background:#fff;color:#a4a8ac;font-size:35px;opacity:.7;font-size:18px}.option-btn[_ngcontent-%COMP%], .option-btn[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{cursor:pointer;display:flex;justify-content:center;align-items:center;border-radius:100%;width:30px;height:30px;margin-top:3%;margin-left:-1%}.option-btn[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{background:#d7d7d7;color:#f3f5fc;opacity:1;font-size:19px;transition:background-color .1s linear}.step[_ngcontent-%COMP%]  .mat-card:not([class*=mat-elevation-z]){box-shadow:none!important}.ScrollStyle[_ngcontent-%COMP%]{height:53vh;overflow-y:scroll}.circle_style[_ngcontent-%COMP%]{align-items:center;font-size:20px;display:flex;justify-content:center}.hr-card[_ngcontent-%COMP%]{height:53vh;overflow-y:scroll}.hr-card[_ngcontent-%COMP%]   .backtodetail[_ngcontent-%COMP%]{cursor:pointer;font-size:24px;color:#a4a8ac;opacity:.7}.hr-card[_ngcontent-%COMP%]   .backtodetail[_ngcontent-%COMP%]:hover{background:#d7d7d7;color:#a4a8ac;border-radius:100%;transition:background-color .1s linear}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]{padding:1rem 3rem;width:100%}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .returnBackItems[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-weight:400;font-size:14px;line-height:16px;letter-spacing:-.02em;text-transform:capitalize;color:#45546e;margin-left:-30px;margin-bottom:1rem}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .returnBackItems[_ngcontent-%COMP%]   .returnBackItemsHeader[_ngcontent-%COMP%]{color:var(--blue-grey-100,#45546e);font-size:14px;font-family:Roboto;font-style:normal;font-weight:400;line-height:16px;letter-spacing:-.28px;text-transform:none}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .returnBackItems[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-family:roboto;font-size:14px;line-height:24px;letter-spacing:.02em;text-transform:capitalize;color:#5f6c81;height:35px;border:2px solid #5f6c81;border-radius:4px;opacity:.7}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .returnBackItems[_ngcontent-%COMP%]   .checkbox-css[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;padding-left:0;gap:25%;margin-right:.75rem}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .checkbox-field-hr[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%]:before{background-color:#f27a6c}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .nsr-number[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;font-weight:400;font-size:14px;line-height:16px;letter-spacing:-.02em;text-transform:capitalize;color:#45546e;column-gap:7%}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .nsr-number[_ngcontent-%COMP%]   .nsr-input[_ngcontent-%COMP%]{font-family:roboto;font-size:13px;line-height:24px;letter-spacing:.02em;text-transform:capitalize;color:#5f6c81;width:200px;border-radius:4px;border:1px solid var(--black-20,#d4d6d8)}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]   .text-area-input[_ngcontent-%COMP%]{border-radius:8px;border:1.5px solid var(--neutral-50,#b9c0ca);display:flex;width:341px;height:96px;padding:12px 16px;align-items:flex-start;flex-shrink:0;resize:none;margin-left:-1%}.hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{margin-left:4%;border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;width:70px;height:35px;font-family:Roboto;font-style:normal;font-weight:500;font-size:13px;letter-spacing:.01em}.hr-card[_ngcontent-%COMP%]   .error-hr[_ngcontent-%COMP%], .hr-card[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{align-items:center;display:flex;justify-content:center}.hr-card[_ngcontent-%COMP%]   .error-hr[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.hr-card[_ngcontent-%COMP%]   .error-hr[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:8rem}.loaDer[_ngcontent-%COMP%]{height:77vh;margin-top:0!important;display:flex;justify-content:center;align-items:center}.label-name[_ngcontent-%COMP%], .label-value[_ngcontent-%COMP%]{color:var(--blue-grey-100,#45546e);font-size:14px;font-family:Roboto;font-style:normal;font-weight:400;line-height:16px;letter-spacing:-.28px;text-transform:none}.label-value[_ngcontent-%COMP%]{margin-left:10px}.data-box[_ngcontent-%COMP%]{display:flex}.hidediv[_ngcontent-%COMP%]{display:none}.hidediv[_ngcontent-%COMP%], .lst-row[_ngcontent-%COMP%]:hover + .hidediv[_ngcontent-%COMP%]{height:100px;border:1px solid #d3d3d3;border-radius:4px;z-index:1;position:absolute;top:120%;left:20%;width:80%;background-color:#fff}.lst-row[_ngcontent-%COMP%]:hover + .hidediv[_ngcontent-%COMP%]{display:flex}.reason_title[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content;padding:10px;margin:10px;font-weight:800;color:#2799ee;background-color:#e8f6fb}"]}),e})(),children:[{path:"manager-status",component:I},{path:"ipsign-status",component:St},{path:"handover-status",component:ct},{path:"systemadmin-status",component:Bt},{path:"admin-status",component:L},{path:"finance-status",component:le},{path:"hrResignationPage",component:(()=>{class e{constructor(){this.employeeDetail=[{employeeID:901212,employeeName:"Subash Chandra Bose",employeeEmail:"<EMAIL>",employeeResignationAppliedDate:"02-Jul-20",employeeLastWorkingDate:"02-Sept-20",daysLeft:"90 Days Left"}],this.detailsOfItems=[{id:1,item:"ID Card",type:"checkbox",value:"option",class:"form-check-input",isItrated:!0},{id:2,item:"Medical Card",type:"checkbox",value:"option",class:"form-check-input",isItrated:!0},{id:3,item:"NSR Number",placeholder:"Enter NSR Number",type:"text",value:"",class:"form-control",isItrated:!1}],this.options=[{id:1,option:"Yes"},{id:2,option:"No"}]}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-hr-resignation-status-page"]],decls:15,vars:2,consts:[[1,"main"],[1,"pl-1","pr-1","mat-card","container-fluid","ed-request-home-page",2,"min-height","77vh","width","100%"],[1,"headerObjects"],[1,"header"],[1,"retire-button"],[1,"retire-btn"],["class","employeeDetails",4,"ngFor","ngForOf"],[1,"returnBack"],["class","returnBackItems",4,"ngFor","ngForOf"],[1,"submit-btn"],[1,"employeeDetails"],[1,"employeeinfo"],[1,"employeeHeader"],[1,"employeeDesc"],[2,"display","flex","align-items","center","justify-content","center","gap","8px"],[1,"days-left"],[1,"returnBackItems"],[1,"returnBackItemsHeader"],["class","form-check form-check-inline","style","margin-left: 2%",4,"ngFor","ngForOf"],[1,"form-check","form-check-inline",2,"margin-left","2%"],[3,"type","placeholder","value"],["class","form-check-label","for","inlineCheckbox1",4,"ngIf"],["for","inlineCheckbox1",1,"form-check-label"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"mat-card",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275elementStart"](3,"div",3),d["\u0275\u0275text"](4,"Resignation Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",4),d["\u0275\u0275elementStart"](6,"button",5),d["\u0275\u0275text"](7,"Make As Retire"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](8,Kn,29,6,"div",6),d["\u0275\u0275element"](9,"hr"),d["\u0275\u0275elementStart"](10,"div",7),d["\u0275\u0275template"](11,Un,4,2,"div",8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](12,"hr"),d["\u0275\u0275elementStart"](13,"button",9),d["\u0275\u0275text"](14,"Submit"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("ngForOf",t.employeeDetail),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",t.detailsOfItems))},directives:[u.a,o.NgForOf,o.NgIf],styles:[".main[_ngcontent-%COMP%]   .headerObjects[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:1.5rem 1.5rem 1.5rem 2.5rem}.main[_ngcontent-%COMP%]   .headerObjects[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;display:grid;place-items:center}.main[_ngcontent-%COMP%]   .headerObjects[_ngcontent-%COMP%]   .retire-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:5px;text-transform:capitalize;background:#fff;width:120px;height:35px;padding:2px 10px;justify-content:center;letter-spacing:-.02em;font-weight:600;font-size:14px;line-height:16px;display:flex;align-items:center;color:#45546e}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]{padding:.2rem;display:flex;justify-content:center;align-items:center;gap:10%}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeDetialsHeader[_ngcontent-%COMP%]{font-size:12px;line-height:14px;opacity:.5;flex:none;order:0;flex-grow:0}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeDetailsDesc[_ngcontent-%COMP%], .main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeDetialsHeader[_ngcontent-%COMP%]{font-weight:400;letter-spacing:.02em;text-transform:capitalize;color:#000;display:flex;justify-content:flex-start;align-items:center}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeDetailsDesc[_ngcontent-%COMP%]{font-size:14px;line-height:16px;flex:none;order:1;gap:1%}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeinfo[_ngcontent-%COMP%]{display:block;align-items:center;justify-content:center}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeinfo[_ngcontent-%COMP%]   .employeeHeader[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;text-transform:capitalize;color:#000;opacity:.5;flex:none;order:0;flex-grow:0}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeinfo[_ngcontent-%COMP%]   .employeeDesc[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;flex:none;order:1;flex-grow:0;margin-top:8px}.main[_ngcontent-%COMP%]   .employeeDetails[_ngcontent-%COMP%]   .employeeinfo[_ngcontent-%COMP%]   .days-left[_ngcontent-%COMP%]{font-weight:400;font-size:10px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#cf0001;opacity:.7;width:87px;height:25px;margin-left:0;display:flex;justify-content:center;line-height:23px;border:1px solid #cf0001;border-radius:17px;margin-top:8px}.main[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{opacity:.1;border:.5px solid #000}.main[_ngcontent-%COMP%]   .returnBack[_ngcontent-%COMP%]{padding:2rem}.main[_ngcontent-%COMP%]   .returnBackItems[_ngcontent-%COMP%]{margin-left:5%;display:flex;justify-content:flex-start;align-items:center;padding:1rem;font-weight:400;font-size:14px;line-height:16px;letter-spacing:-.02em;text-transform:capitalize;color:#45546e;flex:none;order:0;flex-grow:0;column-gap:15%}.main[_ngcontent-%COMP%]   .returnBackItems[_ngcontent-%COMP%]   .returnBackItemsHeader[_ngcontent-%COMP%]{width:8%}.main[_ngcontent-%COMP%]   .returnBackItems[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-family:roboto;font-size:14px;line-height:24px;letter-spacing:.02em;text-transform:capitalize;color:#5f6c81}.main[_ngcontent-%COMP%]   .NSR-number[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;padding:1rem;font-weight:400;font-size:14px;line-height:16px;letter-spacing:-.02em;text-transform:capitalize;color:#45546e;flex:none;order:0;flex-grow:0;column-gap:7%}.main[_ngcontent-%COMP%]   .NSR-number[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{font-family:roboto;font-size:14px;line-height:24px;letter-spacing:.02em;text-transform:capitalize;color:#5f6c81}.main[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{margin-left:3rem;border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;width:100px;height:40px;display:flex;line-height:33px;padding:2px 15px;justify-content:center;font-family:Roboto;font-style:normal;font-weight:500;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}"]}),e})()}]}];let Qn=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(Zn)],i.k]}),e})();var eo=n("A5z7"),to=n("bSwM"),no=n("d3UM"),oo=n("xHqg"),io=n("wZkO"),ro=n("FKr1"),ao=n("Xi0T"),lo=n("jW8c");let so=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,Qn,u.d,x.b,io.g,h.b,io.g,to.b,pe.e,no.d,oo.f,se.E,u.d,de.c,x.b,se.p,ce.b,ao.a,a.g,me.h,ro.n,un.e,hn.b,f.b,eo.e,lo.a]]}),e})()}}]);