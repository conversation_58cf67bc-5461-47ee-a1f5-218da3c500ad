(window.webpackJsonp=window.webpackJsonp||[]).push([[864],{sbtN:function(t,e,i){"use strict";i.r(e),i.d(e,"TasksModule",(function(){return U}));var a=i("ofXK"),r=i("tyNb"),s=i("mrSG"),o=i("xG9w"),d=i("wd/R"),c=i("1G5W"),h=i("XNiG"),u=i("xjlO"),n=i.n(u),l=i("fXoL"),f=i("GnQ3"),m=i("LcQX"),D=i("F97M"),S=i("CPr5"),p=i("0IaG"),v=i("ifXX"),y=i("flaP"),b=i("Yyn8"),k=i("tk/3");let C=(()=>{class t{constructor(t){this.http=t,this.getTasksUdrfList=t=>this.http.post("/api/purchaseRequest/getTasksUdrfList",{filterConfig:t}),this.getCostCenters=()=>this.http.post("/api/purchaseRequest/getCostCenter",{}),this.getSubGroups=()=>this.http.post("/api/purchaseRequest/getSubGroup",{}),this.getCurrencyCode=()=>this.http.post("/api/purchaseRequest/getCurrencyCode",{}),this.saveBudget=t=>this.http.post("/api/purchaseRequest/createP2pBudget",{budgetDetails:t}),this.saveBudgetForEdit=(t,e)=>(console.log("passing",e),this.http.post("/api/purchaseRequest/updateP2pBudget",{budgetDetails:t,budget_id:e})),this.getTallyPaymentEntries=(t,e,i)=>this.http.post("/api/purchaseRequest/getTallyPaymentEntries",{start_date:t,end_date:e,isMonthlyTally:i}),this.getTaskDataForDownload=()=>this.http.post("/api/purchaseRequest/getTaskDataForDownload",{})}getBudgetDataById(t){return this.http.post("/api/purchaseRequest/getBudgetDataById",{budget_id:t})}}return t.\u0275fac=function(e){return new(e||t)(l["\u0275\u0275inject"](k.c))},t.\u0275prov=l["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var g=i("a1r6"),x=i("HmYF"),I=i("xi/V"),O=i("Wk3H");const w=[{path:"",component:(()=>{class t{constructor(t,e,i,a,r,u,l,f,m,D,S,p){this.udrfService=t,this.utilityService=e,this.graphService=i,this.accountService=a,this.dialog=r,this.reloadService=u,this.router=l,this.roleService=f,this.randomQuoteService=m,this.taskService=D,this._p2pGeneralService=S,this._excelService=p,this.applicationId=413,this.accountType=[],this.selectedCard=[],this.isCardClicked=!1,this.accountItemDataIndex=0,this.current_year_start=d(),this.current_year_end=d(),this.accountPersona=[],this.accountClassification=[],this.companyType=[],this.employeeSize=[],this.oemType=[],this.partnerType=[],this._onAppApiCalled=new h.b,this._onDestroy=new h.b,this.$onDestroy=new h.b,this.dataTypeArray=[],this.udrfBodyColumns=[{item:"p2p_header_id",header:"#PR",isActive:!0,isVisible:"true",position:1,colSize:1,sortOrder:"I",width:180,type:"text",textClass:"value13Bold cp",filterId:23},{item:"task_name",header:"Task",isActive:!0,isVisible:"true",position:2,colSize:1,sortOrder:"N",width:260,type:"text",textClass:"colorRed value13light cp"},{item:"task_description",header:"Task Description",isActive:!0,isVisible:"true",position:2,colSize:1,sortOrder:"N",width:260,type:"text",textClass:"value13light cp"},{item:"created_on",isActive:!0,header:"Created On",isVisible:"false",type:"text1",position:7,colSize:2,sortOrder:"I",width:180,filterId:10,textClass:"value13light"},{item:"start_date",isActive:!0,header:"Start Date",isVisible:"true",type:"text1",position:7,colSize:2,sortOrder:"I",width:200,filterId:10,textClass:"value13Bold"},{item:"end_date",isActive:!0,header:"End Date",isVisible:"true",type:"text1",position:8,colSize:2,sortOrder:"I",width:200,filterId:10,textClass:"value13Bold"},{item:"action",isActive:!0,header:"Actions",isVisible:"true",type:"action",position:12,colSize:1,sortOrder:"N",width:140,textClass:"text-center"},{item:"created_by",isActive:!0,header:"Created By",isVisible:"true",type:"text1",position:8,colSize:2,sortOrder:"I",width:200,filterId:10,textClass:"value13Bold"},{item:"is_vendor_invoice_task",isActive:!0,header:"Type",isVisible:"true",type:"text1",position:9,colSize:2,sortOrder:"I",width:200,filterId:10,textClass:"value13Bold"},{item:"assigned_to",isActive:!0,header:"Assigned To",isVisible:"true",type:"text1",position:8,colSize:2,sortOrder:"I",width:200,filterId:10,textClass:"value13Bold"},{item:"status_name",isActive:!0,header:"Status",isVisible:"true",type:"text1",position:8,colSize:2,sortOrder:"I",width:200,filterId:10,textClass:"value13Bold"},{item:"vendor_invoice_id",isActive:!0,header:"Invoice ID",isVisible:"true",type:"text",position:8,colSize:2,sortOrder:"I",width:200,filterId:10,textClass:"value13Bold"}],this.categorisedDataTypeArray=[],this.udrfItemStatusColor=[],this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.resolveVisibleDataTypeArray=()=>{for(let t of this.udrfService.udrfUiData.summaryCards){let e;this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(e=o.contains(this.udrfService.udrfData.udrfSummaryCardCodes,t.dataTypeCode),t.isVisible=e,e&&(this.udrfService.udrfUiData.summaryCardsItem=t))}},this.dataTypeCardSelected=()=>{this.udrfService.udrfData.isItemDataLoading=!0,this.udrfService.udrfData.noItemDataFound=!1;let t=this.udrfService.udrfUiData.summaryCardsItem;for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].dataTypeCode!=t.dataTypeCode&&(this.dataTypeArray[e].isActive=!1);t.isActive=!t.isActive,this.isCardClicked=!0,this.accountItemDataIndex=0,this.udrfService.udrfBodyData=[],this.getTaskist()},this.getTaskist=()=>Object(s.c)(this,void 0,void 0,(function*(){let t=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),e=o.where(this.dataTypeArray,{isActive:!0}),i=[],a=!1;if(e.length>0&&(i=o.where(e,{cardType:"status"}),console.log("summary",i),i.length>0))if(t.length>0){for(let i of t)(i.filterName==e[0].cardFilter||t.filterName==e[0].cardFilter)&&(i.multiOptionSelectSearchValues=[e[0].dataTypeCode],i.multiOptionSelectSearchValuesWithId=[e[0].dataTypeCode],a=!0);if(0==a){let i=JSON.parse(JSON.stringify(o.where(this.udrfService.udrfData.filterTypeArray,{filterName:e[0].cardFilter})));t.push(i[0]);for(let r of t)(r.filterName==e[0].cardFilter||t.filterName==e[0].cardFilter)&&(r.multiOptionSelectSearchValues=[e[0].dataTypeCode],r.multiOptionSelectSearchValuesWithId=[e[0].dataTypeCode],a=!0)}}else console.log(this.udrfService.udrfData.filterTypeArray),t=JSON.parse(JSON.stringify(o.where(this.udrfService.udrfData.filterTypeArray,{filterName:e[0].cardFilter}))),t[0].multiOptionSelectSearchValues=[e[0].dataTypeCode],t[0].multiOptionSelectSearchValuesWithId=[e[0].dataTypeCode];console.log("Fileter Array",t);let r={startIndex:this.accountItemDataIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:t,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails};console.log("filterconfig",r),this.selectedCard=this.udrfService.udrfData.udrfSummaryCardCodes,this.taskService.getTasksUdrfList(r).pipe(Object(c.a)(this._onAppApiCalled)).subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.messData&&t.messData.length>0){this.tallyCheck=t.tallyCheck,this.udrfService.udrfUiData.totalItemDataCount=t.total;for(let i of t.messData)for(let[t,a]of Object.entries(i)){if("object"==typeof a&&null!=a&&null!=a)try{"date"==a.type&&(i[t]=d(a.date).format("DD - MMM - YYYY"))}catch(e){console.log(e)}if("state"==t&&null!=a&&"null"!=a){let e=n.a.getStateById(i[t]);i[t]=e.name}}this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(t.messData)}else this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0;this.udrfService.udrfData.isItemDataLoading=!1})),t=>{this.showErrorMessage(t)})})),this.getTaskStatusMasterData=()=>new Promise((t,e)=>{this._p2pGeneralService.getTaskHeaderStatus().pipe(Object(c.a)(this.$onDestroy)).subscribe(i=>{i.err?e(i):t(i.data)},t=>{console.log(t)})}),this.downloadItemDataReport=()=>{this.udrfService.udrfUiData.isReportDownloading=!0,this.taskService.getTaskDataForDownload().pipe(Object(c.a)(this._onAppApiCalled)).subscribe(t=>{if(0==t.err){let e=d().format("DD-MMM-YYYY");this._excelService.exportAsExcelFile(t.data,"Task Report as on "+e),this.utilityService.showToastMessage("Report downloaded successfully"),this.udrfService.udrfUiData.isReportDownloading=!1}else this.udrfService.udrfUiData.isReportDownloading=!1,this._p2pGeneralService.showMessage("Failed to download report, Kindly contact KEBS team !")},t=>console.log(t))},this.initStatusData=t=>{let e=[],i=[];t.forEach((t,a)=>{e.push({status:t.name,color:t.color}),i.push({dataType:t.name,dataTypeValue:"0",isActive:!1,isVisible:!0,cardType:"status",statusColor:t.color})}),this.dataTypeArray=i,this.udrfItemStatusColor=e,this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:o.pluck(t,"code"),categoryCards:[]}]},this.current_year_start=d().startOf("year"),this.current_year_end=d().endOf("year")}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){this.udrfItemStatusColor=[],this.categorisedDataTypeArray=[];let t=yield this.getTaskStatusMasterData();this.initStatusData(t);let e=[{checkboxId:"BDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"BDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("week"),d(d().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().endOf("week"),d(d().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().endOf("month"),d(d().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(d().add(1,"month").startOf("month"),d(d().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().add(1,"month").endOf("month"),d(d().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().add(2,"month").endOf("month"),d(d().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRD6",checkboxName:"Previous Month",checkboxStartValue:this.utilityService.getFormattedDate(d().subtract(1,"month").startOf("month"),d(d().subtract(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().subtract(1,"month").endOf("month"),d(d().subtract(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1}],i=[{checkboxId:"BDERD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"BDCED2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("week"),d(d().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().endOf("week"),d(d().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCED3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().endOf("month"),d(d().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDED4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(d().add(1,"month").startOf("month"),d(d().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().add(1,"month").endOf("month"),d(d().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDED5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().add(2,"month").endOf("month"),d(d().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDED6",checkboxName:"Previous Month",checkboxStartValue:this.utilityService.getFormattedDate(d().subtract(1,"month").startOf("month"),d(d().subtract(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().subtract(1,"month").endOf("month"),d(d().subtract(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1}],a=[{checkboxId:"BDCRED",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"BDCRED2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("week"),d(d().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().endOf("week"),d(d().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRED3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().endOf("month"),d(d().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRED4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(d().add(1,"month").startOf("month"),d(d().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().add(1,"month").endOf("month"),d(d().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRED5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(d().startOf("month"),d(d().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().add(2,"month").endOf("month"),d(d().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"BDCRED6",checkboxName:"Previous Month",checkboxStartValue:this.utilityService.getFormattedDate(d().subtract(1,"month").startOf("month"),d(d().subtract(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(d().subtract(1,"month").endOf("month"),d(d().subtract(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(5,"date",i),this.udrfService.udrfFunctions.constructCustomRangeData(4,"date",e),this.udrfService.udrfFunctions.constructCustomRangeData(6,"date",a),this.reloadService.getNotification().subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){console.log(t),"update required"==t&&this.initReport()}))),this.udrfService.udrfBodyData=[],this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.bookmarkId="id",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!0,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!1,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.showGroupByButton=!0,this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.itemHasOpenModal=!0,this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this.udrfService.udrfUiData.itemDataScrollDown=this.onOpportunityListScrollDown.bind(this),this.udrfService.udrfFunctions.resolveVisibleColumnConfigItems(),this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this.udrfService.udrfUiData.selectedCard=this.selectedCard,this.udrfService.udrfUiData.variant=0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!1,this.udrfService.udrfUiData.itemHasHierarchyView=!1,this.udrfService.udrfUiData.itemHasBookmark=!1,this.udrfService.udrfUiData.quickCTAInput={},this.udrfService.udrfUiData.commentsInput={},this.udrfService.udrfUiData.commentsContext={},this.udrfService.udrfUiData.onColumnClickItem="",this.udrfService.udrfUiData.countForOnlyThisReport=!1,this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this.udrfService.getNotifyReleasesUDRF(),this.udrfService.udrfUiData.itemHasStarRating=!0,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!0,this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.udrfUiData.downloadItemDataReport=this.downloadItemDataReport.bind(this),this.udrfService.udrfUiData.showActualCurrencyForCurrencyComponent=!0}))}initReport(){return Object(s.c)(this,void 0,void 0,(function*(){this._onAppApiCalled.next(),this.accountItemDataIndex=0,this.isCardClicked=!1,this.udrfService.udrfBodyData=[];for(let t=0;t<this.dataTypeArray.length;t++)this.dataTypeArray[t].isActive=!1;this.udrfService.udrfUiData.resolveColumnConfig(),this.getTaskist()}))}showErrorMessage(t){this.utilityService.showErrorMessage("Error:Unable to fetch Data","KEBS")}onOpportunityListScrollDown(){this.udrfService.udrfData.noItemDataFound||(this.accountItemDataIndex+=this.udrfService.udrfData.defaultRecordsPerFetch,console.log("Next",this.accountItemDataIndex),this.udrfService.udrfData.isItemDataLoading=!0,this.getTaskist())}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}}return t.\u0275fac=function(e){return new(e||t)(l["\u0275\u0275directiveInject"](f.a),l["\u0275\u0275directiveInject"](m.a),l["\u0275\u0275directiveInject"](D.a),l["\u0275\u0275directiveInject"](S.a),l["\u0275\u0275directiveInject"](p.b),l["\u0275\u0275directiveInject"](v.a),l["\u0275\u0275directiveInject"](r.g),l["\u0275\u0275directiveInject"](y.a),l["\u0275\u0275directiveInject"](b.a),l["\u0275\u0275directiveInject"](C),l["\u0275\u0275directiveInject"](g.a),l["\u0275\u0275directiveInject"](x.a))},t.\u0275cmp=l["\u0275\u0275defineComponent"]({type:t,selectors:[["app-tasks-report-landing-page"]],decls:3,vars:0,consts:[[1,"container-fluid","approval-report-landing-page","pl-0","pr-0"]],template:function(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275element"](1,"udrf-header"),l["\u0275\u0275element"](2,"udrf-body"),l["\u0275\u0275elementEnd"]())},directives:[I.a,O.a],styles:[""]}),t})()}];let T=(()=>{class t{}return t.\u0275mod=l["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[r.k.forChild(w)],r.k]}),t})();var V=i("Xi0T");let U=(()=>{class t{}return t.\u0275mod=l["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.CommonModule,T,V.a]]}),t})()}}]);