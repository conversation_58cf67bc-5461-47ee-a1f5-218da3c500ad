(window.webpackJsonp=window.webpackJsonp||[]).push([[682,634,765,821,822,858,983,987,990,991],{"0Ymc":function(e,t,r){"use strict";r.r(t),r.d(t,"TimeSheetTestcaseReportooltipFormOptions",(function(){return V})),r.d(t,"TimesheetTestcaseReportModule",(function(){return F}));var i=r("ofXK"),s=r("tyNb"),a=r("mrSG"),n=r("1G5W"),o=r("XNiG"),c=r("1yaQ"),l=r("FKr1"),d=r("fXoL"),u=r("0IaG"),p=r("JqCM"),h=r("LcQX"),f=r("XXEo"),m=r("GnQ3"),v=r("HmYF"),y=r("BVzC"),g=r("tk/3");let S=(()=>{class e{constructor(e){this.$http=e}getTimesheetTestcaseReportData(e){return this.$http.post("/api/tsPrimary/getTimesheetTestCaseReportItemApi",{filterConfig:e})}getTimesheetTestcaseReportCountData(e){return this.$http.post("/api/tsPrimary/getTimesheetTestCaseReportCountOrDownloadApi",{filterConfig:e})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275inject"](g.c))},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var D=r("JLuW"),x=r("xi/V"),I=r("Wk3H");const C={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"DD - MM - YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},b=[{path:"",component:(()=>{class e{constructor(e,t,r,i,s,a,n,c,l){this.dialog=e,this.spinner=t,this.utilityService=r,this.authService=i,this.udrfService=s,this.excelService=a,this.errorService=n,this.timesheetTestCaseReportService=c,this.sharedLazyLoadedComponentsService=l,this.applicationId=219,this.currentUserOId="",this.TimesheetTestCaseitemDataCurrentIndex=0,this._onDestroy=new o.b,this._onAppApiCalled=new o.b,this.startIndex=0,this.udrfBodyColums=[{item:"id",header:"Id",isVisible:"true",type:"text",position:1,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:1,sortOrder:"N",width:100},{item:"employee_name",header:"Name",isVisible:"true",type:"text",position:2,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:200},{item:"is_testcase",header:"Testcase",isVisible:"true",type:"text",position:3,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:100},{item:"testcase_description",header:"Testcase Description",isVisible:"true",type:"text",position:8,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:8,sortOrder:"N",width:200},{item:"object_value",header:"Cost Centre",isVisible:"true",type:"text",position:4,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:4,sortOrder:"N",width:100},{item:"object_description",header:"Cost Centre Description",isVisible:"true",type:"text",position:6,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:6,sortOrder:"N",width:200},{item:"location",header:"Location",isVisible:"true",type:"text",position:7,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:7,sortOrder:"N",width:200},{item:"day_type",header:"Day Type",isVisible:"true",type:"text",position:8,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:8,sortOrder:"N",width:100},{item:"hours",header:"Hours",isVisible:"true",type:"text",position:7,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:7,sortOrder:"N",width:100},{item:"status_code",header:"Status Code",isVisible:"true",type:"text",position:7,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:7,sortOrder:"N",width:100},{item:"taskId",header:"Task Id Inline Edit",isVisible:"true",type:"text",position:8,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:7,sortOrder:"N",width:100}],this.udrfItemStatusColor=[{status:"Draft",color:"#c7c4c4"},{status:"Approved",color:"#009432"},{status:"Rejected",color:"#cf0001"},{status:"Submitted",color:"#ff7200"},{status:"Unsubmitted",color:"#c7c4c4"},{status:"Recalled",color:"#c7c4c4"}],this.durationRanges=[]}ngOnInit(){this.udrfService.udrfBodyData=[],this.currentUserOId=this.authService.getProfile().profile.oid,this.TimesheetTestCaseitemDataCurrentIndex=0,this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.showReportDownloadButton=!0,this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColums,this.udrfService.udrfUiData.resolveVisibleSummaryCards=()=>{},this.udrfService.udrfUiData.summaryCardsSelected=()=>{},this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.showSettingsModalButton=!1,this.udrfService.udrfUiData.itemDataScrollDown=this.timesheetTestCaseReportScroll.bind(this),this.udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.variant=0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!0,this.udrfService.udrfUiData.quickCTAInput={},this.udrfService.udrfUiData.commentsInput={},this.udrfService.udrfUiData.commentsContext={},this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.isHeaderSort=!0,this.udrfService.udrfUiData.downloadItemDataReport=this.downloadReport.bind(this),this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this.udrfService.getNotifyReleasesUDRF()}initReport(){this._onAppApiCalled.next(),this.TimesheetTestCaseitemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.udrfService.udrfUiData.resolveColumnConfig(),this.initTimesheetTestCaseReportList()}initTimesheetTestCaseReportList(){this.spinner.show();let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));this.timesheetTestCaseReportService.getTimesheetTestcaseReportData({startIndex:this.TimesheetTestCaseitemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(n.a)(this._onDestroy)).pipe(Object(n.a)(this._onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e.data&&e.data.length>0?this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e.data):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0),this.udrfService.udrfData.isItemDataLoading=!1,this.getCountData()})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Report ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getCountData(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));this.timesheetTestCaseReportService.getTimesheetTestcaseReportCountData({startIndex:this.TimesheetTestCaseitemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(n.a)(this._onDestroy)).pipe(Object(n.a)(this._onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e.data&&(this.udrfService.udrfUiData.totalItemDataCount=e.data)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Report ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}timesheetTestCaseReportScroll(){return Object(a.c)(this,void 0,void 0,(function*(){this.udrfService.udrfData.noItemDataFound||this.udrfService.udrfData.isItemDataLoading||(this.TimesheetTestCaseitemDataCurrentIndex+=this.udrfService.udrfData.defaultRecordsPerFetch,this.udrfService.udrfData.isItemDataLoading=!0,yield this.initTimesheetTestCaseReportList())}))}callInlineEditApi(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}downloadReport(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));this.timesheetTestCaseReportService.getTimesheetTestcaseReportCountData({startIndex:this.TimesheetTestCaseitemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(n.a)(this._onDestroy)).pipe(Object(n.a)(this._onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e.data1?this.excelService.exportAsExcelFile(e.data1,"Timesheet-Testcase-Report"):this.utilityService.showMessage("Report Download Failed","Dismiss",3e3)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Report ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](u.b),d["\u0275\u0275directiveInject"](p.c),d["\u0275\u0275directiveInject"](h.a),d["\u0275\u0275directiveInject"](f.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](v.a),d["\u0275\u0275directiveInject"](y.a),d["\u0275\u0275directiveInject"](S),d["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-testcase-report-landing-page"]],features:[d["\u0275\u0275ProvidersFeature"]([{provide:l.c,useClass:c.c,deps:[l.f,c.a]},{provide:l.e,useValue:C}])],decls:3,vars:0,consts:[[1,"container-fluid","timesheet-testcase-report-styles","pl-0","pr-0"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275element"](1,"udrf-header"),d["\u0275\u0275element"](2,"udrf-body"),d["\u0275\u0275elementEnd"]())},directives:[x.a,I.a],styles:[".timesheet-testcase-report-styles[_ngcontent-%COMP%]   .mediumSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:14px}"]}),e})()}];let w=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.k.forChild(b)],s.k]}),e})();var A=r("bTqV"),T=r("Qu3c"),O=r("NFeN"),_=r("kmnG"),R=r("qFsG"),E=r("iadO"),N=r("/1cH"),k=r("3Pt+"),P=r("dlKe"),M=r("bSwM"),L=r("/QRN"),U=r("Xi0T"),B=r("Xa2L"),j=r("mEBv"),q=r("7EHt");r("9op9");const V={width:"auto",height:"auto",showDelay:500,hideDelay:300,trigger:"click"};let F=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,w,A.b,T.b,O.b,_.e,R.c,E.h,N.c,k.p,k.E,P.b,M.b,L.a,U.a,B.b,j.b,q.b,j.b.forRoot(V)]]}),e})()},H44p:function(e,t,r){"use strict";r.d(t,"a",(function(){return v}));var i=r("xG9w"),s=r("fXoL"),a=r("flaP"),n=r("ofXK"),o=r("Qu3c"),c=r("NFeN");function l(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",9),s["\u0275\u0275elementStart"](1,"div",10),s["\u0275\u0275elementStart"](2,"div"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div"),s["\u0275\u0275elementStart"](5,"p",11),s["\u0275\u0275text"](6),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"p",12),s["\u0275\u0275text"](8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](e.label),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function d(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",13),s["\u0275\u0275elementStart"](1,"span"),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",14),s["\u0275\u0275elementStart"](1,"span",15),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",16),s["\u0275\u0275elementStart"](1,"span",15),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",17),s["\u0275\u0275elementStart"](1,"span",18),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function f(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-icon",19),s["\u0275\u0275text"](1,"loop"),s["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",1),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().change()})),s["\u0275\u0275template"](1,l,9,4,"div",2),s["\u0275\u0275template"](2,d,3,2,"div",3),s["\u0275\u0275template"](3,u,3,3,"div",4),s["\u0275\u0275template"](4,p,3,3,"div",5),s["\u0275\u0275template"](5,h,3,3,"div",6),s["\u0275\u0275elementStart"](6,"div",7),s["\u0275\u0275template"](7,f,2,0,"mat-icon",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","big"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","small"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","medium"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","large"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","overview"==e.type),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.toDisplay)}}let v=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=i.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=i.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||i.contains(["big","small"],this.type)?0==this.isConvertValue&&i.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&s["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&s["\u0275\u0275property"]("ngIf",t.currency)},directives:[n.NgIf,o.a,c.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},NJ67:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,r){"use strict";r.d(t,"a",(function(){return u}));var i=r("mrSG"),s=r("XNiG"),a=r("xG9w"),n=r("fXoL"),o=r("tk/3"),c=r("LcQX"),l=r("XXEo"),d=r("flaP");let u=(()=>{class e{constructor(e,t,r,i){this.http=e,this.UtilityService=t,this.loginService=r,this.roleService=i,this.msg=new s.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,r,i,s,a,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:s,limit:a,filterConfig:n,orgIds:o})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,r,i,s,a,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:s,limit:a,filterConfig:n,orgIds:o})}getRequestsForAwaitingApproval(e,t,r,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:r,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,r,i){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:r,filterConfig:i,orgIds:s})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{r(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,r,s,n,o,c){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=o&&o.length>1&&(yield this.getManpowerCostByOId(o,r,n,2))||(yield this.getManpowerCostBasedOnPosition(e,t,r,n,c));let l=yield this.getNonManpowerCost(t,r,s,n,2),d=yield this.getAllocatedCost(),u=0;u=(i?i.cost:0)+l.length>0?a.reduce(a.pluck(l,"cost"),(e,t)=>e+t,0):0;let p=d.length>0?a.reduce(a.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:l,allocatedCost:d,allocatedCostValue:u*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,r,i,s){return new Promise((a,n)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:r,unit:i,position:s}).subscribe(e=>a(e),e=>(console.log(e),n(e)))})}getNonManpowerCost(e,t,r,i,s){return new Promise((a,n)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:r,unit:i,currency_id:s}).subscribe(e=>a(e),e=>(console.log(e),n(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,r,i){return new Promise((s,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:r,currency_id:i}).subscribe(e=>s(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275inject"](o.c),n["\u0275\u0275inject"](c.a),n["\u0275\u0275inject"](l.a),n["\u0275\u0275inject"](d.a))},e.\u0275prov=n["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,r){"use strict";r.d(t,"a",(function(){return c}));var i=r("mrSG"),s=r("xG9w"),a=r("fXoL"),n=r("tk/3"),o=r("BVzC");let c=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>r(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>r(e))})}getApproversHierarchy(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>r(e))})}createWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>r(e))})}getWorkflowDetails(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let r=0;r<e.length;r++){let i=[],a=s.keys(t["cc"+r]);for(let s=0;s<a.length;s++)for(let n=0;n<t["cc"+r][a[s]].length;n++){let o={name:t["cc"+r][a[s]][n].DELEGATE_NAME,oid:t["cc"+r][a[s]][n].DELEGATE_OID,level:s+1,designation:t["cc"+r][a[s]][n].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+r][a[s]][n].IS_DELEGATED,role:t["cc"+r][a[s]][n].DELEGATE_ROLE_NAME};if(1==t["cc"+r][a[s]][n].IS_DELEGATED&&(o.delegated_by={name:t["cc"+r][a[s]][n].APPROVER_NAME,oid:t["cc"+r][a[s]][n].APPROVER_OID,level:s+1,designation:t["cc"+r][a[s]][n].APPROVER_DESIGNATION_NAME}),i.push(o),r==e.length-1&&s==a.length-1&&n==t["cc"+r][a[s]].length-1)return i}}}))}storeComments(e,t,r){return new Promise((i,s)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:r}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),s(e)))})}updateWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),r(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let r=[],i=s.keys(e["cc"+t]);for(let s=0;s<i.length;s++)for(let a=0;a<e["cc"+t][i[s]].length;a++){let n={name:e["cc"+t][i[s]][a].DELEGATE_NAME,oid:e["cc"+t][i[s]][a].DELEGATE_OID,level:e["cc"+t][i[s]][a].APPROVAL_ORDER,designation:e["cc"+t][i[s]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[s]][a].IS_DELEGATED};if(1==e["cc"+t][i[s]][a].IS_DELEGATED&&(n.delegated_by={name:e["cc"+t][i[s]][a].APPROVER_NAME,oid:e["cc"+t][i[s]][a].APPROVER_OID,level:e["cc"+t][i[s]][a].APPROVAL_ORDER,designation:e["cc"+t][i[s]][a].APPROVER_DESIGNATION_NAME}),r.push(n),s==i.length-1&&a==e["cc"+t][i[s]].length-1)return r}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](n.c),a["\u0275\u0275inject"](o.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);