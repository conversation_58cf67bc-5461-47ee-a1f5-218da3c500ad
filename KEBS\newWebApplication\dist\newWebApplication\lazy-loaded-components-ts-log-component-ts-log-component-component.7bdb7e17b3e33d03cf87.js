(window.webpackJsonp=window.webpackJsonp||[]).push([[770],{"M2Y+":function(e,t,n){"use strict";n.r(t),n.d(t,"TsLogComponentComponent",(function(){return x}));var o=n("mrSG"),l=n("XNiG"),i=n("1G5W"),a=n("wd/R"),r=n("fXoL"),s=n("pcqE"),d=n("LcQX"),c=n("BVzC"),m=n("0IaG"),p=n("NFeN"),g=n("Qu3c"),f=n("ofXK");function v(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",16),r["\u0275\u0275elementStart"](1,"div",17),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",18),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",18),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",17),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.associateId," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.associateName),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.associateName," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.fileName),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.fileName," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.timestamp," ")}}function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",12),r["\u0275\u0275elementStart"](1,"div",13),r["\u0275\u0275elementStart"](2,"div",14),r["\u0275\u0275text"](3,"Employee Id"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",14),r["\u0275\u0275text"](5,"Employee Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",14),r["\u0275\u0275text"](7,"File Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",14),r["\u0275\u0275text"](9,"Date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](10,v,9,6,"div",15),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](10),r["\u0275\u0275property"]("ngForOf",e.uploadLogs)}}function u(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",19),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",20),r["\u0275\u0275text"](3," No Logs Found! "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",21),r["\u0275\u0275element"](5,"img",22),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}let x=(()=>{class e{constructor(e,t,n,o){this._leaveUploadService=e,this.utilityService=t,this.errorService=n,this.dialogRef=o,this._onAppApiCalled=new l.b,this._onDestroy=new l.b,this.uploadLogs=[]}ngOnInit(){this._onAppApiCalled.next(),this.getLeaveUploadLogs()}closeForm(){this.dialogRef.close()}getLeaveUploadLogs(){this._leaveUploadService.getLeaveUploadLogs().pipe(Object(i.a)(this._onDestroy)).pipe(Object(i.a)(this._onAppApiCalled)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){if(e.data&&e.data.length>0&&"S"==e.messType){this.uploadLogs=e.data;for(let e of this.uploadLogs)e.timestamp=a(e.timestamp).utc().format("DD - MMM - YYYY HH : mm : ss")}else this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Getting Logs",e&&e.params?e.params:e&&e.error?e.error.params:{})})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](d.a),r["\u0275\u0275directiveInject"](c.a),r["\u0275\u0275directiveInject"](m.h))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-log-component"]],decls:15,vars:2,consts:[[1,"timesheet-upload-logs","container-fluid","pt-3"],[1,"col-12"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["matTooltip","Close",1,"close-Icon",2,"cursor","pointer","color","#4d4d4b",3,"click"],[1,"container-fluid","my-team-styles","pl-0","pr-0"],["class","col-12 pt-3",4,"ngIf"],["class","pt-4",4,"ngIf"],[1,"col-12","pt-3"],[1,"row","headerSection"],[1,"col-3"],["class","row pt-3",4,"ngFor","ngForOf"],[1,"row","pt-3"],[1,"col-3","pt-1","pb-1","item-text"],[1,"col-3","pt-1","pb-1","item-text",3,"matTooltip"],[1,"pt-4"],[1,"d-flex","justify-content-center","align-items-center","mt-0","mb-0","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","220","width","250",1,"mt-3"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275elementStart"](4,"div",4),r["\u0275\u0275elementStart"](5,"mat-icon",5),r["\u0275\u0275text"](6,"history"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"span",6),r["\u0275\u0275text"](8,"Leave Upload Logs"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",7),r["\u0275\u0275elementStart"](10,"mat-icon",8),r["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),r["\u0275\u0275text"](11,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"div",9),r["\u0275\u0275template"](13,h,11,1,"div",10),r["\u0275\u0275template"](14,u,6,0,"div",11),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](13),r["\u0275\u0275property"]("ngIf",t.uploadLogs.length>0),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==t.uploadLogs.length))},directives:[p.a,g.a,f.NgIf,f.NgForOf],styles:[".timesheet-upload-logs[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{background:#ee4961;border-radius:4px 0 0 4px;font-family:Roboto;font-style:normal;font-weight:700;font-size:12px;line-height:16px;letter-spacing:-.02em;text-transform:capitalize;color:#fff}.timesheet-upload-logs[_ngcontent-%COMP%]     .mat-button-toggle-checked{background-color:#ef4a61!important}.timesheet-upload-logs[_ngcontent-%COMP%]   .headerSection[_ngcontent-%COMP%]{font-size:15px;color:#26303e}.timesheet-upload-logs[_ngcontent-%COMP%]   .headerSection[_ngcontent-%COMP%], .timesheet-upload-logs[_ngcontent-%COMP%]   .item-text[_ngcontent-%COMP%]{font-family:Roboto;overflow:hidden;text-overflow:ellipsis;font-weight:500;white-space:nowrap}.timesheet-upload-logs[_ngcontent-%COMP%]   .item-text[_ngcontent-%COMP%]{font-size:13px;color:#4c4d4e}.timesheet-upload-logs[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#ef4a61;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})()}}]);