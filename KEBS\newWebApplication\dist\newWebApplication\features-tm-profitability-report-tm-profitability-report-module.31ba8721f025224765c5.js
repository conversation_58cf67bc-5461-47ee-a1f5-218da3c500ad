(window.webpackJsonp=window.webpackJsonp||[]).push([[690,634,765,821,822,858,983,987,990,991],{BH2e:function(e,t,r){"use strict";r.r(t),r.d(t,"TmProfitabilityReportModule",(function(){return w}));var i=r("ofXK"),a=r("tyNb"),s=r("mrSG"),n=r("xG9w"),o=r("wd/R"),d=r("1G5W"),c=r("1yaQ"),l=r("FKr1"),u=r("XNiG"),p=r("fXoL"),h=r("tk/3");let y=(()=>{class e{constructor(e){this.http=e}getTmprofitabiltyReport(e){return this.http.post("/api/project/getmProfitabiltyReportList",{filterConfig:e})}getTmprofitabiltyReportSummaryCard(e){return this.http.post("/api/project/getmProfitabiltyReportSummaryCard",{filterConfig:e})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275inject"](h.c))},e.\u0275prov=p["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var m=r("JqCM"),f=r("HmYF"),g=r("LcQX"),v=r("GnQ3"),S=r("XXEo"),C=r("0IaG"),D=r("xi/V"),x=r("Wk3H");const _={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"DD - MM - YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},b=[{path:"",component:(()=>{class e{constructor(e,t,r,i,a,s,n){this.tmService=e,this.spinner=t,this.excelService=r,this.utilityService=i,this.udrfService=a,this.authService=s,this.dialog=n,this.currentUserOId="",this.applicationId=109,this.filterConfig={},this.current_fy_year_start=o(),this.current_fy_year_end=o(),this.user_duration_start=o(),this.mainApiDateRangeEnd=o(),this.current_year_start=o(),this.current_year_end=o(),this.start_year=o("2016-01-01").format(),this.end_year=o("2100-12-12").format(),this.itemDataCurrentIndex=0,this.dataTypeArray=[{dataType:"Planned Revenue",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"INR",isActive:!1,dataTypeCode:"PRINR",isVisible:!0,category:"INR Cards"},{dataType:"Actual Revenue",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"INR",isActive:!1,dataTypeCode:"ARINR",isVisible:!0,category:"INR Cards"},{dataType:"Planned Cost",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"INR",isActive:!1,dataTypeCode:"PCINR",isVisible:!0,category:"INR Cards"},{dataType:"Actual Cost",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"INR",isActive:!1,dataTypeCode:"ACINR",isVisible:!0,category:"INR Cards"},{dataType:"Planned Gross Margin",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"INR",isActive:!1,dataTypeCode:"PGMINR",isVisible:!0,category:"INR Cards"},{dataType:"Actual Gross Margin",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"INR",isActive:!1,dataTypeCode:"AGMINR",isVisible:!0,category:"INR Cards"},{dataType:"Planned Revenue",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"USD",isActive:!1,dataTypeCode:"PRUSD",isVisible:!1,category:"USD Cards"},{dataType:"Actual Revenue",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"USD",isActive:!1,dataTypeCode:"ARUSD",isVisible:!1,category:"USD Cards"},{dataType:"Planned Cost",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"USD",isActive:!1,dataTypeCode:"PCUSD",isVisible:!1,category:"USD Cards"},{dataType:"Actual Cost",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"USD",isActive:!1,dataTypeCode:"ACUSD",isVisible:!1,category:"USD Cards"},{dataType:"Planned Gross Margin",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"USD",isActive:!1,dataTypeCode:"PGMUSD",isVisible:!1,category:"USD Cards"},{dataType:"Actual Gross Margin",dataTypeValue:"0",inr:"0 Cr",usd:"0 M",cardType:"USD",isActive:!1,dataTypeCode:"AGMUSD",isVisible:!1,category:"USD Cards"}],this.categorisedDataTypeArray=[{categoryType:"INR Cards",categoryCardCodes:["PRINR","ARINR","PCINR","ACINR","PGMINR","AGMINR"],categoryCards:[]},{categoryType:"USD Cards",categoryCardCodes:["PRUSD","ARUSD","PCUSD","ACUSD","PGMUSD","AGMUSD"],categoryCards:[]}],this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.udrfBodyColumns=[{item:"region",header:"Region",isVisible:"true",type:"text",position:1,isActive:!0,colSize:"2",textClass:"value13Bold",sortOrder:"N",width:180},{item:"associate_name",header:"Associate Name",isVisible:"true",type:"text",position:2,isActive:!0,colSize:"2",textClass:"value13Bold",sortOrder:"N",width:180},{item:"practice_name",header:"Practice Name",mis_level_1:"",isVisible:"true",type:"text",position:3,isActive:!0,colSize:"2",textClass:"value13Bold",sortOrder:"N",width:180},{item:"planned_revenue_INR_Crs",header:"Planned Revenue (\u20b9)",isVisible:"true",type:"number",itemTooltip:"planned_revenue_INR",position:4,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_revenue_INR_Crs",header:"Actual Revenue (\u20b9)",itemTooltip:"actual_revenue_INR",usd_millions:"",isVisible:"true",type:"number",position:5,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"planned_cost_INR_Crs",header:"Planned Cost (\u20b9)",isVisible:"true",type:"number",itemTooltip:"planned_cost_INR",position:6,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_cost_INR_Crs",header:"Actual Cost (\u20b9)",itemTooltip:"actual_cost_INR",usd_millions:"",isVisible:"true",type:"number",position:7,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"planned_gross_margin_INR_Crs",header:"Planned GM (\u20b9)",isVisible:"true",type:"number",itemTooltip:"planned_gross_margin_INR",position:8,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_gross_margin_INR_Crs",header:"Actual GM (\u20b9)",itemTooltip:"actual_gross_margin_INR",usd_millions:"",isVisible:"true",type:"number",position:9,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"planned_gross_margin_INR_percentage",header:"Planned GM% (\u20b9) ",isVisible:"true",type:"number",itemTooltip:"planned_gross_margin_INR_percentage",position:10,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_gross_margin_INR_percentage",header:"Actual GM% (\u20b9)",itemTooltip:"actual_gross_margin_INR_percentage",usd_millions:"",isVisible:"true",type:"number",position:11,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"planned_revenue_USD_M",header:"Planned Revenue ($)",isVisible:"false",type:"number",itemTooltip:"planned_revenue_USD",position:12,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_revenue_USD_M",header:"Actual Revenue ($)",itemTooltip:"actual_revenue_USD",usd_millions:"",isVisible:"false",type:"number",position:13,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"planned_cost_USD_M",header:"Planned Cost ($)",isVisible:"false",type:"number",itemTooltip:"planned_cost_USD",position:14,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_cost_USD_M",header:"Actual Cost ($)",itemTooltip:"actual_cost_USD",usd_millions:"",isVisible:"false",type:"number",position:15,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"planned_gross_margin_USD_M",header:"Planned GM ($)",isVisible:"false",type:"number",itemTooltip:"planned_gross_margin_USD",position:16,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_gross_margin_USD_M",header:"Actual GM ($)",itemTooltip:"actual_gross_margin_USD",usd_millions:"",isVisible:"false",type:"number",position:17,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"planned_gross_margin_USD_percentage",header:"Planned GM% ($) ",isVisible:"false",type:"number",itemTooltip:"plannd_gross_margin_USD_percentage",position:18,isActive:!0,colSize:"1",textClass:"text-right value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"actual_gross_margin_USD_percentage",header:"Actual GM% ($)",itemTooltip:"actual_gross_margin_USD_percentage",usd_millions:"",isVisible:"false",type:"number",position:19,isActive:!0,colSize:"1",textClass:"text-right pr-2 value13Bold",headerTextClass:"text-right",sortOrder:"N",width:180},{item:"item_name",header:"Item Name",mis_level_1:"",isVisible:"false",type:"text",position:20,isActive:!0,colSize:"2",textClass:"value13Bold",sortOrder:"N",width:180}],this._onDestroy=new u.b,this._onAppApiCalled=new u.b,this.current_year_start=o().startOf("year"),this.current_year_end=o().endOf("year")}ngOnInit(){this.itemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.currentUserOId=this.authService.getProfile().profile.oid;let e=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD1",checkboxName:"All",checkboxStartValue:this.start_year,checkboxEndValue:this.end_year,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("month"),o(o().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().endOf("month"),o(o().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"Previous Month",checkboxStartValue:o().startOf("month").subtract(1,"month"),checkboxEndValue:o().endOf("month").subtract(1,"month"),isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD4",checkboxName:"Last 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(o().subtract(3,"month"),o(o().subtract(3,"month")).date,15,0,0,0),checkboxEndValue:o(),isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(6,"posting_date",e),this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="records",this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!0,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!1,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.udrfItemStatusColor=[],this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this.udrfService.udrfUiData.summaryCardsSelected=()=>{},this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.udrfUiData.itemDataScrollDown=this.itemDataScrollDown.bind(this),this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this.udrfService.udrfUiData.selectedCard=[],this.udrfService.udrfUiData.variant=0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!1,this.udrfService.udrfUiData.quickCTAInput={},this.udrfService.udrfUiData.commentsInput={},this.udrfService.udrfUiData.commentsContext={},this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.isHeaderSort=!0,this.udrfService.udrfUiData.isMultipleView=!0,this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this.udrfService.getNotifyReleasesUDRF()}initReport(){this._onAppApiCalled.next(),this.itemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.initList(),this.initSummaryCard()}itemDataScrollDown(){return Object(s.c)(this,void 0,void 0,(function*(){this.udrfService.udrfData.noItemDataFound||this.udrfService.udrfData.isItemDataLoading||(this.itemDataCurrentIndex+=this.udrfService.udrfData.defaultRecordsPerFetch,this.udrfService.udrfData.isItemDataLoading=!0,yield this.initList())}))}initList(){this.spinner.show();let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));this.filterConfig={startIndex:this.itemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails},this.tmService.getTmprofitabiltyReport(this.filterConfig).pipe(Object(d.a)(this._onDestroy)).pipe(Object(d.a)(this._onAppApiCalled)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.messData&&e.messData.length>0?this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e.messData):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0),this.udrfService.udrfData.isItemDataLoading=!1})),e=>{this.showErrorMessage(e)})}openSettingsModal(){return Object(s.c)(this,void 0,void 0,(function*(){for(let r of this.categorisedDataTypeArray)r.categoryCards=n.filter(this.dataTypeArray,(function(e){if(n.contains(r.categoryCardCodes,e.dataTypeCode))return console.log(e.dataTypeCode),e}));let e=[];for(let r of this.categorisedDataTypeArray)if(r.categoryCards.length>0){for(let e of r.categoryCards)e.dataType=e.dataType,e.dataTypeCode=e.dataTypeCode,"status"==e.cardType?e.dataTypeValue=e.dataTypeValue:"INR"==e.cardType?e.dataTypeValue=e.inr?e.inr:"0 Cr":"USD"==e.cardType&&(e.dataTypeValue=e.usd?e.usd:"0 M");e.push(r)}this.categorisedDataTypeArray=e;let t={dataTypeUnit:"",categorisedDataTypeArray:JSON.parse(JSON.stringify(this.categorisedDataTypeArray)),minNoOfVisibleSummaryCards:this.minNoOfVisibleSummaryCards,maxNoOfVisibleSummaryCards:this.maxNoOfVisibleSummaryCards,reportType:"BBR"};const{UdrfSettingsModalComponent:i}=yield r.e(1e3).then(r.bind(null,"c1X9"));this.dialog.open(i,{height:"100%",minWidth:"75%",position:{right:"0px"},data:{modalParams:t}}).afterClosed().subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if("Save"==e.event){this.udrfService.udrfData.udrfSummaryCardCodes=[];for(let t of e.categorisedDataTypeArray)for(let e of t.categoryCards)e.isVisible&&this.udrfService.udrfData.udrfSummaryCardCodes.push(e.dataTypeCode);this.resolveVisibleDataTypeArray(),yield this.udrfService.udrfFunctions.updateUdrfUserConfigFunction(this.udrfService.udrfData,!1)}})))}))}initSummaryCard(){this.tmService.getTmprofitabiltyReportSummaryCard({startIndex:this.itemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:this.udrfService.udrfData.mainFilterArray,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(d.a)(this._onDestroy)).pipe(Object(d.a)(this._onAppApiCalled)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if(console.log("tm summary Card"),console.log(e),this.udrfService.udrfUiData.totalItemDataCount=e.total,this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),e.data.length>0){for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,"status"==this.dataTypeArray[e].cardType?this.dataTypeArray[e].dataTypeValue="0":"INR"==this.dataTypeArray[e].cardType?this.dataTypeArray[e].inr="0 Cr":"USD"==this.dataTypeArray[e].cardType&&(this.dataTypeArray[e].usd="0 M");this.dataTypeArray=this.dataTypeArray.map((t,r)=>{const i=e.data.find(e=>t.dataType==e.dataType);return Object.assign(Object.assign({},t),i)}),console.log(this.dataTypeArray),this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,"status"==this.dataTypeArray[e].cardType?this.dataTypeArray[e].dataTypeValue="0":"INR"==this.dataTypeArray[e].cardType?this.dataTypeArray[e].inr="0 Cr":"status"==this.dataTypeArray[e].cardType&&(this.dataTypeArray[e].usd="0 M");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this.showErrorMessage(e)}),this.spinner.hide()}resolveVisibleDataTypeArray(){return Object(s.c)(this,void 0,void 0,(function*(){for(let e of this.udrfService.udrfUiData.summaryCards){let t;this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=n.contains(this.udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this.udrfService.udrfUiData.summaryCardsItem=e))}}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}showErrorMessage(e){this.utilityService.showErrorMessage(e,"KEBS")}callInlineEditApi(){}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](y),p["\u0275\u0275directiveInject"](m.c),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](v.a),p["\u0275\u0275directiveInject"](S.a),p["\u0275\u0275directiveInject"](C.b))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-tm-profitabilty-landing-page"]],features:[p["\u0275\u0275ProvidersFeature"]([{provide:l.c,useClass:c.c,deps:[l.f,c.a]},{provide:l.e,useValue:_}])],decls:3,vars:0,consts:[[1,"container-fluid","wfh-report-styles","pl-0","pr-0"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275element"](1,"udrf-header"),p["\u0275\u0275element"](2,"udrf-body"),p["\u0275\u0275elementEnd"]())},directives:[D.a,x.a],styles:[""]}),e})()}];let T=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(b)],a.k]}),e})();var I=r("Xi0T"),A=r("iadO");let w=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,T,I.a,A.h]]}),e})()},H44p:function(e,t,r){"use strict";r.d(t,"a",(function(){return f}));var i=r("xG9w"),a=r("fXoL"),s=r("flaP"),n=r("ofXK"),o=r("Qu3c"),d=r("NFeN");function c(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",9),a["\u0275\u0275elementStart"](1,"div",10),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div"),a["\u0275\u0275elementStart"](5,"p",11),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"p",12),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](e.label),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function l(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",13),a["\u0275\u0275elementStart"](1,"span"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",14),a["\u0275\u0275elementStart"](1,"span",15),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",16),a["\u0275\u0275elementStart"](1,"span",15),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"span",18),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function y(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-icon",19),a["\u0275\u0275text"](1,"loop"),a["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",1),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().change()})),a["\u0275\u0275template"](1,c,9,4,"div",2),a["\u0275\u0275template"](2,l,3,2,"div",3),a["\u0275\u0275template"](3,u,3,3,"div",4),a["\u0275\u0275template"](4,p,3,3,"div",5),a["\u0275\u0275template"](5,h,3,3,"div",6),a["\u0275\u0275elementStart"](6,"div",7),a["\u0275\u0275template"](7,y,2,0,"mat-icon",8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","big"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","small"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","medium"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","large"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","overview"==e.type),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",e.toDisplay)}}let f=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=i.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=i.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||i.contains(["big","small"],this.type)?0==this.isConvertValue&&i.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&a["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&a["\u0275\u0275property"]("ngIf",t.currency)},directives:[n.NgIf,o.a,d.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},NJ67:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,r){"use strict";r.d(t,"a",(function(){return u}));var i=r("mrSG"),a=r("XNiG"),s=r("xG9w"),n=r("fXoL"),o=r("tk/3"),d=r("LcQX"),c=r("XXEo"),l=r("flaP");let u=(()=>{class e{constructor(e,t,r,i){this.http=e,this.UtilityService=t,this.loginService=r,this.roleService=i,this.msg=new a.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,r,i,a,s,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:a,limit:s,filterConfig:n,orgIds:o})}getAllRoleAccess(){return s.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,r,i,a,s,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:a,limit:s,filterConfig:n,orgIds:o})}getRequestsForAwaitingApproval(e,t,r,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:r,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,r,i){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:r,filterConfig:i,orgIds:a})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{r(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,r,a,n,o,d){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=o&&o.length>1&&(yield this.getManpowerCostByOId(o,r,n,2))||(yield this.getManpowerCostBasedOnPosition(e,t,r,n,d));let c=yield this.getNonManpowerCost(t,r,a,n,2),l=yield this.getAllocatedCost(),u=0;u=(i?i.cost:0)+c.length>0?s.reduce(s.pluck(c,"cost"),(e,t)=>e+t,0):0;let p=l.length>0?s.reduce(s.pluck(l,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:c,allocatedCost:l,allocatedCostValue:u*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,r,i,a){return new Promise((s,n)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:r,unit:i,position:a}).subscribe(e=>s(e),e=>(console.log(e),n(e)))})}getNonManpowerCost(e,t,r,i,a){return new Promise((s,n)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:r,unit:i,currency_id:a}).subscribe(e=>s(e),e=>(console.log(e),n(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,r,i){return new Promise((a,s)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:r,currency_id:i}).subscribe(e=>a(e),e=>(console.log(e),s(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275inject"](o.c),n["\u0275\u0275inject"](d.a),n["\u0275\u0275inject"](c.a),n["\u0275\u0275inject"](l.a))},e.\u0275prov=n["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,r){"use strict";r.d(t,"a",(function(){return d}));var i=r("mrSG"),a=r("xG9w"),s=r("fXoL"),n=r("tk/3"),o=r("BVzC");let d=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>r(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>r(e))})}getApproversHierarchy(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>r(e))})}createWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>r(e))})}getWorkflowDetails(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let r=0;r<e.length;r++){let i=[],s=a.keys(t["cc"+r]);for(let a=0;a<s.length;a++)for(let n=0;n<t["cc"+r][s[a]].length;n++){let o={name:t["cc"+r][s[a]][n].DELEGATE_NAME,oid:t["cc"+r][s[a]][n].DELEGATE_OID,level:a+1,designation:t["cc"+r][s[a]][n].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+r][s[a]][n].IS_DELEGATED,role:t["cc"+r][s[a]][n].DELEGATE_ROLE_NAME};if(1==t["cc"+r][s[a]][n].IS_DELEGATED&&(o.delegated_by={name:t["cc"+r][s[a]][n].APPROVER_NAME,oid:t["cc"+r][s[a]][n].APPROVER_OID,level:a+1,designation:t["cc"+r][s[a]][n].APPROVER_DESIGNATION_NAME}),i.push(o),r==e.length-1&&a==s.length-1&&n==t["cc"+r][s[a]].length-1)return i}}}))}storeComments(e,t,r){return new Promise((i,a)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:r}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),a(e)))})}updateWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),r(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let r=[],i=a.keys(e["cc"+t]);for(let a=0;a<i.length;a++)for(let s=0;s<e["cc"+t][i[a]].length;s++){let n={name:e["cc"+t][i[a]][s].DELEGATE_NAME,oid:e["cc"+t][i[a]][s].DELEGATE_OID,level:e["cc"+t][i[a]][s].APPROVAL_ORDER,designation:e["cc"+t][i[a]][s].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[a]][s].IS_DELEGATED};if(1==e["cc"+t][i[a]][s].IS_DELEGATED&&(n.delegated_by={name:e["cc"+t][i[a]][s].APPROVER_NAME,oid:e["cc"+t][i[a]][s].APPROVER_OID,level:e["cc"+t][i[a]][s].APPROVAL_ORDER,designation:e["cc"+t][i[a]][s].APPROVER_DESIGNATION_NAME}),r.push(n),a==i.length-1&&s==e["cc"+t][i[a]].length-1)return r}}}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](n.c),s["\u0275\u0275inject"](o.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);