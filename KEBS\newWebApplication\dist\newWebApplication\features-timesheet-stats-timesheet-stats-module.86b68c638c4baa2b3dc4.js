(window.webpackJsonp=window.webpackJsonp||[]).push([[680,765,821,822,983,987,990,991],{ABNh:function(t,e,n){"use strict";n.r(e),n.d(e,"TimesheetStatsModule",(function(){return Ct}));var s=n("ofXK"),a=n("tyNb"),o=n("mrSG"),i=n("3Pt+"),r=n("1yaQ"),c=n("FKr1"),l=n("wd/R"),d=n("xG9w"),p=n("1G5W"),m=n("XNiG"),u=n("fXoL"),h=n("tk/3");let g=(()=>{class t{constructor(t){this.$http=t}getSummaryForTsStats(t,e,n,s,a){return this.$http.post("/api/tsPrimary/getSummaryForTsStats",{currDateTime:t,currentView:e,costCentre:n,startDate:s,endDate:a})}searchCostCentres(t){return this.$http.post("/api/tsPrimary/searchCostCentres",{searchParameter:t,isFromTs:!1})}checkIfTsAdmin(t){return this.$http.post("/api/tsPrimary/checkIfTsAdmin",{associateOId:t})}getAssocMetaSubmStats(t,e){return this.$http.post("/api/tsPrimary/getMetaSubmStats",{currDateTime:t,assocOID:e})}getEmployeeDataForTsStats(t,e,n,s,a,o,i,r){return this.$http.post("/api/tsPrimary/getEmployeeDataForTsStats",{currDateTime:t,forStatus:e,startIndex:n,currentView:s,costCentre:a,assocOID:o,startDate:i,endDate:r})}bulkNotifyFromTsStats(t,e,n,s){return this.$http.post("/api/tsPrimary/bulkNotifyFromTsStats",{currDateTime:t,forStatus:e,startDate:n,endDate:s})}notifyUnsubmittedAssociates(t,e,n,s){return this.$http.post("/api/tsPrimary/notifyUnsubmittedAssociates",{associateData:t,forStatus:e,startDate:n,endDate:s})}downloadTSReportForStats(t,e,n,s){return this.$http.post("/api/tsPrimary/downloadTSReportForStats",{currDateTime:t,forStatus:e,startDate:n,endDate:s})}}return t.\u0275fac=function(e){return new(e||t)(u["\u0275\u0275inject"](h.c))},t.\u0275prov=u["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var f=n("JLuW"),v=n("LcQX"),S=n("XXEo"),y=n("HmYF"),D=n("BVzC"),C=n("bTqV"),b=n("Qu3c"),M=n("NFeN"),x=n("kmnG"),_=n("qFsG"),E=n("iadO"),w=n("d3UM"),A=n("Xa2L"),O=n("/1cH"),I=n("me71"),P=n("bSwM"),k=n("dlKe"),Y=n("/QRN");let T=(()=>{class t{constructor(){}ngOnInit(){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=u["\u0275\u0275defineComponent"]({type:t,selectors:[["app-stats-loader"]],decls:2,vars:5,consts:[[3,"speed","width","height","primaryColor","secondaryColor"],["ngx-rect","","width","1000","height","30","y","50","x","0","rx","5","ry","5"]],template:function(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"ngx-content-loading",0),u["\u0275\u0275namespaceSVG"](),u["\u0275\u0275element"](1,"g",1),u["\u0275\u0275elementEnd"]()),2&t&&u["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7")},directives:[Y.b,Y.c],styles:[""]}),t})();function V(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"mat-icon",47),u["\u0275\u0275text"](1,"cloud_download"),u["\u0275\u0275elementEnd"]())}function F(t,e){1&t&&u["\u0275\u0275element"](0,"mat-spinner",48)}function L(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",44),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().downloadCurrentReport()})),u["\u0275\u0275template"](1,V,2,0,"mat-icon",45),u["\u0275\u0275template"](2,F,1,0,"mat-spinner",46),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275property"]("matTooltip",t.isDownloading?"Downloading":"Download"),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!t.isDownloading),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.isDownloading)}}function R(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",49),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().clearStatusFilter()})),u["\u0275\u0275elementStart"](1,"mat-icon",47),u["\u0275\u0275text"](2,"close"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}}function N(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"mat-option",58),u["\u0275\u0275text"](1),u["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;u["\u0275\u0275propertyInterpolate2"]("matTooltip","",t.cost_centre," - ",t.cost_centre_description,""),u["\u0275\u0275property"]("value",t.cost_centre),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate2"](" ",t.cost_centre," - ",t.cost_centre_description," ")}}function j(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",59),u["\u0275\u0275elementStart"](1,"span",60),u["\u0275\u0275text"](2),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](3," - "),u["\u0275\u0275elementStart"](4,"span",61),u["\u0275\u0275text"](5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate"](t.costCentreData.cost_centre),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("matTooltip",t.getLength(t.costCentreData.cost_centre_description?t.costCentreData.cost_centre_description:"a")>15?t.costCentreData.cost_centre_description:""),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate"](t.costCentreData.cost_centre_description)}}function q(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",62),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"](2).clearCCData()})),u["\u0275\u0275elementStart"](1,"mat-icon",47),u["\u0275\u0275text"](2,"close"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}}function B(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",50),u["\u0275\u0275elementStart"](1,"div",51),u["\u0275\u0275elementStart"](2,"mat-form-field",52),u["\u0275\u0275elementStart"](3,"input",53),u["\u0275\u0275listener"]("ngModelChange",(function(e){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().costCentreData.cost_centre=e}))("ngModelOptions",(function(){return{debounce:1e3}}))("ngModelChange",(function(){u["\u0275\u0275restoreView"](t);const e=u["\u0275\u0275nextContext"]();return e.selectCostCentre(e.costCentreData.cost_centre)})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"mat-autocomplete",null,54),u["\u0275\u0275template"](6,N,2,5,"mat-option",55),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](7,j,6,3,"div",56),u["\u0275\u0275elementStart"](8,"div",25),u["\u0275\u0275template"](9,q,3,0,"button",57),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275reference"](5),e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("matTooltip",""!=e.costCentreData.cost_centre?e.costCentreData.cost_centre+" - "+e.costCentreData.cost_centre_description:""),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngModel",e.costCentreData.cost_centre)("matAutocomplete",t)("disabled",""!=e.costCentreData.cost_centre_description),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngForOf",e.costCentreList),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",""!=e.costCentreData.cost_centre&&""!=e.costCentreData.cost_centre_description),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",""!=e.costCentreData.cost_centre&&""!=e.costCentreData.cost_centre_description)}}function U(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"mat-option",69),u["\u0275\u0275text"](1),u["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;u["\u0275\u0275property"]("value",t.name),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate2"](" ",t.name," - ",t.role," ")}}function W(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",70),u["\u0275\u0275elementStart"](1,"div",71),u["\u0275\u0275elementStart"](2,"span",22),u["\u0275\u0275element"](3,"app-user-image",72),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"span",73),u["\u0275\u0275text"](5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("id",t.selectedAssociateData.oid),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("matTooltip",t.selectedAssociateData.role),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate"](t.selectedAssociateData.name)}}function G(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",74),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"](2).clearAssociateData()})),u["\u0275\u0275elementStart"](1,"mat-icon",47),u["\u0275\u0275text"](2,"close"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}}function H(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",50),u["\u0275\u0275elementStart"](1,"div",51),u["\u0275\u0275elementStart"](2,"mat-form-field",63),u["\u0275\u0275elementStart"](3,"input",64),u["\u0275\u0275listener"]("ngModelChange",(function(e){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().selectedAssociateData.name=e}))("ngModelOptions",(function(){return{debounce:1e3}}))("ngModelChange",(function(){u["\u0275\u0275restoreView"](t);const e=u["\u0275\u0275nextContext"]();return e.selectAssociate(e.selectedAssociateData.name)})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"mat-autocomplete",null,65),u["\u0275\u0275template"](6,U,2,3,"mat-option",66),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](7,W,6,3,"div",67),u["\u0275\u0275elementStart"](8,"div",25),u["\u0275\u0275template"](9,G,3,0,"button",68),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275reference"](5),e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("matTooltip",""!=e.selectedAssociateData.oid?e.selectedAssociateData.name+" - "+e.selectedAssociateData.role:""),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngModel",e.selectedAssociateData.name)("matAutocomplete",t)("disabled",""!=e.selectedAssociateData.oid),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngForOf",e.associateList),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",""!=e.selectedAssociateData.name&&""!=e.selectedAssociateData.oid),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",""!=e.selectedAssociateData.name&&""!=e.selectedAssociateData.oid)}}function z(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"div",1),u["\u0275\u0275elementStart"](1,"span",75),u["\u0275\u0275text"](2,"Current Month Stats"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}function $(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",79),u["\u0275\u0275elementStart"](1,"div",80),u["\u0275\u0275elementStart"](2,"div",81),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](t);const n=e.index;return u["\u0275\u0275nextContext"](2).cardClicked(n)})),u["\u0275\u0275elementStart"](3,"div",82),u["\u0275\u0275elementStart"](4,"span",83),u["\u0275\u0275text"](5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"div",84),u["\u0275\u0275element"](7,"div",85),u["\u0275\u0275elementStart"](8,"span",86),u["\u0275\u0275text"](9),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngClass",t.clicked?"status-card-clicked":""),u["\u0275\u0275advance"](4),u["\u0275\u0275textInterpolate1"](" ",t.count," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngClass","Draft"==t.status?"is-draft":"Submitted"==t.status?"is-submitted":"Approved"==t.status?"is-approved":"Rejected"==t.status?"is-rejected":"Unsubmitted"==t.status||"Recalled"==t.status?"is-draft":""),u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",t.status," ")}}function X(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",76),u["\u0275\u0275elementStart"](1,"div",77),u["\u0275\u0275elementStart"](2,"div",6),u["\u0275\u0275template"](3,$,10,4,"div",78),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngForOf",t.statusArray)}}function K(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",93),u["\u0275\u0275elementStart"](1,"mat-icon",94),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"](2).notifyAllSelected()})),u["\u0275\u0275text"](2,"send"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275nextContext"](2);u["\u0275\u0275property"]("matTooltip","S"==t.currentStatus?"Notify Current Approvers":"Notify Associates")}}function J(t,e){1&t&&u["\u0275\u0275element"](0,"mat-spinner",95)}function Q(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",87),u["\u0275\u0275elementStart"](1,"div",88),u["\u0275\u0275elementStart"](2,"span",89),u["\u0275\u0275elementStart"](3,"mat-checkbox",90),u["\u0275\u0275listener"]("ngModelChange",(function(e){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().selectAllActivated=e}))("change",(function(){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().selectAll()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](4,K,3,1,"button",91),u["\u0275\u0275template"](5,J,1,0,"mat-spinner",92),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngModel",t.selectAllActivated),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!t.notifyingActivated),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.notifyingActivated)}}function Z(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"mat-checkbox",117),u["\u0275\u0275listener"]("ngModelChange",(function(e){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().$implicit.checked=e})),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275nextContext"]().$implicit;u["\u0275\u0275property"]("ngModel",t.checked)}}function tt(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"span",118),u["\u0275\u0275text"](1),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"]().$implicit;u["\u0275\u0275property"]("matTooltip",t.group_description),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate1"]("(",t.group_code,")")}}function et(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"span",119),u["\u0275\u0275text"](1,"(NA)"),u["\u0275\u0275elementEnd"]())}function nt(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"span"),u["\u0275\u0275elementStart"](1,"span",120),u["\u0275\u0275text"](2),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngClass","L"==t.dayType?"is-leave-day":"FH"==t.dayType?"is-fh-day":"is-empty-day"),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate2"](" ",t.count,"",t.dayType," ")}}function st(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"div",121),u["\u0275\u0275elementStart"](2,"div",122),u["\u0275\u0275elementStart"](3,"div",6),u["\u0275\u0275elementStart"](4,"div",123),u["\u0275\u0275text"](5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"div",124),u["\u0275\u0275text"](7),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](8,"div",125),u["\u0275\u0275text"](9),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;u["\u0275\u0275advance"](5),u["\u0275\u0275textInterpolate"](t.cc_hours?t.cc_hours:"-"),u["\u0275\u0275advance"](1),u["\u0275\u0275propertyInterpolate2"]("matTooltip","",t.cc_code?t.cc_code:"-"," - ",t.cc_name?t.cc_name:"-",""),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate2"]("",t.cc_code?t.cc_code:"-"," - ",t.cc_name?t.cc_name:"-",""),u["\u0275\u0275advance"](1),u["\u0275\u0275propertyInterpolate"]("matTooltip",t.cc_location?t.cc_location:"-"),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate1"](" ",t.cc_location?t.cc_location:"-"," ")}}function at(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"div",6),u["\u0275\u0275elementStart"](2,"div",98),u["\u0275\u0275elementStart"](3,"div",99),u["\u0275\u0275elementStart"](4,"div",100),u["\u0275\u0275elementStart"](5,"div",101),u["\u0275\u0275elementStart"](6,"div",102),u["\u0275\u0275elementStart"](7,"span",103),u["\u0275\u0275template"](8,Z,1,1,"mat-checkbox",104),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](9,"span",105),u["\u0275\u0275element"](10,"app-user-image",106),u["\u0275\u0275elementStart"](11,"span",107),u["\u0275\u0275text"](12),u["\u0275\u0275template"](13,tt,2,2,"span",108),u["\u0275\u0275template"](14,et,2,0,"span",109),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](15,"div",110),u["\u0275\u0275element"](16,"span",85),u["\u0275\u0275elementStart"](17,"span",111),u["\u0275\u0275text"](18),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](19,"div",112),u["\u0275\u0275elementStart"](20,"span",113),u["\u0275\u0275text"](21),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](22,nt,3,3,"span",97),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](23,"div",114),u["\u0275\u0275template"](24,st,10,7,"div",97),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](25,"div",115),u["\u0275\u0275elementStart"](26,"span",116),u["\u0275\u0275text"](27),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngClass","Submitted"==t.status?"is-submitted-side":"Approved"==t.status?"is-approved-side":"Rejected"==t.status?"is-rejected-side":"Draft"==t.status||"Unsubmitted"==t.status?"is-draft-side":""),u["\u0275\u0275advance"](5),u["\u0275\u0275property"]("ngIf","All"==n.currentView&&"A"!=n.currentStatus&&"Default"!=n.currentStatus&&"current"==n.selectedMonth&&n.isCurrentUserAdmin),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("matTooltip",n.getLength(t.name?t.name:"a")>15?t.name:""),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("id",t.oid),u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"]("",t.name," "),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf","P"!=t.group_code),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",0==t.is_bot_active&&n.isCurrentUserAdmin),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngClass","Draft"==t.status?"is-draft":"Submitted"==t.status?"is-submitted":"Recalled"==t.status?"is-draft":"Approved"==t.status?"is-approved":"Rejected"==t.status?"is-rejected":"Unsubmitted"==t.status?"is-draft":""),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("matTooltip",t.status),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate1"](" ",t.status,""),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](t.total_hours?t.total_hours:"-"),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",t.nonRegularDays),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngForOf",t.costCenterDetails),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate1"](" ",t.date?n.getLocalDate(t.date):"-","")}}function ot(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",96),u["\u0275\u0275listener"]("scrolled",(function(){return u["\u0275\u0275restoreView"](t),u["\u0275\u0275nextContext"]().onScrollDown()})),u["\u0275\u0275template"](1,at,28,14,"div",97),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275property"]("ngClass","individual"==t.currentView?"infinite-scroll-auto":"infinite-scroll-fixed")("infiniteScrollDistance",2)("scrollWindow",!1),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",t.associateData)}}function it(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",133),u["\u0275\u0275elementStart"](1,"span",134),u["\u0275\u0275elementStart"](2,"span",135),u["\u0275\u0275text"](3),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"span",136),u["\u0275\u0275text"](5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](t.label),u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate"](t.count)}}function rt(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",133),u["\u0275\u0275elementStart"](1,"span",134),u["\u0275\u0275elementStart"](2,"span",135),u["\u0275\u0275text"](3),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"span",136),u["\u0275\u0275text"](5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](t.label),u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate"](t.count)}}function ct(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",127),u["\u0275\u0275elementStart"](1,"div",100),u["\u0275\u0275elementStart"](2,"div",128),u["\u0275\u0275elementStart"](3,"span",129),u["\u0275\u0275text"](4," Stats for this Month "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"div",130),u["\u0275\u0275elementStart"](6,"div",88),u["\u0275\u0275elementStart"](7,"div",6),u["\u0275\u0275template"](8,it,6,2,"div",131),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](9,"div",128),u["\u0275\u0275elementStart"](10,"span",129),u["\u0275\u0275text"](11," Stats for this Year "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](12,"div",132),u["\u0275\u0275elementStart"](13,"div",88),u["\u0275\u0275elementStart"](14,"div",6),u["\u0275\u0275template"](15,rt,6,2,"div",131),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](8),u["\u0275\u0275property"]("ngForOf",t.assocMetaSubmStatsForMonth),u["\u0275\u0275advance"](7),u["\u0275\u0275property"]("ngForOf",t.assocMetaSubmStatsForYear)}}function lt(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",87),u["\u0275\u0275elementStart"](1,"div",98),u["\u0275\u0275template"](2,ct,16,2,"div",126),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",t.assocMetaSubmStatsForMonth.length>0&&t.associateData.length>0)}}function dt(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"div"),u["\u0275\u0275elementStart"](2,"h4",137),u["\u0275\u0275text"](3),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"div",138),u["\u0275\u0275element"](5,"img",139),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate1"](" ","individual"==t.currentView?"Choose a associate to proceed further !":"No Associate Found !"," ")}}function pt(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"h4",137),u["\u0275\u0275text"](2," Choose a cost centre to proceed further ! "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](3,"div",138),u["\u0275\u0275element"](4,"img",141),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}function mt(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"h4",137),u["\u0275\u0275text"](2," No Associate Found ! "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](3,"div",138),u["\u0275\u0275element"](4,"img",139),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}function ut(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",140),u["\u0275\u0275template"](1,pt,5,0,"div",40),u["\u0275\u0275template"](2,mt,5,0,"div",40),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",""==t.costCentreData.cost_centre_description),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",""!=t.costCentreData.cost_centre_description&&t.noData)}}function ht(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"div",6),u["\u0275\u0275elementStart"](2,"div",98),u["\u0275\u0275elementStart"](3,"div",143),u["\u0275\u0275elementStart"](4,"div",100),u["\u0275\u0275elementStart"](5,"div",101),u["\u0275\u0275elementStart"](6,"div",144),u["\u0275\u0275element"](7,"app-stats-loader"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](8,"div",145),u["\u0275\u0275element"](9,"app-stats-loader"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](10,"div",146),u["\u0275\u0275element"](11,"app-stats-loader"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](12,"div",146),u["\u0275\u0275element"](13,"app-stats-loader"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](14,"div",146),u["\u0275\u0275element"](15,"app-stats-loader"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](16,"div",146),u["\u0275\u0275element"](17,"app-stats-loader"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}function gt(t,e){if(1&t&&(u["\u0275\u0275elementStart"](0,"div",142),u["\u0275\u0275template"](1,ht,18,0,"div",97),u["\u0275\u0275elementEnd"]()),2&t){const t=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",t.loader)}}function ft(t,e){1&t&&(u["\u0275\u0275elementStart"](0,"div",147),u["\u0275\u0275elementStart"](1,"div",148),u["\u0275\u0275element"](2,"mat-spinner",149),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}const vt={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},St=[{path:"",component:(()=>{class t{constructor(t,e,n,s,a,o){this._timesheetStatsService=t,this.sharedLazyLoadedComponentsService=e,this.utilityService=n,this.authService=s,this.excelService=a,this.errorService=o,this.currentView="All",this.currentStatus="Default",this.selectedMonth="current",this._onDestroy=new m.b,this.monthYearDate=new i.j(l()),this.isStatusFilterApplied=!1,this.selectAllActivated=!1,this.notifyingActivated=!1,this.isCurrentUserAdmin=!1,this.isDownloading=!1,this.noData=!1,this.isMoreLoading=!1,this.responseData=[],this.associateData=[],this.costCentreList=[],this.associateList=[],this.loader=[1,1,1,1,1,1,1,1,1,1],this.costCentreData={cost_centre:"",cost_centre_type:"",cost_centre_description:""},this.selectedAssociateData={oid:"",name:"",designation:"",role:""},this.assocMetaSubmStatsForMonth=[],this.assocMetaSubmStatsForYear=[],this.statusArray=[{status:"Unsubmitted",status_code:"US",count:0,clicked:!1},{status:"Draft",status_code:"D",count:0,clicked:!1},{status:"Recalled",status_code:"RL",count:0,clicked:!1},{status:"Rejected",status_code:"R",count:0,clicked:!1},{status:"Submitted",status_code:"S",count:0,clicked:!1},{status:"Approved",status_code:"A",count:0,clicked:!1}],this.startIndex=0,this.currentUser={},this.monthEndDateVal=24}ngOnInit(){this.currentUser=this.authService.getProfile().profile,this.startDate=l(this.monthYearDate.value).subtract(1,"months").date(this.monthEndDateVal+1).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.endDate=l(this.monthYearDate.value).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.getSummaryOfTs(),this.checkIfCurrentUserAdmin()}checkIfCurrentUserAdmin(){this._timesheetStatsService.checkIfTsAdmin(this.currentUser.oid).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){this.isCurrentUserAdmin="S"==t.messType})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while checking the user is an admin",t&&t.params?t.params:t&&t.error?t.error.params:{})})}selectMonth(t,e){const n=this.monthYearDate.value;n.month()!=t.month()&&(n.month(t.month()).year(t.year()).date(1),this.changeMonthYear(n)),e.close()}selectYear(t){const e=this.monthYearDate.value;e.year()!=t.year()&&(e.year(t.year()),this.changeMonthYear(e))}changeMonthYear(t){this.monthYearDate.setValue(t),this.getSummaryOfTs(),this.selectedMonth=this.monthYearDate.value.month()==l().month()?"current":this.monthYearDate.value.month()==l().month()-1?"previous":"other"}goToPreviousMonth(){this.startDate=l(this.startDate).subtract(1,"M"),this.endDate=l(this.endDate).subtract(1,"M"),this.monthYearDate.setValue(this.monthYearDate.value.subtract(1,"M")),this.getSummaryOfTs(),this.selectedMonth=this.monthYearDate.value.month()==l().month()?"current":this.monthYearDate.value.month()==l().month()-1?"previous":"other"}goToNextMonth(){this.startDate=l(this.startDate).add(1,"M"),this.endDate=l(this.endDate).add(1,"M"),this.monthYearDate.setValue(this.monthYearDate.value.add(1,"M")),this.getSummaryOfTs(),this.selectedMonth=this.monthYearDate.value.month()==l().month()?"current":this.monthYearDate.value.month()==l().month()-1?"previous":"other"}selectThisMonth(){this.startDate=l(this.monthYearDate.value).subtract(1,"months").date(this.monthEndDateVal+1).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.endDate=l(this.monthYearDate.value).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.monthYearDate.setValue(l()),console.log(this.monthYearDate),this.getSummaryOfTs(),this.selectedMonth="current"}selectPreviousMonth(){this.startDate=l(this.startDate).subtract(1,"M"),this.endDate=l(this.endDate).subtract(1,"M"),this.monthYearDate.setValue(l().subtract(1,"M")),this.getSummaryOfTs(),this.selectedMonth="previous"}getDefaultView(){this.currentView="All",this.associateData=[],this.getSummaryOfTs()}getCCView(){this.currentView="costCentre",this.initSummaryData(),""!=this.costCentreData.cost_centre&&""!=this.costCentreData.cost_centre_description&&(this.startIndex=0,this.getSummaryOfTs())}getIndividualView(){this.currentView="individual",this.associateData=[],""!=this.selectedAssociateData.oid&&(d.each(this.statusArray,t=>{t.clicked=!1}),this.isStatusFilterApplied=!1,this.currentStatus="Default",this.startIndex=0,this.getAssociateData())}checkIfStatusApplied(){if("costCentre"==this.currentView){this.isStatusFilterApplied=!0;let t=0;for(let n=0;n<this.statusArray.length;n++){let e=this.statusArray[n].count;this.statusArray[n].clicked=!1,e>t&&(t=e)}let e=this.statusArray.findIndex(e=>e.count===t);this.statusArray[e].clicked=!0,this.currentStatus=this.statusArray[e].status_code,t>0?(this.startIndex=0,this.getAssociateData()):(this.associateData=[],this.noData=!0)}else this.isStatusFilterApplied?d.where(this.statusArray,{clicked:!0})[0].count>0?(this.startIndex=0,this.getAssociateData()):(this.associateData=[],this.noData=!0):(this.startIndex=0,"individual"==this.currentView?""!=this.selectedAssociateData.oid&&this.getAssociateData():this.getAssociateData())}getSummaryOfTs(){this._timesheetStatsService.getSummaryForTsStats(l(this.monthYearDate.value).format("YYYY-MM-DD"),this.currentView,this.costCentreData.cost_centre,this.startDate,this.endDate).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data.length>0)for(let e=0;e<this.statusArray.length;e++)this.statusArray[e].count="US"==this.statusArray[e].status_code?null==t.data[0].UNSUBMITTED?0:t.data[0].UNSUBMITTED:"S"==this.statusArray[e].status_code?null==t.data[0].SUBMITTED?0:t.data[0].SUBMITTED:"A"==this.statusArray[e].status_code?null==t.data[0].APPROVED?0:t.data[0].APPROVED:"R"==this.statusArray[e].status_code?null==t.data[0].REJECTED?0:t.data[0].REJECTED:"D"==this.statusArray[e].status_code?null==t.data[0].DRAFT?0:t.data[0].DRAFT:"RL"==this.statusArray[e].status_code?null==t.data[0].RECALLED?0:t.data[0].RECALLED:0;this.checkIfStatusApplied()})))}getColorForCC(t){return"D"==t?"#c7c4c4":"S"==t?"#FF7200":"A"==t?"#009432":"R"==t?"#cf0001":"#c7c4c4"}searchCostCentres(t){return new Promise(e=>{this._timesheetStatsService.searchCostCentres(t).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){let n=[];"S"==t.messType&&t.data.length>0&&(n=t.data),e(n)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","No Data Found!",t&&t.params?t.params:t&&t.error?t.error.params:{})})})}selectCostCentre(t){let e=d.where(this.costCentreList,{cost_centre:t});e.length>0?(this.associateData=[],this.costCentreData=e[0],this.getSummaryOfTs()):this.searchCostCentres(t).then(t=>{this.costCentreList=t})}selectAssociate(t){let e=d.where(this.associateList,{name:t});e.length>0?(d.each(this.statusArray,t=>{t.clicked=!1}),this.isStatusFilterApplied=!1,this.currentStatus="Default",this.selectedAssociateData=e[0],this.associateData=[],this.startIndex=0,this.getAssociateData()):this.searchAssociates(t).then(t=>{this.associateList=t})}searchAssociates(t){return new Promise(e=>{this.sharedLazyLoadedComponentsService.searchConsultants(t,"").subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){let n=[];"S"==t.messType&&t.data.length>0&&(n=t.data),e(n)})))})}getAssocMetaSubmStats(){this._timesheetStatsService.getAssocMetaSubmStats(l(this.monthYearDate.value).format("YYYY-MM-DD"),this.selectedAssociateData.oid).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==t.messType){let e=t.data[0];this.assocMetaSubmStatsForMonth=[{label:"Hours Booked",count:e.HOURS_BOOKED_M},{label:"Leaves taken",count:e.L_M},{label:"Carried Leave(CL)",count:e.HOURS_BOOKED_M>0?e.CL_M:0},{label:"FH taken",count:e.FH_M},{label:"CH taken",count:e.CH_M},{label:"CO taken",count:e.CO_M},{label:"WO taken",count:e.WO_M},{label:"Loss of pay(LOP)",count:e.LOP_M}],this.assocMetaSubmStatsForYear=[{label:"Leaves taken",count:e.L_TAKEN_Y},{label:"Leave Balance",count:e.LEAVE_BALANCE},{label:"FH & CH taken",count:e.FH_Y},{label:"CO taken",count:e.CO_Y}]}})))}clearCCData(){this.costCentreData={cost_centre:"",cost_centre_type:"",cost_centre_description:""},this.initSummaryData()}clearAssociateData(){this.selectedAssociateData={oid:"",name:"",designation:"",role:""},this.associateData=[]}initSummaryData(){for(let t=0;t<this.statusArray.length;t++)this.statusArray[t].count=0;this.associateData=[]}getAssociateData(){this.assocSubscription&&this.assocSubscription.unsubscribe(),0==this.startIndex?(this.associateData=[],this.noData=!1):this.isMoreLoading=!0,console.log(this.startDate),console.log(this.endDate),this.assocSubscription=this._timesheetStatsService.getEmployeeDataForTsStats(l(this.monthYearDate.value).format("YYYY-MM-DD"),this.currentStatus,this.startIndex,this.currentView,this.costCentreData.cost_centre,this.selectedAssociateData.oid,this.startDate,this.endDate).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){this.isMoreLoading=!1,"S"==t.messType&&t.data.length>0?(this.noData=!1,this.responseData=t.data,this.resolveResponse(),this.getDetailedEmployeeData(),"individual"==this.currentView&&""!=this.selectedAssociateData.oid&&this.getAssocMetaSubmStats()):0==this.startIndex&&(this.utilityService.showMessage(t.messText,"Dismiss",3e3),this.noData=!0)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving Employee Data For TS stats",t&&t.params?t.params:t&&t.error?t.error.params:{})})}resolveResponse(){for(let t=0;t<this.responseData.length;t++)this.associateData.push(this.responseData[t])}getDetailedEmployeeData(){for(let t=this.startIndex;t<this.associateData.length;t++)if(this.associateData[t].checked=this.selectAllActivated,this.associateData[t].nonRegularDays=[],this.associateData[t].costCenterDetails=[],this.associateData[t].leave_count>0&&this.associateData[t].nonRegularDays.push({dayType:"L",count:this.associateData[t].leave_count}),this.associateData[t].fh_count>0&&this.associateData[t].nonRegularDays.push({dayType:"FH",count:this.associateData[t].fh_count}),null!=this.associateData[t].cc_details){let e=this.associateData[t].cc_details.split(",");for(let n=0;n<e.length;n++){let s=e[n].split("$");this.associateData[t].costCenterDetails.push({cc_code:s[0],cc_name:"NULL"==s[1]?null:s[1],cc_status:s[2],cc_location:s[3],cc_hours:s[4]})}}}cardClicked(t){if(d.each(this.statusArray,t=>{t.clicked=!1}),this.statusArray[t].clicked=!0,this.currentStatus=this.statusArray[t].status_code,this.isStatusFilterApplied=!0,this.statusArray[t].count>0){this.noData=!1,this.startIndex=0,this.loader=[];for(let e=0;e<this.statusArray[t].count&&e<10;e++)this.loader.push(1);this.getAssociateData()}else this.associateData=[],this.noData=!0;this.selectAllActivated=!1,this.selectAll()}clearStatusFilter(){d.each(this.statusArray,t=>{t.clicked=!1}),this.currentStatus="Default",this.isStatusFilterApplied=!1,this.startIndex=0,this.getAssociateData(),this.selectAllActivated=!1,this.selectAll()}getLocalDate(t){return"-"==t?t:l(t,"DD - MMM - YYYY HH : mm : ss").utc(t).local().format("DD - MMM - YYYY HH : mm : ss")}getLength(t){return t.length}onScrollDown(){this.startIndex+=15,this.getAssociateData()}selectAll(){d.each(this.associateData,this.selectAllActivated?t=>{t.checked=!0}:t=>{t.checked=!1})}notifyAllSelected(){let t=d.where(this.associateData,{checked:!0});this.selectAllActivated?(this.notifyingActivated=!0,this._timesheetStatsService.bulkNotifyFromTsStats(l(this.monthYearDate.value).format("YYYY-MM-DD"),this.currentStatus,this.startDate,this.endDate).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this.notifyingActivated=!1,this.utilityService.showMessage(t.messText,"Dismiss",3e3)):this.utilityService.showMessage(t.messText,"Dismiss",3e3),this.selectAllActivated=!1,this.selectAll()})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while doing bulk notification to associates",t&&t.params?t.params:t&&t.error?t.error.params:{})})):t.length>0?(this.notifyingActivated=!0,this._timesheetStatsService.notifyUnsubmittedAssociates(t,this.currentStatus,this.startDate,this.endDate).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this.notifyingActivated=!1,this.utilityService.showMessage(t.messText,"Dismiss",3e3)):this.utilityService.showMessage(t.messText,"Dismiss",3e3),this.selectAllActivated=!1,this.selectAll()})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while notifying usubmitted associates",t&&t.params?t.params:t&&t.error?t.error.params:{})})):this.utilityService.showMessage("No Associate Selected","Dismiss",3e3)}downloadCurrentReport(){this.isDownloading=!0,this._timesheetStatsService.downloadTSReportForStats(l(this.monthYearDate.value).format("YYYY-MM-DD"),this.currentStatus,this.startDate,this.endDate).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){this.isDownloading=!1,"S"==t.messType?this.excelService.exportAsExcelFile(t.data,t.fileName):this.utilityService.showMessage(t.data,"Dismiss",3e3)})),t=>{this.isDownloading=!1,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while downloading Timesheet Report Data",t&&t.params?t.params:t&&t.error?t.error.params:{})})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}goToWeekFilter(){this.monthYearDate.value.date(1).hour(15);let t=l(this.monthYearDate.value).subtract(1,"months").date(this.monthEndDateVal+1).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),e=(l(this.monthYearDate.value).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),+l(t).day());console.log(e);let n=0;0==e?n=6:1==e?n=5:2==e?n=4:3==e?n=3:4==e?n=2:5==e?n=1:6==e&&(n=0);let s=l(t).add(n,"days").format("YYYY-MM-DD"),a=l(s).add(1,"week").format("YYYY-MM-DD"),o=l(s).add(2,"week").format("YYYY-MM-DD"),i=l(s).add(3,"week").format("YYYY-MM-DD");"option1"==this.selected?(this.startDate=l(t).format("YYYY-MM-DD"),this.endDate=l(t).add(n,"days").format("YYYY-MM-DD")):"option2"==this.selected?(this.startDate=l(s).add(1,"days").format("YYYY-MM-DD"),this.endDate=l(s).add(1,"week").format("YYYY-MM-DD")):"option3"==this.selected?(this.startDate=l(a).add(1,"day").format("YYYY-MM-DD"),this.endDate=l(s).add(2,"week").format("YYYY-MM-DD")):"option4"==this.selected?(this.startDate=l(o).add(1,"day").format("YYYY-MM-DD"),this.endDate=l(s).add(3,"week").format("YYYY-MM-DD")):"option5"==this.selected&&(this.startDate=l(i).add(1,"day").format("YYYY-MM-DD"),this.endDate=l(i).add(1,"week").format("YYYY-MM-DD")),this.getSummaryOfTs()}}return t.\u0275fac=function(e){return new(e||t)(u["\u0275\u0275directiveInject"](g),u["\u0275\u0275directiveInject"](f.a),u["\u0275\u0275directiveInject"](v.a),u["\u0275\u0275directiveInject"](S.a),u["\u0275\u0275directiveInject"](y.a),u["\u0275\u0275directiveInject"](D.a))},t.\u0275cmp=u["\u0275\u0275defineComponent"]({type:t,selectors:[["app-timesheet-stats-landing-page"]],features:[u["\u0275\u0275ProvidersFeature"]([{provide:c.c,useClass:r.c,deps:[c.f,r.a]},{provide:c.e,useValue:vt}])],decls:69,vars:22,consts:[[1,"container-fluid","timesheet-stats-styles","pl-0","pr-0"],[1,"row","d-flex","pt-2"],[1,"col-4","pl-2"],["mat-raised-button","",3,"ngClass","click"],["mat-raised-button","",2,"margin-left","2%",3,"ngClass","click"],[1,"col-6",2,"border-left","solid 1px #cacaca"],[1,"row"],[1,"col-6"],[1,"mediumSubtleText","pl-2","pt-3","my-auto"],[1,"col-5"],[1,"row","pl-0","pr-0"],[1,"col-1","pl-0","pr-0","mr-2"],["mat-icon-button","","matTooltip","Previous month"],[1,"iconButton",3,"click"],[1,"col-6","pr-0","pl-0","pt-2"],[2,"width","100% !important"],["id","monthYearDpInput","matInput","","readonly","",1,"ib13",3,"matDatepicker","formControl"],["matSuffix","","matTooltip","Select from Calendar",3,"for"],["disabled","false","startView","year","panelClass","example-month-picker",3,"yearSelected","monthSelected"],["monthYearDp",""],[1,"col-1","pl-0","ml-2"],["mat-icon-button","","matTooltip","Next month"],[1,"col-2"],[1,"col-1","p-0"],["mat-icon-button","","class","view-button-inactive","style","margin-left: 1%",3,"matTooltip","click",4,"ngIf"],[1,"col-1"],["mat-icon-button","","matTooltip","Clear Status Filter","class","view-button-inactive",3,"click",4,"ngIf"],[1,"col-9"],["appearance","fill","floatLabel","never",2,"width","150px"],[3,"ngModel","ngModelChange"],["value","option1",1,"custom-ts-select-option"],["value","option2",1,"custom-ts-select-option"],["value","option3",1,"custom-ts-select-option"],["value","option4",1,"custom-ts-select-option"],["value","option5",1,"custom-ts-select-option"],["class","row d-flex pt-1",4,"ngIf"],["class","row d-flex pt-2",4,"ngIf"],["class","row pt-1",4,"ngIf"],["class","row pt-2",4,"ngIf"],["class","pt-1","infinite-scroll","",3,"ngClass","infiniteScrollDistance","scrollWindow","scrolled",4,"ngIf"],[4,"ngIf"],["style","text-align: center;",4,"ngIf"],["class","pt-1",4,"ngIf"],["class","container d-flex pt-3 flex-column",4,"ngIf"],["mat-icon-button","",1,"view-button-inactive",2,"margin-left","1%",3,"matTooltip","click"],["class","iconButton",4,"ngIf"],["class","spinner-align","diameter","18",4,"ngIf"],[1,"iconButton"],["diameter","18",1,"spinner-align"],["mat-icon-button","","matTooltip","Clear Status Filter",1,"view-button-inactive",3,"click"],[1,"row","d-flex","pt-1"],[1,"col-4",3,"matTooltip"],["appearance","outline",1,"cost-center","slide-in-top",2,"width","100% !important"],["type","text","placeholder","Search Cost Centre","aria-label","Number","matInput","",1,"custom-ts-search-input",3,"ngModel","matAutocomplete","disabled","ngModelChange","ngModelOptions"],["auto","matAutocomplete"],[3,"value","matTooltip",4,"ngFor","ngForOf"],["class","col-4 pt-2",4,"ngIf"],["mat-icon-button","","matTooltip","Remove Cost Center","class","view-button-inactive",3,"click",4,"ngIf"],[3,"value","matTooltip"],[1,"col-4","pt-2"],[1,"name","pl-3","pr-2"],[1,"costcenter","pl-2",3,"matTooltip"],["mat-icon-button","","matTooltip","Remove Cost Center",1,"view-button-inactive",3,"click"],["appearance","outline",1,"custom-ts-search","slide-in-top"],["type","text","placeholder","Search Associates","aria-label","Number","matInput","",1,"custom-ts-search-input",3,"ngModel","matAutocomplete","disabled","ngModelChange","ngModelOptions"],["associateAuto","matAutocomplete"],["class","custom-ts-select-option",3,"value",4,"ngFor","ngForOf"],["class","col-4 pt-2 justify-content-center",4,"ngIf"],["mat-icon-button","","matTooltip","Remove Associate","class","view-button-inactive",3,"click",4,"ngIf"],[1,"custom-ts-select-option",3,"value"],[1,"col-4","pt-2","justify-content-center"],[1,"row","my-auto"],["imgWidth","30px","imgHeight","30px",3,"id"],[1,"col-10","pt-1","name","justify-content-center",3,"matTooltip"],["mat-icon-button","","matTooltip","Remove Associate",1,"view-button-inactive",3,"click"],[1,"col-12","pl-4","mediumSubtleText"],[1,"row","pt-1"],[1,"col-12","pl-0"],["class","col-2 pb-2 pr-0",4,"ngFor","ngForOf"],[1,"col-2","pb-2","pr-0"],[1,"card","status-card","slide-in-top",3,"ngClass"],[1,"card-body","p-2",3,"click"],[1,"row","d-flex","pt-3","pb-1"],[1,"mx-auto","headingBold"],[1,"row","d-flex","pt-2","justify-content-center"],[1,"status-circular",3,"ngClass"],[1,"pl-3","value"],[1,"row","pt-2"],[1,"col-12"],[1,"pl-2"],["ngDefaultControl","",3,"ngModel","ngModelChange","change"],["mat-icon-button","","class","icon-tray-button-visible pt-0 ml-3",3,"matTooltip",4,"ngIf"],["class","spinner-align pt-0 ml-3","matTooltip","Notifying","diameter","18",4,"ngIf"],["mat-icon-button","",1,"icon-tray-button-visible","pt-0","ml-3",3,"matTooltip"],[1,"iconButton",2,"line-height","17px !important",3,"click"],["matTooltip","Notifying","diameter","18",1,"spinner-align","pt-0","ml-3"],["infinite-scroll","",1,"pt-1",3,"ngClass","infiniteScrollDistance","scrollWindow","scrolled"],[4,"ngFor","ngForOf"],[1,"col-12","pl-2","pr-2"],[1,"card","listcard",2,"border-left","3px solid #9a9a9a","padding","1px",3,"ngClass"],[1,"card-body",2,"padding","2px !important"],[1,"row","card-details","p-0"],[1,"col-3","pl-0","d-flex","name"],[1,"mt-2","pl-2"],["ngDefaultControl","",3,"ngModel","ngModelChange",4,"ngIf"],[1,"pl-4","pt-1",3,"matTooltip"],["imgWidth","28px","imgHeight","28px",3,"id"],[1,"name","pl-3"],[3,"matTooltip",4,"ngIf"],["matTooltip","KEBS-BOT NOT ACTIVE",4,"ngIf"],[1,"col-1","d-flex","pl-0","pr-0","pt-1"],[1,"location","ml-2",3,"matTooltip"],[1,"col-2","d-flex","costcenter","pl-5"],[1,"pt-1","mr-2",2,"font-weight","500 !important"],[1,"col-4","details","costcenter","pt-1"],[1,"col-2","date","pl-0","pl-4","pr-4","pt-1","d-flex",2,"text-align","center"],[1,"pr-2"],["ngDefaultControl","",3,"ngModel","ngModelChange"],[3,"matTooltip"],["matTooltip","KEBS-BOT NOT ACTIVE"],[1,"status-dot-days","ml-1",3,"ngClass"],[1,"row","pb-1"],[1,"col-8","pl-0","pr-0"],[1,"col-2","pl-1","pr-1",2,"font-weight","500 !important"],[1,"col-10","costcenter",3,"matTooltip"],[1,"col-4","pl-0","pr-0","location",3,"matTooltip"],["class","card listcard slide-in-top",4,"ngIf"],[1,"card","listcard","slide-in-top"],[1,"row","p-0"],[1,"col-12","ml-2","pt-2","name"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],["class","col-3 pl-0",4,"ngFor","ngForOf"],[1,"row","pt-2","pb-2"],[1,"col-3","pl-0"],[1,"row","pb-2"],[1,"col-8","costcenter","pl-2"],[1,"col-4","name","pl-2"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/assembleteam.png","height","240","width","280",1,"mt-4"],[2,"text-align","center"],["src","https://assets.kebs.app/images/choose_cost_centre.png","height","220","width","250",1,"mt-4"],[1,"pt-1"],[1,"card","loadcard",2,"border-left","3px solid #9a9a9a","padding","1px"],[1,"col-3","pl-0","pl-4","pr-4"],[1,"col-1","pl-0","pr-0","pt-1"],[1,"col-2","pl-0","pl-4","pr-4","pt-1"],[1,"container","d-flex","pt-3","flex-column"],[1,"row","justify-content-center"],["diameter","30","matTooltip","Fetching data"]],template:function(t,e){if(1&t){const t=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275elementStart"](1,"div",1),u["\u0275\u0275elementStart"](2,"div",2),u["\u0275\u0275elementStart"](3,"button",3),u["\u0275\u0275listener"]("click",(function(){return e.getDefaultView()})),u["\u0275\u0275text"](4," All "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"button",4),u["\u0275\u0275listener"]("click",(function(){return e.getCCView()})),u["\u0275\u0275text"](6," Cost center "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](7,"button",4),u["\u0275\u0275listener"]("click",(function(){return e.getIndividualView()})),u["\u0275\u0275text"](8," Individual "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](9,"div",5),u["\u0275\u0275elementStart"](10,"div",6),u["\u0275\u0275elementStart"](11,"div",7),u["\u0275\u0275elementStart"](12,"span",8),u["\u0275\u0275text"](13,"Duration : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](14,"button",4),u["\u0275\u0275listener"]("click",(function(){return e.selectThisMonth()})),u["\u0275\u0275text"](15," This Month "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](16,"button",4),u["\u0275\u0275listener"]("click",(function(){return e.selectPreviousMonth()})),u["\u0275\u0275text"](17," Prev Month "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](18,"div",9),u["\u0275\u0275elementStart"](19,"div",10),u["\u0275\u0275elementStart"](20,"div",11),u["\u0275\u0275elementStart"](21,"button",12),u["\u0275\u0275elementStart"](22,"mat-icon",13),u["\u0275\u0275listener"]("click",(function(){return e.goToPreviousMonth()})),u["\u0275\u0275text"](23,"keyboard_arrow_left"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](24,"div",14),u["\u0275\u0275elementStart"](25,"mat-form-field",15),u["\u0275\u0275element"](26,"input",16),u["\u0275\u0275element"](27,"mat-datepicker-toggle",17),u["\u0275\u0275elementStart"](28,"mat-datepicker",18,19),u["\u0275\u0275listener"]("yearSelected",(function(t){return e.selectYear(t)}))("monthSelected",(function(n){u["\u0275\u0275restoreView"](t);const s=u["\u0275\u0275reference"](29);return e.selectMonth(n,s)})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](30,"div",20),u["\u0275\u0275elementStart"](31,"button",21),u["\u0275\u0275elementStart"](32,"mat-icon",13),u["\u0275\u0275listener"]("click",(function(){return e.goToNextMonth()})),u["\u0275\u0275text"](33,"keyboard_arrow_right"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](34,"div",22),u["\u0275\u0275elementStart"](35,"div",23),u["\u0275\u0275template"](36,L,3,3,"button",24),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](37,"div",25),u["\u0275\u0275template"](38,R,3,0,"button",26),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](39,"div",22),u["\u0275\u0275elementStart"](40,"div",6),u["\u0275\u0275elementStart"](41,"div",27),u["\u0275\u0275elementStart"](42,"mat-form-field",28),u["\u0275\u0275elementStart"](43,"mat-label"),u["\u0275\u0275text"](44,"Select the Week"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](45,"mat-select",29),u["\u0275\u0275listener"]("ngModelChange",(function(t){return e.selected=t}))("ngModelChange",(function(){return e.goToWeekFilter()})),u["\u0275\u0275elementStart"](46,"mat-option"),u["\u0275\u0275text"](47,"None"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](48,"mat-option",30),u["\u0275\u0275text"](49,"Week 1"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](50,"mat-option",31),u["\u0275\u0275text"](51,"Week 2"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](52,"mat-option",32),u["\u0275\u0275text"](53,"Week 3"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](54,"mat-option",33),u["\u0275\u0275text"](55,"Week 4"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](56,"mat-option",34),u["\u0275\u0275text"](57,"Week 5"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](58,B,10,7,"div",35),u["\u0275\u0275template"](59,H,10,7,"div",35),u["\u0275\u0275template"](60,z,3,0,"div",36),u["\u0275\u0275template"](61,X,4,1,"div",37),u["\u0275\u0275template"](62,Q,6,3,"div",38),u["\u0275\u0275template"](63,ot,2,4,"div",39),u["\u0275\u0275template"](64,lt,3,1,"div",38),u["\u0275\u0275template"](65,dt,6,1,"div",40),u["\u0275\u0275template"](66,ut,3,2,"div",41),u["\u0275\u0275template"](67,gt,2,1,"div",42),u["\u0275\u0275template"](68,ft,3,0,"div",43),u["\u0275\u0275elementEnd"]()}if(2&t){const t=u["\u0275\u0275reference"](29);u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngClass","All"==e.currentView?"btn-active":"btn-not-active"),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngClass","costCentre"==e.currentView?"btn-active":"btn-not-active"),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngClass","individual"==e.currentView?"btn-active":"btn-not-active"),u["\u0275\u0275advance"](7),u["\u0275\u0275property"]("ngClass","current"==e.selectedMonth?"btn-active":"btn-not-active"),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngClass","previous"==e.selectedMonth?"btn-active":"btn-not-active"),u["\u0275\u0275advance"](10),u["\u0275\u0275property"]("matDatepicker",t)("formControl",e.monthYearDate),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",t),u["\u0275\u0275advance"](9),u["\u0275\u0275property"]("ngIf",e.isCurrentUserAdmin&&"All"==e.currentView&&"Default"!=e.currentStatus&&e.associateData.length>0&&"current"==e.selectedMonth),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",e.isStatusFilterApplied&&"All"==e.currentView),u["\u0275\u0275advance"](7),u["\u0275\u0275property"]("ngModel",e.selected),u["\u0275\u0275advance"](13),u["\u0275\u0275property"]("ngIf","costCentre"==e.currentView),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf","individual"==e.currentView),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf","individual"==e.currentView&&""!=e.selectedAssociateData.oid),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf","individual"!=e.currentView),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf","All"==e.currentView&&"A"!=e.currentStatus&&"Default"!=e.currentStatus&&e.associateData.length>0&&"current"==e.selectedMonth&&e.isCurrentUserAdmin),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.associateData.length>0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf","individual"==e.currentView&&""!=e.selectedAssociateData.oid),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.noData&&"costCentre"!=e.currentView||"individual"==e.currentView&&""==e.selectedAssociateData.oid),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf","costCentre"==e.currentView),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!e.noData&&0==e.associateData.length&&("All"==e.currentView||"costCentre"==e.currentView&&""!=e.costCentreData.cost_centre_description)),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.isMoreLoading)}},directives:[C.a,s.NgClass,b.a,M.a,x.c,_.b,E.g,i.e,i.v,i.k,E.i,x.i,E.f,s.NgIf,x.g,w.c,i.y,c.p,A.c,O.d,O.b,s.NgForOf,I.a,P.a,k.a,T],styles:[".timesheet-stats-styles[_ngcontent-%COMP%]   .mediumSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.timesheet-stats-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.timesheet-stats-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:31px;padding:0 8px;border-radius:30px;margin-right:6px!important;margin-bottom:3px;width:28%}.timesheet-stats-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.timesheet-stats-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-stats-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:424px;overflow:scroll}.timesheet-stats-styles[_ngcontent-%COMP%]   .infinite-scroll-auto[_ngcontent-%COMP%]{height:auto;overflow:scroll}.timesheet-stats-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.timesheet-stats-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-stats-styles[_ngcontent-%COMP%]   .ib13[_ngcontent-%COMP%]{font-size:14px;font-weight:400;text-align:center;color:#1a1a1a;display:inline}.timesheet-stats-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:20px}.timesheet-stats-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:15px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]{min-height:95px;transition:all .3s linear}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]:hover{cursor:pointer}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-card-clicked[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.timesheet-stats-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.timesheet-stats-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.timesheet-stats-styles[_ngcontent-%COMP%]   .loadcard[_ngcontent-%COMP%]{transition:all .25s linear;max-height:39px!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:hidden}.timesheet-stats-styles[_ngcontent-%COMP%]   .icon-tray-button-visible[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:17px!important;visibility:visible!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.timesheet-stats-styles[_ngcontent-%COMP%]   .costcenter[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-draft-side[_ngcontent-%COMP%]{border-left-color:grey!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-submitted-side[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-approved-side[_ngcontent-%COMP%]{border-left-color:#009432!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-rejected-side[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.timesheet-stats-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-dot-days[_ngcontent-%COMP%]{height:25px;width:25px;font-weight:400;line-height:25px;border-radius:50%;display:inline-block;vertical-align:middle;font-size:13px;text-align:center;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%]{border:1px solid #6ab04c;background:#6ab04c}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%]{font-size:12px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%]{border:1px solid #eb3b5a;background:#eb3b5a}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-empty-day[_ngcontent-%COMP%]{font-size:12px;color:#1a1a1a!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);border:1px solid #c7c4c4}.timesheet-stats-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}.timesheet-stats-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.timesheet-stats-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%]{border:1px solid grey;padding:5px 0!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{text-align:center;font-size:13px;color:#1a1a1a}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-search-input[_ngcontent-%COMP%]{padding:12px 5px 8px;cursor:pointer}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]{width:100%;text-align:center!important;font-size:14px;color:#1a1a1a}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{padding:5px!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{font-size:14px!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:0!important;border-top:0!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.timesheet-stats-styles[_ngcontent-%COMP%]     .mat-form-field-underline{display:none}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-trigger{padding:2px 0!important}"]}),t})()}];let yt=(()=>{class t{}return t.\u0275mod=u["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.k.forChild(St)],a.k]}),t})();var Dt=n("Xi0T");let Ct=(()=>{class t{}return t.\u0275mod=u["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[s.CommonModule,yt,C.b,b.b,M.b,x.e,_.c,E.h,k.b,O.c,P.b,i.p,i.E,Y.a,Dt.a,A.b,w.d]]}),t})()},hJL4:function(t,e,n){"use strict";n.d(e,"a",(function(){return p}));var s=n("mrSG"),a=n("XNiG"),o=n("xG9w"),i=n("fXoL"),r=n("tk/3"),c=n("LcQX"),l=n("XXEo"),d=n("flaP");let p=(()=>{class t{constructor(t,e,n,s){this.http=t,this.UtilityService=e,this.loginService=n,this.roleService=s,this.msg=new a.b,this.taskStatusColor=[],this.approveRequest=(t,e)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:t,approverOid:e}),this.rejectRequest=(t,e)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:t,approverOid:e}),this.getTaskStatusColor()}getAllRequestsOfUser(t,e,n,s,a,o,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:t,statusId:e,statusCode:n,objectIds:s,skip:a,limit:o,filterConfig:i,orgIds:r})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(t,e,n,s,a,o,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:t,statusId:e,statusCode:n,objectIds:s,skip:a,limit:o,filterConfig:i,orgIds:r})}getRequestsForAwaitingApproval(t,e,n,s){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:t,skip:e,limit:n,filterConfig:s})}getObjectIdsBasedOnOid(t){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:t})}getStatusCountBasedOnOid(t,e,n,s){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:t,objectIds:e,dataTypeArray:n,filterConfig:s,orgIds:a})}getHeaderCount(t){return this.http.post("/api/isa/request/getHeaderCount",{oid:t})}getRequestBasedOnStatus(t,e){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:t,status:e})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(t){this.msg.next(t)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((t,e)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(e=>(this.taskStatusColor=e,t(e)),t=>{})})}getAllRequestsForRmg(t){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:t})}getTotalForRmg(t){return new Promise((e,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:t}).subscribe(t=>e(t),t=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(t){return new Promise((e,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",t).subscribe(t=>e(t.data),t=>{n(t)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(t){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}saveAndApproveRequest(t){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",t)}getVisibilityMatrix(t){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getISAAttachmentFromS3(t){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(t){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:t})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(t){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:t})}getISACustomReport(t){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:t})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(t,e,n,a,i,r,c){return Object(s.c)(this,void 0,void 0,(function*(){let s;s=r&&r.length>1&&(yield this.getManpowerCostByOId(r,n,i,2))||(yield this.getManpowerCostBasedOnPosition(t,e,n,i,c));let l=yield this.getNonManpowerCost(e,n,a,i,2),d=yield this.getAllocatedCost(),p=0;p=(s?s.cost:0)+l.length>0?o.reduce(o.pluck(l,"cost"),(t,e)=>t+e,0):0;let m=d.length>0?o.reduce(o.pluck(d,"percentage"),(t,e)=>t+e,0):0;return{cost:p,currency:s&&s.currency_code?s.currency_code:"",manpowerCost:s,nonManpowerCost:l,allocatedCost:d,allocatedCostValue:p*(m/100)}}))}getManpowerCostBasedOnPosition(t,e,n,s,a){return new Promise((o,i)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:t,nationalityId:e,locationGroupId:n,unit:s,position:a}).subscribe(t=>o(t),t=>(console.log(t),i(t)))})}getNonManpowerCost(t,e,n,s,a){return new Promise((o,i)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:t,locationGroupId:e,locationId:n,unit:s,currency_id:a}).subscribe(t=>o(t),t=>(console.log(t),i(t)))})}getAllocatedCost(){return new Promise((t,e)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(e=>t(e),t=>(console.log(t),e(t)))})}getManpowerCostByOId(t,e,n,s){return new Promise((a,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:t,location_group_id:e,unit_id:n,currency_id:s}).subscribe(t=>a(t),t=>(console.log(t),o(t)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(t,e){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:t,docID:e})}getVendorList(t,e){return this.http.post("/api/isa/request/getVendorList",{documentID:t,limitval:e})}removeVendor(t,e){return this.http.post("/api/isa/request/removeVendors",{vendorOid:t,documentID:e})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275inject"](r.c),i["\u0275\u0275inject"](c.a),i["\u0275\u0275inject"](l.a),i["\u0275\u0275inject"](d.a))},t.\u0275prov=i["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},ucYs:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var s=n("mrSG"),a=n("xG9w"),o=n("fXoL"),i=n("tk/3"),r=n("BVzC");let c=(()=>{class t{constructor(t,e){this.http=t,this.errorService=e,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(t=>{this.workflowStatusList=t.data},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server While Getting Workflow Status List",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getWorkflowProperties(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:t}).subscribe(t=>e(t),t=>n(t))})}getWorkflowPropertiesByWorkflowId(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:t}).subscribe(t=>e(t),t=>n(t))})}getApproversHierarchy(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",t).subscribe(t=>e(t),t=>n(t))})}createWorkflowItems(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",t).subscribe(t=>e(t),t=>n(t))})}getWorkflowDetails(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:t}).subscribe(t=>e(t),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting workflow details",t&&t.params?t.params:t&&t.error?t.error.params:{})})})}formatApproversHierarchy(t,e){return Object(s.c)(this,void 0,void 0,(function*(){0==t.length&&e.cc0&&e.cc0.appr0&&e.cc0.appr0.length>0&&t.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<t.length;n++){let s=[],o=a.keys(e["cc"+n]);for(let a=0;a<o.length;a++)for(let i=0;i<e["cc"+n][o[a]].length;i++){let r={name:e["cc"+n][o[a]][i].DELEGATE_NAME,oid:e["cc"+n][o[a]][i].DELEGATE_OID,level:a+1,designation:e["cc"+n][o[a]][i].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+n][o[a]][i].IS_DELEGATED,role:e["cc"+n][o[a]][i].DELEGATE_ROLE_NAME};if(1==e["cc"+n][o[a]][i].IS_DELEGATED&&(r.delegated_by={name:e["cc"+n][o[a]][i].APPROVER_NAME,oid:e["cc"+n][o[a]][i].APPROVER_OID,level:a+1,designation:e["cc"+n][o[a]][i].APPROVER_DESIGNATION_NAME}),s.push(r),n==t.length-1&&a==o.length-1&&i==e["cc"+n][o[a]].length-1)return s}}}))}storeComments(t,e,n){return new Promise((s,a)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:t,newComments:e,commentor:n}).subscribe(t=>s(t),t=>(this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server Error while Updating Workflow Comments",t&&t.params?t.params:t&&t.error?t.error.params:{}),a(t)))})}updateWorkflowItems(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",t).subscribe(t=>e(t),t=>(console.log(t),n(t)))})}formatApproversHierarchyForOpportunityApprovalActivity(t){return Object(s.c)(this,void 0,void 0,(function*(){for(let e=0;e<1;e++){let n=[],s=a.keys(t["cc"+e]);for(let a=0;a<s.length;a++)for(let o=0;o<t["cc"+e][s[a]].length;o++){let i={name:t["cc"+e][s[a]][o].DELEGATE_NAME,oid:t["cc"+e][s[a]][o].DELEGATE_OID,level:t["cc"+e][s[a]][o].APPROVAL_ORDER,designation:t["cc"+e][s[a]][o].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+e][s[a]][o].IS_DELEGATED};if(1==t["cc"+e][s[a]][o].IS_DELEGATED&&(i.delegated_by={name:t["cc"+e][s[a]][o].APPROVER_NAME,oid:t["cc"+e][s[a]][o].APPROVER_OID,level:t["cc"+e][s[a]][o].APPROVAL_ORDER,designation:t["cc"+e][s[a]][o].APPROVER_DESIGNATION_NAME}),n.push(i),a==s.length-1&&o==t["cc"+e][s[a]].length-1)return n}}}))}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275inject"](i.c),o["\u0275\u0275inject"](r.a))},t.\u0275prov=o["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);