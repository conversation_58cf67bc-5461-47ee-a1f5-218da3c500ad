(window.webpackJsonp=window.webpackJsonp||[]).push([[848],{"2Pgj":function(t,e,n){"use strict";var r;n.d(e,"a",(function(){return o}));var a=new Uint8Array(16);function o(){if(!r&&!(r="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return r(a)}},"4USb":function(t,e,n){"use strict";n.r(e),n.d(e,"v1",(function(){return l})),n.d(e,"v3",(function(){return x})),n.d(e,"v4",(function(){return b.a})),n.d(e,"v5",(function(){return M})),n.d(e,"NIL",(function(){return O})),n.d(e,"version",(function(){return R})),n.d(e,"validate",(function(){return s.a})),n.d(e,"stringify",(function(){return i.a})),n.d(e,"parse",(function(){return p}));var r,a,o=n("2Pgj"),i=n("WM9j"),d=0,c=0,l=function(t,e,n){var l=e&&n||0,s=e||new Array(16),p=(t=t||{}).node||r,g=void 0!==t.clockseq?t.clockseq:a;if(null==p||null==g){var u=t.random||(t.rng||o.a)();null==p&&(p=r=[1|u[0],u[1],u[2],u[3],u[4],u[5]]),null==g&&(g=a=16383&(u[6]<<8|u[7]))}var f=void 0!==t.msecs?t.msecs:Date.now(),h=void 0!==t.nsecs?t.nsecs:c+1,m=f-d+(h-c)/1e4;if(m<0&&void 0===t.clockseq&&(g=g+1&16383),(m<0||f>d)&&void 0===t.nsecs&&(h=0),h>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");d=f,c=h,a=g;var C=(1e4*(268435455&(f+=122192928e5))+h)%4294967296;s[l++]=C>>>24&255,s[l++]=C>>>16&255,s[l++]=C>>>8&255,s[l++]=255&C;var v=f/4294967296*1e4&268435455;s[l++]=v>>>8&255,s[l++]=255&v,s[l++]=v>>>24&15|16,s[l++]=v>>>16&255,s[l++]=g>>>8|128,s[l++]=255&g;for(var y=0;y<6;++y)s[l+y]=p[y];return e||Object(i.a)(s)},s=n("BuRe"),p=function(t){if(!Object(s.a)(t))throw TypeError("Invalid UUID");var e,n=new Uint8Array(16);return n[0]=(e=parseInt(t.slice(0,8),16))>>>24,n[1]=e>>>16&255,n[2]=e>>>8&255,n[3]=255&e,n[4]=(e=parseInt(t.slice(9,13),16))>>>8,n[5]=255&e,n[6]=(e=parseInt(t.slice(14,18),16))>>>8,n[7]=255&e,n[8]=(e=parseInt(t.slice(19,23),16))>>>8,n[9]=255&e,n[10]=(e=parseInt(t.slice(24,36),16))/1099511627776&255,n[11]=e/4294967296&255,n[12]=e>>>24&255,n[13]=e>>>16&255,n[14]=e>>>8&255,n[15]=255&e,n},g=function(t,e,n){function r(t,r,a,o){if("string"==typeof t&&(t=function(t){t=unescape(encodeURIComponent(t));for(var e=[],n=0;n<t.length;++n)e.push(t.charCodeAt(n));return e}(t)),"string"==typeof r&&(r=p(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var d=new Uint8Array(16+t.length);if(d.set(r),d.set(t,r.length),(d=n(d))[6]=15&d[6]|e,d[8]=63&d[8]|128,a){o=o||0;for(var c=0;c<16;++c)a[o+c]=d[c];return a}return Object(i.a)(d)}try{r.name=t}catch(a){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r};function u(t){return 14+(t+64>>>9<<4)+1}function f(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}function h(t,e,n,r,a,o){return f((i=f(f(e,t),f(r,o)))<<(d=a)|i>>>32-d,n);var i,d}function m(t,e,n,r,a,o,i){return h(e&n|~e&r,t,e,a,o,i)}function C(t,e,n,r,a,o,i){return h(e&r|n&~r,t,e,a,o,i)}function v(t,e,n,r,a,o,i){return h(e^n^r,t,e,a,o,i)}function y(t,e,n,r,a,o,i){return h(n^(e|~r),t,e,a,o,i)}var x=g("v3",48,(function(t){if("string"==typeof t){var e=unescape(encodeURIComponent(t));t=new Uint8Array(e.length);for(var n=0;n<e.length;++n)t[n]=e.charCodeAt(n)}return function(t){for(var e=[],n=32*t.length,r="0123456789abcdef",a=0;a<n;a+=8){var o=t[a>>5]>>>a%32&255,i=parseInt(r.charAt(o>>>4&15)+r.charAt(15&o),16);e.push(i)}return e}(function(t,e){t[e>>5]|=128<<e%32,t[u(e)-1]=e;for(var n=1732584193,r=-271733879,a=-1732584194,o=271733878,i=0;i<t.length;i+=16){var d=n,c=r,l=a,s=o;n=m(n,r,a,o,t[i],7,-680876936),o=m(o,n,r,a,t[i+1],12,-389564586),a=m(a,o,n,r,t[i+2],17,606105819),r=m(r,a,o,n,t[i+3],22,-1044525330),n=m(n,r,a,o,t[i+4],7,-176418897),o=m(o,n,r,a,t[i+5],12,1200080426),a=m(a,o,n,r,t[i+6],17,-1473231341),r=m(r,a,o,n,t[i+7],22,-45705983),n=m(n,r,a,o,t[i+8],7,1770035416),o=m(o,n,r,a,t[i+9],12,-1958414417),a=m(a,o,n,r,t[i+10],17,-42063),r=m(r,a,o,n,t[i+11],22,-1990404162),n=m(n,r,a,o,t[i+12],7,1804603682),o=m(o,n,r,a,t[i+13],12,-40341101),a=m(a,o,n,r,t[i+14],17,-1502002290),n=C(n,r=m(r,a,o,n,t[i+15],22,1236535329),a,o,t[i+1],5,-165796510),o=C(o,n,r,a,t[i+6],9,-1069501632),a=C(a,o,n,r,t[i+11],14,643717713),r=C(r,a,o,n,t[i],20,-373897302),n=C(n,r,a,o,t[i+5],5,-701558691),o=C(o,n,r,a,t[i+10],9,38016083),a=C(a,o,n,r,t[i+15],14,-660478335),r=C(r,a,o,n,t[i+4],20,-405537848),n=C(n,r,a,o,t[i+9],5,568446438),o=C(o,n,r,a,t[i+14],9,-1019803690),a=C(a,o,n,r,t[i+3],14,-187363961),r=C(r,a,o,n,t[i+8],20,1163531501),n=C(n,r,a,o,t[i+13],5,-1444681467),o=C(o,n,r,a,t[i+2],9,-51403784),a=C(a,o,n,r,t[i+7],14,1735328473),n=v(n,r=C(r,a,o,n,t[i+12],20,-1926607734),a,o,t[i+5],4,-378558),o=v(o,n,r,a,t[i+8],11,-2022574463),a=v(a,o,n,r,t[i+11],16,1839030562),r=v(r,a,o,n,t[i+14],23,-35309556),n=v(n,r,a,o,t[i+1],4,-1530992060),o=v(o,n,r,a,t[i+4],11,1272893353),a=v(a,o,n,r,t[i+7],16,-155497632),r=v(r,a,o,n,t[i+10],23,-1094730640),n=v(n,r,a,o,t[i+13],4,681279174),o=v(o,n,r,a,t[i],11,-358537222),a=v(a,o,n,r,t[i+3],16,-722521979),r=v(r,a,o,n,t[i+6],23,76029189),n=v(n,r,a,o,t[i+9],4,-640364487),o=v(o,n,r,a,t[i+12],11,-421815835),a=v(a,o,n,r,t[i+15],16,530742520),n=y(n,r=v(r,a,o,n,t[i+2],23,-995338651),a,o,t[i],6,-198630844),o=y(o,n,r,a,t[i+7],10,1126891415),a=y(a,o,n,r,t[i+14],15,-1416354905),r=y(r,a,o,n,t[i+5],21,-57434055),n=y(n,r,a,o,t[i+12],6,1700485571),o=y(o,n,r,a,t[i+3],10,-1894986606),a=y(a,o,n,r,t[i+10],15,-1051523),r=y(r,a,o,n,t[i+1],21,-2054922799),n=y(n,r,a,o,t[i+8],6,1873313359),o=y(o,n,r,a,t[i+15],10,-30611744),a=y(a,o,n,r,t[i+6],15,-1560198380),r=y(r,a,o,n,t[i+13],21,1309151649),n=y(n,r,a,o,t[i+4],6,-145523070),o=y(o,n,r,a,t[i+11],10,-1120210379),a=y(a,o,n,r,t[i+2],15,718787259),r=y(r,a,o,n,t[i+9],21,-343485551),n=f(n,d),r=f(r,c),a=f(a,l),o=f(o,s)}return[n,r,a,o]}(function(t){if(0===t.length)return[];for(var e=8*t.length,n=new Uint32Array(u(e)),r=0;r<e;r+=8)n[r>>5]|=(255&t[r/8])<<r%32;return n}(t),8*t.length))})),b=n("7Cbv");function w(t,e,n,r){switch(t){case 0:return e&n^~e&r;case 1:return e^n^r;case 2:return e&n^e&r^n&r;case 3:return e^n^r}}function _(t,e){return t<<e|t>>>32-e}var M=g("v5",80,(function(t){var e=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof t){var r=unescape(encodeURIComponent(t));t=[];for(var a=0;a<r.length;++a)t.push(r.charCodeAt(a))}else Array.isArray(t)||(t=Array.prototype.slice.call(t));t.push(128);for(var o=Math.ceil((t.length/4+2)/16),i=new Array(o),d=0;d<o;++d){for(var c=new Uint32Array(16),l=0;l<16;++l)c[l]=t[64*d+4*l]<<24|t[64*d+4*l+1]<<16|t[64*d+4*l+2]<<8|t[64*d+4*l+3];i[d]=c}i[o-1][14]=8*(t.length-1)/Math.pow(2,32),i[o-1][14]=Math.floor(i[o-1][14]),i[o-1][15]=8*(t.length-1)&4294967295;for(var s=0;s<o;++s){for(var p=new Uint32Array(80),g=0;g<16;++g)p[g]=i[s][g];for(var u=16;u<80;++u)p[u]=_(p[u-3]^p[u-8]^p[u-14]^p[u-16],1);for(var f=n[0],h=n[1],m=n[2],C=n[3],v=n[4],y=0;y<80;++y){var x=Math.floor(y/20),b=_(f,5)+w(x,h,m,C)+v+e[x]+p[y]>>>0;v=C,C=m,m=_(h,30)>>>0,h=f,f=b}n[0]=n[0]+f>>>0,n[1]=n[1]+h>>>0,n[2]=n[2]+m>>>0,n[3]=n[3]+C>>>0,n[4]=n[4]+v>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),O="00000000-0000-0000-0000-000000000000",R=function(t){if(!Object(s.a)(t))throw TypeError("Invalid UUID");return parseInt(t.substr(14,1),16)}},"7Cbv":function(t,e,n){"use strict";var r=n("2Pgj"),a=n("WM9j");e.a=function(t,e,n){var o=(t=t||{}).random||(t.rng||r.a)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,e){n=n||0;for(var i=0;i<16;++i)e[n+i]=o[i];return e}return Object(a.a)(o)}},BuRe:function(t,e,n){"use strict";var r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;e.a=function(t){return"string"==typeof t&&r.test(t)}},W3to:function(t,e,n){"use strict";n.r(e),n.d(e,"PmChooseRateCardPositionComponent",(function(){return w}));var r=n("mrSG"),a=n("xG9w"),o=n("0IaG"),i=n("fXoL"),d=n("y6fH"),c=n("5+WD"),l=n("NFeN"),s=n("ofXK"),p=n("ZzPI"),g=n("6t9p"),u=n("bTqV"),f=n("R898");function h(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"dxi-column",17),i["\u0275\u0275element"](1,"dxo-lookup",18),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275property"]("dataField",t.field_key)("caption",t.field_label),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("dataSource",t.master_data)}}function m(t,e){if(1&t&&i["\u0275\u0275element"](0,"dxi-column",17),2&t){const t=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275property"]("dataField",t.field_key)("caption",t.field_label)}}function C(t,e){if(1&t&&i["\u0275\u0275element"](0,"dxi-column",17),2&t){const t=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275property"]("dataField",t.field_key)("caption",t.field_label)}}function v(t,e){if(1&t&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,h,2,3,"dxi-column",16),i["\u0275\u0275template"](2,m,1,2,"dxi-column",16),i["\u0275\u0275template"](3,C,1,2,"dxi-column",16),i["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.has_master_data),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.has_master_data&&("start_date"==t.field_key||"end_date"==t.field_key||"updated_on"==t.field_key)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.has_master_data&&!("start_date"==t.field_key||"end_date"==t.field_key||"updated_on"==t.field_key))}}function y(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",8),i["\u0275\u0275elementStart"](1,"dx-data-grid",9),i["\u0275\u0275listener"]("onRowClick",(function(e){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().selectedRow(e)})),i["\u0275\u0275element"](2,"dxo-sorting",10),i["\u0275\u0275element"](3,"dxo-filter-row",11),i["\u0275\u0275element"](4,"dxo-header-filter",12),i["\u0275\u0275element"](5,"dxo-selection",13),i["\u0275\u0275element"](6,"dxo-scrolling",14),i["\u0275\u0275template"](7,v,4,3,"ng-container",15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("dataSource",t.rateCardDetails)("hoverStateEnabled",!0)("allowColumnReordering",!0)("showBorders",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",t.columnResizingMode)("columnAutoWidth",!0)("dateSerializationFormat",t.yyyy-t.MM-t.dd)("selectedRowKeys",t.selectedRows),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("visible",!0)("applyFilter",!0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("visible",!0)("allowSearch",!0),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",t.rateCardConfiguration)}}function x(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",19),i["\u0275\u0275elementStart"](1,"div",20),i["\u0275\u0275elementStart"](2,"div",21),i["\u0275\u0275element"](3,"img",22),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",23),i["\u0275\u0275elementStart"](5,"div",24),i["\u0275\u0275text"](6,"No Rate Card found!"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("src",t.noDataImage?t.noDataImage:"https://assets.kebs.app/No-milestone-image.png",i["\u0275\u0275sanitizeUrl"])}}function b(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",25),i["\u0275\u0275element"](1,"div",26),i["\u0275\u0275element"](2,"div",27),i["\u0275\u0275element"](3,"div",26),i["\u0275\u0275elementStart"](4,"div",28),i["\u0275\u0275elementStart"](5,"button",29),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().onCloseClick()})),i["\u0275\u0275text"](6,"Cancel"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",26),i["\u0275\u0275elementStart"](8,"button",30),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().addRateCard()})),i["\u0275\u0275text"](9,"Save"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}n("4USb");let w=(()=>{class t{constructor(t,e,n,r){this.dialogData=t,this.toasterService=e,this.dialogRef=n,this.dialog=r,this.rateCardDetails=[],this.formConfig=[],this.entityList=[],this.divisionList=[],this.subDivisionList=[],this.positionList=[],this.currencyList=[],this.rateUnitList=[],this.locationList=[],this.noDataImage="https://assets.kebs.app/No-milestone-image.png",this.rateCardConfiguration=[],this.selectedRows=[]}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.formConfig=this.dialogData.formConfig;const t=a.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});if(this.button=t.length>0&&t[0].data.button_color?t[0].data.button_color:"#90ee90",this.noDataImage=t.length>0&&t[0].data.no_data_image?t[0].data.no_data_image:"https://assets.kebs.app/No-milestone-image.png",document.documentElement.style.setProperty("--teamButtonRate",this.button),this.fontStyle=t.length>0&&t[0].data.font_style?t[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--teamFontRate",this.fontStyle),console.log("Rate Card Position Component",this.dialogData),this.rateCardDetails=this.dialogData.rateCardDetails,this.selectedRateCardId=this.dialogData.selectedRateCardId,this.rateCardConfiguration=this.dialogData.rateCardConfiguration,this.selectedRows=[this.selectedRateCardId],this.selectRow=a.findWhere(this.rateCardDetails,{id:this.selectedRateCardId}),this.selectRow){let t=[],e=a.filter(this.rateCardDetails,e=>{if(e.id==this.selectedRateCardId)return!0;t.push(e)});this.rateCardDetails=[],this.rateCardDetails=[...e,...t]}console.log("Rate Card Configuration",this.rateCardConfiguration)}))}selectedRow(t){console.log("On Row Click Event",t,t.data),this.selectRow=t.data,console.log("Select Row",this.selectRow)}onCloseClick(){this.dialogRef.close({messType:"E"})}addRateCard(){this.selectRow?this.dialogRef.close({messType:"S",data:this.selectRow}):this.toasterService.showWarning("Select a rate card!",3e3)}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](o.a),i["\u0275\u0275directiveInject"](d.a),i["\u0275\u0275directiveInject"](o.h),i["\u0275\u0275directiveInject"](o.b))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-pm-choose-rate-card-position"]],decls:13,vars:9,consts:[[1,"add-choose-rate-card-styles"],["mat-dialog-title","","cdkDrag","","cdkDragRootElement",".cdk-overlay-pane","cdkDragHandle","",1,"row","header",2,"display","flex","cursor","all-scroll"],[1,"header-title-rate-card"],[2,"margin-left","356px","margin-top","15px"],[1,"close-button",3,"click"],["class","row pl-1 pr-1",4,"ngIf"],["class","row pl-1 pr-1","style","height: 362px; overflow-y: auto;",4,"ngIf"],["class","row footer-buttons",4,"ngIf"],[1,"row","pl-1","pr-1"],["id","gridContainer","keyExpr","id",2,"height","362px","width","1013px",3,"dataSource","hoverStateEnabled","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","columnAutoWidth","dateSerializationFormat","selectedRowKeys","onRowClick"],["mode","multiple"],[3,"visible","applyFilter"],[3,"visible","allowSearch"],["mode","single"],["mode","virtual"],[4,"ngFor","ngForOf"],[3,"dataField","caption",4,"ngIf"],[3,"dataField","caption"],["valueExpr","id","displayExpr","name",3,"dataSource"],[1,"row","pl-1","pr-1",2,"height","362px","overflow-y","auto"],[1,"content"],[1,"img-rate-card"],[1,"img-rate-card",3,"src"],[1,"inner-content","pt-1"],[1,"tittle","pt-2"],[1,"row","footer-buttons"],[1,"col-1"],[1,"col-8"],[1,"col-1",2,"margin-left","-3px"],["mat-button","",1,"button-back-rate-card",3,"click"],["mat-button","",1,"button-next-rate-card",3,"click"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275text"](3),i["\u0275\u0275pipe"](4,"checkLabel"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](5,"div"),i["\u0275\u0275elementStart"](6,"div",3),i["\u0275\u0275elementStart"](7,"mat-icon",4),i["\u0275\u0275listener"]("click",(function(){return e.onCloseClick()})),i["\u0275\u0275text"](8,"clear"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](9,"div"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](10,y,8,18,"div",5),i["\u0275\u0275template"](11,x,7,1,"div",6),i["\u0275\u0275template"](12,b,10,0,"div",7),i["\u0275\u0275elementEnd"]()),2&t&&(i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"](" ",i["\u0275\u0275pipeBind4"](4,4,"heading",e.formConfig,"rate-card","Add Rate Card")," "),i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("ngIf",e.rateCardDetails.length>0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==e.rateCardDetails.length),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.rateCardDetails.length>0))},directives:[c.a,c.b,l.a,s.NgIf,p.a,g.ae,g.dc,g.Cc,g.Od,g.Jd,s.NgForOf,g.g,g.Wc,u.a],pipes:[f.a],styles:[".add-choose-rate-card-styles[_ngcontent-%COMP%]{width:1020px;height:470px;top:-.5px;border-radius:4px;overflow:hidden}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:1016px;height:50px;border-radius:8px,8px,0,0;background:#fff}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:8.49px;height:8.49px;top:3.76px;left:3.76px;color:#7d838b;margin-left:360px;font-size:20px;cursor:pointer}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .header-title-rate-card[_ngcontent-%COMP%]{width:220px;height:24px;font-family:var(--teamFontRate)!important;font-size:16px;font-weight:600;line-height:24px;letter-spacing:0;text-align:left;color:#111434;margin-top:12px;margin-left:25px;white-space:nowrap}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{left:43%;position:fixed;top:31%}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .img-rate-card[_ngcontent-%COMP%]{vertical-align:middle;border-style:none;height:172px;width:178px}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .tittle[_ngcontent-%COMP%]{width:272px;height:24px;font-family:var(--milestoneFont)!important;font-size:14px;font-weight:700;line-height:24px;letter-spacing:.02em;text-align:center;color:#45546e;margin-left:-27px}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]{display:block;white-space:nowrap;border:1px solid #ddd}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]{position:sticky;top:0;background-color:#f2f2f2}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   .rate-card-header[_ngcontent-%COMP%]{border-bottom:1px solid #ddd;text-align:center;padding:8px;background-color:#e6e6d1}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   .dependency-row[_ngcontent-%COMP%]{text-align:left;padding:8px;border-bottom:1px solid #ddd}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   .highlightCard[_ngcontent-%COMP%]{background-color:var(--teamButtonRate)!important}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   .display-row-card[_ngcontent-%COMP%]{cursor:pointer;height:48px!important}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   .display-row-card[_ngcontent-%COMP%]:hover{background-color:#f3f3f3}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   .select-row[_ngcontent-%COMP%]{text-align:center;padding:8px;border:1px solid #ddd}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]     .mat-form-field-infix{height:38px!important;margin-top:-9px}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]     .mat-form-field{height:18px;margin-top:-12px!important}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .rate-card-table[_ngcontent-%COMP%]   .expand-icon[_ngcontent-%COMP%]{font-size:16px;padding-top:5px;cursor:pointer}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]{margin-top:110px;position:sticky;bottom:0;left:0;right:0;background-color:#f5f5f5;border-top:1px solid grey;height:56px}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .button-back-rate-card[_ngcontent-%COMP%]{align-items:center;margin-top:10px;font-weight:700;border-radius:5px;color:#000;margin-left:-13px}.add-choose-rate-card-styles[_ngcontent-%COMP%]   .button-next-rate-card[_ngcontent-%COMP%]{height:35px;margin-top:10px;margin-bottom:35px;background-color:var(--teamButtonRate)!important;border-radius:5px;color:#fff;margin-left:-18px;font-family:var(--teamFontRate)!important;font-size:14px;font-weight:700;letter-spacing:-.02em;text-align:left;display:flex;justify-content:center;align-items:center;width:64px}.add-choose-rate-card-styles[_ngcontent-%COMP%]     .dx-selection{background-color:#add8e6}"]}),t})()},WM9j:function(t,e,n){"use strict";for(var r=n("BuRe"),a=[],o=0;o<256;++o)a.push((o+256).toString(16).substr(1));e.a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(a[t[e+0]]+a[t[e+1]]+a[t[e+2]]+a[t[e+3]]+"-"+a[t[e+4]]+a[t[e+5]]+"-"+a[t[e+6]]+a[t[e+7]]+"-"+a[t[e+8]]+a[t[e+9]]+"-"+a[t[e+10]]+a[t[e+11]]+a[t[e+12]]+a[t[e+13]]+a[t[e+14]]+a[t[e+15]]).toLowerCase();if(!Object(r.a)(n))throw TypeError("Stringified UUID is invalid");return n}}}]);