(window.webpackJsonp=window.webpackJsonp||[]).push([[901],{"4ywr":function(t,e,n){var a=n("mop9"),i=n("54ld");function r(t){var e=this;e.set=a(),e.itemMap={},t&&t.forEach((function(t){e.add(t)}))}r.prototype.add=function(t){if("string"!=typeof t)return!1;var e=i(t);return!this.itemMap[e]&&!1!==this.set.add(e)&&(this.itemMap[e]=t,!0)},r.prototype.get=function(t,e){var n={distance:0,value:null};if("string"!=typeof t)return n;e=e||{};var a=this.set.get(i(t));return a?(a={distance:a[0][0],value:this.itemMap[a[0][1]]},e.min&&a.distance<e.min||void 0!==e.maxChanges&&a.value.length&&a.distance<1-e.maxChanges/a.value.length?n:a):n},t.exports=r},"54ld":function(t,e){t.exports=function(t){return t.replace(/[^\u0000-\u007E]/g,(function(t){return a[t]||t}))};for(var n=[{base:"A",letters:"A\u24b6\uff21\xc0\xc1\xc2\u1ea6\u1ea4\u1eaa\u1ea8\xc3\u0100\u0102\u1eb0\u1eae\u1eb4\u1eb2\u0226\u01e0\xc4\u01de\u1ea2\xc5\u01fa\u01cd\u0200\u0202\u1ea0\u1eac\u1eb6\u1e00\u0104\u023a\u2c6f"},{base:"AA",letters:"\ua732"},{base:"AE",letters:"\xc6\u01fc\u01e2"},{base:"AO",letters:"\ua734"},{base:"AU",letters:"\ua736"},{base:"AV",letters:"\ua738\ua73a"},{base:"AY",letters:"\ua73c"},{base:"B",letters:"B\u24b7\uff22\u1e02\u1e04\u1e06\u0243\u0182\u0181"},{base:"C",letters:"C\u24b8\uff23\u0106\u0108\u010a\u010c\xc7\u1e08\u0187\u023b\ua73e"},{base:"D",letters:"D\u24b9\uff24\u1e0a\u010e\u1e0c\u1e10\u1e12\u1e0e\u0110\u018b\u018a\u0189\ua779"},{base:"DZ",letters:"\u01f1\u01c4"},{base:"Dz",letters:"\u01f2\u01c5"},{base:"E",letters:"E\u24ba\uff25\xc8\xc9\xca\u1ec0\u1ebe\u1ec4\u1ec2\u1ebc\u0112\u1e14\u1e16\u0114\u0116\xcb\u1eba\u011a\u0204\u0206\u1eb8\u1ec6\u0228\u1e1c\u0118\u1e18\u1e1a\u0190\u018e"},{base:"F",letters:"F\u24bb\uff26\u1e1e\u0191\ua77b"},{base:"G",letters:"G\u24bc\uff27\u01f4\u011c\u1e20\u011e\u0120\u01e6\u0122\u01e4\u0193\ua7a0\ua77d\ua77e"},{base:"H",letters:"H\u24bd\uff28\u0124\u1e22\u1e26\u021e\u1e24\u1e28\u1e2a\u0126\u2c67\u2c75\ua78d"},{base:"I",letters:"I\u24be\uff29\xcc\xcd\xce\u0128\u012a\u012c\u0130\xcf\u1e2e\u1ec8\u01cf\u0208\u020a\u1eca\u012e\u1e2c\u0197"},{base:"J",letters:"J\u24bf\uff2a\u0134\u0248"},{base:"K",letters:"K\u24c0\uff2b\u1e30\u01e8\u1e32\u0136\u1e34\u0198\u2c69\ua740\ua742\ua744\ua7a2"},{base:"L",letters:"L\u24c1\uff2c\u013f\u0139\u013d\u1e36\u1e38\u013b\u1e3c\u1e3a\u0141\u023d\u2c62\u2c60\ua748\ua746\ua780"},{base:"LJ",letters:"\u01c7"},{base:"Lj",letters:"\u01c8"},{base:"M",letters:"M\u24c2\uff2d\u1e3e\u1e40\u1e42\u2c6e\u019c"},{base:"N",letters:"N\u24c3\uff2e\u01f8\u0143\xd1\u1e44\u0147\u1e46\u0145\u1e4a\u1e48\u0220\u019d\ua790\ua7a4"},{base:"NJ",letters:"\u01ca"},{base:"Nj",letters:"\u01cb"},{base:"O",letters:"O\u24c4\uff2f\xd2\xd3\xd4\u1ed2\u1ed0\u1ed6\u1ed4\xd5\u1e4c\u022c\u1e4e\u014c\u1e50\u1e52\u014e\u022e\u0230\xd6\u022a\u1ece\u0150\u01d1\u020c\u020e\u01a0\u1edc\u1eda\u1ee0\u1ede\u1ee2\u1ecc\u1ed8\u01ea\u01ec\xd8\u01fe\u0186\u019f\ua74a\ua74c"},{base:"OI",letters:"\u01a2"},{base:"OO",letters:"\ua74e"},{base:"OU",letters:"\u0222"},{base:"P",letters:"P\u24c5\uff30\u1e54\u1e56\u01a4\u2c63\ua750\ua752\ua754"},{base:"Q",letters:"Q\u24c6\uff31\ua756\ua758\u024a"},{base:"R",letters:"R\u24c7\uff32\u0154\u1e58\u0158\u0210\u0212\u1e5a\u1e5c\u0156\u1e5e\u024c\u2c64\ua75a\ua7a6\ua782"},{base:"S",letters:"S\u24c8\uff33\u1e9e\u015a\u1e64\u015c\u1e60\u0160\u1e66\u1e62\u1e68\u0218\u015e\u2c7e\ua7a8\ua784"},{base:"T",letters:"T\u24c9\uff34\u1e6a\u0164\u1e6c\u021a\u0162\u1e70\u1e6e\u0166\u01ac\u01ae\u023e\ua786"},{base:"TZ",letters:"\ua728"},{base:"U",letters:"U\u24ca\uff35\xd9\xda\xdb\u0168\u1e78\u016a\u1e7a\u016c\xdc\u01db\u01d7\u01d5\u01d9\u1ee6\u016e\u0170\u01d3\u0214\u0216\u01af\u1eea\u1ee8\u1eee\u1eec\u1ef0\u1ee4\u1e72\u0172\u1e76\u1e74\u0244"},{base:"V",letters:"V\u24cb\uff36\u1e7c\u1e7e\u01b2\ua75e\u0245"},{base:"VY",letters:"\ua760"},{base:"W",letters:"W\u24cc\uff37\u1e80\u1e82\u0174\u1e86\u1e84\u1e88\u2c72"},{base:"X",letters:"X\u24cd\uff38\u1e8a\u1e8c"},{base:"Y",letters:"Y\u24ce\uff39\u1ef2\xdd\u0176\u1ef8\u0232\u1e8e\u0178\u1ef6\u1ef4\u01b3\u024e\u1efe"},{base:"Z",letters:"Z\u24cf\uff3a\u0179\u1e90\u017b\u017d\u1e92\u1e94\u01b5\u0224\u2c7f\u2c6b\ua762"},{base:"a",letters:"a\u24d0\uff41\u1e9a\xe0\xe1\xe2\u1ea7\u1ea5\u1eab\u1ea9\xe3\u0101\u0103\u1eb1\u1eaf\u1eb5\u1eb3\u0227\u01e1\xe4\u01df\u1ea3\xe5\u01fb\u01ce\u0201\u0203\u1ea1\u1ead\u1eb7\u1e01\u0105\u2c65\u0250"},{base:"aa",letters:"\ua733"},{base:"ae",letters:"\xe6\u01fd\u01e3"},{base:"ao",letters:"\ua735"},{base:"au",letters:"\ua737"},{base:"av",letters:"\ua739\ua73b"},{base:"ay",letters:"\ua73d"},{base:"b",letters:"b\u24d1\uff42\u1e03\u1e05\u1e07\u0180\u0183\u0253"},{base:"c",letters:"c\u24d2\uff43\u0107\u0109\u010b\u010d\xe7\u1e09\u0188\u023c\ua73f\u2184"},{base:"d",letters:"d\u24d3\uff44\u1e0b\u010f\u1e0d\u1e11\u1e13\u1e0f\u0111\u018c\u0256\u0257\ua77a"},{base:"dz",letters:"\u01f3\u01c6"},{base:"e",letters:"e\u24d4\uff45\xe8\xe9\xea\u1ec1\u1ebf\u1ec5\u1ec3\u1ebd\u0113\u1e15\u1e17\u0115\u0117\xeb\u1ebb\u011b\u0205\u0207\u1eb9\u1ec7\u0229\u1e1d\u0119\u1e19\u1e1b\u0247\u025b\u01dd"},{base:"f",letters:"f\u24d5\uff46\u1e1f\u0192\ua77c"},{base:"g",letters:"g\u24d6\uff47\u01f5\u011d\u1e21\u011f\u0121\u01e7\u0123\u01e5\u0260\ua7a1\u1d79\ua77f"},{base:"h",letters:"h\u24d7\uff48\u0125\u1e23\u1e27\u021f\u1e25\u1e29\u1e2b\u1e96\u0127\u2c68\u2c76\u0265"},{base:"hv",letters:"\u0195"},{base:"i",letters:"i\u24d8\uff49\xec\xed\xee\u0129\u012b\u012d\xef\u1e2f\u1ec9\u01d0\u0209\u020b\u1ecb\u012f\u1e2d\u0268\u0131"},{base:"j",letters:"j\u24d9\uff4a\u0135\u01f0\u0249"},{base:"k",letters:"k\u24da\uff4b\u1e31\u01e9\u1e33\u0137\u1e35\u0199\u2c6a\ua741\ua743\ua745\ua7a3"},{base:"l",letters:"l\u24db\uff4c\u0140\u013a\u013e\u1e37\u1e39\u013c\u1e3d\u1e3b\u017f\u0142\u019a\u026b\u2c61\ua749\ua781\ua747"},{base:"lj",letters:"\u01c9"},{base:"m",letters:"m\u24dc\uff4d\u1e3f\u1e41\u1e43\u0271\u026f"},{base:"n",letters:"n\u24dd\uff4e\u01f9\u0144\xf1\u1e45\u0148\u1e47\u0146\u1e4b\u1e49\u019e\u0272\u0149\ua791\ua7a5"},{base:"nj",letters:"\u01cc"},{base:"o",letters:"o\u24de\uff4f\xf2\xf3\xf4\u1ed3\u1ed1\u1ed7\u1ed5\xf5\u1e4d\u022d\u1e4f\u014d\u1e51\u1e53\u014f\u022f\u0231\xf6\u022b\u1ecf\u0151\u01d2\u020d\u020f\u01a1\u1edd\u1edb\u1ee1\u1edf\u1ee3\u1ecd\u1ed9\u01eb\u01ed\xf8\u01ff\u0254\ua74b\ua74d\u0275"},{base:"oi",letters:"\u01a3"},{base:"ou",letters:"\u0223"},{base:"oo",letters:"\ua74f"},{base:"p",letters:"p\u24df\uff50\u1e55\u1e57\u01a5\u1d7d\ua751\ua753\ua755"},{base:"q",letters:"q\u24e0\uff51\u024b\ua757\ua759"},{base:"r",letters:"r\u24e1\uff52\u0155\u1e59\u0159\u0211\u0213\u1e5b\u1e5d\u0157\u1e5f\u024d\u027d\ua75b\ua7a7\ua783"},{base:"s",letters:"s\u24e2\uff53\xdf\u015b\u1e65\u015d\u1e61\u0161\u1e67\u1e63\u1e69\u0219\u015f\u023f\ua7a9\ua785\u1e9b"},{base:"t",letters:"t\u24e3\uff54\u1e6b\u1e97\u0165\u1e6d\u021b\u0163\u1e71\u1e6f\u0167\u01ad\u0288\u2c66\ua787"},{base:"tz",letters:"\ua729"},{base:"u",letters:"u\u24e4\uff55\xf9\xfa\xfb\u0169\u1e79\u016b\u1e7b\u016d\xfc\u01dc\u01d8\u01d6\u01da\u1ee7\u016f\u0171\u01d4\u0215\u0217\u01b0\u1eeb\u1ee9\u1eef\u1eed\u1ef1\u1ee5\u1e73\u0173\u1e77\u1e75\u0289"},{base:"v",letters:"v\u24e5\uff56\u1e7d\u1e7f\u028b\ua75f\u028c"},{base:"vy",letters:"\ua761"},{base:"w",letters:"w\u24e6\uff57\u1e81\u1e83\u0175\u1e87\u1e85\u1e98\u1e89\u2c73"},{base:"x",letters:"x\u24e7\uff58\u1e8b\u1e8d"},{base:"y",letters:"y\u24e8\uff59\u1ef3\xfd\u0177\u1ef9\u0233\u1e8f\xff\u1ef7\u1e99\u1ef5\u01b4\u024f\u1eff"},{base:"z",letters:"z\u24e9\uff5a\u017a\u1e91\u017c\u017e\u1e93\u1e95\u01b6\u0225\u0240\u2c6c\ua763"}],a={},i=0;i<n.length;i++)for(var r=n[i].letters.split(""),o=0;o<r.length;o++)a[r[o]]=n[i].base},G0SC:function(t,e,n){"use strict";n.r(e),n.d(e,"AccountAgreementFormComponent",(function(){return K}));var a=n("mrSG"),i=n("0IaG"),r=n("XsyG"),o=n("wd/R"),c=n.n(o),l=n("fXoL"),s=n("3Pt+"),d=n("WGBV"),m=n("LcQX"),p=n("dNgK"),u=n("flaP"),g=n("ofXK"),h=n("Xa2L"),f=n("Qu3c"),D=n("bTqV"),v=n("1jcm"),b=n("kmnG"),F=n("qFsG"),_=n("iadO"),C=n("wKOf");function S(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",2),l["\u0275\u0275element"](2,"mat-spinner",3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function M(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275elementStart"](1,"mat-slide-toggle",17),l["\u0275\u0275listener"]("change",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"](2).isMsaClicked(e.checked)})),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind3"](3,1,"isMSA",t.formFieldData,"label"))}}function x(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275elementStart"](1,"mat-slide-toggle",18),l["\u0275\u0275listener"]("change",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"](2).isMsaPerpetualClicked(e.checked)})),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("disabled",!t.accountForm.get("isMSA").value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind3"](3,2,"isMSAPerpetual",t.formFieldData,"label"))}}function P(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function A(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div",20),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275template"](4,P,2,0,"span",21),l["\u0275\u0275pipe"](5,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",22),l["\u0275\u0275element"](7,"input",23),l["\u0275\u0275pipe"](8,"tenant"),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275element"](10,"mat-datepicker-toggle",24),l["\u0275\u0275element"](11,"mat-datepicker",null,25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275reference"](12),e=l["\u0275\u0275nextContext"](2);let n=null;l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,8,"msaStartDate",e.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,12,"msaStartDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matDatepicker",t)("max",null==e.accountForm||null==(n=e.accountForm.get("msaFinishDate"))?null:n.value)("disabled",!e.accountForm.get("isMSA").value)("placeholder",l["\u0275\u0275pipeBind3"](8,16,"msaStartDate",e.formFieldData,"label"))("required",l["\u0275\u0275pipeBind3"](9,20,"msaStartDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("for",t)}}function y(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function O(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div",20),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275template"](4,y,2,0,"span",21),l["\u0275\u0275pipe"](5,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",22),l["\u0275\u0275element"](7,"input",27),l["\u0275\u0275pipe"](8,"tenant"),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275element"](10,"mat-datepicker-toggle",24),l["\u0275\u0275element"](11,"mat-datepicker",null,28),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275reference"](12),e=l["\u0275\u0275nextContext"](2);let n=null;l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,8,"msaFinishDate",e.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,12,"msaFinishDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matDatepicker",t)("min",null==e.accountForm||null==(n=e.accountForm.get("msaStartDate"))?null:n.value)("disabled",!e.accountForm.get("isMSA").value)("placeholder",l["\u0275\u0275pipeBind3"](8,16,"msaFinishDate",e.formFieldData,"label"))("required",l["\u0275\u0275pipeBind3"](9,20,"msaFinishDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("for",t)}}function E(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function k(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div",20),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275template"](4,E,2,0,"span",21),l["\u0275\u0275pipe"](5,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",22),l["\u0275\u0275element"](7,"input",29),l["\u0275\u0275pipe"](8,"tenant"),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275element"](10,"mat-datepicker-toggle",24),l["\u0275\u0275element"](11,"mat-datepicker",null,25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275reference"](12),e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,6,"lastRateChanged",e.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,10,"lastRateChanged",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matDatepicker",t)("placeholder",l["\u0275\u0275pipeBind3"](8,14,"lastRateChanged",e.formFieldData,"label"))("required",l["\u0275\u0275pipeBind3"](9,18,"lastRateChanged",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("for",t)}}function w(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function I(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div",20),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275template"](4,w,2,0,"span",21),l["\u0275\u0275pipe"](5,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",22),l["\u0275\u0275element"](7,"input",30),l["\u0275\u0275pipe"](8,"tenant"),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275element"](10,"mat-datepicker-toggle",24),l["\u0275\u0275element"](11,"mat-datepicker",null,28),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275reference"](12),e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,6,"lastRateEscalated",e.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,10,"lastRateEscalated",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matDatepicker",t)("placeholder",l["\u0275\u0275pipeBind3"](8,14,"lastRateEscalated",e.formFieldData,"label"))("required",l["\u0275\u0275pipeBind3"](9,18,"lastRateEscalated",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("for",t)}}function B(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function V(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div"),l["\u0275\u0275elementStart"](2,"div",20),l["\u0275\u0275text"](3),l["\u0275\u0275pipe"](4,"tenant"),l["\u0275\u0275template"](5,B,2,0,"span",21),l["\u0275\u0275pipe"](6,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](7,"app-input-search",31),l["\u0275\u0275pipe"](8,"tenant"),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](4,5,"msaStatus",t.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](6,9,"msaStatus",t.formFieldData,"isMandant")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("list",t.msaStatusList)("placeholder",l["\u0275\u0275pipeBind3"](8,13,"msaStatus",t.formFieldData,"label"))("required",l["\u0275\u0275pipeBind3"](9,17,"msaStatus",t.formFieldData,"isMandant"))}}function N(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275elementStart"](1,"mat-slide-toggle",32),l["\u0275\u0275listener"]("change",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"](2).isNdaClicked(e.checked)})),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind3"](3,1,"isNDA",t.formFieldData,"label"))}}function R(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275elementStart"](1,"mat-slide-toggle",33),l["\u0275\u0275listener"]("change",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"](2).isNdaPerpetualClicked(e.checked)})),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("disabled",!t.accountForm.get("isNDA").value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind3"](3,2,"isNDAPerpetual",t.formFieldData,"label"))}}function z(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function Y(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div",20),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275template"](4,z,2,0,"span",21),l["\u0275\u0275pipe"](5,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",22),l["\u0275\u0275element"](7,"input",34),l["\u0275\u0275pipe"](8,"tenant"),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275element"](10,"mat-datepicker-toggle",24),l["\u0275\u0275element"](11,"mat-datepicker",null,35),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275reference"](12),e=l["\u0275\u0275nextContext"](2);let n=null;l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,8,"ndaStartDate",e.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,12,"ndaStartDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matDatepicker",t)("max",null==e.accountForm||null==(n=e.accountForm.get("ndaFinishDate"))?null:n.value)("disabled",!e.accountForm.get("isNDA").value)("placeholder",l["\u0275\u0275pipeBind3"](8,16,"ndaStartDate",e.formFieldData,"label"))("required",l["\u0275\u0275pipeBind3"](9,20,"ndaStartDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("for",t)}}function j(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function q(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div",20),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275template"](4,j,2,0,"span",21),l["\u0275\u0275pipe"](5,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",22),l["\u0275\u0275element"](7,"input",36),l["\u0275\u0275pipe"](8,"tenant"),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275element"](10,"mat-datepicker-toggle",24),l["\u0275\u0275element"](11,"mat-datepicker",null,37),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275reference"](12),e=l["\u0275\u0275nextContext"](2);let n=null;l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,8,"ndaFinishDate",e.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,12,"ndaFinishDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matDatepicker",t)("min",null==e.accountForm||null==(n=e.accountForm.get("ndaStartDate"))?null:n.value)("disabled",!e.accountForm.get("isNDA").value)("placeholder",l["\u0275\u0275pipeBind3"](8,16,"ndaFinishDate",e.formFieldData,"label"))("required",l["\u0275\u0275pipeBind3"](9,20,"ndaFinishDate",e.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("for",t)}}function L(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275elementStart"](1,"mat-slide-toggle",38),l["\u0275\u0275listener"]("change",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"](2).isGDPRClicked(e.checked)})),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind3"](3,1,"isGDPR",t.formFieldData,"label"))}}function G(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275elementStart"](1,"mat-slide-toggle",39),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("disabled",!t.accountForm.get("isGDPR").value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind3"](3,2,"gdprAgreement",t.formFieldData,"label"))}}function T(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",26),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function U(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div"),l["\u0275\u0275elementStart"](2,"div",20),l["\u0275\u0275text"](3),l["\u0275\u0275pipe"](4,"tenant"),l["\u0275\u0275template"](5,T,2,0,"span",21),l["\u0275\u0275pipe"](6,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"mat-form-field",22),l["\u0275\u0275element"](8,"input",40),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275pipe"](10,"tenant"),l["\u0275\u0275pipe"](11,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](4,5,"dpoEmail",t.formFieldData,"label"),""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](6,9,"dpoEmail",t.formFieldData,"isMandant")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("placeholder",l["\u0275\u0275pipeBind3"](9,13,"dpoEmail",t.formFieldData,"label"))("required",l["\u0275\u0275pipeBind4"](10,17,"dpoEmail",t.formFieldData,"isMandant",1))("readonly",l["\u0275\u0275pipeBind3"](11,22,"dpoEmail",t.formFieldData,"isDisabled"))}}function X(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"span",41),l["\u0275\u0275element"](1,"mat-spinner",42),l["\u0275\u0275elementEnd"]())}function Z(t,e){1&t&&l["\u0275\u0275text"](0," Save ")}function J(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",4),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",5),l["\u0275\u0275elementStart"](5,"div",6),l["\u0275\u0275template"](6,M,4,5,"div",7),l["\u0275\u0275pipe"](7,"tenant"),l["\u0275\u0275template"](8,x,4,6,"div",7),l["\u0275\u0275pipe"](9,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",8),l["\u0275\u0275template"](11,A,13,24,"div",9),l["\u0275\u0275pipe"](12,"tenant"),l["\u0275\u0275template"](13,O,13,24,"div",9),l["\u0275\u0275pipe"](14,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"div",8),l["\u0275\u0275template"](16,k,13,22,"div",9),l["\u0275\u0275pipe"](17,"tenant"),l["\u0275\u0275template"](18,I,13,22,"div",9),l["\u0275\u0275pipe"](19,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](20,"div",8),l["\u0275\u0275template"](21,V,10,21,"div",9),l["\u0275\u0275pipe"](22,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](23,"div",8),l["\u0275\u0275template"](24,N,4,5,"div",7),l["\u0275\u0275pipe"](25,"tenant"),l["\u0275\u0275template"](26,R,4,6,"div",7),l["\u0275\u0275pipe"](27,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](28,"div",8),l["\u0275\u0275template"](29,Y,13,24,"div",9),l["\u0275\u0275pipe"](30,"tenant"),l["\u0275\u0275template"](31,q,13,24,"div",9),l["\u0275\u0275pipe"](32,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](33,"div",8),l["\u0275\u0275template"](34,L,4,5,"div",7),l["\u0275\u0275pipe"](35,"tenant"),l["\u0275\u0275template"](36,G,4,6,"div",7),l["\u0275\u0275pipe"](37,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](38,"div",8),l["\u0275\u0275template"](39,U,12,26,"div",9),l["\u0275\u0275pipe"](40,"tenant"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](41,"div",10),l["\u0275\u0275elementStart"](42,"div",11),l["\u0275\u0275elementStart"](43,"button",12),l["\u0275\u0275listener"]("click",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().closeClicked(e)})),l["\u0275\u0275text"](44," Cancel "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](45,"button",13),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().verifyAccountDetails()})),l["\u0275\u0275template"](46,X,2,0,"span",14),l["\u0275\u0275template"](47,Z,1,0,"ng-template",null,15,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275reference"](48),e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" Enter ",l["\u0275\u0275pipeBind3"](3,19,"accountFinancialAgreement",e.formFieldData,"label")," Details "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("formGroup",e.accountForm),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](7,23,"isMSA",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](9,28,"isMSAPerpetual",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](12,33,"msaStartDate",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](14,38,"msaFinishDate",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](17,43,"lastRateChanged",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](19,48,"lastRateEscalated",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](22,53,"msaStatus",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](25,58,"isNDA",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](27,63,"isNDAPerpetual",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](30,68,"ndaStartDate",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](32,73,"ndaFinishDate",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](35,78,"isGDPR",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](37,83,"gdprAgreement",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind4"](40,88,"dpoEmail",e.formFieldData,"isActive",1)),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("disabled",e.createButtonDisabled),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.saving)("ngIfElse",t)}}n("4ywr");let K=(()=>{class t{constructor(t,e,n,i,r,o,c,l,s){this.dialogRef=t,this.data=e,this.dialog=n,this.fb=i,this.accountService=r,this.utilityService=o,this._snackBar=c,this.roleService=l,this.labelformat=s,this.loading=!1,this.saving=!1,this.isAdminAccess=!1,this.formFieldDataExist=!1,this.createButtonDisabled=!1,this.accountForm=this.fb.group({msaStartDate:[""],msaFinishDate:[""],lastRateEscalated:[""],lastRateChanged:[""],msaStatus:[""],isMSA:[""],isNDA:[""],isMSAPerpetual:[""],isNDAPerpetual:[""],ndaStartDate:[""],ndaFinishDate:[""],isGDPR:[""],gdprAgreement:[""],dpoEmail:[""]}),this.verifyAccountDetails=()=>Object(a.c)(this,void 0,void 0,(function*(){if(this.createButtonDisabled=!0,this.saving=!0,console.log(this.accountForm.value),this.accountForm.get("dpoEmail").value.length>=1&&/^(?:(?![a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}).)*$/.test(this.accountForm.get("dpoEmail").value)){this.createButtonDisabled=!1,this.saving=!1;let t=this.labelformat.transform("dpoEmail",this.formFieldData,"label",null,null);return this.utilityService.showMessage("Please enter Valid "+t+"!","close")}this.updateAccount()})),this.isMsaClicked=t=>{t?(this.accountForm.get("msaStartDate").enable(),this.accountForm.get("msaFinishDate").enable(),this.accountForm.get("isMSAPerpetual").enable(),this.accountForm.get("msaStatus").enable()):(this.accountForm.controls.msaStartDate.setErrors(null),this.accountForm.controls.msaStartDate.clearValidators(),this.accountForm.controls.msaStartDate.setValue(""),this.accountForm.controls.msaStartDate.updateValueAndValidity(),this.accountForm.get("msaStartDate").disable(),this.accountForm.controls.msaFinishDate.setErrors(null),this.accountForm.controls.msaFinishDate.clearValidators(),this.accountForm.controls.msaFinishDate.setValue(""),this.accountForm.controls.msaFinishDate.updateValueAndValidity(),this.accountForm.get("msaFinishDate").disable(),this.accountForm.controls.isMSAPerpetual.setErrors(null),this.accountForm.controls.isMSAPerpetual.clearValidators(),this.accountForm.controls.isMSAPerpetual.setValue(""),this.accountForm.controls.isMSAPerpetual.updateValueAndValidity(),this.accountForm.get("isMSAPerpetual").disable(),this.accountForm.controls.msaStatus.setErrors(null),this.accountForm.controls.msaStatus.clearValidators(),this.accountForm.controls.msaStatus.setValue(""),this.accountForm.controls.msaStatus.updateValueAndValidity(),this.accountForm.get("msaStatus").disable())},this.isNdaClicked=t=>{t?(this.accountForm.get("ndaStartDate").enable(),this.accountForm.get("ndaFinishDate").enable(),this.accountForm.get("isNDAPerpetual").enable()):(this.accountForm.controls.ndaStartDate.setErrors(null),this.accountForm.controls.ndaStartDate.clearValidators(),this.accountForm.controls.ndaStartDate.setValue(""),this.accountForm.controls.ndaStartDate.updateValueAndValidity(),this.accountForm.get("ndaStartDate").disable(),this.accountForm.controls.ndaFinishDate.setErrors(null),this.accountForm.controls.ndaFinishDate.clearValidators(),this.accountForm.controls.ndaFinishDate.setValue(""),this.accountForm.controls.ndaFinishDate.updateValueAndValidity(),this.accountForm.get("ndaFinishDate").disable(),this.accountForm.controls.isNDAPerpetual.setErrors(null),this.accountForm.controls.isNDAPerpetual.clearValidators(),this.accountForm.controls.isNDAPerpetual.setValue(""),this.accountForm.controls.isNDAPerpetual.updateValueAndValidity(),this.accountForm.get("isNDAPerpetual").disable())},this.isMsaPerpetualClicked=t=>{t?this.accountForm.patchValue({msaFinishDate:"9999-12-31"}):(this.accountForm.controls.msaFinishDate.setErrors(null),this.accountForm.controls.msaFinishDate.clearValidators(),this.accountForm.controls.msaFinishDate.setValue(""),this.accountForm.controls.msaFinishDate.updateValueAndValidity())},this.isNdaPerpetualClicked=t=>{t?this.accountForm.patchValue({ndaFinishDate:"9999-12-31"}):(this.accountForm.controls.ndaFinishDate.setErrors(null),this.accountForm.controls.ndaFinishDate.clearValidators(),this.accountForm.controls.ndaFinishDate.setValue(""),this.accountForm.controls.ndaFinishDate.updateValueAndValidity())},this.isGDPRClicked=t=>{t?this.accountForm.get("gdprAgreement").enable():(this.accountForm.controls.gdprAgreement.setErrors(null),this.accountForm.controls.gdprAgreement.clearValidators(),this.accountForm.controls.gdprAgreement.setValue(""),this.accountForm.controls.gdprAgreement.updateValueAndValidity(),this.accountForm.get("gdprAgreement").disable())}}ngOnInit(){var t,e;return Object(a.c)(this,void 0,void 0,(function*(){this.mode=this.data.mode,this.loading=!0,this.accountDetail=this.data.accountDetails,console.log("Mode is ",this.mode),console.log("Mode is ",this.accountDetail),this.accountService.getTaxMaster().subscribe(t=>{this.taxMaster=t}),this.accountService.paymentTerms().subscribe(t=>{this.paymentTermsList=t}),this.accountService.getMsaStatusList().subscribe(t=>{this.msaStatusList=t}),yield this.accountService.getFormFieldCollection().then(t=>{this.formFieldData=t,console.log("form is ",this.formFieldData),this.formFieldDataExist=!0},t=>{console.log(t)}),"Edit"==this.mode&&(yield this.updateFormWithAccountDetais()),this.isMsaClicked(1==(null===(t=this.accountDetail[0])||void 0===t?void 0:t.is_msa)||1==(null===(e=this.accountDetail)||void 0===e?void 0:e.is_msa)||!1),this.loading=!1}))}updateFormWithAccountDetais(){return Object(a.c)(this,void 0,void 0,(function*(){this.accountDetail=this.accountDetail?this.accountDetail[0]:null,"Edit"==this.mode?(this.accountForm.enable(),this.accountForm.patchValue({accountNo:this.accountDetail.customer_id,isMSA:this.accountDetail.is_msa?this.accountDetail.is_msa:"",isNDA:this.accountDetail.is_nda?this.accountDetail.is_nda:"",msaStartDate:this.accountDetail.msa_start_date?this.accountDetail.msa_start_date:"",msaStatus:this.accountDetail.msa_status?this.accountDetail.msa_status:"",msaFinishDate:this.accountDetail.msa_end_date?this.accountDetail.msa_end_date:"",isMSAPerpetual:this.accountDetail.is_msa_perpetual?this.accountDetail.is_msa_perpetual:"",isNDAPerpetual:this.accountDetail.is_nda_perpetual?this.accountDetail.is_nda_perpetual:"",ndaStartDate:this.accountDetail.nda_start_date?this.accountDetail.nda_start_date:"",ndaFinishDate:this.accountDetail.nda_end_date?this.accountDetail.nda_end_date:"",isGDPR:this.accountDetail.is_gdpr?this.accountDetail.is_gdpr:"",gdprAgreement:this.accountDetail.gdpr_agreement?this.accountDetail.gdpr_agreement:"",dpoEmail:this.accountDetail.dpo_email?this.accountDetail.dpo_email:"",lastRateEscalated:this.accountDetail.last_date_escalated?this.accountDetail.last_date_escalated:"",lastRateChanged:this.accountDetail.last_rate_changed?this.accountDetail.last_rate_changed:""}),console.log("Mode is ",this.accountForm),this.loading=!1):this.accountForm.reset()}))}closeClicked(t){"mouse"==t.pointerType&&(this.accountForm.reset(),this.dialogRef.close())}updateAccount(){console.log(this.accountForm.value);let t=this.accountForm.value;if(this.accountForm.valid){const e=this.accountForm.value,n=c()().format("YYYY-MM-DD hh:mm:ss");e.current_date=n,e.msaStartDate=t.msaStartDate&&""!=t.msaStartDate?c()(t.msaStartDate).format("YYYY-MM-DD"):null,e.msaFinishDate=t.msaFinishDate&&""!=t.msaFinishDate?c()(t.msaFinishDate).format("YYYY-MM-DD"):null,e.ndaStartDate=t.ndaStartDate&&""!=t.ndaStartDate?c()(t.ndaStartDate).format("YYYY-MM-DD"):null,e.ndaFinishDate=t.ndaFinishDate&&""!=t.ndaFinishDate?c()(t.ndaFinishDate).format("YYYY-MM-DD"):null,e.msaStatus=t.msaStatus||null,e.lastRateEscalated=t.lastRateEscalated&&""!=t.lastRateEscalated?c()(t.lastRateEscalated).format("YYYY-MM-DD"):null,e.lastRateChanged=t.lastRateChanged&&""!=t.lastRateChanged?c()(t.lastRateChanged).format("YYYY-MM-DD"):null,this.accountService.updateAccountAgreementById(this.accountDetail.customer_id,e).subscribe(t=>{this.dialogRef.close("update required"),this._snackBar.open(t,"close",{duration:2e3})},t=>{console.error(t)})}else this.saving=!1,this.createButtonDisabled=!1,this._snackBar.open("Enter All Mandatory Fields! ","close",{duration:2e3})}isValidUrl(t){try{return new URL(t),!0}catch(e){return!1}}}return t.\u0275fac=function(e){return new(e||t)(l["\u0275\u0275directiveInject"](i.h),l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](i.b),l["\u0275\u0275directiveInject"](s.i),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](m.a),l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](u.a),l["\u0275\u0275directiveInject"](r.a))},t.\u0275cmp=l["\u0275\u0275defineComponent"]({type:t,selectors:[["app-account-agreement-form"]],features:[l["\u0275\u0275ProvidersFeature"]([r.a])],decls:3,vars:2,consts:[[1,"container","account_agreement"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","section-header","mt-4","pl-3"],[3,"formGroup"],[1,"row","pt-3","mt-3"],["class","pt-2 pl-3","style","width: fit-content",4,"ngIf"],[1,"row","pt-3"],["class","col-4",4,"ngIf"],[1,"row","mt-4"],[1,"col-12","d-flex","align-items-center","justify-content-end",2,"text-align","end"],["type","button","mat-stroked-button","",1,"cancel-btn",3,"click"],["type","button","mat-raised-button","",1,"ml-3","create-btn",3,"disabled","click"],["class","d-flex align-items-center justify-content-center",4,"ngIf","ngIfElse"],["buttonContent",""],[1,"pt-2","pl-3",2,"width","fit-content"],["color","primary","formControlName","isMSA",1,"create-account-field",3,"change"],["color","primary","formControlName","isMSAPerpetual",1,"create-account-field",3,"disabled","change"],[1,"col-4"],[1,"row","field-title"],["class","required-star",4,"ngIf"],["appearance","outline",1,"create-account-field","pt-1"],["matInput","","formControlName","msaStartDate",3,"matDatepicker","max","disabled","placeholder","required"],["matSuffix","",3,"for"],["picker1",""],[1,"required-star"],["matInput","","formControlName","msaFinishDate",3,"matDatepicker","min","disabled","placeholder","required"],["picker2",""],["matInput","","formControlName","lastRateChanged",3,"matDatepicker","placeholder","required"],["matInput","","formControlName","lastRateEscalated",3,"matDatepicker","placeholder","required"],["formControlName","msaStatus",1,"create-account-field-inputsearch","pt-1",3,"list","placeholder","required"],["color","primary","formControlName","isNDA",1,"create-account-field",3,"change"],["color","primary","formControlName","isNDAPerpetual",1,"create-account-field",3,"disabled","change"],["matInput","","formControlName","ndaStartDate",3,"matDatepicker","max","disabled","placeholder","required"],["picker4",""],["matInput","","formControlName","ndaFinishDate",3,"matDatepicker","min","disabled","placeholder","required"],["picker5",""],["color","primary","formControlName","isGDPR",1,"create-account-field",3,"change"],["color","primary","formControlName","gdprAgreement",1,"create-account-field",3,"disabled"],["matInput","","formControlName","dpoEmail",3,"placeholder","required","readonly"],[1,"d-flex","align-items-center","justify-content-center"],["diameter","20","matTooltip","Saving...","color","accent"]],template:function(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275template"](1,S,3,0,"div",1),l["\u0275\u0275template"](2,J,49,93,"div",1),l["\u0275\u0275elementEnd"]()),2&t&&(l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.loading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0==e.loading))},directives:[g.NgIf,h.c,f.a,s.w,s.n,D.a,v.a,s.v,s.l,b.c,F.b,s.e,_.g,s.F,_.i,b.i,_.f,C.a],pipes:[r.a],styles:[".account_agreement[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:16px!important;height:16px!important;transform:translate(50%,50%)}.account_agreement[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:24px!important;width:40px!important}.account_agreement[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-4px!important;left:-5px!important}.account_agreement[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:#f27a6c}.account_agreement[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.account_agreement[_ngcontent-%COMP%]     .mat-form-field{width:100%!important}.account_agreement[_ngcontent-%COMP%]   .center-screen[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:16vh}.account_agreement[_ngcontent-%COMP%]   .mat-spinner-color[_ngcontent-%COMP%]  circle{stroke:#cf0001!important}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]{background-size:215px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:96% 107%;min-height:95vh}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;align-items:center;font-size:14px}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]   .marginbottom[_ngcontent-%COMP%]{margin-bottom:10px}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .matches[_ngcontent-%COMP%]{color:#797878;font-weight:lighter;font-size:12px}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .acc-names[_ngcontent-%COMP%]{color:#4e4e4e;font-size:12px;font-weight:400}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-weight:500}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{position:fixed;bottom:10vh;background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{border-radius:100%;display:flex;flex:0 0 150px;height:70px;justify-content:center;overflow:hidden;position:relative;width:70px}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .img__overlay[_ngcontent-%COMP%]{align-items:center;bottom:0;display:flex;justify-content:center;left:0;opacity:0;position:absolute;right:0;top:0;transition:opacity .25s;z-index:1}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .img__overlay[_ngcontent-%COMP%]:hover{opacity:1}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .img__overlay[_ngcontent-%COMP%]{background-color:#9a9a9a;background:linear-gradient(65deg,hsla(0,0%,63.1%,.4),rgba(253,246,236,.4));color:#fafafa;font-size:24px}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .image-upload[_ngcontent-%COMP%] > input[_ngcontent-%COMP%]{display:none}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .image-upload[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{cursor:pointer}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{min-width:30rem;min-height:15rem}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}.account_agreement[_ngcontent-%COMP%]   .createAccountContainer[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.account_agreement[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.account_agreement[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.account_agreement[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#45546e}.account_agreement[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.account_agreement[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#f27a6c}.account_agreement[_ngcontent-%COMP%]     .mat-form-field-outline{color:#b9c0ca!important}"]}),t})()},ROcA:function(t,e,n){!function(){var e=function(t,e,n,a){var i={version:"0.0.1"};t=t||[],i.gramSizeLower=n||2,i.gramSizeUpper=a||3,i.useLevenshtein=e||!0,i.exactSet={},i.matchDict={},i.items={};var r=function(t,e){if(null==t&&null==e)throw"Trying to compare two null values";if(null==t||null==e)return 0;var n=function(t,e){for(var n,a,i=[],r=0;r<=e.length;r++)for(var o=0;o<=t.length;o++)a=r&&o?t.charAt(o-1)===e.charAt(r-1)?n:Math.min(i[o],i[o-1],n)+1:r+o,n=i[o],i[o]=a;return i.pop()}(t=String(t),e=String(e));return t.length>e.length?1-n/t.length:1-n/e.length},o=/[^\w, ]+/,c=function(t,e){for(var n={},a=function(t,e){e=e||2;var n="-"+t.toLowerCase().replace(o,"")+"-",a=e-n.length,i=[];if(a>0)for(var r=0;r<a;++r)t+="-";for(r=0;r<n.length-e+1;++r)i.push(n.slice(r,r+e));return i}(t,e=e||2),i=0;i<a.length;++i)a[i]in n?n[a[i]]+=1:n[a[i]]=1;return n};i.get=function(t,e){var n=this._get(t);return!n&&e?e:n},i._get=function(t){var e=this._normalizeStr(t),n=this.exactSet[e];if(n)return[[1,n]];for(var a=[],i=this.gramSizeUpper;i>this.gramSizeLower;--i)if(a=this.__get(t,i))return a;return null},i.__get=function(t,e){var n,a,i,o,l=this._normalizeStr(t),s={},d=c(l,e),m=this.items[e],p=0;for(n in d)if(a=d[n],p+=Math.pow(a,2),n in this.matchDict)for(b=0;b<this.matchDict[n].length;++b)o=this.matchDict[n][b][1],(i=this.matchDict[n][b][0])in s?s[i]+=a*o:s[i]=a*o;if(function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}(s))return null;var u=Math.sqrt(p),g=[];for(var h in s)g.push([s[h]/(u*m[h][0]),m[h][1]]);var f=function(t,e){return t[0]<e[0]?1:t[0]>e[0]?-1:0};if(g.sort(f),this.useLevenshtein){for(var D=[],v=Math.min(50,g.length),b=0;b<v;++b)D.push([r(g[b][1],l),g[b][1]]);(g=D).sort(f)}for(D=[],b=0;b<g.length;++b)g[b][0]==g[0][0]&&D.push([g[b][0],this.exactSet[g[b][1]]]);return D},i.add=function(t){if(this._normalizeStr(t)in this.exactSet)return!1;for(var e=this.gramSizeLower;e<this.gramSizeUpper+1;++e)this._add(t,e)},i._add=function(t,e){var n=this._normalizeStr(t),a=this.items[e]||[],i=a.length;a.push(0);var r,o=c(n,e),l=0;for(var s in o)r=o[s],l+=Math.pow(r,2),s in this.matchDict?this.matchDict[s].push([i,r]):this.matchDict[s]=[[i,r]];var d=Math.sqrt(l);a[i]=[d,n],this.items[e]=a,this.exactSet[n]=t},i._normalizeStr=function(t){if("[object String]"!==Object.prototype.toString.call(t))throw"Must use a string as argument to FuzzySet functions";return t.toLowerCase()},i.length=function(){var t,e=0;for(t in this.exactSet)this.exactSet.hasOwnProperty(t)&&(e+=1);return e},i.isEmpty=function(){for(var t in this.exactSet)if(this.exactSet.hasOwnProperty(t))return!1;return!0},i.values=function(){var t,e=[];for(t in this.exactSet)this.exactSet.hasOwnProperty(t)&&e.push(this.exactSet[t]);return e};for(var l=i.gramSizeLower;l<i.gramSizeUpper+1;++l)i.items[l]=[];for(l=0;l<t.length;++l)i.add(t[l]);return i};t.exports?(t.exports=e,this.FuzzySet=e):this.FuzzySet=e}()},mop9:function(t,e,n){t.exports=n("ROcA")}}]);