(window.webpackJsonp=window.webpackJsonp||[]).push([[866,634,765,783,821,822,858,861,977,981,983,987,990,991],{"1d+P":function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var n=i("Iab2"),r=i("fXoL");let o=(()=>{class e{constructor(){this.saveAsFile=(e,t,i)=>{var r=this.base64ToBlob(e,i);n.saveAs(r,t)},this.base64ToBlob=(e,t,i=512)=>{e=e.replace(/\s/g,"");let n=atob(e),r=[];for(let a=0;a<n.length;a+=i){let e=n.slice(a,a+i),t=new Array(e.length);for(var o=0;o<e.length;o++)t[o]=e.charCodeAt(o);let s=new Uint8Array(t);r.push(s)}return new Blob(r,{type:t})}}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},"9Y7K":function(e,t,i){"use strict";i.r(t),i.d(t,"VendorInvoiceModule",(function(){return R}));var n=i("ofXK"),r=i("tyNb"),o=i("mrSG"),a=i("R0Ic"),s=i("wd/R"),l=i("XNiG"),d=i("1G5W"),c=i("xG9w"),h=i("tk/3"),u=i("fXoL"),p=i("GnQ3"),f=i("BVzC"),m=i("LcQX"),g=i("1d+P"),v=i("a1r6"),_=i("0IaG"),y=i("XXEo"),S=i("vFIH"),D=i("F97M"),b=i("1A3m"),C=i("HmYF"),x=i("vgCS"),I=i("xi/V"),O=i("Wk3H"),w=i("Wp6s");function E(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",7),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](e);const i=t.$implicit;return u["\u0275\u0275nextContext"](2).onSideNavItemSelected(i)})),u["\u0275\u0275elementStart"](1,"div",8),u["\u0275\u0275text"](2),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](3,"div",9),u["\u0275\u0275text"](4),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=u["\u0275\u0275nextContext"](2);u["\u0275\u0275property"]("ngClass",e.id==i.selectedItemId?"sr-nav-item":"r-nav-item"),u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",e.name," "),u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",e.count," ")}}function M(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275template"](1,E,5,3,"div",6),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",e.sideNavData)}}function P(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",11),u["\u0275\u0275element"](1,"div",12),u["\u0275\u0275elementEnd"]())}function A(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275template"](1,P,2,0,"div",10),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",e.dummySideNavData)}}const T=[{path:"",component:(()=>{class e{constructor(e,t,i,n,r,a,h,u,p,f,m,g,v,_,y){this._udrfService=e,this._errorService=t,this._utilityService=i,this._FileSaver=n,this._p2pGeneralService=r,this.router=a,this.dialog=h,this._auth=u,this._mailService=p,this._loginService=f,this._graphService=m,this._toaster=g,this._excelService=v,this.http=_,this._o365=y,this.applicationId=276,this.$onDestroy=new l.b,this.$onAppApiCalled=new l.b,this.isSideNavLoading=!1,this.selectedItemId=1,this.dummySideNavData=Array.from({length:15},(e,t)=>t+1),this.loggedInProfile=this._auth.getProfile().profile,this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.selectedCard=[],this.sideNavData=[],this.taskStatusColor=[],this.user=this._loginService.getProfile().profile,this.dataForDownload=[],this.d_start_idx=0,this.d_end_idx=250,this.d_item_length=1,this.dataTypeArray=[{dataType:"My Invoices",dataTypeValue:0,isActive:!0,isVisible:!0,cardType:"status",statusColor:"#ff7200",dataTypeCode:"MI"},{dataType:"Approvals",dataTypeValue:0,isActive:!1,isVisible:!0,cardType:"status",statusColor:"#009432",dataTypeCode:"A"},{dataType:"Treasury",dataTypeValue:0,isActive:!1,isVisible:!0,cardType:"status",statusColor:"#cf0001",dataTypeCode:"T"}],this.categorisedDataTypeArray=[{categoryType:"Invoice Category Cards",categoryCardCodes:["MI","A","T"],categoryCards:[]}],this.udrfBodyColumns=[{item:"checkBox",header:"Provisional",isActive:!0,isVisible:"true",type:"checkBox",position:1,colSize:1,width:90,sortOrder:"N"},{item:"id",header:"ID",isActive:!0,isVisible:"true",type:"id",position:2,colSize:1,sortOrder:"N",width:75,textClass:"value13Bold cp"},{item:"action",header:"Actions",isActive:!0,isVisible:"true",type:"action",position:3,colSize:3,width:240,sortOrder:"N"},{item:"description",header:"Description",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold colorRed",position:4,colSize:2,sortOrder:"N",width:280},{item:"status_name",header:"Invoice Status",isActive:!0,isVisible:"true",type:"status",isInlineEdit:!0,inlineEditVarient:["vendor-invoice-status","minimal-dropdown"],textClass:"value13Bold",position:5,colSize:2,sortOrder:"N",width:180},{item:"pr_status",header:"PR status",isActive:!0,isVisible:"true",type:"status",textClass:"value13Bold",position:6,colSize:2,sortOrder:"N",width:180},{item:"amount",header:"Amount",isActive:!0,isVisible:"true",type:"currency",textClass:"value13Bold cp colorRed",position:7,colSize:3,sortOrder:"N",width:180},{item:"pr_code",header:"PR code",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold",position:8,colSize:2,sortOrder:"N",width:180},{item:"invoice_number",header:"Invoice Number",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold",position:9,colSize:2,sortOrder:"N",width:180},{item:"invoice_date",header:"Invoice Date",isActive:!0,isVisible:"true",type:"date",textClass:"value13Bold",isInlineEdit:!0,inlineEditVarient:["p2p_invoice_date","date-picker"],position:10,colSize:1,sortOrder:"N",width:180},{item:"payment_term_name",header:"Payment Term",isActive:!0,isVisible:"false",type:"text",textClass:"value13Bold colorRed",position:11,colSize:2,sortOrder:"N",width:300},{item:"vendor_name",header:"Vendor",isActive:!0,isVisible:"false",type:"text",textClass:"value13Bold colorRed",position:12,colSize:2,sortOrder:"N",width:280},{item:"is_msme",header:"Vendor Type",isActive:!0,isVisible:"false",type:"text",textClass:"value13Bold cp colorRed",position:3,colSize:3,sortOrder:"N",width:300},{item:"provisional_status",header:"Provisional status",isActive:!0,isVisible:"false",type:"status",textClass:"value13Bold",position:13,colSize:2,sortOrder:"N",width:180},{item:"payment_model",header:"Payment model",isActive:!0,isVisible:"false",type:"text",position:14,colSize:3,sortOrder:"N",width:240},{item:"po_number",header:"PO Number",isActive:!0,isVisible:"true",type:"text",position:15,colSize:3,sortOrder:"N",width:240},{item:"cc_approvers",header:"Cost center approvers",isVisible:"false",type:"profileImgList",position:16,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:240},{item:"treasury_approvers",header:"Treasury approvers",isVisible:"false",type:"profileImgList",position:17,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:240},{item:"invoice_name",header:"Invoice",isActive:!0,isVisible:"false",type:"text",textClass:"valueGrey14",position:18,colSize:2,sortOrder:"N",width:180},{item:"created_by_name",header:"Created by",isActive:!0,isVisible:"false",type:"text",textClass:"valueGrey14",position:19,colSize:2,sortOrder:"N",width:180},{item:"entity_name",header:"Entity Name",isActive:!0,isVisible:"false",type:"text",textClass:"valueGrey14",position:20,colSize:2,sortOrder:"N",width:180},{item:"people_involved_names",header:"People Involved",isActive:!0,isVisible:"false",type:"text",position:21,colSize:3,sortOrder:"N",width:240}],this.udrfItemStatusColor=[],this.confirmation=!1,this.taskCompleted=!1,this.sharepointInvoiceIntegration=!1,this.invoiceFileDetailsSPIntegration=[],this.isCancelinvoice=!1,this.checkForPrStatus=e=>"Rejected"!=e.pr_status&&"Cancelled"!=e.pr_status||(this._toaster.showError("Invoice upload not allowed","PR status is being rejected / cancelled !",3e3),!1),this.checkForInvoiceStatus=e=>9==(null==e?void 0:e.vendor_invoice_status_id)?(this._toaster.showError("Invoice upload not allowed","Invoice status is being rejected / cancelled !",3e3),0):1,this.getWorkflowProperties=()=>{this._p2pGeneralService.getPurchaseRequestWorkflowProperties().subscribe(e=>{this.workflowProperties=e.data,console.log(this.workflowProperties)},e=>{console.error(e)})},this.downloadItemDataReport=()=>Object(o.c)(this,void 0,void 0,(function*(){this._udrfService.udrfUiData.isReportDownloading=!0;let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t=c.findWhere(this.dataTypeArray,{isActive:!0}),i={startIndex:"D",startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,activeSummaryCard:t},n=c.findWhere(this.sideNavData,{id:this.selectedItemId});n=n||null,yield this.downloadReportAsExcel(i,n,this.d_start_idx,this.d_end_idx)})),this.downloadReportAsExcel=(e,t,i,n)=>Object(o.c)(this,void 0,void 0,(function*(){if(this._udrfService.udrfUiData.isReportDownloading=!0,!((this.d_item_length>0||0==this.d_start_idx)&&this.d_end_idx<=5e3))return this.dataForDownload.length>0?(this._excelService.exportAsExcelFile(this.dataForDownload,"Invoice Report as on "+s().format("YYYY-MM-DD")),this._utilityService.showToastMessage("Report downloaded successfully")):this._p2pGeneralService.showMessage("Failed to download report, Kindly contact KEBS team !"),this.d_item_length=1,this.d_end_idx=250,this.d_start_idx=0,this.dataForDownload=[],this._udrfService.udrfUiData.isReportDownloading=!1,0;yield this._p2pGeneralService.getPRVendorInvoicesForDownload(e,t,"PR",i,n).pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(i=>Object(o.c)(this,void 0,void 0,(function*(){this._p2pGeneralService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(n=>Object(o.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=n,!("S"==i.messType&&i.messData.length>0))return"N"==i.messType&&this.dataForDownload.length>0?(this._excelService.exportAsExcelFile(this.dataForDownload,"Invoice Report as on "+s().format("YYYY-MM-DD")),this._utilityService.showToastMessage("Report downloaded successfully")):this._p2pGeneralService.showMessage("Failed to download report, Kindly contact KEBS team !"),this._udrfService.udrfUiData.isReportDownloading=!1,this.d_item_length=1,this.d_end_idx=250,this.d_start_idx=0,this.dataForDownload=[],0;{let n,r=[],o=i.messData;console.log("dataPR : ",o),c.each(o,e=>{void 0!==e.is_msme&&(null!==e.is_msme?(n=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=n[0].vendortype):n=null,r.push(n))}),console.log("is_msme data : ",r),this.d_item_length=i.messData.length,this.dataForDownload.push(...i.messData),this.d_start_idx+=250,this.d_end_idx+=250,console.log(this.d_start_idx),console.log(this.d_end_idx),console.log(this.d_item_length),console.log(this.dataForDownload),yield this.downloadReportAsExcel(e,t,this.d_start_idx,this.d_end_idx)}})))})),e=>(this._udrfService.udrfUiData.isReportDownloading=!1,this._utilityService.showErrorMessage(e,"KEBS"),this.d_item_length=1,this.d_end_idx=250,this.d_start_idx=0,this.dataForDownload=[],0))})),this.getPurchaseRequestTenantConfig=e=>Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,i)=>{this._p2pGeneralService.getP2pTenantConfig(e).subscribe(e=>{"S"==e.messType?(this.isCancelinvoice=!0,t(!0)):(this.isCancelinvoice=!1,t(!1))},e=>{i(e)})})}))}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.sharepointInvoiceIntegration=yield this.getPurchaseRequestTenantConfig("SPINV"),this.resolveObservables(),this.current_financial_start="04-01-"+s().year().toString(),this.current_financial_end="03-31-"+(s().year()+1).toString(),this.current_financial_start=s(this.current_financial_start).format(),this.current_financial_end=s(this.current_financial_end).format(),this.previous_financial_start="04-01-"+(s().year()-1).toString(),this.previous_financial_end="03-31-"+s().year().toString(),this.previous_financial_start=s(this.previous_financial_start).format(),this.previous_financial_end=s(this.previous_financial_end).format(),this.next_financial_start="04-01-"+(s().year()+1).toString(),this.next_financial_end="03-31-"+(s().year()+2).toString(),this.next_financial_start=s(this.next_financial_start).format(),this.next_financial_end=s(this.next_financial_end).format();let e=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:s().startOf("year"),checkboxEndValue:s().endOf("year"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this._utilityService.getFormattedDate(s().startOf("week"),s(s().startOf("week")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(s().endOf("week"),s(s().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this._utilityService.getFormattedDate(s().startOf("month"),s(s().startOf("month")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(s().endOf("month"),s(s().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Previous Month",checkboxStartValue:this._utilityService.getFormattedDate(s().subtract(1,"months").startOf("month"),s(s().subtract(1,"months").startOf("month")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(s().subtract(1,"months").endOf("month"),s(s().subtract(1,"months").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"All",checkboxStartValue:s("1920-01-01"),checkboxEndValue:s("2100-12-12"),isCheckboxDefaultSelected:!0}],t=[{checkboxId:"BDCRED",checkboxName:"Current Financial Year",checkboxStartValue:this.current_financial_start,checkboxEndValue:this.current_financial_end,isCheckboxDefaultSelected:!1},{checkboxId:"BDCRED2",checkboxName:"Previous Financial Year",checkboxStartValue:this.previous_financial_start,checkboxEndValue:this.previous_financial_end,isCheckboxDefaultSelected:!1},{checkboxId:"BDCRED3",checkboxName:"Next Financial Year",checkboxStartValue:this.next_financial_start,checkboxEndValue:this.next_financial_end,isCheckboxDefaultSelected:!1}];this._udrfService.udrfFunctions.constructCustomRangeData(1,"date",e),this._udrfService.udrfFunctions.constructCustomRangeData(7,"date",t),this.getInlineEditMasterData(),this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfUiData.showNewReleasesButton=!0,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.itemDataType="",this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showSettingsModalButton=!0,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showReportDownloadButton=!1,this._udrfService.udrfUiData.showColumnConfigButton=!0,this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this._udrfService.udrfUiData.summaryCardsItem={},this._udrfService.udrfUiData.showHierarchyData={},this._udrfService.udrfUiData.inlineEditData={},this._udrfService.udrfUiData.downloadItemDataReport=()=>{},this._udrfService.udrfUiData.summaryCards=this.dataTypeArray,this._udrfService.udrfUiData.openComments=this.openComments.bind(this),this._udrfService.udrfUiData.openCommentsData={},this._udrfService.udrfUiData.itemDataScrollDown=this.onItemDataScrollDown.bind(this),this._udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this._udrfService.udrfUiData.inlineEditData={},this._udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this._udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this._udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this._udrfService.udrfUiData.selectedCard=this.selectedCard,this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this._udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this._udrfService.udrfUiData.variant=1,this._udrfService.udrfUiData.openFileDownload=this.downloadInvoice.bind(this),this._udrfService.udrfUiData.itemcardSelected=this.itemCardClicked.bind(this),this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.itemHasComments=!0,this._udrfService.udrfUiData.itemHasHierarchyView=!1,this._udrfService.udrfUiData.itemHasInfoButton=!1,this._udrfService.udrfUiData.itemHasMoreActions=!1,this._udrfService.udrfUiData.showCollapseButton=!0,this._udrfService.udrfUiData.horizontalScroll=!0,this._udrfService.udrfUiData.showActualCurrencyForCurrencyComponent=!0,this._udrfService.udrfUiData.itemHasSendButton=!0,this._udrfService.udrfUiData.openSend=this.openMailBoxModal.bind(this),this._udrfService.udrfUiData.showCreateNewComponentButton=!1,this._udrfService.udrfUiData.showProvisionalModalButtonForP2P=!0,this._udrfService.udrfUiData.openProvisionalModalButtonForP2P=this.openProvisionalModal.bind(this),this._udrfService.udrfUiData.openInFullScreen=this.openTaskDetailModal.bind(this),this._udrfService.udrfUiData.itemHasOpenInFullScreen=!0,this._udrfService.udrfUiData.itemHasOpenInNewTab=!0,this._udrfService.udrfUiData.openInNewTab=this.navigateToPRDetailPage.bind(this),this._udrfService.udrfUiData.itemHasDownloadButton=!0,this._udrfService.udrfUiData.itemDownloadButtonToolTip="Download Invoice",this._udrfService.udrfUiData.attachFile=this.activateUploadLoader.bind(this),this._udrfService.udrfUiData.onCompleteItem=this.updateInvoiceDetails.bind(this),this._udrfService.udrfUiData.onProgressItem=this.onProgressItem.bind(this),this._udrfService.udrfUiData.itemUploadButtonToolTip="Upload Invoice",this._udrfService.udrfUiData.itemHasOpenModal=!0,this._udrfService.udrfUiData.openModal=this.openEditScreen.bind(this),this._udrfService.udrfUiData.openModalName="Edit",this._udrfService.udrfUiData.openModalMatIcon="edit",this._udrfService.udrfUiData.onApproval=this.onItemApproval.bind(this),this._udrfService.udrfUiData.onItemVerifyClicked=this.verifyInvoice.bind(this),this._udrfService.udrfUiData.itemVerifyButtonToolTip="Verify Invoice",this.controlActionButtonsBasedOnSummaryCard(),this._udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this._udrfService.getNotifyReleasesUDRF(),this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showReportDownloadButton=!0,this._udrfService.udrfUiData.downloadItemDataReport=()=>{},this._udrfService.udrfUiData.downloadItemDataReport=this.downloadItemDataReport.bind(this),this._p2pGeneralService.getVendorInvoiceWorkflowProperties().pipe(Object(d.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?this.vendorInvoiceWorkflowProperties=e.data:this._p2pGeneralService.showMessage("Failed to retrieve workflow properties. Kindly contact KEBS team.")},e=>{this._p2pGeneralService.showMessage("Failed to retrieve workflow properties. Kindly contact KEBS team."),console.log(e)}),this.initMailConfig(),this._udrfService.udrfFunctions.resolveVisibleColumnConfigItems(),this._udrfService.udrfUiData.resolveColumnConfig()}))}initReport(){return Object(o.c)(this,void 0,void 0,(function*(){this.$onAppApiCalled.next(),this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this.selectedItemId=1,this._udrfService.udrfUiData.resolveColumnConfig(),this._udrfService.udrfUiData.s3URL="/api/purchaseRequest/uploadPRAttachment",this._udrfService.udrfUiData.initFileUploaderOptions(),this.getVendorInvoiceList(),this.getSideNavData()}))}getVendorInvoiceList(){let e=c.findWhere(this.sideNavData,{id:this.selectedItemId}),t=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),i=c.findWhere(this.dataTypeArray,{isActive:!0}),n={startIndex:this.itemDataCurrentIndex,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:t,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,activeSummaryCard:i};this.$p2pMilestoneReportSubscription&&this.$p2pMilestoneReportSubscription.unsubscribe(),e=e||null,this.$p2pMilestoneReportSubscription=this._p2pGeneralService.getPRVendorInvoices(n,e,"PR").pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this._p2pGeneralService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=t,"S"==e.messType&&e.messData&&e.messData.length>0){let t;this.globalData=e.messData,c.each(this.globalData,e=>{null!=e.is_msme?(t=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=t[0].vendortype):e.is_msme="-"}),this.globalData=e.messData,console.log("This.globaldata",this.globalData);let i=JSON.parse(JSON.stringify(this._udrfService.udrfBodyData));i=i.concat(e.messData),i=c.uniq(i,e=>e.id),this._udrfService.udrfBodyData=i,this.userAuthObjectValue=e.auth_object_value}else this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfData.noItemDataFound=!0;this._udrfService.udrfData.isItemDataLoading=!1,this.resolveVisibleDataTypeArray(),this.vendorInvoiceSummaryCardUdrf()})))})),e=>{this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Invoice data",e&&e.params?e.params:e&&e.error?e.error.params:{})})}resolveVisibleDataTypeArray(){return Object(o.c)(this,void 0,void 0,(function*(){for(let e of this._udrfService.udrfUiData.summaryCards){let t;this._udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=c.contains(this._udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this._udrfService.udrfUiData.summaryCardsItem=e))}}))}dataTypeCardSelected(){this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.noItemDataFound=!1;let e=this._udrfService.udrfUiData.summaryCardsItem;console.log(this._udrfService.udrfUiData.summaryCardsItem);for(let t=0;t<this.dataTypeArray.length;t++)if(e.dataTypeCode==this.dataTypeArray[t].dataTypeCode)this.dataTypeArray[t].isActive=!0,e.isActive=!0;else{let e=c.where(this._udrfService.udrfUiData.summaryCards,{dataTypeCode:this.dataTypeArray[t].dataTypeCode});e.length>0&&(e[0].isActive=!1),this.dataTypeArray[t].isActive=!1}this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this.controlActionButtonsBasedOnSummaryCard(),this.getVendorInvoiceList(),this.getSideNavData()}onItemDataScrollDown(){return Object(o.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.noItemDataFound||this._udrfService.udrfData.isItemDataLoading||(this.itemDataCurrentIndex+=this._udrfService.udrfData.defaultRecordsPerFetch,this._udrfService.udrfData.isItemDataLoading=!0,this.getVendorInvoiceList())}))}getSideNavData(){this.isSideNavLoading=!0;let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t=c.findWhere(this.dataTypeArray,{isActive:!0}),i={startIndex:this.itemDataCurrentIndex,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,activeSummaryCard:t};this.$p2pMilestoneReportTotalSubscription&&this.$p2pMilestoneReportTotalSubscription.unsubscribe(),this.$p2pMilestoneReportTotalSubscription=this._p2pGeneralService.getPRVendorInvoiceSideNavData(i).pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this._udrfService.udrfUiData.totalItemDataCount=e.itemDataCount,this.sideNavData=e.data):this._udrfService.udrfUiData.totalItemDataCount=0,this.isSideNavLoading=!1})),e=>{this.isSideNavLoading=!1,this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving WFH Approvals",e&&e.params?e.params:e&&e.error?e.error.params:{})})}vendorInvoiceSummaryCardUdrf(){let e=c.findWhere(this.sideNavData,{id:this.selectedItemId}),t=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),i=c.findWhere(this.dataTypeArray,{isActive:!0});this._p2pGeneralService.vendorInvoiceSummaryCardUdrf({startIndex:this.itemDataCurrentIndex,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:t,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,activeSummaryCard:i},e,"PR").pipe(Object(d.a)(this.$onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.dataTypeArray=this.dataTypeArray.map(t=>{let i=e.data.find(e=>t.dataTypeCode==e.dataTypeCode);return Object.assign(Object.assign({},t),i)}),this._udrfService.udrfUiData.summaryCards=this.dataTypeArray,this._udrfService.udrfUiData.totalItemDataCount=e.totalItemCount,console.log(this.dataTypeArray)):this._udrfService.udrfUiData.totalItemDataCount=0})),e=>{this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving count",e&&e.params?e.params:e&&e.error?e.error.params:{})})}downloadInvoice(){console.log(this._udrfService.udrfUiData.openFileDownloadData);let e=this._udrfService.udrfUiData.openFileDownloadData.data.attachment;e?(e=e.files_json,this._p2pGeneralService.getInvoiceAttachment(e.key).pipe(Object(d.a)(this.$onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){console.log(t),"S"==t.messType?"image/png"==e.type||"image/jpg"==e.type||"image/jpeg"==e.type||"application/pdf"==e.type?(window.open("").document.write(`<iframe width=100% height=100% src=data:${e.type};base64,${t.data.fileData}></iframe>`),this._FileSaver.saveAsFile(t.data.fileData,e.fileName,e.type)):this._FileSaver.saveAsFile(t.data.fileData,e.fileName,e.type):this._p2pGeneralService.showMessage(t.messText)})),e=>{console.log(e),this._errorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)})):this._p2pGeneralService.showMessage("No invoice found !")}activateUploadLoader(){this._udrfService.udrfBodyData[this._udrfService.udrfUiData.attachFileData.index].isUploadOnProgress=!0}updateInvoiceDetails(){return Object(o.c)(this,void 0,void 0,(function*(){let e=this._udrfService.udrfUiData.attachFileData.data;console.log("Item Data on Invoice Upload : ",e),this.invoiceFileDetailsSPIntegration=[];let t=this._udrfService.udrfUiData.attachFileData.event.srcElement.files[0];console.log("File event: ",t);let i=t.name.lastIndexOf("."),n=t.name.substring(i+1),r=t.name.substring(0,i);if(this.invoiceFileDetailsSPIntegration.push({fileName:r,fileType:n,file:t}),this.checkForPrStatus(e)&&this.checkForInvoiceStatus(e)){let t=this._udrfService.udrfUiData.attachFileData.index;if(this._udrfService.udrfBodyData[t].isUploadOnProgress=!1,console.log("Index value from activateUploadLoader : ",t),this.confirmation=!1,this.title="Do you want to upload the invoice and change the status to submitted ?",this.confirmation=yield this._utilityService.openConfirmationSweetAlertWithCustom(this.title,""),console.log("confirm",this.confirmation),1==this.confirmation){console.log("itemdata",e);let i=yield this.onInvoiceSubmission(this.confirmation,t,e);if(console.log(this.taskCompleted),console.log(i),i)if(1==e.vendor_invoice_status_id||2==e.vendor_invoice_status_id){let i=this._udrfService.udrfUiData.onCompleteItemResponse;if(i&&i.length>0){let n=JSON.parse(i);if(n.error)this._p2pGeneralService.showMessage("Unable to upload");else{let i;if(i=1==e.payment_model_id?{p2p_header_id:e.p2p_header_id,payment_model_id:e.payment_model_id}:{p2p_header_id:e.p2p_header_id,payment_model_id:e.payment_model_id,milestone_id:e.milestone_id},2==i.payment_model_id?(this.finalInvoiceDate=s(e.invoice_date).format("YYYY-MM-DD HH:mm:ss"),this.vendorName=e.vendor_name,this.milestoneName=e.description):(null==e?void 0:e.invoice_date)&&"0000-00-00 00:00:00"!=(null==e?void 0:e.invoice_date)?(this.finalInvoiceDate=s(null==e?void 0:e.invoice_date).format("YYYY-MM-DD HH:mm:ss"),this.vendorName=e.vendor_name):this.finalInvoiceDate="0000-00-00 00:00:00",this.sharepointInvoiceIntegration)if(1==i.payment_model_id){if("0000-00-00 00:00:00"!=this.finalInvoiceDate){console.log("Invoice Attachments Data : ",this.invoiceFileDetailsSPIntegration);let e,t,i,n,r=new Date(this.finalInvoiceDate).getFullYear().toString(),a=["January","February","March","April","May","June","July","August","September","October","November","December"][new Date(this.finalInvoiceDate).getMonth()];console.log("Invoice Year & Invoice Month ",r,a),yield this._o365.getToken().then(t=>{e=t});let l=[],d=[],h=[],u=244;this._p2pGeneralService.getSharePointIntegrationDetails(u).subscribe(p=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==p.messType){if(t=p.data.teams_channel_name,i=p.data.group_id,n=p.data.channel_item_id,n||(n=yield this.getChannelItemID(e,i,t),this._p2pGeneralService.postSharePointIntegrationDetails(u,n,i,t).subscribe(e=>{"S"==e.messType&&console.log("Channel Itam ID Updated successfully!")})),l=yield this.getFolderDetails(e,i,n),console.log("Year Folders : ",l),l.map(e=>e.name).includes(r)){let t=l.find(e=>e.name==r);if(t=t.id,console.log(`Existing folder identified for the folder ${r}...: ${t}`),d=yield this.getFolderDetails(e,i,t),console.log("Month Folders : ",d),d.map(e=>e.name).includes(a)){let t=d.find(e=>e.name==a);t=t.id,console.log(`Existing folder identified for the folder ${a}...: ${t}`),h=yield this.getFolderDetails(e,i,t),console.log("Vendor Folders : ",h);let n=h.map(e=>e.name),r=this.vendorName.trim();if(n.includes(r)){let t=h.find(e=>e.name==r);t=t.id,console.log(`Existing folder identified for the folder ${r}...: ${t}`),yield this.getFileandFolderDetails(e,i,t),console.log("Vendor File Folders : ",h),console.log(`In this Vendor folder ${r} files have to be stored....`),this.invoiceFileDetailsSPIntegration.length>0?c.each(this.invoiceFileDetailsSPIntegration,n=>Object(o.c)(this,void 0,void 0,(function*(){n.fileName&&n.fileType&&n.file&&(yield this.uploadFiles(e,i,t,`Inv_${s(this.finalInvoiceDate).format("YYYY-MM-DD")}_${n.fileName}`,n.fileType,n.file))}))):console.log("No file to upload!")}else{yield this.createVendorFolder(e,i,t,r),h=yield this.getFolderDetails(e,i,t),console.log("Vendor Folders : ",d),l.map(e=>e.name);let n=h.find(e=>e.name==r);n=n.id,console.log(`Existing folder identified for the folder ${r}...: ${n}`),yield this.getFileandFolderDetails(e,i,n),console.log("Vendor File Folders : ",h),console.log(`In this Vendor folder ${r} files have to be stored....`),this.invoiceFileDetailsSPIntegration.length>0?c.each(this.invoiceFileDetailsSPIntegration,t=>Object(o.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,i,n,`Inv_${s(this.finalInvoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}}else{yield this.createMonthFolder(e,i,t,a),d=yield this.getFolderDetails(e,i,t),console.log("Month Folders : ",d),d.map(e=>e.name);let n=d.find(e=>e.name==a);n=n.id,console.log(`Existing folder identified for the folder ${a}...: ${n}`);let r=this.vendorName.trim();yield this.createVendorFolder(e,i,n,r),h=yield this.getFolderDetails(e,i,n),console.log("Vendor Folders : ",d),l.map(e=>e.name);let u=h.find(e=>e.name==r);u=u.id,console.log(`Existing folder identified for the folder ${r}...: ${u}`),console.log("Attachemnts will be posted inside this vendor folder : ",r,u),this.invoiceFileDetailsSPIntegration.length>0?c.each(this.invoiceFileDetailsSPIntegration,t=>Object(o.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,i,u,`Inv_${s(this.finalInvoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}}else{yield this.createYearFolder(e,i,n,r),l=yield this.getFolderDetails(e,i,n),console.log("Year Folders : ",l),l.map(e=>e.name);let t=l.find(e=>e.name==r);t=t.id,console.log(`Existing folder identified for the folder ${r}...: ${t}`),yield this.createMonthFolder(e,i,t,a),d=yield this.getFolderDetails(e,i,t),console.log("Month Folders : ",d),d.map(e=>e.name);let u=d.find(e=>e.name==a);u=u.id,console.log(`Existing folder identified for the folder ${a}...: ${u}`);let p=this.vendorName.trim();yield this.createVendorFolder(e,i,u,p),h=yield this.getFolderDetails(e,i,u),console.log("Vendor Folders : ",d),l.map(e=>e.name);let f=h.find(e=>e.name==p);f=f.id,console.log(`Existing folder identified for the folder ${p}...: ${f}`),yield this.getFileandFolderDetails(e,i,f),console.log("Vendor File Folders : ",h),console.log(`In this Vendor folder ${p} files have to be stored....`),console.log("Attachemnts will be posted inside this vendor folder : ",p,f),this.invoiceFileDetailsSPIntegration.length>0?c.each(this.invoiceFileDetailsSPIntegration,t=>Object(o.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,i,f,`Inv_${s(this.finalInvoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}console.log("Nullyfying the invoice attachemnts..."),this.invoiceFileDetailsSPIntegration=[]}else this._toaster.showError("SharePoint Details not found!","",2e3)})),e=>{this._toaster.showError("Failed to create request !","",2e3),console.error(e)})}}else if(2==i.payment_model_id){let e=[];if("0000-00-00 00:00:00"!=this.finalInvoiceDate){let t;e.push({invoiceDate:this.finalInvoiceDate,milestoneName:this.milestoneName}),yield this._o365.getToken().then(e=>{t=e});for(let i=0;i<e.length;i++)yield this.milestoneInvSP_Integ(i,t,e[i]);this.invoiceFileDetailsSPIntegration=[]}}else{let e=[];if("0000-00-00 00:00:00"!=this.finalInvoiceDate){let t;e.push({invoiceDate:this.finalInvoiceDate,milestoneName:this.milestoneName}),yield this._o365.getToken().then(e=>{t=e});for(let i=0;i<e.length;i++)yield this.milestoneInvSP_Integ(i,t,e[i]);this.invoiceFileDetailsSPIntegration=[]}}this._p2pGeneralService.updateInvoiceDetails(i,n).pipe(Object(d.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this._udrfService.udrfBodyData[t].attachment=n,this._udrfService.udrfBodyData[t].invoice_name=n.files_json?n.files_json.fileName:"-",this._p2pGeneralService.showMessage(e.userMess)):this._p2pGeneralService.showMessage(e.userMess)},e=>{this._udrfService.udrfUiData.resetFileInput(),this._p2pGeneralService.showMessage("Failed to upload invoice !"),console.log(e)})}}this.taskCompleted=!1}else this.taskCompleted=!1,this.confirmation=!1,this._udrfService.udrfUiData.resetFileInput(),this._toaster.showError("Invoice upload access denied","Invoice can be uploaded only if inovice status is open or submitted !",3e3);console.log("accepeted")}else this.taskCompleted=!1,this.confirmation=!1,this._toaster.showError("Invoice upload cancelled","Try again to upload and submit Invoice !",3e3),console.log("here else"),this._udrfService.udrfUiData.resetFileInput()}else this._udrfService.udrfBodyData[this._udrfService.udrfUiData.attachFileData.index].isUploadOnProgress=!1}))}openComments(){return Object(o.c)(this,void 0,void 0,(function*(){let e,t=this._udrfService.udrfUiData.openCommentsData.data;if(t){e={application_id:276,unique_id_1:t.p2p_header_id,unique_id_2:t.id,application_name:"P2P - Vendor Invoices",title:t.description?t.description:""};let n={inputData:e,context:{"Milestone name":t.description?t.description:"","Payment term":t.payment_term_name?t.payment_term_name:"","Invoice Date":t.invoice_date?s(t.invoice_date).format("DD-MMM-YY"):""},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};const{ChatCommentContextModalComponent:r}=yield Promise.all([i.e(4),i.e(63),i.e(75),i.e(983)]).then(i.bind(null,"vg2w"));this.dialog.open(r,{height:"100%",width:"65%",position:{right:"0px"},data:{modalParams:n}})}}))}onSideNavItemSelected(e){this.selectedItemId!=e.id&&(this.selectedItemId=e.id,this.itemDataCurrentIndex=0,this.$onAppApiCalled.next(),this._udrfService.udrfBodyData=[],this._udrfService.udrfUiData.resolveColumnConfig(),this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.noItemDataFound=!1,this.getVendorInvoiceList())}itemCardClicked(){this.router.navigateByUrl("/main/p2p/invoiceDetail/"+this._udrfService.udrfUiData.itemCardSelecteditem.p2p_header_id+"/"+this._udrfService.udrfUiData.itemCardSelecteditem.id)}controlActionButtonsBasedOnSummaryCard(){let e=c.findWhere(this.dataTypeArray,{isActive:!0});e&&("MI"==e.dataTypeCode?(this._udrfService.udrfUiData.itemHasUploadButton=!0,this._udrfService.udrfUiData.itemHasApproval=!1,this._udrfService.udrfUiData.itemHasVerifyButton=!1):"A"==e.dataTypeCode?(this._udrfService.udrfUiData.itemHasApproval=!0,this._udrfService.udrfUiData.itemHasUploadButton=!1,this._udrfService.udrfUiData.itemHasVerifyButton=!1):(this._udrfService.udrfUiData.itemHasUploadButton=!0,this._udrfService.udrfUiData.itemHasVerifyButton=!0,this._udrfService.udrfUiData.itemHasApproval=!1))}verifyInvoice(){let e=!0,t=this._udrfService.udrfUiData.verifyItemData.data,i=this._udrfService.udrfUiData.verifyItemData.index;"*"==this.userAuthObjectValue&&(e=this.checkForCurrentApprover(this._udrfService.udrfBodyData[i].treasury_approvers)),e?(t.selected_milestone_id=t.milestone_id,5!=t.vendor_invoice_status_id?this._p2pGeneralService.updateInvoiceStatus(t,1).pipe(Object(d.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this._udrfService.udrfBodyData[i].vendor_invoice_status_id=5,this._udrfService.udrfBodyData[i].status_name="Verified",e.provisional_status_res.err||e.provisional_status_res.is_updated&&(this._udrfService.udrfBodyData[i].provisional_status=e.provisional_status_res.updated_status_name,this._udrfService.udrfBodyData[i].isItemChecked=!0,this._udrfService.udrfBodyData[i].isItemCheckBoxDisabled=!0),this._p2pGeneralService.showMessage(e.messData)):(this._p2pGeneralService.showMessage("Something went wrong !"),console.log(e))},e=>{this._p2pGeneralService.showMessage("Failed to verify invoice !"),console.log(e)}):this._p2pGeneralService.showMessage("Invoice already verified !")):this._p2pGeneralService.showMessage("Access denied ! You are not a approver.")}callInlineEditApi(e=!1,t){return Object(o.c)(this,void 0,void 0,(function*(){let i=this._udrfService.udrfUiData.inlineEditData.index;1==e&&(i=t,console.log("inside if",this._udrfService.udrfUiData.inlineEditData.inlineEditResponse));let n=this._udrfService.udrfUiData.inlineEditData.indexL2;if(console.log(this._udrfService.udrfUiData.inlineEditData),"l1"==this._udrfService.udrfUiData.inlineEditData.hierarchyLevel){if("vendor-invoice-status"==this._udrfService.udrfUiData.inlineEditData.inlineEditField){let e=c.findWhere(this.dataTypeArray,{isActive:!0});e&&"MI"==e.dataTypeCode&&2==this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.id?this._udrfService.udrfUiData.inlineEditData.dataSelected.invoice_status!=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.id&&this.checkForInvoiceSubmission(i)&&this.triggerWfForInvoice(i):this._p2pGeneralService.showMessage("Invoice status control access denied !")}else if("p2p_invoice_date"==this._udrfService.udrfUiData.inlineEditData.inlineEditField&&s(new Date(this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.p2p_invoice_date)).format("YYYY-MM-DD")!=s(this._udrfService.udrfUiData.inlineEditData.invoice_date).format("YYYY-MM-DD")){let e=s(new Date(this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.p2p_invoice_date)).format("YYYY-MM-DD"),t={p2p_header_id:this._udrfService.udrfUiData.inlineEditData.dataSelected.p2p_header_id,milestone_id:this._udrfService.udrfUiData.inlineEditData.dataSelected.milestone_id,payment_model_id:this._udrfService.udrfUiData.inlineEditData.dataSelected.payment_model_id,invoice_date:e};yield this._p2pGeneralService.editInvoiceDate(t).subscribe(t=>{"S"==t.messType?(this._udrfService.udrfBodyData[i].invoice_date=e,this._p2pGeneralService.showMessage("Invoice date edited successfully !")):this._p2pGeneralService.showMessage("Failed to edit date !")},e=>{console.log(e)})}}else"l2"==this._udrfService.udrfUiData.inlineEditData.hierarchyLevel&&"vendor-invoice-status"==this._udrfService.udrfUiData.inlineEditData.inlineEditField&&(console.log(this._udrfService.udrfUiData.inlineEditData),this._udrfService.udrfUiData.inlineEditData.dataSelected.status_id!=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.id&&this._p2pGeneralService.updateP2pTaskData(this._udrfService.udrfUiData.inlineEditData.dataSelected.id,"status_id",this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.id).pipe(Object(d.a)(this.$onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._udrfService.udrfBodyData[i].l2[n].status_name=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.name,this._udrfService.udrfBodyData[i].l2[n].status_id=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.id,this._p2pGeneralService.showMessage("Task Status updated Successfully !")):this._p2pGeneralService.showMessage("Error in updating task status")})),e=>{this._errorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)}))}))}onItemApproval(){return Object(o.c)(this,void 0,void 0,(function*(){let e=this._udrfService.udrfUiData.openApprovalData.data;e.status=e.clickStatus,this._p2pGeneralService.handleInvoiceApproval(e).pipe(Object(d.a)(this.$onDestroy)).subscribe(t=>{if("S"==t.messType){let t=c.findWhere(this._udrfService.udrfBodyData,{id:e.id});t&&("A"==e.status?(t.status_name="Approved",t.vendor_invoice_status_id=3):(t.status_name="Rejected",t.vendor_invoice_status_id=4)),this._p2pGeneralService.showMessage(`Invoice ${t.status_name} !`)}"E"==t.messType&&t.statusPr&&"S"==t.statusPr.messType&&"Y"==t.statusPr.messSubType&&this._p2pGeneralService.showMessage(t.messText)},e=>{console.log(e)})}))}openTaskDetailModal(){return Object(o.c)(this,void 0,void 0,(function*(){let e=this._udrfService.udrfUiData.openInFullScreenData.l2Index,t=this._udrfService.udrfUiData.openInFullScreenData.data;t.task_name=t.description;let n={taskId:t.l2[e].id,taskItem:t.l2[e]};const{VendorInvoiceTaskDetailComponent:r}=yield Promise.all([i.e(131),i.e(0)]).then(i.bind(null,"ZzYy"));this.dialog.open(r,{height:"99%",width:"86%",maxWidth:"86%",data:{modalParams:n}})}))}navigateToPRDetailPage(){return Object(o.c)(this,void 0,void 0,(function*(){let e,t=this._udrfService.udrfUiData.openinNewTabData.data.p2p_header_id,i=c.findWhere(this.dataTypeArray,{isActive:!0});i&&"MI"==i.dataTypeCode?e=window.location.origin+"/main/p2p/requestorDetail/"+t:i&&"A"==i.dataTypeCode?e=window.location.origin+"/main/p2p/approverDetail/"+t:i&&"T"==i.dataTypeCode&&(e=window.location.origin+"/main/p2p/treasuryDetail/"+t),window.open(e)}))}getInlineEditMasterData(){this._p2pGeneralService.getVendorInvoiceInlineEditStatusMasterData().pipe(Object(d.a)(this.$onDestroy)).subscribe(e=>{if("S"==e.messType){this._udrfService.udrfUiData.inlineEditDropDownMasterDatas={invoiceStatusMasterData:e.invoiceStatusMaster,taskStatusMasterData:e.taskStatusMaster};let t=[],i=[];i=e.invoiceStatusMaster.concat(e.taskStatusMaster),c.each(i,e=>{t.push({status:e.name,color:e.color})}),this.udrfItemStatusColor=t,this.udrfItemStatusColor=this.udrfItemStatusColor.concat([{status:"Yet to be taken",color:"#ffb142"},{status:"Taken",color:"#009432"},{status:"Knock off",color:"#079992"},{status:"Not applicable",color:"#cf0001"}]),this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor}},e=>{console.log(e)})}triggerWfForInvoice(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=c.pluck(this._udrfService.udrfUiData.inlineEditData.dataSelected.ccSplitups,"cost_center_name").map(e=>({costCenter:e,costCenterType:"",costCentreDescription:""})),i=this._udrfService.udrfUiData.inlineEditData.dataSelected.amount.map(e=>({currency_code:e.currency_code,amount:e.value}));console.log(t),yield this._p2pGeneralService.determineWfPropertyForInvoice(i,t,this.vendorInvoiceWorkflowProperties).pipe(Object(d.a)(this.$onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==t.messType){let i=t.data,n=t.workflow_id;yield this._p2pGeneralService.invoiceWFTrigger(this._udrfService.udrfUiData.inlineEditData.dataSelected.p2p_header_id,this._udrfService.udrfUiData.inlineEditData.dataSelected.id,this._udrfService.udrfUiData.inlineEditData.dataSelected.milestone_id,i,n).pipe(Object(d.a)(this.$onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this._udrfService.udrfBodyData[e].vendor_invoice_status_id=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.id,this._udrfService.udrfBodyData[e].status_name=this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.name,this._p2pGeneralService.showMessage(`Invoice status marked as ${this._udrfService.udrfUiData.inlineEditData.inlineEditResponse.name} !`)):this._p2pGeneralService.showMessage("Error in updating invoice status")})),e=>{this._errorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)})}console.log(t)})),e=>{console.log(e)})}))}initMailConfig(){this._mailService.mUtilityData.applicationId=this.applicationId,this._mailService.mUtilityData.hasInitiateNewMailTemplate=!0,this._mailService.mUtilityData.formatTableForTemplate=!0,this._mailService.mUtilityData.currentUserMailId=this.user.email}openMailBoxModal(){return Object(o.c)(this,void 0,void 0,(function*(){this.initMailConfig();let e=this._udrfService.udrfUiData.openSendData.data;console.log(this._udrfService.udrfUiData.openSendData.data),yield this._p2pGeneralService.getVendorInvoiceMailTemplate(e.p2p_header_id,e.id).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==e.messType){let t=e.data;this._mailService.mUtilityData.newMailTemplateData.push(t),this._mailService.mUtilityData.authorizedMailSenders=t.authorizedMailSenders,this._mailService.mUtilityData.currentMailMode={mode:"create"},this._mailService.mUtilityData.o365Token={token:yield this._graphService.getO365Token()};const{ViewMailComponent:n}=yield Promise.resolve().then(i.bind(null,"vFIH"));this.dialog.open(n,{width:"96%",height:"97%",maxWidth:"100vw",maxHeight:"100vw",data:{},disableClose:!0}).afterClosed().subscribe(e=>{this._mailService.resetMailData()})}})))}))}checkForInvoiceSubmission(e){let t=this._udrfService.udrfBodyData[e];if(t.l2.length>0)for(let i of t.l2)if(3!=i.status_id)return this._udrfService.udrfUiData.resetFileInput(),this._p2pGeneralService.showMessage("Kindly complete the tasks before submitting the invoice !"),!1;return!!t.attachment||(this._udrfService.udrfUiData.resetFileInput(),this._p2pGeneralService.showMessage("Kindly attach the invoice document"),!1)}checkForInvoiceSubmissionOnInvoiceUpload(e){let t=this._udrfService.udrfBodyData[e];if(console.log(t),t.l2.length>0)for(let i of t.l2)if(3!=i.status_id)return this._udrfService.udrfUiData.resetFileInput(),this._p2pGeneralService.showMessage("Kindly complete the tasks before submitting the invoice !"),!1;return!0}checkForCurrentApprover(e){let t=!1;return e&&(e="string"==typeof e?JSON.parse(e):e,c.findWhere(e,{oid:this.user.oid})&&(t=!0)),t}openProvisionalModal(){return Object(o.c)(this,void 0,void 0,(function*(){let e=c.filter(this._udrfService.udrfBodyData,e=>e.isItemChecked&&!e.isItemCheckBoxDisabled);if(e.length>0){let t=c.filter(e,e=>"Draft"==e.pr_status||"D"==e.pr_status);if(t.length>0)return console.log("Selected Invoices : ",t),this._toaster.showWarning("Draft status PR invoice can not be used","Kindly unselect the Draft status PR invoice to proceed !",3e3);const{ProvisionalEntryModalComponent:n}=yield Promise.all([i.e(8),i.e(31),i.e(894)]).then(i.bind(null,"3GNX"));this.dialog.open(n,{height:"94vh",minWidth:"95%",position:{left:"77px"},data:{modalParams:{provisionalEntry:e}},disableClose:!0}).afterClosed().subscribe(t=>{t&&!t.err&&e.forEach((e,t)=>{e.provisional_status="Taken",e.isItemCheckBoxDisabled=!0})})}else this._toaster.showError("No invoice selected","Kindly select the invoice to proceed !",3e3)}))}resolveObservables(){this._p2pGeneralService.$compDestroyObservable.subscribe(e=>{"invoices"==e&&"all"!=e||this.ngOnDestroy()})}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete(),this._udrfService.resetUdrfData(),this.$p2pMilestoneReportSubscription&&this.$p2pMilestoneReportSubscription.unsubscribe()}openEditScreen(){return Object(o.c)(this,void 0,void 0,(function*(){let e=this._udrfService.udrfUiData.openModalData?this._udrfService.udrfUiData.openModalData:this._udrfService.udrfUiData.udrfUiOpenModalDataItem;if(null!=e){const{EditMilestoneComponent:t}=yield i.e(887).then(i.bind(null,"znuk"));let n=e.id;this.globalData.forEach(e=>{e.id==n&&(console.log("single data",e),this.dialog.open(t,{height:"100%",width:"50%",position:{right:"0px"},data:{milestoneDetails:e,mode:"Edit",editDataForPatching:e}}).afterClosed().subscribe(e=>{console.log(e),"update"==e&&this.initReport()}))})}}))}onInvoiceSubmission(e,t,i){return Object(o.c)(this,void 0,void 0,(function*(){let e=t,n=c.findWhere(this.dataTypeArray,{isActive:!0});if(console.log("data type",n),n&&"MI"==n.dataTypeCode){if(2!=i.invoice_status)return!!this.checkForInvoiceSubmissionOnInvoiceUpload(e)&&(this.taskCompleted=!0,this.triggerWfForInvoiceOnSubmission(i),this.confirmation=!1,this.taskCompleted)}else this._p2pGeneralService.showMessage("Invoice status control access denied !")}))}triggerWfForInvoiceOnSubmission(e){return Object(o.c)(this,void 0,void 0,(function*(){console.log("inside wf",e);let t=c.pluck(e.ccSplitups,"cost_center_name").map(e=>({costCenter:e,costCenterType:"",costCentreDescription:""})),i=e.amount.map(e=>({currency_code:e.currency_code,amount:e.value}));console.log(t),console.log("amount",i),console.log("vendor",this.vendorInvoiceWorkflowProperties),yield this._p2pGeneralService.determineWfPropertyForInvoice(i,t,this.vendorInvoiceWorkflowProperties).pipe(Object(d.a)(this.$onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if(console.log("res",t),"S"==t.messType){let i=t.data,n=t.workflow_id;yield this._p2pGeneralService.invoiceWFTrigger(e.p2p_header_id,e.id,e.milestone_id,i,n).pipe(Object(d.a)(this.$onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType?(e.vendor_invoice_status_id=2,e.status_name="Submitted",this._p2pGeneralService.showMessage("Invoice status marked as Submitted !")):this._p2pGeneralService.showMessage("Error in updating invoice status")})),e=>{this._errorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)})}console.log(t)})),e=>{console.log(e)})}))}onProgressItem(){this._udrfService.udrfBodyData[this._udrfService.udrfUiData.attachFileData.index].isUploadOnProgress=!0}milestoneInvSP_Integ(e,t,i){return Object(o.c)(this,void 0,void 0,(function*(){try{let n=new Date(i.invoiceDate).getFullYear().toString(),r=["January","February","March","April","May","June","July","August","September","October","November","December"][new Date(i.invoiceDate).getMonth()];console.log("Invoice Year & Invoice Month for milestoneCount: ",e,n,r);let o=244,a=yield this._p2pGeneralService.getSharePointIntegrationDetails(o).toPromise();if("S"===a.messType){let{teams_channel_name:s,group_id:l,channel_item_id:d}=a.data,c=d;c||(c=yield this.getChannelItemID(t,l,s),yield this._p2pGeneralService.postSharePointIntegrationDetails(o,c,l,s).toPromise(),console.log("Channel Item ID Updated successfully!"));let h=yield this.getFolderDetails(t,l,c);if(console.log("Year Folders: ",h),h.map(e=>e.name).includes(n)){let o=h.find(e=>e.name===n).id;console.log(`Existing folder identified for the folder ${n}...: ${o}`);let a=yield this.getFolderDetails(t,l,o);if(console.log("Month Folders: ",a),a.map(e=>e.name).includes(r)){let n=a.find(e=>e.name===r).id;console.log(`Existing folder identified for the folder ${r}...: ${n}`);let o=yield this.getFolderDetails(t,l,n);console.log("Vendor Folders: ",o);let s=o.map(e=>e.name),d=this.vendorName;if(s.includes(d)){let n=o.find(e=>e.name===d).id;console.log(`Existing folder identified for the folder ${d}...: ${n}`);let r=this.invoiceFileDetailsSPIntegration[e];r.fileName&&r.fileType&&r.file&&(yield this.uploadFiles(t,l,n,`Milestone_${i.milestoneName}_${r.fileName}`,r.fileType,r.file))}else{yield this.createVendorFolder(t,l,n,d);let r=yield this.getFolderDetails(t,l,n);console.log("Vendor Folders: ",r),r.map(e=>e.name);let o=r.find(e=>e.name===d).id;console.log(`Existing folder identified for the folder ${d}...: ${o}`);let a=this.invoiceFileDetailsSPIntegration[e];a.fileName&&a.fileType&&a.file&&(yield this.uploadFiles(t,l,o,`Milestone_${i.milestoneName}_${a.fileName}`,a.fileType,a.file))}}else{yield this.createMonthFolder(t,l,o,r),a=yield this.getFolderDetails(t,l,o),console.log("Month Folders : ",a),a.map(e=>e.name);let n=a.find(e=>e.name==r);n=n.id,console.log(`Existing folder identified for the folder ${r}...: ${n}`);let s=this.vendorName.trim();yield this.createVendorFolder(t,l,n,s);let d=yield this.getFolderDetails(t,l,n);console.log("Vendor Folders: ",d),d.map(e=>e.name);let c=d.find(e=>e.name===s).id;console.log(`Existing folder identified for the folder ${s}...: ${c}`);let h=this.invoiceFileDetailsSPIntegration[e];h.fileName&&h.fileType&&h.file&&(yield this.uploadFiles(t,l,c,`Milestone_${i.milestoneName}_${h.fileName}`,h.fileType,h.file))}}else{yield this.createYearFolder(t,l,c,n);let o=yield this.getFolderDetails(t,l,c);console.log("Year Folders: ",o),o.map(e=>e.name);let a=o.find(e=>e.name==n);a=a.id,console.log(`Existing folder identified for the folder ${n}...: ${a}`),yield this.createMonthFolder(t,l,a,r);let s=yield this.getFolderDetails(t,l,a);console.log("Month Folders : ",s),s.map(e=>e.name);let d=s.find(e=>e.name==r);d=d.id,console.log(`Existing folder identified for the folder ${r}...: ${d}`);let h=this.vendorName.trim();yield this.createVendorFolder(t,l,d,h);let u=yield this.getFolderDetails(t,l,d);console.log("Vendor Folders: ",u),u.map(e=>e.name);let p=u.find(e=>e.name===h).id;console.log(`Existing folder identified for the folder ${h}...: ${p}`);let f=this.invoiceFileDetailsSPIntegration[e];f.fileName&&f.fileType&&f.file&&(yield this.uploadFiles(t,l,p,`Milestone_${i.milestoneName}_${f.fileName}`,f.fileType,f.file))}}else this._toaster.showError("SharePoint Details not found!","",5e3)}catch(n){this._toaster.showError("Failed to create request!","",5e3),console.error(n)}}))}uploadFiles(e,t,i,n,r,a){return Object(o.c)(this,void 0,void 0,(function*(){try{let o=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${i}:/${n}.${r}:/content`;console.log("Endpoint upload file Folder : ",o,n,r);let s=yield this.http.put(o,a,{headers:new h.f({Authorization:"Bearer "+e})}).toPromise();return s&&s.name&&console.log("File has been uploaded successfully !  : ",s.name),Promise.resolve(s)}catch(o){return console.log(`Error at file uploading for ${a}  : ${o}`),o}}))}createYearFolder(e,t,i,n){return Object(o.c)(this,void 0,void 0,(function*(){yield this.createFolder(e,t,i,n)}))}createMonthFolder(e,t,i,n){return Object(o.c)(this,void 0,void 0,(function*(){yield this.createFolder(e,t,i,n)}))}createVendorFolder(e,t,i,n){return Object(o.c)(this,void 0,void 0,(function*(){yield this.createFolder(e,t,i,n)}))}getChannelItemID(e,t,i){return Object(o.c)(this,void 0,void 0,(function*(){let n,r=`https://graph.microsoft.com/v1.0/groups/${t}/drive/root/children`;try{console.log("Endpoint : ",r);let t=yield this.http.get(r,{headers:new h.f({Authorization:"Bearer "+e})}).toPromise();return console.log("Response Data : ",t),t&&t.value.length>0&&(n=t.value.find(e=>e.name===i),n=n.id),Promise.resolve(n)}catch(o){return console.log("Error at getting channel's item id : ",o),o}}))}getFolderDetails(e,t,i){return Object(o.c)(this,void 0,void 0,(function*(){try{let n=[],r=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${i}/children`;console.log("Endpoint get Folder Details : ",r);let o=yield this.http.get(r,{headers:new h.f({Authorization:"Bearer "+e})}).toPromise();return o&&o.value.length>0&&c.each(o.value,e=>{e.folder&&n.push(e)}),Promise.resolve(n)}catch(n){return console.log("Error at getting folder names : ",n),n}}))}getFileandFolderDetails(e,t,i){return Object(o.c)(this,void 0,void 0,(function*(){try{let n=[],r=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${i}/children`;console.log("Endpoint : ",r);let o=yield this.http.get(r,{headers:new h.f({Authorization:"Bearer "+e})}).toPromise();return o&&o.value.length>0&&c.each(o.value,e=>{n.push(e)}),Promise.resolve(n)}catch(n){return console.log("Error at getting folder names : ",n),n}}))}createFolder(e,t,i,n){return Object(o.c)(this,void 0,void 0,(function*(){try{let r=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${i}/children`;console.log(`Endpoint Create Folder for ${n}: ${r}`);const o={name:n,folder:{},"@microsoft.graph.conflictBehavior":"rename"};let a=yield this.http.post(r,o,{headers:new h.f({Authorization:"Bearer "+e,"Content-Type":"application/json"})}).toPromise();return a&&a.name&&console.log("New folder has been created successfully !  : ",a.name),Promise.resolve(a)}catch(r){return console.log(`Error at creating new folder for ${n}  : `,r),r}}))}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](p.a),u["\u0275\u0275directiveInject"](f.a),u["\u0275\u0275directiveInject"](m.a),u["\u0275\u0275directiveInject"](g.a),u["\u0275\u0275directiveInject"](v.a),u["\u0275\u0275directiveInject"](r.g),u["\u0275\u0275directiveInject"](_.b),u["\u0275\u0275directiveInject"](y.a),u["\u0275\u0275directiveInject"](S.MailUtilityService),u["\u0275\u0275directiveInject"](y.a),u["\u0275\u0275directiveInject"](D.a),u["\u0275\u0275directiveInject"](b.a),u["\u0275\u0275directiveInject"](C.a),u["\u0275\u0275directiveInject"](h.c),u["\u0275\u0275directiveInject"](x.a))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["vendor-invoice-landing-page"]],decls:9,vars:2,consts:[[1,"container-fluid","vendor-invoice-landing-page","pl-0","pr-0"],[1,"row"],[1,"col-lg-10","col-sm-12","pr-0",2,"overflow-y","scroll","height","100vh"],[1,"col-lg-2","col-sm-12","px-0",2,"z-index","5000"],[1,"edit-nav","p-0"],[4,"ngIf"],["class","py-3 px-3 slide-from-down row",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"py-3","px-3","slide-from-down","row",3,"ngClass","click"],[1,"col-10","px-0","my-auto"],[1,"col-2","px-0","my-auto"],["class","py-3 px-3 row",4,"ngFor","ngForOf"],[1,"py-3","px-3","row"],[1,"col-12","px-0","my-auto","px-2","sm-lines","shimmer",2,"border-radius","5px"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275elementStart"](1,"div",1),u["\u0275\u0275elementStart"](2,"div",2),u["\u0275\u0275element"](3,"udrf-header"),u["\u0275\u0275element"](4,"udrf-body"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"div",3),u["\u0275\u0275elementStart"](6,"mat-card",4),u["\u0275\u0275template"](7,M,2,1,"div",5),u["\u0275\u0275template"](8,A,2,1,"div",5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e&&(u["\u0275\u0275advance"](7),u["\u0275\u0275property"]("ngIf",!t.isSideNavLoading),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.isSideNavLoading))},directives:[I.a,O.a,w.a,n.NgIf,n.NgForOf,n.NgClass],styles:[".vendor-invoice-landing-page[_ngcontent-%COMP%]{overflow:hidden!important;height:83vh}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .infinite-scroll-fixed{min-height:40vh!important;max-height:54vh!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .infinite-scroll-fixed-shortened{max-height:64vh!important;min-height:50vh!important;overflow-y:scroll;overflow-x:scroll}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .search-bar .mat-form-field{width:16vw!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .search-bar{padding-left:0!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .udrf-header-card{background-color:#fafafa!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]     owl-carousel-o{max-width:65vw!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .owl-item{max-width:15.5vw!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .owl-item .mat-select-item{width:15vw!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{font-size:13px!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .vendor-invoice-landing-page[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-size:14px;font-weight:300;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{color:#4a4a4a;font-size:12px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .vendor-invoice-landing-page[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.vendor-invoice-landing-page[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .sr-nav-item[_ngcontent-%COMP%]{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:.3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]{font-weight:400;cursor:pointer}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]:hover{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:box-shadow .3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .edit-nav[_ngcontent-%COMP%]{width:15.5vw;overflow-y:scroll;height:83vh;overflow-x:hidden!important;background-color:#fff;color:#000}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{color:#4e4c4c;font-size:13px!important;font-weight:400!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .shimmer[_ngcontent-%COMP%]{background:#f6f7f8;background-image:linear-gradient(90deg,#f6f7f8 0,#edeef1 20%,#f6f7f8 100%,#f6f7f8 0);background-repeat:no-repeat;background-size:800px 400px;display:inline-block;position:relative;-webkit-animation-duration:1s;-webkit-animation-fill-mode:forwards;-webkit-animation-iteration-count:infinite;-webkit-animation-name:placeholderShimmer;-webkit-animation-timing-function:linear}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .sm-lines[_ngcontent-%COMP%]{height:30px;margin-top:10px;width:100px}@keyframes placeholderShimmer{0%{background-position:-468px 0}to{background-position:468px 0}}.vendor-invoice-landing-page[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"],data:{animation:[Object(a.o)("slideInOut",[Object(a.l)("in",Object(a.m)({height:"*",overflow:"hidden"})),Object(a.l)("out",Object(a.m)({height:0,overflow:"hidden"})),Object(a.n)("* => in",[Object(a.m)({height:0}),Object(a.e)(250,Object(a.m)({height:"*"}))]),Object(a.n)("in=> *",[Object(a.m)({height:"*"}),Object(a.e)(250,Object(a.m)({height:0}))])]),Object(a.o)("smallCardAnimation",[Object(a.n)("* => *",[Object(a.i)(":leave",[Object(a.k)(100,[Object(a.e)("0.5s",Object(a.m)({opacity:0}))])],{optional:!0}),Object(a.i)(":enter",[Object(a.m)({opacity:0}),Object(a.k)(100,[Object(a.e)("0.5s",Object(a.m)({opacity:1}))])],{optional:!0})])])]}}),e})()}];let F=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(T)],r.k]}),e})();var k=i("Xi0T"),U=i("4/q7"),N=i("Kb4U");let R=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,F,k.a,w.d,U.b,N.b]]}),e})()},H44p:function(e,t,i){"use strict";i.d(t,"a",(function(){return g}));var n=i("xG9w"),r=i("fXoL"),o=i("flaP"),a=i("ofXK"),s=i("Qu3c"),l=i("NFeN");function d(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"div",10),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div"),r["\u0275\u0275elementStart"](5,"p",11),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"p",12),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](e.label),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function c(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",13),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",14),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",16),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",17),r["\u0275\u0275elementStart"](1,"span",18),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function f(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",19),r["\u0275\u0275text"](1,"loop"),r["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",1),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().change()})),r["\u0275\u0275template"](1,d,9,4,"div",2),r["\u0275\u0275template"](2,c,3,2,"div",3),r["\u0275\u0275template"](3,h,3,3,"div",4),r["\u0275\u0275template"](4,u,3,3,"div",5),r["\u0275\u0275template"](5,p,3,3,"div",6),r["\u0275\u0275elementStart"](6,"div",7),r["\u0275\u0275template"](7,f,2,0,"mat-icon",8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","big"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","small"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","medium"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","large"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","overview"==e.type),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",e.toDisplay)}}let g=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=n.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=n.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||n.contains(["big","small"],this.type)?0==this.isConvertValue&&n.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&r["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&r["\u0275\u0275property"]("ngIf",t.currency)},directives:[a.NgIf,s.a,l.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},HmYF:function(e,t,i){"use strict";i.d(t,"a",(function(){return d}));var n=i("mrSG"),r=i("Iab2"),o=i("EUZL"),a=i("wd/R"),s=i("xG9w"),l=i("fXoL");let d=(()=>{class e{constructor(){this.formatColumn=(e,t,i)=>{const n=o.utils.decode_range(e["!ref"]);for(let r=n.s.r+1;r<=n.e.r;++r){const n=o.utils.encode_cell({r:r,c:t});e[n]&&e[n].v&&(e[n].t="d",e[n].z=i)}}}exportAsExcelFile(e,t,i,n,r){console.log("Excel to JSON Service",e);const a=o.utils.json_to_sheet(e);if(r&&r.length){const e=o.utils.sheet_to_json(a,{header:1}).shift();for(const t of r){const i=e.indexOf(t.fieldKey);this.formatColumn(a,i,t.fieldFormat)}}null==i&&(i=[]),null==n&&(n="DD-MM-YYYY"),this.formatExcelDateData(a,i,n);const s=o.write({Sheets:{data:a},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}formatExcelDateData(e,t,i){for(let o of Object.keys(e))if(null!=e[o]&&null!=e[o].t&&null!=e[o].v&&a(e[o].v,i,!0).isValid()){let n=o.replace(/[0-9]/g,"")+"1";0==s.where(t,{value:e[n].v}).length&&null!=e[n]&&null!=e[n].t&&t.push({value:e[n].v,format:i})}let n=[],r=1;for(let o of t)for(let t of Object.keys(e)){let i=parseInt(t.replace(/[^0-9]/g,""));i>r&&(r=i),null!=e[t]&&null!=e[t].v&&e[t].v==o.value&&n.push({value:t.replace(/[0-9]/g,""),format:o.format})}for(let o of n)for(let t=2;t<=r;t++)null!=e[o.value+""+t]&&null!=e[o.value+""+t].t&&(e[o.value+""+t].t="d",null!=e[o.value+""+t].v&&"Invalid date"!=e[o.value+""+t].v?e[o.value+""+t].v=a(e[o.value+""+t].v,o.format).format("YYYY/MM/DD"):(console.log(e[o.value+""+t].t),e[o.value+""+t].v="",e[o.value+""+t].t="s"))}saveAsExcelFile(e,t){const i=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});r.saveAs(i,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,i){const n=o.utils.json_to_sheet(e),r=o.utils.json_to_sheet(t),a=o.write({Sheets:{All_Approvals:n,Pending_Approvals:r},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,i)}exportAsExcelFileForPayroll(e,t,i,n,r,a){const s=o.utils.json_to_sheet(e),l=o.utils.json_to_sheet(t),d=o.utils.json_to_sheet(i),c=o.utils.json_to_sheet(n),h=o.utils.json_to_sheet(r),u=o.write({Sheets:{Regular_Report:s,Intern_Report:l,Contract_Report:d,Perdiem_Report:c,RP_Report:h},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(u,a)}exportAsCsvFileWithSheetName(e,t){return Object(n.c)(this,void 0,void 0,(function*(){let i=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(i,e,t.sheetName)}let n=o.write(i,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(n,t)}))}saveAsCsvFile(e,t){return Object(n.c)(this,void 0,void 0,(function*(){const i=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});r.saveAs(i,t.concat(".csv"))}))}s2ab(e){return Object(n.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),i=new Uint8Array(t),n=0;n<e.length;n++)i[n]=255&e.charCodeAt(n);return t}))}exportAsExcelFileWithCellMerge(e,t,i){const n=o.utils.json_to_sheet(e);n["!merges"]=i;const r=o.write({Sheets:{data:n},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(n.c)(this,void 0,void 0,(function*(){let i=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(i,e,t.sheetName)}let n=o.write(i,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));class n{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return h}));var n=i("mrSG"),r=i("XNiG"),o=i("xG9w"),a=i("fXoL"),s=i("tk/3"),l=i("LcQX"),d=i("XXEo"),c=i("flaP");let h=(()=>{class e{constructor(e,t,i,n){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=n,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,n,r,o,a){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:n,skip:r,limit:o,filterConfig:a,orgIds:s})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,n,r,o,a){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:n,skip:r,limit:o,filterConfig:a,orgIds:s})}getRequestsForAwaitingApproval(e,t,i,n){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:n})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,n){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:n,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,r,a,s,l){return Object(n.c)(this,void 0,void 0,(function*(){let n;n=s&&s.length>1&&(yield this.getManpowerCostByOId(s,i,a,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,a,l));let d=yield this.getNonManpowerCost(t,i,r,a,2),c=yield this.getAllocatedCost(),h=0;h=(n?n.cost:0)+d.length>0?o.reduce(o.pluck(d,"cost"),(e,t)=>e+t,0):0;let u=c.length>0?o.reduce(o.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:h,currency:n&&n.currency_code?n.currency_code:"",manpowerCost:n,nonManpowerCost:d,allocatedCost:c,allocatedCostValue:h*(u/100)}}))}getManpowerCostBasedOnPosition(e,t,i,n,r){return new Promise((o,a)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:n,position:r}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getNonManpowerCost(e,t,i,n,r){return new Promise((o,a)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:n,currency_id:r}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,n){return new Promise((r,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:n}).subscribe(e=>r(e),e=>(console.log(e),o(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](s.c),a["\u0275\u0275inject"](l.a),a["\u0275\u0275inject"](d.a),a["\u0275\u0275inject"](c.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var n=i("mrSG"),r=i("xG9w"),o=i("fXoL"),a=i("tk/3"),s=i("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>i(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>i(e))})}getApproversHierarchy(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>i(e))})}createWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>i(e))})}getWorkflowDetails(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(n.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<e.length;i++){let n=[],o=r.keys(t["cc"+i]);for(let r=0;r<o.length;r++)for(let a=0;a<t["cc"+i][o[r]].length;a++){let s={name:t["cc"+i][o[r]][a].DELEGATE_NAME,oid:t["cc"+i][o[r]][a].DELEGATE_OID,level:r+1,designation:t["cc"+i][o[r]][a].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+i][o[r]][a].IS_DELEGATED,role:t["cc"+i][o[r]][a].DELEGATE_ROLE_NAME};if(1==t["cc"+i][o[r]][a].IS_DELEGATED&&(s.delegated_by={name:t["cc"+i][o[r]][a].APPROVER_NAME,oid:t["cc"+i][o[r]][a].APPROVER_OID,level:r+1,designation:t["cc"+i][o[r]][a].APPROVER_DESIGNATION_NAME}),n.push(s),i==e.length-1&&r==o.length-1&&a==t["cc"+i][o[r]].length-1)return n}}}))}storeComments(e,t,i){return new Promise((n,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:i}).subscribe(e=>n(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(n.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let i=[],n=r.keys(e["cc"+t]);for(let r=0;r<n.length;r++)for(let o=0;o<e["cc"+t][n[r]].length;o++){let a={name:e["cc"+t][n[r]][o].DELEGATE_NAME,oid:e["cc"+t][n[r]][o].DELEGATE_OID,level:e["cc"+t][n[r]][o].APPROVAL_ORDER,designation:e["cc"+t][n[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][n[r]][o].IS_DELEGATED};if(1==e["cc"+t][n[r]][o].IS_DELEGATED&&(a.delegated_by={name:e["cc"+t][n[r]][o].APPROVER_NAME,oid:e["cc"+t][n[r]][o].APPROVER_OID,level:e["cc"+t][n[r]][o].APPROVAL_ORDER,designation:e["cc"+t][n[r]][o].APPROVER_DESIGNATION_NAME}),i.push(a),r==n.length-1&&o==e["cc"+t][n[r]].length-1)return i}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.c),o["\u0275\u0275inject"](s.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return d})),i.d(t,"b",(function(){return c}));var n=i("jhN1"),r=i("fXoL"),o=i("oHs6"),a=i("PVOt"),s=i("6t9p");const l=["*"];let d=(()=>{let e=class extends a.b{constructor(e,t,i,n,r,o,a,s){super(e,t,i,n,a,s),this._watcherHelper=n,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new o.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),n=null!==this._idh.getChanges(e,t);(i||n)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](a.e),r["\u0275\u0275directiveInject"](a.j),r["\u0275\u0275directiveInject"](a.g),r["\u0275\u0275directiveInject"](a.i),r["\u0275\u0275directiveInject"](n.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&r["\u0275\u0275contentQuery"](i,s.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([a.e,a.j,a.i,a.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,a.c,a.f,n.b],s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,a.f]}),e})()}}]);