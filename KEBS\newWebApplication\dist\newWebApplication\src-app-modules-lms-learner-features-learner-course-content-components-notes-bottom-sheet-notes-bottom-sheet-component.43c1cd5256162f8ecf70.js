(window.webpackJsonp=window.webpackJsonp||[]).push([[962],{Zjln:function(e,t,o){"use strict";o.r(t),o.d(t,"NotesBottomSheetComponent",(function(){return b}));var n=o("bEYa"),r=o("3Pt+"),i=o("2ChS"),c=o("33Jv"),s=o("fXoL"),l=o("bpb9"),d=o("ofXK"),a=o("bTqV"),u=o("NFeN"),p=o("FKr1");const m=function(e){return{"color-circle-clicked ":e}},h=function(e){return{backgroundColor:e}};function g(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",8),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const o=t.$implicit;return s["\u0275\u0275nextContext"]().selectColor(o)})),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,o=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](2,m,o.noteFormGroup.value.color==e))("ngStyle",s["\u0275\u0275pureFunction1"](4,h,e))}}let b=(()=>{class e{constructor(e,t,o,i){this._bottomSheetRef=e,this._LmsLearnerService=t,this.noteData=o,this.fb=i,this.toolbar=[["bold","italic"],["underline"],["code"],["ordered_list","bullet_list"],["link"]],this.colors=["#f7d1d1","#d4d4d4","#d2f7e1","#ffeddb"],this.editorFormControl=new r.j,this.subs=new c.a,this.editor=new n.a,this.noteFormGroup=this.fb.group({noteContent:["",r.H.required],color:["white"]})}ngOnInit(){this.editorFormControl.valueChanges.subscribe(e=>{this.noteFormGroup.get("noteContent").patchValue(Object(n.g)(e)+"")})}selectColor(e){this.noteFormGroup.get("color").patchValue(e),console.log(this.noteFormGroup.value)}saveNote(){console.log(this.noteFormGroup.value),this.noteFormGroup.valid?this.subs.sink=this._LmsLearnerService.createNote({courseId:this.noteData.courseId,contentId:this.noteData.contentId,lessonId:this.noteData.lessonId,sectionId:this.noteData.sectionId,isInstructor:!1,noteContent:this.noteFormGroup.value.noteContent,color:this.noteFormGroup.value.color}).subscribe(e=>{this._LmsLearnerService.showSnack("Notes added successfully !"),this._bottomSheetRef.dismiss(e)},e=>{console.error(e),this._LmsLearnerService.showSnack("Error while adding notes !")}):this._LmsLearnerService.showSnack("Kindly enter the note")}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](i.d),s["\u0275\u0275directiveInject"](l.a),s["\u0275\u0275directiveInject"](i.a),s["\u0275\u0275directiveInject"](r.i))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-notes-bottom-sheet"]],decls:12,vars:5,consts:[[1,"py-2",2,"color","#cf0001","font-weight","500"],[1,"editor"],[3,"editor","toolbar"],[1,"color-picker"],["class","color-circle","matRipple","",3,"ngClass","ngStyle","click",4,"ngFor","ngForOf"],[3,"editor","formControl"],[1,"d-flex","justify-content-center","my-3","w-100"],["mat-raised-button","",1,"save-btn",3,"click"],["matRipple","",1,"color-circle",3,"ngClass","ngStyle","click"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"h3",0),s["\u0275\u0275text"](1,"Add a note"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](2,"div",1),s["\u0275\u0275element"](3,"ngx-editor-menu",2),s["\u0275\u0275elementStart"](4,"div",3),s["\u0275\u0275template"](5,g,1,6,"div",4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](6,"ngx-editor",5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"div",6),s["\u0275\u0275elementStart"](8,"button",7),s["\u0275\u0275listener"]("click",(function(){return t.saveNote()})),s["\u0275\u0275elementStart"](9,"mat-icon"),s["\u0275\u0275text"](10,"done_all"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](11," Save "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("editor",t.editor)("toolbar",t.toolbar),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",t.colors),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("editor",t.editor)("formControl",t.editorFormControl))},directives:[n.b,d.NgForOf,n.c,r.v,r.k,a.a,u.a,p.u,d.NgClass,d.NgStyle],styles:[".editor[_ngcontent-%COMP%]{border:1px solid rgba(0,0,0,.13);border-radius:5px;min-height:10rem}.editor[_ngcontent-%COMP%]    .NgxEditor__MenuBar{display:flex;padding:.2rem;border-bottom:1px solid rgba(0,0,0,.13);cursor:default;background-color:#fff}.editor[_ngcontent-%COMP%]    .NgxEditor{border:none!important}.editor[_ngcontent-%COMP%]     .mat-bottom-sheet-container-large{width:800px;overflow-x:hidden}.editor[_ngcontent-%COMP%]   .CodeMirror[_ngcontent-%COMP%]{border:1px solid #eee;height:auto;margin-bottom:.7rem}.editor[_ngcontent-%COMP%]   .CodeMirror[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{white-space:pre!important}.color-circle[_ngcontent-%COMP%]{height:15px;width:15px;border-radius:50%;display:inline-block;margin-right:7px;cursor:pointer}.color-picker[_ngcontent-%COMP%]{top:-27px;position:relative;right:-18rem}.color-circle-clicked[_ngcontent-%COMP%]{border:1px solid}.save-btn[_ngcontent-%COMP%]{color:#fff;background-color:#cf0001;width:9rem}.save-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{height:16px;width:16px;color:#fff;font-size:16px}"]}),e})()}}]);