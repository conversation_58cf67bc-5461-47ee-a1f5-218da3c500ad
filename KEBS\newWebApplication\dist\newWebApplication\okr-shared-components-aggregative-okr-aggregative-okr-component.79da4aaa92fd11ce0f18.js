(window.webpackJsonp=window.webpackJsonp||[]).push([[826],{Fkny:function(e,t,i){"use strict";i.r(t),i.d(t,"AggregativeOkrComponent",(function(){return y}));var r=i("0IaG"),n=i("fXoL"),l=i("25DO"),o=i("LcQX"),a=i("3Pt+"),s=i("BVzC"),c=i("XXEo"),d=i("bTqV"),p=i("NFeN"),m=i("ofXK"),h=i("kmnG"),g=i("d3UM"),u=i("QH2n"),f=i("FKr1");function v(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",22),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.value),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.viewValue," ")}}function b(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"div",11),n["\u0275\u0275elementStart"](1,"form",16),n["\u0275\u0275elementStart"](2,"div",12),n["\u0275\u0275elementStart"](3,"mat-form-field",17),n["\u0275\u0275elementStart"](4,"mat-select",18),n["\u0275\u0275listener"]("selectionChange",(function(t){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().getSelectedName(t.value)})),n["\u0275\u0275template"](5,v,2,2,"mat-option",19),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"div",20),n["\u0275\u0275element"](7,"app-input-search-okr",21),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}if(2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formGroup",e.AggregateList),n["\u0275\u0275advance"](4),n["\u0275\u0275property"]("ngForOf",e.types),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("required",!0)("placeholder",e.placeHolder)("list",e.list)("multiple",!1)}}function E(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-icon",23),n["\u0275\u0275text"](1,"done_all"),n["\u0275\u0275elementEnd"]())}function S(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"div",24),n["\u0275\u0275elementStart"](1,"span",25),n["\u0275\u0275text"](2,"Loading..."),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]())}let y=(()=>{class e{constructor(e,t,i,r,n,l,o,a){this._okr=e,this._util=t,this._formBuilder=i,this._matDialogRef=r,this.data=n,this._dialog=l,this._ErrorService=o,this._loginService=a,this.AggregateList=this._formBuilder.group({type:[""],lists:[""]}),this.isUpdate=!1,this.list=[],this.placeHolder="Search",this.types=[{value:"Objective",viewValue:"Objective"},{value:"Initiative",viewValue:"Initiative"}],console.log(this.data),this.name=this.data.name,this.id=this.data.id}ngOnInit(){}getSelectedName(e){this.AggregateList.get("type").patchValue(e),"Objective"==e?(this.placeHolder="Search Objective",this._okr.getObjectiveAggregate({id:this.id}).subscribe(e=>{this.list=e.data,console.log(this.list)},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),this.closeDialog("")})):"KeyResult"==e?(this.placeHolder="Search Key Result",this._okr.getKRAggregate({id:this.id}).subscribe(e=>{this.list=e.data,console.log(this.list)},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),this.closeDialog("")})):"Initiative"==e&&(this.placeHolder="Search Initiative",this._okr.getInitAggregate({id:this.id}).subscribe(e=>{this.list=e.data,console.log(this.list)},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),this.closeDialog("")}))}onSave(){console.log(this.AggregateList);let e=this.AggregateList.value.lists,t=this.AggregateList.value.type;if(this.isUpdate=!0,"KeyResult"==this.name){let i={parent_kr_id:this.id,child_type:t,child_id:e._id};console.log(i),this._okr.createKRAggregate(i).subscribe(e=>{"You don't have access to perform the required action"!=e.msg?(this._util.showMessage("KeyResult Successfully Aggregated","Dismiss",3e3),this.closeDialog("sucess")):(this._util.showMessage(e.msg,"Dismiss"),this.closeDialog(""))},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),this.closeDialog("")})}else if("Initiative"==this.name){let i={parent_init_id:this.id,child_type:t,child_id:e._id};console.log(i),this._okr.createInitAggregate(i).subscribe(e=>{"You don't have access to perform the required action"!=e.msg?(this._util.showMessage("Initiative Successfully Aggregated","Dismiss",3e3),this.closeDialog("sucess")):(this._util.showMessage(e.msg,"Dismiss"),this.closeDialog(""))},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),this.closeDialog("")})}}closeDialog(e){this._matDialogRef.close(e)}get token(){return this._loginService.getToken()}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](l.a),n["\u0275\u0275directiveInject"](o.a),n["\u0275\u0275directiveInject"](a.i),n["\u0275\u0275directiveInject"](r.h),n["\u0275\u0275directiveInject"](r.a),n["\u0275\u0275directiveInject"](r.b),n["\u0275\u0275directiveInject"](s.a),n["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-aggregative-okr"]],decls:19,vars:4,consts:[[1,"row","px-3","py-2"],[1,"col-12","p-0","m-0"],[1,"row","mt-3","p-0"],[1,"col-12","m-0","p-0"],[1,"row"],[1,"col-10","pl-0","pr-0"],[1,"pl-2",2,"font-size","16px","font-weight","500","color","brown"],[1,"col-2","p-0"],["mat-icon-button","",1,"close-btn",3,"click"],[2,"font-size","20px","line-height","5px"],["class","row mt-1 p-0",4,"ngIf"],[1,"row","mt-1","p-0"],[1,"col-12"],["mat-mini-fab","",1,"done-all-btn",3,"disabled","click"],["style","color: white; font-size: 21px",4,"ngIf"],["class","spinner-border text-danger","role","status",4,"ngIf"],[3,"formGroup"],["appearance","outline"],["placeholder","Select Type",3,"selectionChange"],[3,"value",4,"ngFor","ngForOf"],[1,"col-12","pt-2"],["formControlName","lists",3,"required","placeholder","list","multiple"],[3,"value"],[2,"color","white","font-size","21px"],["role","status",1,"spinner-border","text-danger"],[1,"sr-only"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"div",0),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275elementStart"](4,"div",4),n["\u0275\u0275elementStart"](5,"div",5),n["\u0275\u0275elementStart"](6,"span",6),n["\u0275\u0275text"](7,"Aggregate OKR"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](8,"div",7),n["\u0275\u0275elementStart"](9,"button",8),n["\u0275\u0275listener"]("click",(function(){return t.closeDialog("")})),n["\u0275\u0275elementStart"](10,"mat-icon",9),n["\u0275\u0275text"](11,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](12,b,8,6,"div",10),n["\u0275\u0275elementStart"](13,"div",11),n["\u0275\u0275elementStart"](14,"div",12),n["\u0275\u0275elementStart"](15,"button",13),n["\u0275\u0275listener"]("click",(function(){return t.onSave()})),n["\u0275\u0275template"](16,E,2,0,"mat-icon",14),n["\u0275\u0275template"](17,S,3,0,"div",15),n["\u0275\u0275text"](18," Done "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("ngIf","KeyResult"==t.name||"Initiative"==t.name),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("disabled",t.isUpdate),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",!t.isUpdate),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.isUpdate))},directives:[d.a,p.a,m.NgIf,a.J,a.w,a.n,h.c,g.c,m.NgForOf,u.a,a.v,a.l,a.F,f.p],styles:[".close-btn[_ngcontent-%COMP%]{color:grey}.done-all-btn[_ngcontent-%COMP%]{width:11.8rem;border-radius:4px;background-color:#cf0001;color:#fff;box-shadow:0 1px 3px 0 rgba(0,0,0,.51),0 0 14px 0 rgba(0,0,0,.11);margin-bottom:8px}"]}),e})()},QH2n:function(e,t,i){"use strict";i.d(t,"a",(function(){return E}));var r=i("fXoL"),n=i("3Pt+"),l=i("jtHE"),o=i("XNiG"),a=i("NJ67"),s=i("1G5W"),c=i("ofXK"),d=i("kmnG"),p=i("d3UM"),m=i("FKr1"),h=i("WJ5W"),g=i("Qu3c");function u(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const i=t.$implicit;return r["\u0275\u0275nextContext"](2).emitChanges(i)})),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](3,"br"),r["\u0275\u0275elementStart"](4,"span"),r["\u0275\u0275text"](5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),r["\u0275\u0275property"]("value",e),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"]("ORG NAME : ",e.Org_name,""),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"]("NAME : ",e.name,"")}}function f(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-form-field",1),r["\u0275\u0275elementStart"](1,"mat-label"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-select",2,3),r["\u0275\u0275elementStart"](5,"mat-option"),r["\u0275\u0275element"](6,"ngx-mat-select-search",4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"mat-option",5),r["\u0275\u0275text"](8,"None"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](9,u,6,4,"mat-option",6),r["\u0275\u0275pipe"](10,"async"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("value",null),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",r["\u0275\u0275pipeBind1"](10,9,e.filteredList))}}function v(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const i=t.$implicit;return r["\u0275\u0275nextContext"](2).emitChanges(i)})),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),r["\u0275\u0275property"]("value",e),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"]("NAME : ",e.name,"")}}function b(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-form-field",1),r["\u0275\u0275elementStart"](1,"mat-label"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-select",8,3),r["\u0275\u0275elementStart"](5,"mat-option"),r["\u0275\u0275element"](6,"ngx-mat-select-search",4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"mat-option",5),r["\u0275\u0275text"](8,"None"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](9,v,3,3,"mat-option",6),r["\u0275\u0275pipe"](10,"async"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("value",null),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",r["\u0275\u0275pipeBind1"](10,9,e.filteredList))}}let E=(()=>{class e extends a.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new n.j,this.fieldFilterCtrl=new n.j,this.list=[],this.placeholder="Search",this.required=!1,this.valueChange=new r.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this.change=new r.EventEmitter,this._onDestroy=new o.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){console.log(this.list,this.placeholder),this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.Renderer2))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-okr"]],inputs:{list:"list",multiple:"multiple",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:n.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],decls:2,vars:2,consts:[["appearance","outline",4,"ngIf"],["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[1,"custom-opt",3,"value"],["class","custom-opt",3,"matTooltip","value","click",4,"ngFor","ngForOf"],[1,"custom-opt",3,"matTooltip","value","click"],["multiple","",3,"formControl","placeholder","required","disabled"]],template:function(e,t){1&e&&(r["\u0275\u0275template"](0,f,11,11,"mat-form-field",0),r["\u0275\u0275template"](1,b,11,11,"mat-form-field",0)),2&e&&(r["\u0275\u0275property"]("ngIf",0==t.multiple),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==t.multiple))},directives:[c.NgIf,d.c,d.g,p.c,n.v,n.k,n.F,m.p,h.a,c.NgForOf,g.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}.custom-opt[_ngcontent-%COMP%]{line-height:2em!important;height:auto!important;border-bottom:groove;border-bottom-color:#fff}"]}),e})()}}]);