(window.webpackJsonp=window.webpackJsonp||[]).push([[912],{Q1Hw:function(e,t,n){"use strict";n.r(t),n.d(t,"EmailLogsDialogComponent",(function(){return k}));var i=n("mrSG"),r=n("+rOU"),o=n("0IaG"),a=n("XNiG"),s=n("1G5W"),c=n("fXoL"),l=n("YVm3"),d=n("XNFG"),g=n("rDax"),h=n("NFeN"),m=n("jaxi"),f=n("ofXK"),C=n("3Pt+"),p=n("Xa2L"),u=n("vzmP"),v=n("IeBn");const y=["triggerSearchBarTemplateRef"],w=function(e){return{"btn-toggle-selected":e}};function L(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"mat-button-toggle",28),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("value",e.id)("ngClass",c["\u0275\u0275pureFunction1"](3,w,n.selectedToggle==e.id)),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function O(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",29,30),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275reference"](2);return c["\u0275\u0275nextContext"]().openSearchBarOverlay(t,-36,0)})),c["\u0275\u0275elementStart"](3,"div",31),c["\u0275\u0275elementStart"](4,"input",32,33),c["\u0275\u0275listener"]("ngModelChange",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().searchParams=t}))("keydown.backspace",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"]();return t.onEnterSearch(t.searchParams)})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",34),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](7,"svg",35),c["\u0275\u0275element"](8,"path",36),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("ngModel",e.searchParams)}}function b(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",37,30),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275reference"](2);return c["\u0275\u0275nextContext"]().openSearchBarOverlay(t,-36,-23)})),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](3,"svg",35),c["\u0275\u0275element"](4,"path",36),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function x(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",38),c["\u0275\u0275element"](1,"mat-spinner",39),c["\u0275\u0275elementEnd"]())}function S(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",40),c["\u0275\u0275element"](1,"img",41),c["\u0275\u0275elementStart"](2,"div",42),c["\u0275\u0275text"](3,"No Logs Found"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]())}function E(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"app-list-view",43),c["\u0275\u0275listener"]("onClick",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onClickRowData(t)})),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("list",e.mailLogsData)("fieldConfig",e.fieldConfig)("variant",2)("isCheckboxActive",!1)("disableScroll",!0)}}const _=function(){return[]};function P(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"app-search-overlay",44),c["\u0275\u0275listener"]("onEnter",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onEnterSearch(t)})),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("currentSearchText",e.searchParams)("recentSearch",c["\u0275\u0275pureFunction0"](2,_))}}const M=function(e){return{"pointer-events":e}};let k=(()=>{class e{constructor(e,t,n,i,r,o){this.data=e,this._dialogRef=t,this._jobService=n,this._toaster=i,this._overlay=r,this._viewContainerRef=o,this._onDestroy=new a.b,this.dynamicSubHeight="",this.searchParams="",this.isLoading=!0,this.selectedToggle=1,this.toggleList=[{id:1,name:"Candidate Email",isSelected:!0},{id:2,name:"College Email",isSelected:!1}],this.fieldConfig=[],this.mailLogsData=[]}ngOnInit(){var e;return Object(i.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),this.fieldConfig=(null===(e=this.data.logsConfig)||void 0===e?void 0:e.candidateEmail)||[],this.getEmailLogsForJobId()}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){this.dynamicSubHeight=window.innerHeight-140+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight)}onClose(){this._dialogRef.close()}handleSectionSelect(e){var t,n;this.selectedToggle=e.value,this.toggleList.forEach(t=>{t.isSelected=t.id==e.value}),this.fieldConfig=1==this.selectedToggle?(null===(t=this.data.logsConfig)||void 0===t?void 0:t.candidateEmail)||[]:(null===(n=this.data.logsConfig)||void 0===n?void 0:n.collegeEmail)||[],this.getEmailLogsForJobId()}onClickRowData(e){return Object(i.c)(this,void 0,void 0,(function*(){e.functionName&&""!=e.functionName&&(yield this[e.functionName](e.data))}))}openSearchBarOverlay(e,t,n){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);i.withDefaultOffsetY(t),n&&i.withDefaultOffsetX(n);const o=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:o,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const a=new r.h(this.triggerSearchBarTemplateRef,this._viewContainerRef);this.overlayRef.attach(a),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}onEnterSearch(e){return Object(i.c)(this,void 0,void 0,(function*(){this.searchParams=e,this.closeOverlay(),yield this.getEmailLogsForJobId()}))}reTriggerEmail(e){return Object(i.c)(this,void 0,void 0,(function*(){return this.data.editAccess?0==e.email_status?this._toaster.showWarning("Warning \u26a0\ufe0f","Please await a moment as the email is currently being processed. It will be sent shortly!",7e3):1==e.email_status?this._toaster.showWarning("Warning \u26a0\ufe0f","Email Already Sent!",7e3):(yield this.retriggerEmail(e.email_log_id),void(yield this.getEmailLogsForJobId())):this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3)}))}getEmailLogsForJobId(){return Object(i.c)(this,void 0,void 0,(function*(){return this.mailLogsData=[],this.isLoading=!0,new Promise((e,t)=>this._jobService.getEmailLogsForJobId(this.data.jobId,this.searchParams,2==this.selectedToggle,1==this.selectedToggle).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.mailLogsData=t.data:this._toaster.showError("Error",t.msg,7e3),this.isLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Retrieval failed!",7e3),this.isLoading=!1,t()}}))}))}retriggerEmail(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._jobService.retriggerEmail(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success \u2705","Mail Retriggered Successfully!",7e3):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Retrieval failed!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](o.a),c["\u0275\u0275directiveInject"](o.h),c["\u0275\u0275directiveInject"](l.a),c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](g.e),c["\u0275\u0275directiveInject"](c.ViewContainerRef))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-email-logs-dialog"]],viewQuery:function(e,t){if(1&e&&c["\u0275\u0275viewQuery"](y,!0),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.triggerSearchBarTemplateRef=e.first)}},hostBindings:function(e,t){1&e&&c["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,c["\u0275\u0275resolveWindow"])},decls:31,vars:11,consts:[[1,"main-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"d-flex","align-items-center"],["width","269","height","56","viewBox","0 0 269 56","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M133.603 -22.5168V4.76469C133.603 8.35397 132.087 11.811 129.39 14.4067C125.91 17.7302 120.857 19.1903 115.947 18.2782L115.946 18.2781L93.9778 14.2877L103.74 5.17581L116.468 7.51274L116.468 7.51278C119.364 8.04124 122.088 5.97651 122.088 3.19398V-11.8208L133.603 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.019 14.2879L157.079 18.2781L157.078 18.2782C152.174 19.1903 147.127 17.73 143.651 14.4063L143.649 14.4043C140.909 11.856 139.394 8.40031 139.394 4.81089V-22.5164L150.895 -11.8208V2.96299C150.895 5.88334 153.764 8.04193 156.758 7.46638C156.758 7.46632 156.758 7.46626 156.759 7.46621L169.221 5.17578L179.019 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.603 37.6429V64.9403L122.088 54.256V38.8889C122.088 36.2929 119.563 34.3707 116.817 34.8512L116.816 34.8515L103.74 37.2322L93.9781 28.1306L115.946 24.1447L115.947 24.1445C120.857 23.2335 125.91 24.6921 129.391 28.012C132.087 30.6048 133.603 34.0579 133.603 37.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.394 64.9399V37.6584C139.394 34.0692 140.91 30.6122 143.607 28.0166C147.087 24.693 152.141 23.2328 157.05 24.1448L157.051 24.145L179.018 28.1352L169.258 37.2012L155.933 34.7718C155.933 34.7718 155.933 34.7718 155.933 34.7718C153.335 34.2893 150.91 36.1234 150.91 38.6285V54.2438L139.394 64.9399Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.5926L63.5512 55.5828L63.5504 55.5829C58.6468 56.495 53.5996 55.0347 50.1236 51.711L50.1214 51.709C47.3813 49.1606 45.8669 45.705 45.8669 42.1156V14.7883L57.3676 25.4838V40.2677C57.3676 43.188 60.2364 45.3466 63.2304 44.7711C63.2307 44.771 63.231 44.771 63.2313 44.7709L75.6933 42.4805L85.4914 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.2879L63.5512 55.2781L63.5504 55.2782C58.6468 56.1903 53.5996 54.73 50.1236 51.4063L50.1214 51.4043C47.3813 48.856 45.8669 45.4003 45.8669 41.8109V14.4836L57.3676 25.1792V39.963C57.3676 42.8833 60.2364 45.0419 63.2304 44.4664C63.2307 44.4663 63.231 44.4663 63.2313 44.4662L75.6933 42.1758L85.4914 51.2879Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 14.7878V42.0694C227.13 45.6587 225.614 49.1157 222.918 51.7114C219.437 55.0348 214.384 56.495 209.475 55.5829L209.474 55.5828L187.505 51.5924L197.267 42.4805L209.995 44.8174L209.995 44.8175C212.892 45.3459 215.615 43.2812 215.615 40.4987V25.4839L227.13 14.7878Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.546 51.5926L250.606 55.5828L250.605 55.5829C245.702 56.495 240.654 55.0347 237.178 51.711L237.176 51.709C234.436 49.1606 232.922 45.705 232.922 42.1156V14.7883L244.422 25.4838V40.2677C244.422 43.188 247.291 45.3466 250.285 44.7711C250.285 44.771 250.286 44.771 250.286 44.7709L262.748 42.4805L272.546 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],[1,"icon",3,"click"],[1,"icon"],[1,"d-flex","align-items-center","justify-content-between",2,"padding","0px 24px 16px 24px"],[3,"value","ngStyle","change"],["class","toggle-btn",3,"value","ngClass",4,"ngFor","ngForOf"],["class","d-flex align-items-center justify-content-between search-ui","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","header-icon","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","d-flex align-items-center justify-content-center loading-screen",4,"ngIf"],["class","empty-state",4,"ngIf"],[2,"padding","0px 24px"],[3,"list","fieldConfig","variant","isCheckboxActive","disableScroll","onClick",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerSearchBarTemplateRef",""],[1,"toggle-btn",3,"value","ngClass"],["cdkOverlayOrigin","",1,"d-flex","align-items-center","justify-content-between","search-ui",3,"click"],["triggerSearchBar","cdkOverlayOrigin","triggerSearchField",""],[1,"search-bar"],["type","text","placeholder","Search Here...",3,"ngModel","ngModelChange","keydown.backspace"],["inputField",""],[1,"header-icon"],["width","18","height","18","viewBox","0 0 18 18","fill","none"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["cdkOverlayOrigin","",1,"header-icon",3,"click"],[1,"d-flex","align-items-center","justify-content-center","loading-screen"],["diameter","40",1,"green-spinner"],[1,"empty-state"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"title"],[3,"list","fieldConfig","variant","isCheckboxActive","disableScroll","onClick"],[3,"currentSearchText","recentSearch","onEnter"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275text"](3,"Email Logs"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",3),c["\u0275\u0275elementStart"](5,"div"),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](6,"svg",4),c["\u0275\u0275element"](7,"path",5),c["\u0275\u0275element"](8,"path",6),c["\u0275\u0275element"](9,"path",7),c["\u0275\u0275element"](10,"path",8),c["\u0275\u0275element"](11,"path",9),c["\u0275\u0275element"](12,"path",10),c["\u0275\u0275element"](13,"path",11),c["\u0275\u0275element"](14,"path",12),c["\u0275\u0275element"](15,"path",13),c["\u0275\u0275element"](16,"path",14),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275namespaceHTML"](),c["\u0275\u0275elementStart"](17,"div",15),c["\u0275\u0275listener"]("click",(function(){return t.onClose()})),c["\u0275\u0275elementStart"](18,"mat-icon",16),c["\u0275\u0275text"](19,"close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](20,"div",17),c["\u0275\u0275elementStart"](21,"mat-button-toggle-group",18),c["\u0275\u0275listener"]("change",(function(e){return t.handleSectionSelect(e)})),c["\u0275\u0275template"](22,L,2,5,"mat-button-toggle",19),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](23,O,9,1,"div",20),c["\u0275\u0275template"](24,b,5,0,"div",21),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](25,x,2,0,"div",22),c["\u0275\u0275template"](26,S,4,0,"div",23),c["\u0275\u0275elementStart"](27,"div",24),c["\u0275\u0275template"](28,E,1,5,"app-list-view",25),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](29,P,1,3,"ng-template",26,27,c["\u0275\u0275templateRefExtractor"])),2&e&&(c["\u0275\u0275advance"](21),c["\u0275\u0275property"]("value",t.selectedToggle)("ngStyle",c["\u0275\u0275pureFunction1"](9,M,t.isLoading?"none":"")),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",t.toggleList),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",""!=t.searchParams&&null!=t.searchParams),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",""==t.searchParams||null==t.searchParams),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.mailLogsData||0==t.mailLogsData.length),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",t.mailLogsData&&t.mailLogsData.length>0),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerSearchBar))},directives:[h.a,m.b,f.NgStyle,f.NgForOf,f.NgIf,g.a,m.a,f.NgClass,g.b,C.e,C.v,C.y,p.c,u.a,v.a],styles:[".main-container[_ngcontent-%COMP%]{overflow:hidden}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{height:56px;padding:8px 24px;margin-bottom:20px;background-color:#f4f4f6}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.main-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;height:var(--dynamicSubHeight)}.main-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140;margin-bottom:4px}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:var(--dynamicSubHeight)}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]     .mat-button-toggle-label-content{font-size:12px!important;font-family:var(--atsfontFamily)!important}.main-container[_ngcontent-%COMP%]     .mat-button-toggle-checked{background-color:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem}.main-container[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;font-family:var(--atsfontFamily)!important;background-color:var(--atsprimaryColor)!important;color:#fff}.main-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{width:350px;height:36px;padding:0 8px;border:2px solid #dadce2;border-radius:8px;cursor:text}.main-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.main-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.main-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.main-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81;width:280px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.main-container[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{cursor:pointer}"]}),e})()}}]);