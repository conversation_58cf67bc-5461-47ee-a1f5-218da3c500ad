(window.webpackJsonp=window.webpackJsonp||[]).push([[679,634,765,821,822,858,983,987,990,991],{H44p:function(t,e,i){"use strict";i.d(e,"a",(function(){return y}));var a=i("xG9w"),s=i("fXoL"),r=i("flaP"),n=i("ofXK"),o=i("Qu3c"),c=i("NFeN");function l(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",9),s["\u0275\u0275elementStart"](1,"div",10),s["\u0275\u0275elementStart"](2,"div"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div"),s["\u0275\u0275elementStart"](5,"p",11),s["\u0275\u0275text"](6),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"p",12),s["\u0275\u0275text"](8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",t.toDisplay?(null==t.currency[t.index]?null:t.currency[t.index].currency_code)+" "+(null==t.currency[t.index]?null:t.currency[t.index].value):"Value is masked"),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](null==t.currency[t.index]?null:t.currency[t.index].currency_code),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.label),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",t.toDisplay?t.convert(null==t.currency[t.index]?null:t.currency[t.index].value,null==t.currency[t.index]?null:t.currency[t.index].currency_code):"*****"," ")}}function d(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",13),s["\u0275\u0275elementStart"](1,"span"),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",t.toDisplay?(null==t.currency[t.index]?null:t.currency[t.index].currency_code)+" "+(null==t.currency[t.index]?null:t.currency[t.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",t.toDisplay?t.convert(null==t.currency[t.index]?null:t.currency[t.index].value,null==t.currency[t.index]?null:t.currency[t.index].currency_code):"*****"," ")}}function u(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",14),s["\u0275\u0275elementStart"](1,"span",15),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",t.toDisplay?(null==t.currency[t.index]?null:t.currency[t.index].currency_code)+" "+(null==t.currency[t.index]?null:t.currency[t.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==t.currency[t.index]?null:t.currency[t.index].currency_code," ",t.toDisplay?t.convert(null==t.currency[t.index]?null:t.currency[t.index].value,null==t.currency[t.index]?null:t.currency[t.index].currency_code):"*****"," ")}}function h(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",16),s["\u0275\u0275elementStart"](1,"span",15),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",t.toDisplay?(null==t.currency[t.index]?null:t.currency[t.index].currency_code)+" "+(null==t.currency[t.index]?null:t.currency[t.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==t.currency[t.index]?null:t.currency[t.index].currency_code," ",t.toDisplay?t.convert(null==t.currency[t.index]?null:t.currency[t.index].value,null==t.currency[t.index]?null:t.currency[t.index].currency_code):"*****"," ")}}function p(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",17),s["\u0275\u0275elementStart"](1,"span",18),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",t.toDisplay?(null==t.currency[t.index]?null:t.currency[t.index].currency_code)+" "+(null==t.currency[t.index]?null:t.currency[t.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==t.currency[t.index]?null:t.currency[t.index].currency_code," ",t.toDisplay?t.convert(null==t.currency[t.index]?null:t.currency[t.index].value,null==t.currency[t.index]?null:t.currency[t.index].currency_code):"*****"," ")}}function m(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"mat-icon",19),s["\u0275\u0275text"](1,"loop"),s["\u0275\u0275elementEnd"]())}function f(t,e){if(1&t){const t=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",1),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](t),s["\u0275\u0275nextContext"]().change()})),s["\u0275\u0275template"](1,l,9,4,"div",2),s["\u0275\u0275template"](2,d,3,2,"div",3),s["\u0275\u0275template"](3,u,3,3,"div",4),s["\u0275\u0275template"](4,h,3,3,"div",5),s["\u0275\u0275template"](5,p,3,3,"div",6),s["\u0275\u0275elementStart"](6,"div",7),s["\u0275\u0275template"](7,m,2,0,"mat-icon",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&t){const t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","big"==t.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","small"==t.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","medium"==t.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","large"==t.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","overview"==t.type),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",t.toDisplay)}}let y=(()=>{class t{constructor(t){this.roleService=t,this.showActualAmount=!1,this.currency=[]}set currencyList(t){if(this.currency=t,t){const e=a.findIndex(t,t=>t.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const t=a.findIndex(this.currency,t=>t.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(t,e){return t?(this.showActualAmount?t=new Intl.NumberFormat("INR"==e?"en-IN":"en-US",{}).format(t)+" "+e:1==this.isConvertValue?t="INR"==e?(t/1e7).toFixed(2)+" Cr":(t/1e6).toFixed(2)+" M":0!=this.isConvertValue||a.contains(["big","small"],this.type)?0==this.isConvertValue&&a.contains(["big","small"],this.type)&&(t="INR"==e?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(t):new Intl.NumberFormat("en-IN",{}).format(t):"big"!=this.type?e+" "+new Intl.NumberFormat("en-US",{}).format(t):new Intl.NumberFormat("en-US",{}).format(t)):t="INR"==e?(t/1e7).toFixed(2)+" Cr":(t/1e6).toFixed(2)+" M",t):"-"}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](r.a))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(t,e){1&t&&s["\u0275\u0275template"](0,f,8,6,"div",0),2&t&&s["\u0275\u0275property"]("ngIf",e.currency)},directives:[n.NgIf,o.a,c.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),t})()},JeY9:function(t,e,i){"use strict";i.r(e),i.d(e,"TimeSheetStatsNewReportTooltipFormOptions",(function(){return z})),i.d(e,"TimesheetStatsNewModule",(function(){return q}));var a=i("ofXK"),s=i("tyNb"),r=i("mrSG"),n=i("3Pt+"),o=i("1yaQ"),c=i("FKr1"),l=i("wd/R"),d=i("xG9w"),u=i("1G5W"),h=i("XNiG"),p=i("PSD3"),m=i.n(p),f=i("fXoL"),y=i("0IaG"),g=i("tk/3");let D=(()=>{class t{constructor(t){this.$http=t}getSummaryForTsStats(t,e,i){return this.$http.post("/api/tsPrimary/getSummaryForTsStats",{currDateTime:t,currentView:e,costCentre:i})}searchCostCentres(t){return this.$http.post("/api/tsPrimary/searchCostCentres",{searchParameter:t,isFromTs:!1})}checkIfTsAdmin(t){return this.$http.post("/api/tsPrimary/checkIfTsAdmin",{associateOId:t})}searchApprovers(t){return this.$http.post("/api/tsPrimary/searchApprovers",{searchParameter:t})}getAssocMetaSubmStats(t,e){return this.$http.post("/api/tsPrimary/getMetaSubmStats",{currDateTime:t,assocOID:e})}getEmployeeDataForTsStats(t,e,i,a,s,r){return this.$http.post("/api/tsPrimary/getEmployeeDataForTsStats",{currDateTime:t,forStatus:e,startIndex:i,currentView:a,costCentre:s,assocOID:r})}bulkNotifyFromTsStats(t,e){return this.$http.post("/api/tsPrimary/bulkNotifyFromTsStats",{currDateTime:t,forStatus:e})}notifyUnsubmittedAssociates(t,e){return this.$http.post("/api/tsPrimary/notifyUnsubmittedAssociates",{associateData:t,forStatus:e})}downloadTSReportForStats(t,e){return this.$http.post("/api/tsPrimary/downloadTSReportForStats",{currDateTime:t,forStatus:e})}getTsStatsByAssociateOid(t,e,i,a,s,r,n,o){return this.$http.post("/api/tsPrimary/getTsStatsReportList",{startDate:t,endDate:e,mainFilterArray:i,mainSearchParameter:a,startIndex:s,shouldUnsubmittedBeRetrieved:r,unsubmittedIndex:n,dayTypeFilter:o})}getTsStatsSummary(t,e,i,a,s){return this.$http.post("/api/tsPrimary/getTsStatsCardSummary",{startDate:t,endDate:e,mainFilterArray:i,mainSearchParameter:a,shouldUnsubmittedBeRetrieved:s})}getTsStatsSummarySR(t){return this.$http.post("/api/tsPrimary/getTimesheetStatsReportSummaryData",{filterConfig:t})}getTsStatsByAssociateOidSR(t){return this.$http.post("/api/tsPrimary/getTimesheetStatsReportItemData",{filterConfig:t})}getCustomDateRestriction(){try{return new Promise((t,e)=>{this.$http.post("/api/tsPrimary/getMinAndMaxDate",{}).subscribe(e=>t(e.data),t=>(console.log(t),e(t)))})}catch(t){return Promise.reject()}}getTimesheetProperties(){try{return new Promise((t,e)=>{this.$http.post("/api/tsPrimary/getTimesheetProperties",{}).subscribe(e=>t(e.data),t=>(console.log(t),e(t)))})}catch(t){return Promise.reject()}}getTsStasReportDownload(t){return this.$http.post("/api/tsPrimary/downloadTimesheetStatsReportItemData",{filterConfig:t})}}return t.\u0275fac=function(e){return new(e||t)(f["\u0275\u0275inject"](g.c))},t.\u0275prov=f["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var S=i("LcQX"),v=i("XXEo"),C=i("HmYF"),b=i("JqCM"),x=i("BVzC"),M=i("GnQ3"),O=i("wiVK"),_=i("xi/V"),Y=i("Wk3H");const w={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},P=[{path:"",component:(()=>{class t{constructor(t,e,i,a,s,r,o,c,d){this.dialog=t,this._timesheetStatsService=e,this.utilityService=i,this.authService=a,this.excelService=s,this.spinner=r,this.errorService=o,this.udrfService=c,this._help=d,this.currentStatus="Default",this._onDestroy=new h.b,this.monthYearDate=new n.j(l()),this.selectAllActivated=!1,this.notifyingActivated=!1,this.isCurrentUserAdmin=!1,this.isDownloading=!1,this.noData=!1,this.isMoreLoading=!1,this.responseData=[],this.associateData=[],this.costCentreList=[],this.associateList=[],this.employeedata=!1,this.count=0,this.loader=[1,1,1,1,1,1,1,1,1,1],this.costCentreData={cost_centre:"",cost_centre_type:"",cost_centre_description:""},this.selectedAssociateData={oid:"",name:"",designation:"",role:""},this.tsStatTotal=0,this.assocMetaSubmStatsForMonth=[],this.assocMetaSubmStatsForYear=[],this.statusArray=[{dataType:"Unsubmitted",dataTypeValue:"0",dataTypeCode:"US",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#c7c4c4",category:"Status Cards"},{dataType:"Draft",dataTypeValue:"0",dataTypeCode:"D",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#c7c4c4",category:"Status Cards"},{dataType:"Recalled",dataTypeValue:"0",dataTypeCode:"RL",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#c7c4c4",category:"Status Cards"},{dataType:"Rejected",dataTypeValue:"0",dataTypeCode:"R",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#cf0001",category:"Status Cards"},{dataType:"Submitted",dataTypeValue:"0",dataTypeCode:"S",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#ff7200",category:"Status Cards"},{dataType:"Approved",dataTypeValue:"0",dataTypeCode:"A",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#009432",category:"Status Cards"},{dataType:"Regular",dataTypeValue:"0",dataTypeCode:"RG",clicked:!1,isVisible:!1,isActive:!1,cardType:"status",statusColor:"#D55127",category:"Hours Card"}],this.startIndex=0,this.currentUser={},this.mainSearchParameter="",this.currentUserOId="",this.applicationId=64,this.tsStatsHeaderDate=[],this.tsStatsItemDataCurrentIndex=0,this.unsubmittedIndex=0,this.dayTypeFilter="",this.shouldUnsubmittedBeRetrieved=!1,this.isTsStstsItemDataLoading=!1,this.areSortandFiltersApplied=!1,this.defaultDataRetrieval=20,this.isSearchingActive=!1,this.noTsStatsDataFound=!1,this.headerFilter=[],this.durationRanges=[],this.temp=[],this.fh_sum=0,this.r_sum=0,this.l_sum=0,this.user_duration_start=l(),this.user_duration_end=l(),this.defaultDataRetrievalCount=20,this.listCardViewActivated=!0,this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:["D","RL","R","S","A","US"],categoryCards:[]}],this.minNoOfVisibleSummaryCards=1,this.maxNoOfVisibleSummaryCards=6,this.isActiveSummaryCardTsStasNew=!1,this.udrfBodyColumns=[{item:"associate_id",header:"Associate Id",status_description:"",isVisible:"true",type:"text1",position:1,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"name",header:"Associate Name",associate_oid:"",isVisible:"true",type:"tsprofile",position:2,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:9,sortOrder:"N",width:300},{item:"employment_type_name",header:"Employment Type",status_description:"",isVisible:"true",type:"text1",position:3,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:12,sortOrder:"N",width:200},{item:"costCenterDetails",header:"Cost Centre",status_description:"",isVisible:"true",type:"text5",position:4,isActive:!0,colSize:"3",textClass:"value13Bold",headerTextClass:"text-center",filterId:1,sortOrder:"N",width:400},{item:"monthYear",header:"Month - Year",status_description:"",isVisible:"true",type:"text1",position:5,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"status",header:"Status",status_description:"",isVisible:"true",type:"status",position:6,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:200},{item:"entity_name",header:"Entity",status_description:"",isVisible:"true",type:"text1",position:7,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:4,sortOrder:"N",width:200},{item:"division_name",header:"Division",status_description:"",isVisible:"true",type:"text1",position:9,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:6,sortOrder:"N",width:200},{item:"sub_division_name",header:"Sub Division",status_description:"",isVisible:"true",type:"text1",position:10,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:7,sortOrder:"N",width:200},{item:"total_hours",nestedItem:"nonRegularDays",header:"Total Working Hours",status_description:"",isVisible:"true",type:"text6",position:11,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:11,sortOrder:"N",width:250},{item:"additional_hours",header:"Additional Working Hours",status_description:"",isVisible:"true",type:"text6",position:12,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:11,sortOrder:"N",width:150},{item:"billedHours",header:"Total Billed Hours",status_description:"",isVisible:"true",type:"text6",position:13,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:11,sortOrder:"N",width:100},{item:"machine_hours",header:"Machine Hours",status_description:"",isVisible:"true",type:"text",position:14,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:11,sortOrder:"N",width:100},{item:"billingEfficiency",header:"Billing Efficiency",status_description:"",isVisible:"true",type:"text1",position:15,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"billingDetails",header:"Billing Details",status_description:"",isVisible:"true",type:"text5",position:16,isActive:!0,colSize:"3",textClass:"value13Bold",headerTextClass:"text-center",filterId:1,sortOrder:"N",width:400},{item:"stdHours",header:"Standard Hours",status_description:"",isVisible:"true",type:"text1",position:17,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"employment_status",header:"Employment Status",status_description:"",isVisible:"true",type:"text1",position:18,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100}],this.udrfItemStatusColor=[{status:"Draft",color:"#c7c4c4"},{status:"Approved",color:"#009432"},{status:"Rejected",color:"#cf0001"},{status:"Submitted",color:"#ff7200"},{status:"Unsubmitted",color:"#c7c4c4"},{status:"Recalled",color:"#c7c4c4"}],this.udrfMatExpansionView=[{heading:"Stats For This Month",subItemData:[{label:"Hours Booked",item:"HOURS_BOOKED_M"},{label:"Leaves taken",item:"L_M"},{label:"Carried Leave(CL)",item:"CL_M"},{label:"FH taken",item:"FH_M"},{label:"CH taken",item:"CH_M"},{label:"CO taken",item:"CO_M"},{label:"WO taken",item:"WO_M"},{label:"Loss of pay(LOP)",item:"LOP_M"}]},{heading:"Stats For This Year",subItemData:[{label:"Leaves taken",item:"L_TAKEN_Y"},{label:"Leave Balance",item:"LEAVE_BALANCE"},{label:"FH & CH taken",item:"FH_Y"},{label:"CO taken",item:"CO_Y"}]}],this.selectedCard=[],this.isCardClicked=!1,this._onAppApiCalled=new h.b,this.isFirstTime=!0,this.weekRange=[],this.shouldDownloadLeaveDetails=!1}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.tsProperties=yield this._timesheetStatsService.getTimesheetProperties(),this.currentUser=this.authService.getProfile().profile,this.udrfService.udrfUiData.udrfExpansionPanelView=this.udrfMatExpansionView,this.currentUserOId=this.authService.getProfile().profile.oid,this.udrfBodyColumns[3].header=1==this.tsProperties[0].ts_ui_change_and_download_icon?this.tsProperties[0].ts_ui_text:"Cost Centre",this.udrfService.udrfBodyData=[],this.udrfService.udrfUiData.expansionPanelInnerItemData=[],this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleSummaryCards.bind(this),this.udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelectedSR.bind(this),this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.showSettingsModalButton=!0,this.udrfService.udrfUiData.itemDataScrollDown=this.onTsStatsNewItemDataScrollDown.bind(this),this.udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this.udrfService.udrfUiData.summaryCards=this.statusArray,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this.udrfService.udrfUiData.selectedCard=this.selectedCard,this.udrfService.udrfUiData.variant=0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!1,this.udrfService.udrfUiData.quickCTAInput={},this.udrfService.udrfUiData.commentsInput={},this.udrfService.udrfUiData.commentsContext={},this.udrfService.udrfUiData.variant2View=!0,this.udrfService.udrfUiData.expansionCardClick=this.expansionData.bind(this),this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.udrfService.udrfUiData.notifyAllSelected=this.notifyAllSelected.bind(this),this.udrfService.udrfUiData.selectAll=this.selectAll.bind(this),this.udrfService.udrfUiData.isHeaderSort=!0,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.isMultipleView=!0,this.udrfService.udrfUiData.showReportDownloadButton=!0,this.udrfService.udrfUiData.downloadItemDataReport=this.downloadItemDataReport.bind(this),this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this.udrfService.udrfUiData.isMoreOptionsNeeded=!0,this.udrfService.udrfUiData.showHelpButton=!0,this.udrfService.udrfUiData.openHelpDialog=this.openHelpDialog.bind(this);let t=yield this._timesheetStatsService.getCustomDateRestriction();this.udrfService.udrfData.minDate=t.startYear+"-0"+t.startMonth+"-01",this.udrfService.udrfData.maxDate=t.endYear+"-0"+t.endMonth+"-31";let e="END"==this.tsProperties[0].month_end_date?l().endOf("month").format("DD"):parseInt(this.tsProperties[0].month_end_date);console.log(e);let i,a,s="END"==this.tsProperties[0].month_end_date?l().startOf("month").format("DD"):parseInt(this.tsProperties[0].month_end_date)+1,r=l().format("YYYY-MM-DD");l().date()>this.tsProperties[0].monthly_resubm_cut_off_date?(a=l([11==l(r).month()?l(r).year()+1:l(r).year(),11==l(r).month()?0:l(r).month()+1,e]).format("YYYY-MM-DD"),i=l(a).subtract(1,"month").add(1,"days").format("YYYY-MM-DD")):(a=l([l(r).year(),l(r).month(),e]).format("YYYY-MM-DD"),i="END"==this.tsProperties[0].month_end_date?l().startOf("month").format("YYYY-MM-DD"):l(a).subtract(1,"month").add(1,"days").format("YYYY-MM-DD"));let n=l(i).add(1,"month"),o=l(a).add(1,"month");this.durationRanges=[{checkboxId:"CDCRD3",checkboxName:"This Month ("+s+"th to"+e+"th)",checkboxStartValue:l(i).format("YYYY-MM-DD"),checkboxEndValue:l(a).format("YYYY-MM-DD"),isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD4",checkboxName:"Next Month("+s+"th to"+e+"th)",checkboxStartValue:l(n).format("YYYY-MM-DD"),checkboxEndValue:l(o).format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Calendar Month (1st to 30/31st)",checkboxStartValue:this.utilityService.getFormattedDate(l().startOf("month"),l(l().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(l().endOf("month"),l(l().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"Calendar Previous Month (1st to 30/31st)",checkboxStartValue:this.utilityService.getFormattedDate(l().add(-1,"month").startOf("month"),l(l().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(l().add(-1,"month").endOf("month"),l(l().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD7",checkboxName:"Previous Month ("+s+"th to"+e+"th)",checkboxStartValue:l(i).subtract(1,"month").format("YYYY-MM-DD"),checkboxEndValue:l(a).subtract(1,"month").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1}],this.udrfService.udrfFunctions.constructCustomRangeData(8,"date",this.durationRanges),this.monthYearDate.value.date(1).hour(15);let c=l(i).format("YYYY-MM-DD"),d=l(a).format("YYYY-MM-DD"),u=+l(c).day(),h=0;0==u?h=6:1==u?h=5:2==u?h=4:3==u?h=3:4==u?h=2:5==u?h=1:6==u&&(h=0);let p,m,f,y=l(c).add(h,"days").format("YYYY-MM-DD"),g=l(y).add(1,"week").format("YYYY-MM-DD"),D=l(y).add(2,"week").format("YYYY-MM-DD"),S=l(y).add(3,"week").format("YYYY-MM-DD"),v=l(d).format("YYYY-MM-DD"),C=l(S).add(1,"days").format("YYYY-MM-DD"),b=l(v).diff(C,"days");b>7?(f=l(y).add(4,"week").format("YYYY-MM-DD"),p=l(y).add(4,"week").format("YYYY-MM-DD"),m=l(d).format("YYYY-MM-DD")):(f=l(d).format("YYYY-MM-DD"),p="",m=""),b&&(this.weekRange=[{checkboxId:"CDCRD1",checkboxName:"Week 1",checkboxStartValue:l(c).format("YYYY-MM-DD"),checkboxEndValue:l(c).add(h,"days").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"Week 2",checkboxStartValue:l(y).add(1,"days").format("YYYY-MM-DD"),checkboxEndValue:l(y).add(1,"week").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"Week 3",checkboxStartValue:l(g).add(1,"day").format("YYYY-MM-DD"),checkboxEndValue:l(y).add(2,"week").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Week 4",checkboxStartValue:l(D).add(1,"day").format("YYYY-MM-DD"),checkboxEndValue:l(y).add(3,"week").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Week 5",checkboxStartValue:l(S).add(1,"day").format("YYYY-MM-DD"),checkboxEndValue:f,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"Week 6",checkboxStartValue:p,checkboxEndValue:m,isCheckboxDefaultSelected:!1}]),this.udrfService.udrfFunctions.constructCustomRangeData(11,"date",this.weekRange),this.udrfService.getNotifyReleasesUDRF()}))}initReport(){return Object(r.c)(this,void 0,void 0,(function*(){this.isAdmin=yield this._timesheetStatsService.checkIfTsAdmin(this.currentUser.oid),"S"==this.isAdmin?(this.isCurrentUserAdmin=!0,this.udrfService.udrfUiData.isAdminView=!0,this.udrfService.udrfUiData.udrfBodyColumns[0].isVisible="true",this.cardClicked&&this.isFirstTime&&this.udrfService.udrfUiData.udrfBodyColumns.unshift({item:"checked",header:"Approve",status_description:"",isVisible:"true",type:"icons",position:1,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:0,sortOrder:"I",width:200})):(this.isCurrentUserAdmin=!1,this.udrfService.udrfUiData.isAdminView=!1),this._onAppApiCalled.next(),this.udrfService.udrfBodyData=[],this.udrfService.udrfUiData.expansionPanelInnerItemData=[],this.isCardClicked=!1,this.cardClicked="",this.udrfService.udrfUiData.resolveColumnConfig(),this.tsStatsItemDataCurrentIndex=0,this.unsubmittedIndex=0,this.associateData=[],this.isFirstTime=!1,yield this.initTsStatsList()}))}initTsStatsList(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.initTsStatsReportListSR()}))}showErrorMessage(t){this.utilityService.showErrorMessage(t,"KEBS")}resolveResponse(){return Object(r.c)(this,void 0,void 0,(function*(){this.associateData=[];for(let t=0;t<this.responseData.length;t++)this.associateData.push(this.responseData[t])}))}getDetailedEmployeeData(){return Object(r.c)(this,void 0,void 0,(function*(){for(let t=this.startIndex;t<this.associateData.length;t++){if(this.associateData[t].nonRegularDays=[],this.associateData[t].costCenterDetails=[],this.associateData[t].billingDetails=[],this.associateData[t].leave_count>0&&this.associateData[t].nonRegularDays.push({dayType:"L",count:this.associateData[t].leave_count}),this.associateData[t].fh_count>0&&this.associateData[t].nonRegularDays.push({dayType:"FH",count:this.associateData[t].fh_count}),this.associateData[t].CL>0&&this.associateData[t].nonRegularDays.push({dayType:"CL",count:this.associateData[t].CL}),this.associateData[t].PL>0&&this.associateData[t].nonRegularDays.push({dayType:"PL",count:this.associateData[t].PL}),this.associateData[t].OH>0&&this.associateData[t].nonRegularDays.push({dayType:"OH",count:this.associateData[t].OH}),this.associateData[t].H>0&&this.associateData[t].nonRegularDays.push({dayType:"H",count:this.associateData[t].H}),this.associateData[t].CEL>0&&this.associateData[t].nonRegularDays.push({dayType:"CEL",count:this.associateData[t].CEL}),this.associateData[t].BL>0&&this.associateData[t].nonRegularDays.push({dayType:"BL",count:this.associateData[t].BL}),this.associateData[t].SL>0&&this.associateData[t].nonRegularDays.push({dayType:"SL",count:this.associateData[t].SL}),this.associateData[t].WL>0&&this.associateData[t].nonRegularDays.push({dayType:"WL",count:this.associateData[t].WL}),this.associateData[t].lop_Count>0&&this.associateData[t].nonRegularDays.push({dayType:"LOP",count:this.associateData[t].lop_Count}),this.associateData[t].hc_count>0&&this.associateData[t].nonRegularDays.push({dayType:"H-C",count:this.associateData[t].hc_count}),this.associateData[t].hp_count>0&&this.associateData[t].nonRegularDays.push({dayType:"H-P",count:this.associateData[t].hp_count}),this.associateData[t].ho_count>0&&this.associateData[t].nonRegularDays.push({dayType:"H-O",count:this.associateData[t].ho_count}),this.associateData[t].HF>0&&this.associateData[t].nonRegularDays.push({dayType:"HF",count:this.associateData[t].HF}),null!=this.associateData[t].cost_center){let e=this.associateData[t].cost_center.split("~"),i=this.associateData[t].billing.split("~");if(this.associateData[t].total_hours){this.associateData[t].total_hours=0;for(let i=0;i<e.length;i++){let a=e[i].split("$");this.associateData[t].costCenterDetails.push({cc_code:"null"==a[0]?"":a[0],cc_name:"null"==a[1]?"":a[1],cc_location:"null"==a[2]?"":a[2],cc_hours:"null"==a[3]?"":a[3]}),this.associateData[t].total_hours+=Math.round(100*("null"==a[3]?0:Math.round(100*parseFloat(a[3]))/100))/100}this.associateData[t].total_hours+=" H"}if(this.associateData[t].billedHours){this.associateData[t].billedHours=0,this.associateData[t].billingEfficiency=0;for(let e=0;e<i.length;e++){let a=i[e].split("$");this.associateData[t].billingDetails.push({cc_code:"null"==a[0]?"":a[0],cc_hours:"null"==a[1]?"":a[1],cc_location:"null"==a[2]?"":a[2]}),this.associateData[t].billedHours+=0==a[1]||null==a[1]?0:parseInt(a[1]),this.associateData[t].billingEfficiency+=0==a[2]||null==a[2]?0:parseInt(a[2])}}}0==this.associateData[t].billingDetails.length&&this.associateData[t].billingDetails.push({cc_code:"-",cc_hours:"-",cc_location:"-"})}if(this.udrfService.udrfBodyData.length>0)for(let t=0;t<this.associateData.length;t++)for(let e of this.udrfService.udrfBodyData)this.associateData[t].oid==e.oid&&(e.costCenterDetails=e.costCenterDetails.concat(this.associateData[t].costCenterDetails),e.billingDetails=e.billingDetails.concat(this.associateData[t].billingDetails),e.total_hours=parseInt(e.total_hours.replace("H",""))+this.associateData[t].total_hrs+" H",this.associateData.splice(t,1));this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(this.associateData)}))}getLocalDate(t){return"-"==t?t:l(t,"DD - MMM - YYYY HH : mm : ss").utc(t).local().format("DD - MMM - YYYY HH : mm : ss")}getLength(t){return t.length}selectAll(){d.each(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.selectAllActivated?t=>{t.checked=!0}:t=>{t.checked=!1})}notifyAllSelected(){let t=d.where(this.udrfService.udrfBodyData,{checked:!0});this.udrfService.udrfUiData.selectAllActivated?(this.udrfService.udrfUiData.notifyingActivated=!0,this._timesheetStatsService.bulkNotifyFromTsStats(l(this.monthYearDate.value).format("YYYY-MM-DD"),this.currentStatus).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this.udrfService.udrfUiData.notifyingActivated=!1,this.utilityService.showMessage(t.messText,"Dismiss",3e3)):this.utilityService.showMessage(t.messText,"Dismiss",3e3),this.udrfService.udrfUiData.selectAllActivated=!1,this.selectAll()})))):t.length>0?(this.udrfService.udrfUiData.notifyingActivated=!0,this._timesheetStatsService.notifyUnsubmittedAssociates(t,this.currentStatus).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this.udrfService.udrfUiData.notifyingActivated=!1,this.utilityService.showMessage(t.messText,"Dismiss",3e3)):this.utilityService.showMessage(t.messText,"Dismiss",3e3),this.udrfService.udrfUiData.selectAllActivated=!1,this.selectAll()})))):this.utilityService.showMessage("No Associate Selected","Dismiss",3e3)}downloadCurrentReport(){this.isDownloading=!0,this._timesheetStatsService.downloadTSReportForStats(l(this.monthYearDate.value).format("YYYY-MM-DD"),this.currentStatus).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){this.isDownloading=!1,"S"==t.messType?this.excelService.exportAsExcelFile(t.data,t.fileName):this.utilityService.showMessage(t.data,"Dismiss",3e3)})),t=>{this.isDownloading=!1,this.utilityService.showMessage("Something went Wrong!","Dismiss",3e3)})}initTsStatusCardSR(){return Object(r.c)(this,void 0,void 0,(function*(){this.shouldUnsubmittedBeRetrieved=!1;let t=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),e=d.where(this.statusArray,{isActive:!0}),i=[],a=!1;if(e.length>0&&(i=d.where(e,{cardType:"status"}),i.length>0))if(t.length>0){for(let i of t)"Status"==i.filterName&&(i.multiOptionSelectSearchValues=[e[0].dataType],a=!0);if(0==a){let i=JSON.parse(JSON.stringify(d.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Status"})));t.push(i[0]);for(let s of t)"Status"==s.filterName&&(s.multiOptionSelectSearchValues=[e[0].dataType],a=!0)}}else t=JSON.parse(JSON.stringify(d.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Status"}))),t[0].multiOptionSelectSearchValues=[e[0].dataType];this.shouldUnsubmittedBeRetrieved=!1;let s=d.where(t,{filterName:"Status"});s.length>0&&"Unsubmitted"==s[0].multiOptionSelectSearchValues?this.shouldUnsubmittedBeRetrieved=!0:s.length>0&&("Draft"==s[0].multiOptionSelectSearchValues||"Recalled"==s[0].multiOptionSelectSearchValues||"Rejected"==s[0].multiOptionSelectSearchValues||"Submitted"==s[0].multiOptionSelectSearchValues||"Approved"==s[0].multiOptionSelectSearchValues)?(this.unsubmittedIndex=0,this.shouldUnsubmittedBeRetrieved=!1):s.length>0&&"Regular"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="RG":s.length>0&&"Leave"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="L":s.length>0&&"Flexi Holiday"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="FH":s.length>0&&"Weekly Off"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="WO":s.length>0&&"Submitted Hours"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="SH":s.length>0&&"Approved Hours"==s[0].multiOptionSelectSearchValues&&(this.dayTypeFilter="AH"),0==s.length&&(this.shouldUnsubmittedBeRetrieved=!0);let n=d.findIndex(this.udrfService.udrfData.mainFilterArray,{filterName:"Region"}),o=d.findIndex(this.udrfService.udrfData.mainFilterArray,{filterId:1});if(-1!=n&&-1==o){let e=d.findIndex(this.udrfService.udrfData.filterTypeArray,{filterId:1});t.push(this.udrfService.udrfData.filterTypeArray[e])}let c=yield this.getTSStatsReportDate(this.udrfService.udrfData.filterTypeArray);this._timesheetStatsService.getTsStatsSummarySR({startIndex:this.tsStatsItemDataCurrentIndex,startDate:c.startDate,endDate:c.endDate,mainFilterArray:t,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails,shouldUnsubmittedBeRetrieved:this.shouldUnsubmittedBeRetrieved,dayTypeFilter:this.dayTypeFilter,associateOid:this.currentUser.oid}).pipe(Object(u.a)(this._onDestroy)).pipe(Object(u.a)(this._onAppApiCalled)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){if(t.data&&"S"==t.messType){this.udrfService.udrfUiData.totalItemDataCount=t.data.TOTAL?t.data.TOTAL:0;for(let e of this.statusArray)e.dataTypeValue=t.data[e.dataTypeCode]?t.data[e.dataTypeCode]:0,e.clicked&&(this.udrfService.udrfUiData.totalItemDataCount="day_type"==e.cardType?t.data[e.dataTypeCode+"_C"]?t.data[e.dataTypeCode+"_C"]:0:t.data[e.dataTypeCode]?t.data[e.dataTypeCode]:0);this.udrfService.udrfUiData.summaryCards=this.statusArray}this.spinner.hide()})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving WFH Approvals",t&&t.params?t.params:t&&t.error?t.error.params:{})})}))}initTsStatsReportListSR(){return Object(r.c)(this,void 0,void 0,(function*(){this.spinner.show();let t=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),e=d.where(this.statusArray,{isActive:!0}),i=[],a=!1;if(e.length>0&&(i=d.where(e,{cardType:"status"}),i.length>0))if(t.length>0){for(let i of t)"Status"==i.filterName&&(i.multiOptionSelectSearchValues=[e[0].dataType],a=!0);if(0==a){let i=JSON.parse(JSON.stringify(d.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Status"})));t.push(i[0]);for(let s of t)"Status"==s.filterName&&(s.multiOptionSelectSearchValues=[e[0].dataType],a=!0)}}else t=JSON.parse(JSON.stringify(d.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Status"}))),t[0].multiOptionSelectSearchValues=[e[0].dataType];this.shouldUnsubmittedBeRetrieved=!1;let s=d.where(t,{filterName:"Status"});s.length>0&&"Unsubmitted"==s[0].multiOptionSelectSearchValues?this.shouldUnsubmittedBeRetrieved=!0:s.length>0&&("Draft"==s[0].multiOptionSelectSearchValues||"Recalled"==s[0].multiOptionSelectSearchValues||"Rejected"==s[0].multiOptionSelectSearchValues||"Submitted"==s[0].multiOptionSelectSearchValues||"Approved"==s[0].multiOptionSelectSearchValues)?(this.unsubmittedIndex=0,this.shouldUnsubmittedBeRetrieved=!1):s.length>0&&"Regular"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="RG":s.length>0&&"Leave"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="L":s.length>0&&"Flexi Holiday"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="FH":s.length>0&&"Weekly Off"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="WO":s.length>0&&"Submitted Hours"==s[0].multiOptionSelectSearchValues?this.dayTypeFilter="SH":s.length>0&&"Approved Hours"==s[0].multiOptionSelectSearchValues&&(this.dayTypeFilter="AH"),0==s.length&&(this.shouldUnsubmittedBeRetrieved=!0);let n=d.findIndex(this.udrfService.udrfData.mainFilterArray,{filterName:"Region"}),o=d.findIndex(this.udrfService.udrfData.mainFilterArray,{filterId:1});if(-1!=n&&-1==o){let e=d.findIndex(this.udrfService.udrfData.filterTypeArray,{filterId:1});t.push(this.udrfService.udrfData.filterTypeArray[e])}let c=yield this.getTSStatsReportDate(this.udrfService.udrfData.filterTypeArray);this._timesheetStatsService.getTsStatsByAssociateOidSR({startIndex:this.tsStatsItemDataCurrentIndex,startDate:c.startDate,endDate:c.endDate,mainFilterArray:t,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails,shouldUnsubmittedBeRetrieved:this.shouldUnsubmittedBeRetrieved,unsubmittedIndex:this.unsubmittedIndex,dayTypeFilter:this.dayTypeFilter,associateOid:this.currentUser.oid}).pipe(Object(u.a)(this._onDestroy)).pipe(Object(u.a)(this._onAppApiCalled)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){t.data&&t.data.length>0&&"S"==t.messType?(this.responseData=t.data,yield this.resolveResponse(),yield this.getDetailedEmployeeData()):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0),this.udrfService.udrfData.isItemDataLoading=!1,yield this.initTsStatusCardSR()})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving WFH Approvals",t&&t.params?t.params:t&&t.error?t.error.params:{})})}))}expansionData(){this.udrfService.udrfUiData.expansionPanelInnerItemData=[],this._timesheetStatsService.getAssocMetaSubmStats(this.udrfService.udrfData.mainApiDateRangeStart,this.udrfService.udrfUiData.expansionCardClickInputData).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this.udrfService.udrfUiData.expansionPanelInnerItemData=[],this.udrfService.udrfUiData.expansionPanelInnerItemData=this.udrfService.udrfUiData.expansionPanelInnerItemData.concat(t.data[0])):this.udrfService.udrfUiData.expansionPanelInnerItemData=this.udrfService.udrfUiData.expansionPanelInnerItemData.concat([])})))}resolveVisibleSummaryCards(){return Object(r.c)(this,void 0,void 0,(function*(){for(let t of this.udrfService.udrfUiData.summaryCards){let e;null!=this.udrfService.udrfData.udrfSummaryCardCodes&&(e=d.contains(this.udrfService.udrfData.udrfSummaryCardCodes,t.dataTypeCode),t.isVisible=e,e&&(this.udrfService.udrfUiData.summaryCardsItem=t))}}))}dataTypeCardSelectedSR(){this.udrfService.udrfData.isItemDataLoading=!0,this.udrfService.udrfData.noItemDataFound=!1;let t=this.udrfService.udrfUiData.summaryCardsItem;for(let e=0;e<this.statusArray.length;e++)if(t.dataTypeCode==this.statusArray[e].dataTypeCode)1==this.statusArray[e].isActive?(this.udrfService.udrfBodyData=[],this.statusArray[e].isActive=!1,t.isActive=!1):(this.udrfService.udrfBodyData=[],this.statusArray[e].isActive=!0,t.isActive=!0);else{let t=d.where(this.udrfService.udrfUiData.summaryCards,{dataTypeCode:this.statusArray[e].dataTypeCode});t.length>0&&(t[0].isActive=!1),this.statusArray[e].isActive=!1,this.udrfService.udrfUiData.cardClicked=!1}this.cardClicked=t.dataType,this.tsStatsItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.initTsStatsList()}callInlineEditApi(){}onTsStatsNewItemDataScrollDown(){return Object(r.c)(this,void 0,void 0,(function*(){this.udrfService.udrfData.noItemDataFound||this.udrfService.udrfData.isItemDataLoading||(this.tsStatsItemDataCurrentIndex+=this.udrfService.udrfData.defaultRecordsPerFetch,this.udrfService.udrfData.isItemDataLoading=!0,this.unsubmittedIndex+=16,yield this.initTsStatsReportListSR())}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}downloadItemDataReport(){return Object(r.c)(this,void 0,void 0,(function*(){this.shouldDownloadLeaveDetails=!1;const t=["Monthy","Weekly","Summary"];1==this.tsProperties.ts_stats_download_option_config?m.a.fire({title:"Leave Details",text:"Should the report contain leave details?",icon:"warning",input:"radio",inputOptions:t,inputValidator:t=>{if(!t)return"Kindly choose the report you need to download"},showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes",cancelButtonText:"No"}).then(t=>Object(r.c)(this,void 0,void 0,(function*(){console.log(t.value),t.isConfirmed&&t.value&&(this.shouldDownloadLeaveDetails=!0),this.downloadItemReportFn(t.value)}))):m.a.fire({title:"Leave Details",text:"Should the report contain leave details?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes",cancelButtonText:"No"}).then(t=>Object(r.c)(this,void 0,void 0,(function*(){t.isConfirmed&&(this.shouldDownloadLeaveDetails=!0),this.downloadItemReportFn(9999)}))),m.a.fire({title:"Leave Details",text:"Should the report contain leave details?",icon:"warning",input:"radio",inputOptions:t,inputValidator:t=>{if(!t)return"Kindly choose the report you need to download"},showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes",cancelButtonText:"No"}).then(t=>Object(r.c)(this,void 0,void 0,(function*(){console.log(t.value),t.isConfirmed&&t.value&&(this.shouldDownloadLeaveDetails=!0),this.downloadItemReportFn(t.value)})))}))}downloadItemReportFn(t){return Object(r.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),i=d.where(this.statusArray,{isActive:!0}),a=[],s=!1;if(i.length>0&&(a=d.where(i,{cardType:"status"}),a.length>0))if(e.length>0){for(let t of e)"Status"==t.filterName&&(t.multiOptionSelectSearchValues=[i[0].dataType],s=!0);if(0==s){let t=JSON.parse(JSON.stringify(d.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Status"})));e.push(t[0]);for(let a of e)"Status"==a.filterName&&(a.multiOptionSelectSearchValues=[i[0].dataType],s=!0)}}else e=JSON.parse(JSON.stringify(d.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Status"}))),e[0].multiOptionSelectSearchValues=[i[0].dataType];this.shouldUnsubmittedBeRetrieved=!1;let n=d.where(e,{filterName:"Status"});n.length>0&&"Unsubmitted"==n[0].multiOptionSelectSearchValues?this.shouldUnsubmittedBeRetrieved=!0:n.length>0&&("Draft"==n[0].multiOptionSelectSearchValues||"Recalled"==n[0].multiOptionSelectSearchValues||"Rejected"==n[0].multiOptionSelectSearchValues||"Submitted"==n[0].multiOptionSelectSearchValues||"Approved"==n[0].multiOptionSelectSearchValues)?(this.unsubmittedIndex=0,this.shouldUnsubmittedBeRetrieved=!1):n.length>0&&"Regular"==n[0].multiOptionSelectSearchValues?this.dayTypeFilter="RG":n.length>0&&"Leave"==n[0].multiOptionSelectSearchValues?this.dayTypeFilter="L":n.length>0&&"Flexi Holiday"==n[0].multiOptionSelectSearchValues?this.dayTypeFilter="FH":n.length>0&&"Weekly Off"==n[0].multiOptionSelectSearchValues?this.dayTypeFilter="WO":n.length>0&&"Submitted Hours"==n[0].multiOptionSelectSearchValues?this.dayTypeFilter="SH":n.length>0&&"Approved Hours"==n[0].multiOptionSelectSearchValues&&(this.dayTypeFilter="AH"),0==n.length&&(this.shouldUnsubmittedBeRetrieved=!0);let o=d.findIndex(this.udrfService.udrfData.mainFilterArray,{filterName:"Region"}),c=d.findIndex(this.udrfService.udrfData.mainFilterArray,{filterId:1});if(-1!=o&&-1==c){let t=d.findIndex(this.udrfService.udrfData.filterTypeArray,{filterId:1});e.push(this.udrfService.udrfData.filterTypeArray[t])}let l=this.weekRange;this.weekRange=[];let h=yield this.getTSStatsReportDate(this.udrfService.udrfData.filterTypeArray),p=d.filter(e,{filterId:11}),m=p&&p.length>0?e.indexOf(p[0]):-1;-1!=m&&e.splice(m,1),this._timesheetStatsService.getTsStasReportDownload({startIndex:this.tsStatsItemDataCurrentIndex,startDate:h.startDate,endDate:h.endDate,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails,shouldUnsubmittedBeRetrieved:this.shouldUnsubmittedBeRetrieved,unsubmittedIndex:this.unsubmittedIndex,dayTypeFilter:this.dayTypeFilter,associateOid:this.currentUser.oid,weekData:this.weekRange,downloadValue:t}).pipe(Object(u.a)(this._onDestroy)).pipe(Object(u.a)(this._onAppApiCalled)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfUiData.isReportDownloading=!1,"S"==t.messType){let e=this.deleteKeys(t.data),i=this.deleteKeys(t.weekData),a=t.summaryReport,s=[];e.length>0&&s.push({data:e,sheetName:"Monthly Report"}),i.length>0&&s.push({data:i,sheetName:"Weekly Report"}),a.length>0&&s.push({data:a,sheetName:"Summary Report"}),this.excelService.exportJsonToExcelWithMultipleSheets(s,"TimesheetStats-Report")}else this.utilityService.showMessage("Report Download Failed","Dismiss",3e3);this.udrfService.udrfData.isItemDataLoading=!1})),t=>{this.udrfService.udrfUiData.isReportDownloading=!1,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving WFH Approvals",t&&t.params?t.params:t&&t.error?t.error.params:{})}),this.weekRange=l}))}shouldDisplayKey(t){return d.filter(this.udrfService.udrfUiData.udrfBodyColumns,(function(e){return("month"!=t&&"year"!=t||"monthYear"!=e.item||null==e.isVisible||"true"!=e.isVisible&&1!=e.isVisible)&&("cost_center"!=t&&"cost_center_description"!=t&&"location"!=t&&"Charge Code"!=t&&"Charge Code Description"!=t||"costCenterDetails"!=e.item||null==e.isVisible||"true"!=e.isVisible&&1!=e.isVisible)&&("cost_center"!=t&&"location"!=t&&"Charge Code"!=t||"billingDetails"!=e.item||null==e.isVisible||"true"!=e.isVisible&&1!=e.isVisible)&&(e.item!=t||null==e.isVisible||"true"!=e.isVisible&&1!=e.isVisible)&&("BL"!=t&&"CEL"!=t&&"Casual Leave"!=t&&"Holiday"!=t&&"HL"!=t&&"Loss Of Pay"!=t&&"LOP_Count"!=t&&"Maternity Leave"!=t&&"Optional Holidays"!=t&&"Privilege Leave"!=t&&"Sick Leave"!=t&&"WL"!=t&&"fh_count"!=t&&"leave_count"!=t&&"Annual Vaccation"!=t&&"Compensatory Off"!=t&&"MA"!=t&&"On Duty"!=t&&"Personal Leave"!=t&&"Work From Home"!=t&&"Flexi Holiday"!=t||!this.shouldDownloadLeaveDetails)?void 0:e}),this).length>0}getTSStatsReportDate(t){return Object(r.c)(this,void 0,void 0,(function*(){try{let e,i,a,s,r=d.filter(t,{filterId:8}),n=d.filter(t,{filterId:11});if(r.length>0&&r[0].isCustomButtonActivated)return this.weekRange=this.getAllWeeks(l(r[0].customButtonValueStart).format("YYYY-MM-DD"),l(r[0].customButtonValueEnd).format("YYYY-MM-DD"),!0),{startDate:l(r[0].customButtonValueStart).format("YYYY-MM-DD"),endDate:l(r[0].customButtonValueEnd).format("YYYY-MM-DD")};let o=1;for(let t of r[0].checkboxValues)t.isCheckboxSelected&&(o=0,e=l(t.checkboxStartValue).format("YYYY-MM-DD"),i=l(t.checkboxEndValue).format("YYYY-MM-DD"),this.weekRange=this.weekRange.concat(this.getAllWeeks(l(e).format("YYYY-MM-DD"),l(i).format("YYYY-MM-DD"))));if(1==o){let t=l().format("YYYY-MM-DD");for(let a of r[0].checkboxValues)a.isDefaultWithCustomButton&&t>=l(a.checkboxStartValue).format("YYYY-MM-DD")&&t<=l(a.checkboxEndValue).format("YYYY-MM-DD")&&(e=l(a.checkboxStartValue).format("YYYY-MM-DD"),i=l(a.checkboxEndValue).format("YYYY-MM-DD"),this.weekRange=this.weekRange.concat(this.getAllWeeks(l(e).format("YYYY-MM-DD"),l(i).format("YYYY-MM-DD"))))}let c=this.getDaysCount(e);a=l(e).format("YYYY-MM-DD"),s=l(i).format("YYYY-MM-DD");let u,h,p,m=l(e).add(c,"days").format("YYYY-MM-DD"),f=l(m).add(1,"week").format("YYYY-MM-DD"),y=l(m).add(2,"week").format("YYYY-MM-DD"),g=l(m).add(3,"week").format("YYYY-MM-DD"),D=l(g).add(1,"days").format("YYYY-MM-DD");if(l(i).diff(D,"days")>7?(p=l(m).add(4,"week").format("YYYY-MM-DD"),p=l(p).add(1,"days").format("YYYY-MM-DD"),u=l(m).add(4,"week").format("YYYY-MM-DD"),h=l(i).format("YYYY-MM-DD")):(p=l(i).format("YYYY-MM-DD"),u="9999-12-31",h="9999-12-31"),n.length>0){for(let t of n[0].checkboxValues)"Week 1"==t.checkboxName&&t.isCheckboxSelected?(a=l(e).format("YYYY-MM-DD"),s=l(m).format("YYYY-MM-DD")):"Week 2"==t.checkboxName&&t.isCheckboxSelected?(a=l(m).add(1,"days").format("YYYY-MM-DD"),s=l(m).add(1,"week").format("YYYY-MM-DD")):"Week 3"==t.checkboxName&&t.isCheckboxSelected?(a=l(f).add(1,"day").format("YYYY-MM-DD"),s=l(m).add(2,"week").format("YYYY-MM-DD")):"Week 4"==t.checkboxName&&t.isCheckboxSelected?(a=l(y).add(1,"day").format("YYYY-MM-DD"),s=l(m).add(3,"week").format("YYYY-MM-DD")):"Week 5"==t.checkboxName&&t.isCheckboxSelected?(a=l(g).add(1,"day").format("YYYY-MM-DD"),s=l(p).format("YYYY-MM-DD")):"Week 6"==t.checkboxName&&t.isCheckboxSelected&&(a=l(u).format("YYYY-MM-DD"),s=l(h).format("YYYY-MM-DD"));return{startDate:a,endDate:s}}}catch(e){console.log(e)}}))}getDaysCount(t){try{let e=+l(t).day(),i=0;return 0==e?i=6:1==e?i=5:2==e?i=4:3==e?i=3:4==e?i=2:5==e?i=1:6==e&&(i=0),i}catch(e){console.log(e)}}openHelpDialog(){this._help.openHelpDialog(this.authService.getToken(),52,this.currentUser)}deleteKeys(t){let e=[];t.length>0&&(e=Object.keys(t[0]));for(let i of t)for(let t of e)this.shouldDisplayKey(t)||"weekNo"==t||delete i[t];return t}getWeekOfMonth(t){var e=new Date(t),i=new Date(t);i.setDate(1);for(var a=1;i<e;)i.setDate(i.getDate()+1),0==i.getDay()&&a++;return a}getAllWeeks(t,e,i=!1){var a=l(t).format("YYYY-MM-DD"),s=l(e).format("YYYY-MM-DD"),r=[];let n=1;for(;a<=s;){var o=l(a).weekday(6).format("YYYY-MM-DD")<=s?l(a).weekday(6).format("YYYY-MM-DD"):s,c={checkboxName:i?l(a).format("MMM")+" "+l(a).format("YYYY")+" - Week "+this.getWeekOfMonth(new Date(a)):l(s).format("MMM")+" "+l(s).format("YYYY")+" - Week "+n,checkboxStartValue:a,checkboxEndValue:o,isCustom:!0};n+=1,r.push(c),a=l(o).add(1,"days").format("YYYY-MM-DD")}return r}}return t.\u0275fac=function(e){return new(e||t)(f["\u0275\u0275directiveInject"](y.b),f["\u0275\u0275directiveInject"](D),f["\u0275\u0275directiveInject"](S.a),f["\u0275\u0275directiveInject"](v.a),f["\u0275\u0275directiveInject"](C.a),f["\u0275\u0275directiveInject"](b.c),f["\u0275\u0275directiveInject"](x.a),f["\u0275\u0275directiveInject"](M.a),f["\u0275\u0275directiveInject"](O.c))},t.\u0275cmp=f["\u0275\u0275defineComponent"]({type:t,selectors:[["app-timesheet-stats-new-landing-page"]],features:[f["\u0275\u0275ProvidersFeature"]([{provide:c.c,useClass:o.c,deps:[c.f,o.a]},{provide:c.e,useValue:w}])],decls:3,vars:0,consts:[[1,"container-fluid","timesheet-stats-styles","pl-0","pr-0"]],template:function(t,e){1&t&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275element"](1,"udrf-header"),f["\u0275\u0275element"](2,"udrf-body"),f["\u0275\u0275elementEnd"]())},directives:[_.a,Y.a],styles:['.timesheet-stats-styles[_ngcontent-%COMP%]   .mediumSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.timesheet-stats-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.timesheet-stats-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%]{font-size:14px}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%]{font-family:Roboto,Segoe UI,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;text-align:left!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%]{font-size:12px;color:#66615b;font-weight:400}.timesheet-stats-styles[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-family:Lato}.timesheet-stats-styles[_ngcontent-%COMP%]   .prevent-click[_ngcontent-%COMP%]{pointer-events:none}.timesheet-stats-styles[_ngcontent-%COMP%]   .authorize-click[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   button.toggle-panel[_ngcontent-%COMP%]{pointer-events:auto}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-accordion[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .mat-expansion-panel-header.mat-row[_ngcontent-%COMP%]{border-bottom:none}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%]{height:auto}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]{height:auto;min-width:100%}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]{display:flex;border-bottom-width:1px;border-bottom-style:solid;align-items:center;padding:0 20px;box-sizing:border-box}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%]:after, .timesheet-stats-styles[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]:after{display:inline-block;min-height:inherit;content:""}.timesheet-stats-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.timesheet-stats-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:31px;padding:0 8px;border-radius:30px;margin-right:6px!important;margin-bottom:3px;width:28%}.timesheet-stats-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.timesheet-stats-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-stats-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:395px;overflow-y:scroll}.timesheet-stats-styles[_ngcontent-%COMP%]   .infinite-scroll-auto[_ngcontent-%COMP%]{height:auto;overflow:scroll}.timesheet-stats-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:345px;overflow-y:scroll}.timesheet-stats-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-right:5px!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .ib13[_ngcontent-%COMP%]{font-size:14px;font-weight:400;text-align:center;color:#1a1a1a;display:inline}.timesheet-stats-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:20px}.timesheet-stats-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:15px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]{min-height:95px;transition:all .3s linear}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]:hover{cursor:pointer}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-card-clicked[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.timesheet-stats-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.timesheet-stats-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.timesheet-stats-styles[_ngcontent-%COMP%]   .loadcard[_ngcontent-%COMP%]{transition:all .25s linear;max-height:39px!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:hidden}.timesheet-stats-styles[_ngcontent-%COMP%]   .icon-tray-button-visible[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:17px!important;visibility:visible!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.timesheet-stats-styles[_ngcontent-%COMP%]   .costcenter[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-draft-side[_ngcontent-%COMP%]{border-left-color:grey!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-submitted-side[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-approved-side[_ngcontent-%COMP%]{border-left-color:#009432!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-rejected-side[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.timesheet-stats-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.timesheet-stats-styles[_ngcontent-%COMP%]   .status-dot-days[_ngcontent-%COMP%]{height:25px;width:25px;font-weight:400;line-height:25px;border-radius:50%;display:inline-block;vertical-align:middle;font-size:12px;text-align:center;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%]{border:1px solid #6ab04c;background:#6ab04c}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%]{font-size:12px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%]{border:1px solid #eb3b5a;background:#eb3b5a}.timesheet-stats-styles[_ngcontent-%COMP%]   .is-empty-day[_ngcontent-%COMP%]{font-size:12px;color:#1a1a1a!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);border:1px solid #c7c4c4}.timesheet-stats-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}.timesheet-stats-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%]{border:1px solid grey;padding:5px 0!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%], .timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{text-align:center;font-size:13px;color:#1a1a1a}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-search-input[_ngcontent-%COMP%]{padding:12px 5px 8px;cursor:pointer}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]{width:100%;text-align:center!important;font-size:14px;color:#1a1a1a}.timesheet-stats-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{padding:5px!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{font-size:14px!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:0!important;border-top:0!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.timesheet-stats-styles[_ngcontent-%COMP%]     .mat-form-field-underline{display:none}.timesheet-stats-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-trigger{padding:2px 0!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.timesheet-stats-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.timesheet-stats-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.timesheet-stats-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-stats-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.timesheet-stats-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{margin-top:3px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-stats-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}']}),t})()}];let I=(()=>{class t{}return t.\u0275mod=f["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[s.k.forChild(P)],s.k]}),t})();var T=i("bTqV"),k=i("Qu3c"),A=i("NFeN"),R=i("kmnG"),V=i("qFsG"),N=i("iadO"),U=i("/1cH"),E=i("dlKe"),B=i("bSwM"),F=i("/QRN"),L=i("Xi0T"),H=i("Xa2L"),j=i("mEBv"),W=i("7EHt");i("9op9");const z={width:"auto",height:"auto",showDelay:500,hideDelay:300,trigger:"click"};let q=(()=>{class t{}return t.\u0275mod=f["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.CommonModule,I,T.b,k.b,A.b,R.e,V.c,N.h,E.b,U.c,B.b,n.p,n.E,F.a,L.a,H.b,W.b,j.b.forRoot(z)]]}),t})()},NJ67:function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));class a{constructor(){this.disabled=!1}onChange(t){}onTouched(t){}writeValue(t){this.value=t}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t}}},hJL4:function(t,e,i){"use strict";i.d(e,"a",(function(){return u}));var a=i("mrSG"),s=i("XNiG"),r=i("xG9w"),n=i("fXoL"),o=i("tk/3"),c=i("LcQX"),l=i("XXEo"),d=i("flaP");let u=(()=>{class t{constructor(t,e,i,a){this.http=t,this.UtilityService=e,this.loginService=i,this.roleService=a,this.msg=new s.b,this.taskStatusColor=[],this.approveRequest=(t,e)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:t,approverOid:e}),this.rejectRequest=(t,e)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:t,approverOid:e}),this.getTaskStatusColor()}getAllRequestsOfUser(t,e,i,a,s,r,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:t,statusId:e,statusCode:i,objectIds:a,skip:s,limit:r,filterConfig:n,orgIds:o})}getAllRoleAccess(){return r.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(t,e,i,a,s,r,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:t,statusId:e,statusCode:i,objectIds:a,skip:s,limit:r,filterConfig:n,orgIds:o})}getRequestsForAwaitingApproval(t,e,i,a){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:t,skip:e,limit:i,filterConfig:a})}getObjectIdsBasedOnOid(t){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:t})}getStatusCountBasedOnOid(t,e,i,a){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:t,objectIds:e,dataTypeArray:i,filterConfig:a,orgIds:s})}getHeaderCount(t){return this.http.post("/api/isa/request/getHeaderCount",{oid:t})}getRequestBasedOnStatus(t,e){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:t,status:e})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(t){this.msg.next(t)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((t,e)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(e=>(this.taskStatusColor=e,t(e)),t=>{})})}getAllRequestsForRmg(t){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:t})}getTotalForRmg(t){return new Promise((e,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:t}).subscribe(t=>e(t),t=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(t){return new Promise((e,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",t).subscribe(t=>e(t.data),t=>{i(t)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(t){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}saveAndApproveRequest(t){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",t)}getVisibilityMatrix(t){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getISAAttachmentFromS3(t){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(t){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:t})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(t){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:t})}getISACustomReport(t){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:t})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(t,e,i,s,n,o,c){return Object(a.c)(this,void 0,void 0,(function*(){let a;a=o&&o.length>1&&(yield this.getManpowerCostByOId(o,i,n,2))||(yield this.getManpowerCostBasedOnPosition(t,e,i,n,c));let l=yield this.getNonManpowerCost(e,i,s,n,2),d=yield this.getAllocatedCost(),u=0;u=(a?a.cost:0)+l.length>0?r.reduce(r.pluck(l,"cost"),(t,e)=>t+e,0):0;let h=d.length>0?r.reduce(r.pluck(d,"percentage"),(t,e)=>t+e,0):0;return{cost:u,currency:a&&a.currency_code?a.currency_code:"",manpowerCost:a,nonManpowerCost:l,allocatedCost:d,allocatedCostValue:u*(h/100)}}))}getManpowerCostBasedOnPosition(t,e,i,a,s){return new Promise((r,n)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:t,nationalityId:e,locationGroupId:i,unit:a,position:s}).subscribe(t=>r(t),t=>(console.log(t),n(t)))})}getNonManpowerCost(t,e,i,a,s){return new Promise((r,n)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:t,locationGroupId:e,locationId:i,unit:a,currency_id:s}).subscribe(t=>r(t),t=>(console.log(t),n(t)))})}getAllocatedCost(){return new Promise((t,e)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(e=>t(e),t=>(console.log(t),e(t)))})}getManpowerCostByOId(t,e,i,a){return new Promise((s,r)=>{this.http.post("/api/project/getEmployeeSalary",{oid:t,location_group_id:e,unit_id:i,currency_id:a}).subscribe(t=>s(t),t=>(console.log(t),r(t)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(t,e){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:t,docID:e})}getVendorList(t,e){return this.http.post("/api/isa/request/getVendorList",{documentID:t,limitval:e})}removeVendor(t,e){return this.http.post("/api/isa/request/removeVendors",{vendorOid:t,documentID:e})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return t.\u0275fac=function(e){return new(e||t)(n["\u0275\u0275inject"](o.c),n["\u0275\u0275inject"](c.a),n["\u0275\u0275inject"](l.a),n["\u0275\u0275inject"](d.a))},t.\u0275prov=n["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},ucYs:function(t,e,i){"use strict";i.d(e,"a",(function(){return c}));var a=i("mrSG"),s=i("xG9w"),r=i("fXoL"),n=i("tk/3"),o=i("BVzC");let c=(()=>{class t{constructor(t,e){this.http=t,this.errorService=e,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(t=>{this.workflowStatusList=t.data},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server While Getting Workflow Status List",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getWorkflowProperties(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:t}).subscribe(t=>e(t),t=>i(t))})}getWorkflowPropertiesByWorkflowId(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:t}).subscribe(t=>e(t),t=>i(t))})}getApproversHierarchy(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",t).subscribe(t=>e(t),t=>i(t))})}createWorkflowItems(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",t).subscribe(t=>e(t),t=>i(t))})}getWorkflowDetails(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:t}).subscribe(t=>e(t),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting workflow details",t&&t.params?t.params:t&&t.error?t.error.params:{})})})}formatApproversHierarchy(t,e){return Object(a.c)(this,void 0,void 0,(function*(){0==t.length&&e.cc0&&e.cc0.appr0&&e.cc0.appr0.length>0&&t.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<t.length;i++){let a=[],r=s.keys(e["cc"+i]);for(let s=0;s<r.length;s++)for(let n=0;n<e["cc"+i][r[s]].length;n++){let o={name:e["cc"+i][r[s]][n].DELEGATE_NAME,oid:e["cc"+i][r[s]][n].DELEGATE_OID,level:s+1,designation:e["cc"+i][r[s]][n].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+i][r[s]][n].IS_DELEGATED,role:e["cc"+i][r[s]][n].DELEGATE_ROLE_NAME};if(1==e["cc"+i][r[s]][n].IS_DELEGATED&&(o.delegated_by={name:e["cc"+i][r[s]][n].APPROVER_NAME,oid:e["cc"+i][r[s]][n].APPROVER_OID,level:s+1,designation:e["cc"+i][r[s]][n].APPROVER_DESIGNATION_NAME}),a.push(o),i==t.length-1&&s==r.length-1&&n==e["cc"+i][r[s]].length-1)return a}}}))}storeComments(t,e,i){return new Promise((a,s)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:t,newComments:e,commentor:i}).subscribe(t=>a(t),t=>(this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server Error while Updating Workflow Comments",t&&t.params?t.params:t&&t.error?t.error.params:{}),s(t)))})}updateWorkflowItems(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",t).subscribe(t=>e(t),t=>(console.log(t),i(t)))})}formatApproversHierarchyForOpportunityApprovalActivity(t){return Object(a.c)(this,void 0,void 0,(function*(){for(let e=0;e<1;e++){let i=[],a=s.keys(t["cc"+e]);for(let s=0;s<a.length;s++)for(let r=0;r<t["cc"+e][a[s]].length;r++){let n={name:t["cc"+e][a[s]][r].DELEGATE_NAME,oid:t["cc"+e][a[s]][r].DELEGATE_OID,level:t["cc"+e][a[s]][r].APPROVAL_ORDER,designation:t["cc"+e][a[s]][r].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+e][a[s]][r].IS_DELEGATED};if(1==t["cc"+e][a[s]][r].IS_DELEGATED&&(n.delegated_by={name:t["cc"+e][a[s]][r].APPROVER_NAME,oid:t["cc"+e][a[s]][r].APPROVER_OID,level:t["cc"+e][a[s]][r].APPROVAL_ORDER,designation:t["cc"+e][a[s]][r].APPROVER_DESIGNATION_NAME}),i.push(n),s==a.length-1&&r==t["cc"+e][a[s]].length-1)return i}}}))}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](n.c),r["\u0275\u0275inject"](o.a))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);