(window.webpackJsonp=window.webpackJsonp||[]).push([[677],{Wnn1:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetProjectReportModule",(function(){return D}));var o=n("ofXK"),a=n("tyNb"),r=n("mrSG"),i=n("xG9w"),d=n("wd/R"),s=n("fXoL"),c=n("JYr9"),l=n("tk/3");let p=(()=>{class e{constructor(e){this.http=e,this.getTimesheetReportForTaskV1=e=>new Promise((t,n)=>{this.http.post("/api/project/reports/getTimesheetReportForTaskV1",{filter_config:e}).subscribe(e=>{t(e)},e=>{n(e)})})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](l.c))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var m=n("XXEo"),g=n("qAI/"),h=n("kmnG"),f=n("qFsG"),u=n("iadO"),b=n("3Pt+"),x=n("bTqV"),_=n("Qu3c"),y=n("NFeN"),C=n("ZzPI"),M=n("6t9p");function O(e,t){if(1&e&&s["\u0275\u0275element"](0,"dxi-column",23),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("dataField",e.dataField),s["\u0275\u0275propertyInterpolate"]("caption",e.caption),s["\u0275\u0275propertyInterpolate"]("alignment",e.alignment),s["\u0275\u0275property"]("visible",e.visible)}}const v=[{path:"",component:(()=>{class e{constructor(e,t,n,o,a){this.productBurndownService=e,this.tsprojectservice=t,this.route=n,this.authService=o,this.masterService=a,this.timesheetReportData=[],this.customerDataColumnConfigs=[{dataField:"external_reference_id",caption:"Task Id",visible:!0,alignment:"left",hasSummaryType:!0,summaryType:"count"},{dataField:"activity_name",caption:"Task Name",visible:!0,alignment:"left",hasSummaryType:!0,summaryType:"count"},{dataField:"planned_hours",caption:"Planned Hours",visible:!0,alignment:"right",hasSummaryType:!0,summaryType:"sum"},{dataField:"timesheet_actual_hours",caption:"Actual Hours",visible:!0,alignment:"right",hasSummaryType:!0,summaryType:"sum"},{dataField:"remaining_hours",caption:"Remaining Hours",visible:!0,alignment:"right",hasSummaryType:!0,summaryType:"sum"},{dataField:"employee_name",caption:"Assigned to",visible:!0,alignment:"right",hasSummaryType:!1},{dataField:"associate_id",caption:"Associate ID",visible:!0,alignment:"right",hasSummaryType:!1},{dataField:"date",caption:"Task Date",visible:!0,alignment:"right",hasSummaryType:!1}],this.productList=[],this.loading=!1,this.iterationName=[],this.selectedEmployeeIds=[],this.token=this.authService.getToken()}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.searchEmployee=window.location.origin+"/api/okr/objective/searchEmployee",this.route.queryParams.subscribe(e=>{this.projectId=e?e.projectId:void 0}),yield this.masterService.getProjectTenantConfig().then(e=>{this.config=e,this.config_retrieved=!0});let e=this.config.timesheet_start_duration?this.config.timesheet_start_duration:0,t=this.config.timesheet_end_duration?this.config.timesheet_end_duration:0,n=d().add(e).startOf("month").format("YYYY-MM-DD"),o=d().add(t).endOf("month").format("YYYY-MM-DD");this.selectedStartDate=n,this.selectedEndDate=o,this.getTimesheetReport()}))}getTimesheetReport(){this.loading=!0,this.filteredData={start_date:d(this.selectedStartDate).format("YYYY-MM-DD"),end_date:d(this.selectedEndDate).format("YYYY-MM-DD")},console.log(this.filteredData),this.tsprojectservice.getTimesheetReportForTaskV1(this.filteredData).then(e=>{this.tsData=e,console.log(this.tsData),this.timesheetReportData=e.data,console.log(this.timesheetReportData)})}selectEmployees(e){this.selectedEmployeeIds=i.pluck(e,"id"),console.log(this.selectedEmployeeIds)}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](p),s["\u0275\u0275directiveInject"](a.a),s["\u0275\u0275directiveInject"](m.a),s["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-project-landing-page"]],decls:35,vars:23,consts:[[1,"container","ts-project-dashboard"],[1,"col-12","row","p-0","m-0"],[1,"p-0","mr-2",2,"margin-top","1px"],["appearance","outline",2,"flex","0 0 auto"],["matInput","",3,"matDatepicker","ngModel","ngModelChange"],["matIconSuffix","",3,"for"],["picker1",""],[1,"p-0","ml-1","mr-2",2,"margin-top","1px"],["appearance","outline"],["picker2",""],[1,"col","p-0","mr-2",2,"margin-top","10px"],["mat-icon-button","","matTooltip","Get timesheet report","type","submit",1,"iconbtn","ml-2","mt-2",3,"click"],[1,"m-4"],["id","dataGrid","keyExpr","external_reference_id",3,"dataSource","allowColumnResizing","columnAutoWidth","allowColumnReordering","showBorders","rowAlternationEnabled","showColumnLines","showRowLines"],[3,"visible","applyFilter"],[3,"enabled"],[3,"visible"],[3,"pageSize","pageIndex"],["mode","select",3,"enabled"],[3,"dataField","caption","alignment","visible",4,"ngFor","ngForOf"],["column","external_reference_id","summaryType","count"],["column","planned_hours","summaryType","sum"],["column","timesheet_actual_hours","summaryType","sum"],[3,"dataField","caption","alignment","visible"]],template:function(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"mat-form-field",3),s["\u0275\u0275elementStart"](4,"mat-label"),s["\u0275\u0275text"](5,"Start Date"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"input",4),s["\u0275\u0275listener"]("ngModelChange",(function(e){return t.selectedStartDate=e})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](7,"mat-datepicker-toggle",5),s["\u0275\u0275element"](8,"mat-datepicker",null,6),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",7),s["\u0275\u0275elementStart"](11,"mat-form-field",8),s["\u0275\u0275elementStart"](12,"mat-label"),s["\u0275\u0275text"](13,"End Date"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"input",4),s["\u0275\u0275listener"]("ngModelChange",(function(e){return t.selectedEndDate=e})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](15,"mat-datepicker-toggle",5),s["\u0275\u0275element"](16,"mat-datepicker",null,9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](18,"div",10),s["\u0275\u0275elementStart"](19,"button",11),s["\u0275\u0275listener"]("click",(function(){return t.getTimesheetReport()})),s["\u0275\u0275elementStart"](20,"mat-icon"),s["\u0275\u0275text"](21," done_all"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"div",12),s["\u0275\u0275elementStart"](23,"dx-data-grid",13),s["\u0275\u0275element"](24,"dxo-filter-row",14),s["\u0275\u0275element"](25,"dxo-column-chooser",15),s["\u0275\u0275element"](26,"dxo-header-filter",16),s["\u0275\u0275element"](27,"dxo-paging",17),s["\u0275\u0275element"](28,"dxo-column-chooser",18),s["\u0275\u0275element"](29,"dxo-export",15),s["\u0275\u0275template"](30,O,1,4,"dxi-column",19),s["\u0275\u0275elementStart"](31,"dxo-summary"),s["\u0275\u0275element"](32,"dxi-total-item",20),s["\u0275\u0275element"](33,"dxi-total-item",21),s["\u0275\u0275element"](34,"dxi-total-item",22),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275reference"](9),n=s["\u0275\u0275reference"](17);s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("matDatepicker",e)("ngModel",t.selectedStartDate),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",e),s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("matDatepicker",n)("ngModel",t.selectedEndDate),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",n),s["\u0275\u0275advance"](8),s["\u0275\u0275property"]("dataSource",t.timesheetReportData)("allowColumnResizing",!0)("columnAutoWidth",!0)("allowColumnReordering",!0)("showBorders",!0)("rowAlternationEnabled",!0)("showColumnLines",!0)("showRowLines",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("visible",!0)("applyFilter",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("pageSize",10)("pageIndex",0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",t.customerDataColumnConfigs)}},directives:[h.c,h.g,f.b,u.g,b.e,b.v,b.y,u.i,u.f,x.a,_.a,y.a,C.a,M.dc,M.tb,M.Cc,M.od,M.Sb,o.NgForOf,M.ve,M.N,M.g],styles:[".ts-project-dashboard[_ngcontent-%COMP%]{margin-top:80px;max-width:none}.ts-project-dashboard[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:13px!important;padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px;display:flex!important}.ts-project-dashboard[_ngcontent-%COMP%]   .sticky-header[_ngcontent-%COMP%]{position:absolute;top:65px;z-index:3}.ts-project-dashboard[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:25px}.ts-project-dashboard[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.ts-project-dashboard[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.ts-project-dashboard[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:425px;overflow-y:scroll}.ts-project-dashboard[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:435px;overflow-y:scroll}.ts-project-dashboard[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.ts-project-dashboard[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:30px;width:204px}.ts-project-dashboard[_ngcontent-%COMP%]   .lead-score[_ngcontent-%COMP%]{font-size:11px;font-weight:400;color:#4d4d4b}.ts-project-dashboard[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.ts-project-dashboard[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.ts-project-dashboard[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.ts-project-dashboard[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{min-width:30rem}.ts-project-dashboard[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.ts-project-dashboard[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.ts-project-dashboard[_ngcontent-%COMP%]   .checkbox-text[_ngcontent-%COMP%]{color:#7b7b7a;font-weight:400;font-size:14px}.ts-project-dashboard[_ngcontent-%COMP%]   .checkbox-text[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%]{padding-top:3px!important}.ts-project-dashboard[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.ts-project-dashboard[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.ts-project-dashboard[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.ts-project-dashboard[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.ts-project-dashboard[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.ts-project-dashboard[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.ts-project-dashboard[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.ts-project-dashboard[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.ts-project-dashboard[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.ts-project-dashboard[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.ts-project-dashboard[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.ts-project-dashboard[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.ts-project-dashboard[_ngcontent-%COMP%]    .mat-form-field .mat-form-field-infix{padding:.75em 0!important;border-top:.54375em solid transparent!important;display:flex!important}.ts-project-dashboard[_ngcontent-%COMP%]    .mat-icon-button{padding:0;min-width:0;width:40px;height:40px;flex-shrink:0;line-height:-12px;border-radius:50%;margin-top:-13px!important;margin-bottom:-10px!important}"]}),e})()}];let P=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(v)],a.k]}),e})();var w=n("KFJe"),j=n("lVl8"),k=n("Xa2L"),S=n("Xi0T");let D=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,P,C.b,u.h,h.e,f.c,w.b,b.p,y.b,x.b,_.b,j.b,k.b,S.a]]}),e})()}}]);