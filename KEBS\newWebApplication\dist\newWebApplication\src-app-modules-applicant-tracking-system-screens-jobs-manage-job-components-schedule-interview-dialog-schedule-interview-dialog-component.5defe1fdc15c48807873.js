(window.webpackJsonp=window.webpackJsonp||[]).push([[915],{DnWP:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("tk/3"),a=n("wd/R"),r=n("fXoL");let o=(()=>{class e{constructor(e){this._http=e}fetchCandidatesForJob(e){return this._http.post("api/ats/candidate/fetchCandidatesForJob",{jobDetails:e})}deleteCandidatefromTag(e){return this._http.post("api/ats/candidate/deleteCandidatefromTag",{tagDetails:e})}tagCandidates(e){return this._http.post("api/ats/candidate/tagCandidates",{tagDetails:e})}moveCandidateToTalentPipeline(e,t,n,i,a,r,o){return this._http.post("api/ats/candidate/moveCandidateToTalentPipeline",{candidateIds:e,jobId:t,isBulkSelectActive:n,candidatesExcluded:i,stageId:a,filter:r,search_params:o})}blacklistCandidates(e,t,n,i,a,r,o,s){return this._http.post("api/ats/candidate/blacklistCandidates",{candidateIds:e,isBulkSelectActive:t,candidatesExcluded:n,stageId:i,jobId:a,filter:r,search_params:o,candidateStatusId:s})}updateCandidateJobStage(e){return this._http.post("api/ats/candidate/updateCandidateJobStage",{details:e})}sendEmailForCandidates(e){return this._http.post("api/ats/utilService/sendEmailForCandidates",{details:e})}uploadBenchMarkDetails(e,t){return this._http.post("api/ats/candidate/uploadBenchMarkDetails",{jobId:e,data:t})}uploadAptitudeScoreDetails(e,t,n,i){return this._http.post("api/ats/candidate/uploadAptitudeScoreDetails",{jobId:e,data:t,imageLinkArray:n,reportLinkArray:i})}uploadNonScientificScoreDetails(e,t,n,i){return this._http.post("api/ats/candidate/uploadNonScientificScoreDetails",{jobId:e,data:t,imageLinkArray:n,reportLinkArray:i})}downloadBenchmarkReport(e,t,n=0){return this._http.post("api/ats/candidate/downloadBenchmarkReport",{jobId:e,candidateId:n,filter:t})}downloadAptitudeReport(e,t,n=0){return this._http.post("api/ats/candidate/downloadAptitudeReport",{jobId:e,candidateId:n,filter:t})}downloadNonScientificReport(e,t,n=0){return this._http.post("api/ats/candidate/downloadNonScientificReport",{jobId:e,candidateId:n,filter:t})}fetchAllcandidateTags(){return this._http.post("api/ats/candidate/fetchAllcandidateTags",{})}getCandidateOtherApplicationDetails(e,t){return this._http.post("api/ats/candidate/getCandidateOtherApplicationDetails",{jobId:e,candidateId:t})}updateCandidateStageStatus(e){return this._http.post("api/ats/candidate/updateCandidateStageStatus",{details:e})}getCandidateBasicDetails(e,t,n){return this._http.post("api/ats/candidate/getCandidateBasicDetails",{jobId:e,candidateId:t,currentUserAid:n})}getInterviewPanelist(e){return this._http.post("api/ats/candidate/getInterviewPanelist",{details:e})}getInterviewerScoreDetails(e){return this._http.post("api/ats/candidate/getInterviewerScoreDetails",{details:e})}getCandidateResumeDetails(e,t){return this._http.post("api/ats/candidate/getCandidateResumeDetails",{jobId:e,candidateId:t})}fetchCandidateCardViewScoreDetails(e,t,n,i,a,r){return this._http.post("api/ats/candidate/fetchCandidateCardViewScoreDetails",{jobId:e,stageId:t,sectionId:n,sortByMetricId:i,skip:a,limit:r})}fetchScorecardSectionDetails(e,t){return this._http.post("api/ats/candidate/fetchScorecardSectionDetails",{jobId:e,stageId:t})}fetchScorecardMetricDetails(e,t,n){return this._http.post("api/ats/candidate/fetchScorecardMetricDetails",{jobId:e,stageId:t,sectionId:n})}getCandidateCommentsDetails(e,t,n,i){return this._http.post("api/ats/candidate/getCandidateCommentsDetails",{jobId:e,candidateId:t,currentUserAid:n,searchText:i})}postCandidateComments(e,t){return this._http.post("api/ats/candidate/postCandidateComments",{candidateId:e,commentsDetails:t})}getCandidateInterviewDetails(e,t){return this._http.post("api/ats/candidate/getCandidateInterviewDetails",{jobId:e,candidateId:t})}scheduleInterview(e){return this._http.post("api/ats/interview/scheduleInterview",{details:e})}getCandidateDocumentDetails(e,t){return this._http.post("api/ats/candidate/fetchcandidateDocuments",{jobId:e,candidateId:t})}fetchCandidateDetailsForQueue(e,t,n){return this._http.post("api/ats/candidate/fetchCandidateDetailsForQueue",{jobId:e,candidateId:t,currentUserAid:n})}getCandidateStreamDetails(e,t){return this._http.post("api/ats/candidate/getCandidateStreamDetails",{jobId:e,candidateId:t})}fetchCandidateUploadTemplate(){return this._http.post("api/ats/bulkActions/fetchCandidateUploadTemplate",{})}simulateCandidateApply(e,t){return this._http.post("api/ats/bulkActions/simulateCandidateApply",{jobId:e,data:t})}performCandidateBulkApply(e,t,n,i,a,r){return this._http.post("api/ats/bulkActions/performCandidateBulkApply",{jobId:e,data:t,fileName:n,fileSize:i,fileUploadStatus:a,isFromTalentPipeline:r})}fetchCandidateUploadFileHistory(e){return this._http.post("api/ats/bulkActions/fetchCandidateUploadFileHistory",{job_id:e})}fetchCandidateUpdSimulationResult(e){return this._http.post("api/ats/bulkActions/fetchCandidateUpdSimulationResult",{id:e})}getAllCandidateDetails(e,t,n,i){return this._http.post("api/ats/candidate/getAllCandidateDetails",{skip:e.skip,limit:e.limit,sort:e.sort,filter:e.filter,condition_query:e.condition_query,search_params:e.search_params,aid:t,isTalentPipeLine:n,candidateStatusId:e.candidateStatusId,isCampusJobTalentPipeline:i})}getTotalCandidateCount(e,t,n){return this._http.post("api/ats/candidate/getTotalCandidateCount",{skip:e.skip,limit:e.limit,sort:e.sort,filter:e.filter,condition_query:e.condition_query,search_params:e.search_params,isTalentPipeLine:t,candidateStatusId:e.candidateStatusId,isCampusJobTalentPipeline:n})}performResumeSimulation(e,t){return this._http.post("api/ats/bulkActions/simulateBulkResumeUpload",{data:t,jobId:e})}performCandidateBulkResumeUpload(e,t,n,i){return this._http.post("api/ats/bulkActions/performCandidateBulkResumeApply",{data:e,jobId:t,isFromTalentPipeline:n,dataArrayForResumeUpload:i})}getCandidateLatestJobDetails(e){return this._http.post("api/ats/candidate/getCandidateLatestJobDetails",{candidateId:e})}getScheduledInterviewDetails(e){return this._http.post("api/ats/interview/getScheduledInterviewDetails",{interviewId:e})}rescheduleInterview(e){return this._http.post("api/ats/interview/rescheduleInterview",{details:e})}cancelScheduledInterview(e){return this._http.post("api/ats/interview/cancelScheduledInterview",{interviewId:e})}getCandidateCountForJobByJobId(e){return this._http.post("api/ats/candidate/getCandidateCountForJobByJobId",{jobDetails:e})}updateStarRating(e,t,n,i){return this._http.post("api/ats/candidate/updateStarRating",{jobId:e,candidateId:t,rating:n,currentUserAid:i})}getMeetingsofInterviewers(e){return this._http.post("api/ats/interview/getMeetingsofInterviewers",{details:e})}getScorecardOtherDetails(e,t,n,i){return this._http.post("api/ats/candidate/getScorecardOtherDetails",{jobId:e,candidateId:t,stageId:n,interviewer:i})}sendDocumentUploadLink(e){return this._http.post("api/ats/candidate/sendDocumentUploadLink",{details:e})}getCandidateCustomQuestionDetails(e,t,n){return this._http.post("api/ats/candidate/getCandidateCustomQuestionDetails",{jobId:e,candidateId:t,currentUserAid:n})}uploadCandidateDocuments(e,t,n,a){const r=new FormData;r.append("jobId",e),r.append("candidateId",t),r.append("document",JSON.stringify(n)),r.append("file",a);const o=new i.f;return this._http.post("/api/ats/candidate/uploadCandidateDocuments",r,{headers:o})}deleteCandidateDocuments(e,t,n){return this._http.post("/api/ats/candidate/deleteCandidateDocuments",{jobId:e,candidateId:t,documentId:n})}retrieveCandidateOfferTrackingDetails(e,t){return this._http.post("api/ats/candidate/retrieveCandidateOfferTrackingDetails",{jobId:e,candidateId:t,currentDate:a().format("YYYY-MM-DD")})}getAllCandidateIds(e,t,n){return this._http.post("api/ats/candidate/getAllCandidateIds",{skip:e.skip,limit:e.limit,sort:e.sort,filter:e.filter,condition_query:e.condition_query,search_params:e.search_params,isTalentPipeLine:t,candidateStatusId:e.candidateStatusId,isCampusJobTalentPipeline:n})}getCandidateIdsForJobByJobId(e){return this._http.post("api/ats/candidate/getCandidateIdsForJobByJobId",{jobDetails:e})}uploadCertifications(e){return this._http.post("/api/ats/candidate/uploadCertifications",e)}performUploadUpdation(e,t){return this._http.post("api/ats/candidate/performUploadUpdation",{fileUploadDetails:e,candidateId:t})}getCandidateCertificates(e){return this._http.post("api/ats/candidate/getCandidateCertificates",{candidateId:e})}deleteCandidateCertificate(e){return this._http.post("api/ats/candidate/deleteCandidateCertificate",{certificateDetails:e})}createCampusInterviewForcandidates(e){return this._http.post("api/ats/interview/createCampusInterviewForcandidates",{details:e})}storeCandidateEmails(e){return this._http.post("api/ats/interview/storeCandidateEmails",{details:e})}getInterviewEmailData(e,t){return this._http.post("api/ats/interview/getInterviewEmailData",{jobId:t,candidateId:e})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](i.c))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},GCKX:function(e,t,n){"use strict";n.r(t),n.d(t,"MY_FORMATS",(function(){return tt})),n.d(t,"ScheduleInterviewDialogComponent",(function(){return nt}));var i=n("mrSG"),a=n("3Pt+"),r=n("0IaG"),o=n("XNiG"),s=n("1G5W"),d=n("1yaQ"),l=n("FKr1"),c=n("wd/R"),m=n.n(c),p=n("fXoL"),u=n("rQiX"),h=n("XNFG"),v=n("DnWP"),g=n("URR/"),f=n("ofXK"),C=n("NFeN"),w=n("Qu3c"),I=n("Xa2L"),F=n("f0Cb"),x=n("UVjm"),b=n("QibW"),M=n("kmnG"),y=n("qFsG"),_=n("iadO"),S=n("yyQj"),D=n("su5B"),E=n("rKCF"),O=n("6t9p"),P=n("II/y"),T=n("pPzn"),k=n("IEgo"),V=n("/rGH");const L=function(e){return{"pointer-events":e}};function j(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",27),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().goToForms()})),p["\u0275\u0275elementStart"](1,"mat-icon",28),p["\u0275\u0275text"](2,"navigate_before"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275property"]("matTooltip","Go Back To Interview Scheduling")("ngStyle",p["\u0275\u0275pureFunction1"](2,L,e.isApiInProgress?"none":""))}}function A(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2),t=e.index;p["\u0275\u0275classMap"](e.$implicit.isSelected?"circle-selected":"circle"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",t+1," ")}}function z(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",35),p["\u0275\u0275element"](2,"circle",36),p["\u0275\u0275elementStart"](3,"mask",37),p["\u0275\u0275element"](4,"rect",38),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"g",39),p["\u0275\u0275element"](6,"path",40),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function B(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",33),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275nextContext"]().index;return p["\u0275\u0275nextContext"](2).changeSection(t)})),p["\u0275\u0275template"](1,A,2,3,"div",34),p["\u0275\u0275template"](2,z,7,0,"div",20),p["\u0275\u0275elementStart"](3,"div"),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](),t=e.index,n=e.$implicit,i=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==t&&i.scheduleInterviewForm.invalid||1==t),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==t&&i.scheduleInterviewForm.valid),p["\u0275\u0275advance"](1),p["\u0275\u0275classMap"](n.isSelected?"section-text-selected":"section-text"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",n.name," ")}}function H(e,t){1&e&&p["\u0275\u0275element"](0,"div",41)}function R(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,B,5,5,"div",31),p["\u0275\u0275template"](2,H,1,0,"div",32),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e?null:e.isVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",n<i.sections.length-1)}}function Y(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",29),p["\u0275\u0275template"](1,R,3,2,"ng-container",30),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.sections)}}function U(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",42),p["\u0275\u0275element"](1,"mat-spinner",43),p["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",52),p["\u0275\u0275elementStart"](1,"div",53),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",54),p["\u0275\u0275text"](5),p["\u0275\u0275pipe"](6,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,2,e.scheduleInterviewFormFields,"hiringStageText","label")," "),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](6,6,e.scheduleInterviewFormFields,"hiringStageText","description")," ")}}function J(e,t){}function Z(e,t){if(1&e&&p["\u0275\u0275template"](0,J,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](4);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}const N=function(){return{field:"hiringStage"}};function W(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",55),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,Z,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"app-single-select-chip",57),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,8,e.scheduleInterviewFormFields,"hiringStage","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,12,e.scheduleInterviewFormFields,"hiringStage","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("masterData",e.hiringStageMasterData)("placeholder",p["\u0275\u0275pipeBind3"](7,16,e.scheduleInterviewFormFields,"hiringStage","placeholder"))("selectedValue",e.scheduleInterviewForm.get("hiringStage").value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](20,N))("disabled","V"==e.data.mode)}}function q(e,t){}function $(e,t){if(1&e&&p["\u0275\u0275template"](0,q,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](4);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Q=function(){return{field:"template"}};function X(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",55),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,$,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"app-single-select-chip",57),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,8,e.scheduleInterviewFormFields,"template","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,12,e.scheduleInterviewFormFields,"template","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("masterData",e.templateMasterData)("placeholder",p["\u0275\u0275pipeBind3"](7,16,e.scheduleInterviewFormFields,"template","placeholder"))("selectedValue",e.scheduleInterviewForm.get("template").value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](20,Q))("disabled","V"==e.data.mode)}}function K(e,t){}function ee(e,t){if(1&e&&p["\u0275\u0275template"](0,K,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](4);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}const te=function(){return{field:"interviewMode"}};function ne(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",55),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,ee,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"app-single-select-chip",57),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,8,e.scheduleInterviewFormFields,"interviewMode","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,12,e.scheduleInterviewFormFields,"interviewMode","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("masterData",e.interviewModeMasterData)("placeholder",p["\u0275\u0275pipeBind3"](7,16,e.scheduleInterviewFormFields,"interviewMode","placeholder"))("selectedValue",e.scheduleInterviewForm.get("interviewMode").value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](20,te))("disabled","V"==e.data.mode)}}function ie(e,t){}function ae(e,t){if(1&e&&p["\u0275\u0275template"](0,ie,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](4);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function re(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",62),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",63),p["\u0275\u0275elementStart"](2,"g",64),p["\u0275\u0275element"](3,"path",65),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"defs"),p["\u0275\u0275elementStart"](5,"clipPath",66),p["\u0275\u0275element"](6,"rect",67),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](7),p["\u0275\u0275pipe"](8,"columnCustomization"),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](7),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](8,1,e.scheduleInterviewFormFields,"schedulingMethod","description")," ")}}function oe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"mat-radio-button",68),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275property"]("value",e.id)("matTooltip",e.name),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.name)}}function se(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",52),p["\u0275\u0275elementStart"](1,"div",53),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,ae,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](6,re,9,5,"div",59),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275pipe"](8,"columnCustomization"),p["\u0275\u0275elementStart"](9,"mat-radio-group",60),p["\u0275\u0275template"](10,oe,2,3,"mat-radio-button",61),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,5,e.scheduleInterviewFormFields,"schedulingMethod","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,9,e.scheduleInterviewFormFields,"schedulingMethod","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",null!=p["\u0275\u0275pipeBind3"](7,13,e.scheduleInterviewFormFields,"schedulingMethod","description")&&""!=p["\u0275\u0275pipeBind3"](8,17,e.scheduleInterviewFormFields,"schedulingMethod","description")),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("disabled","V"==e.data.mode),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.schedulingMethodMasterData)}}function de(e,t){}function le(e,t){if(1&e&&p["\u0275\u0275template"](0,de,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ce(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",70),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,le,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"mat-form-field",71),p["\u0275\u0275elementStart"](7,"div",72),p["\u0275\u0275element"](8,"input",73),p["\u0275\u0275pipe"](9,"columnCustomization"),p["\u0275\u0275elementStart"](10,"mat-datepicker-toggle",74),p["\u0275\u0275elementStart"](11,"mat-icon",75),p["\u0275\u0275text"](12,"calendar_today"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](13,"mat-datepicker",76,77),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275reference"](14),t=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,7,t.scheduleInterviewFormFields,"scheduledDate","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,11,t.scheduleInterviewFormFields,"scheduledDate","isMandatory")),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("min",t.currentDate)("placeholder",p["\u0275\u0275pipeBind3"](9,15,t.scheduleInterviewFormFields,"scheduledDate","placeholder"))("matDatepicker",e),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("for",e),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("disabled","V"==t.data.mode)}}function me(e,t){}function pe(e,t){if(1&e&&p["\u0275\u0275template"](0,me,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ue(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",70),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,pe,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](6,"app-input-time",78),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,3,e.scheduleInterviewFormFields,"scheduledStartTime","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,7,e.scheduleInterviewFormFields,"scheduledStartTime","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("disabled","V"==e.data.mode)}}function he(e,t){}function ve(e,t){if(1&e&&p["\u0275\u0275template"](0,he,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ge(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",70),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,ve,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](6,"app-input-time",79),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,3,e.scheduleInterviewFormFields,"scheduledEndTime","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,7,e.scheduleInterviewFormFields,"scheduledEndTime","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("disabled","V"==e.data.mode)}}function fe(e,t){}function Ce(e,t){if(1&e&&p["\u0275\u0275template"](0,fe,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}const we=function(){return{field:"scheduledTimeZone"}};function Ie(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",70),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,Ce,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"app-single-select-chip",57),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,8,e.scheduleInterviewFormFields,"scheduledTimeZone","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,12,e.scheduleInterviewFormFields,"scheduledTimeZone","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("masterData",e.scheduledTimeZoneMasterData)("placeholder",p["\u0275\u0275pipeBind3"](7,16,e.scheduleInterviewFormFields,"scheduledTimeZone","placeholder"))("selectedValue",e.scheduleInterviewForm.get("scheduledTimeZone").value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](20,we))("disabled","V"==e.data.mode)}}function Fe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",48),p["\u0275\u0275template"](1,ce,15,19,"div",69),p["\u0275\u0275pipe"](2,"columnCustomization"),p["\u0275\u0275template"](3,ue,7,11,"div",69),p["\u0275\u0275pipe"](4,"columnCustomization"),p["\u0275\u0275template"](5,ge,7,11,"div",69),p["\u0275\u0275pipe"](6,"columnCustomization"),p["\u0275\u0275template"](7,Ie,8,21,"div",69),p["\u0275\u0275pipe"](8,"columnCustomization"),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](2,4,e.scheduleInterviewFormFields,"scheduledDate","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](4,8,e.scheduleInterviewFormFields,"scheduledStartTime","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](6,12,e.scheduleInterviewFormFields,"scheduledEndTime","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](8,16,e.scheduleInterviewFormFields,"scheduledTimeZone","isActive"))}}function xe(e,t){}function be(e,t){if(1&e&&p["\u0275\u0275template"](0,xe,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](4);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Me(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"mat-radio-button",68),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275property"]("value",e.id)("matTooltip",e.name),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.name)}}function ye(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",52),p["\u0275\u0275elementStart"](1,"div",53),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,be,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",80),p["\u0275\u0275text"](7),p["\u0275\u0275pipe"](8,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"mat-radio-group",81),p["\u0275\u0275template"](10,Me,2,3,"mat-radio-button",61),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,5,e.scheduleInterviewFormFields,"interviewTeam","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,9,e.scheduleInterviewFormFields,"interviewTeam","isMandatory")),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](8,13,e.scheduleInterviewFormFields,"interviewTeam","description")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("disabled","V"==e.data.mode),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.interviewTeamMasterData)}}function _e(e,t){}function Se(e,t){if(1&e&&p["\u0275\u0275template"](0,_e,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}function De(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",84),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,Se,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"mat-form-field",71),p["\u0275\u0275elementStart"](7,"input",85),p["\u0275\u0275listener"]("keydown",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).preventInvalidValues(t)}))("paste",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).preventPaste(t)})),p["\u0275\u0275pipe"](8,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,3,e.scheduleInterviewFormFields,"noOfInterviewers","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,7,e.scheduleInterviewFormFields,"noOfInterviewers","isMandatory")||2==e.scheduleInterviewForm.get("interviewTeam").value),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("placeholder",p["\u0275\u0275pipeBind3"](8,11,e.scheduleInterviewFormFields,"noOfInterviewers","label"))}}function Ee(e,t){}function Oe(e,t){if(1&e&&p["\u0275\u0275template"](0,Ee,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Pe=function(){return{field:"teamMembers"}};function Te(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",86),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,Oe,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"app-multi-select-chip",87),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,9,e.scheduleInterviewFormFields,"teamMembers","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,13,e.scheduleInterviewFormFields,"teamMembers","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("type",2)("placeholder",p["\u0275\u0275pipeBind3"](7,17,e.scheduleInterviewFormFields,"teamMembers","placeholder"))("masterData",e.teamMembersMasterData)("selectedValues",e.scheduleInterviewForm.get("teamMembers").value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](21,Pe))("disabled","V"==e.data.mode)}}function ke(e,t){}function Ve(e,t){if(1&e&&p["\u0275\u0275template"](0,ke,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Le=function(){return{field:"host"}};function je(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",84),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,Ve,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"app-single-select-chip",57),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,8,e.scheduleInterviewFormFields,"host","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,12,e.scheduleInterviewFormFields,"host","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("masterData",e.hostMasterData)("placeholder",p["\u0275\u0275pipeBind3"](7,16,e.scheduleInterviewFormFields,"host","placeholder"))("selectedValue",e.scheduleInterviewForm.get("host").value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](20,Le))("disabled","V"==e.data.mode)}}function Ae(e,t){}function ze(e,t){if(1&e&&p["\u0275\u0275template"](0,Ae,0,0,"ng-template",58),2&e){p["\u0275\u0275nextContext"](5);const e=p["\u0275\u0275reference"](31);p["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Be=function(){return{field:"conferenceProvider"}};function He(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",84),p["\u0275\u0275elementStart"](1,"div",56),p["\u0275\u0275text"](2),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275template"](4,ze,1,1,void 0,20),p["\u0275\u0275pipe"](5,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"app-single-select-chip",57),p["\u0275\u0275listener"]("onValueChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),p["\u0275\u0275pipe"](7,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](3,8,e.scheduleInterviewFormFields,"conferenceProvider","label")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](5,12,e.scheduleInterviewFormFields,"conferenceProvider","isMandatory")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("masterData",e.conferenceProviderMasterData)("placeholder",p["\u0275\u0275pipeBind3"](7,16,e.scheduleInterviewFormFields,"conferenceProvider","placeholder"))("selectedValue",e.scheduleInterviewForm.get("conferenceProvider").value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](20,Be))("disabled","V"==e.data.mode)}}function Re(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,De,9,15,"div",82),p["\u0275\u0275pipe"](2,"columnCustomization"),p["\u0275\u0275template"](3,Te,8,22,"div",83),p["\u0275\u0275pipe"](4,"columnCustomization"),p["\u0275\u0275template"](5,je,8,21,"div",82),p["\u0275\u0275pipe"](6,"columnCustomization"),p["\u0275\u0275template"](7,He,8,21,"div",82),p["\u0275\u0275pipe"](8,"columnCustomization"),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](2,4,e.scheduleInterviewFormFields,"noOfInterviewers","isActive")&&2==e.scheduleInterviewForm.get("interviewTeam").value),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](4,8,e.scheduleInterviewFormFields,"teamMembers","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](6,12,e.scheduleInterviewFormFields,"host","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](8,16,e.scheduleInterviewFormFields,"conferenceProvider","isActive"))}}function Ye(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",45),p["\u0275\u0275elementStart"](1,"div",46),p["\u0275\u0275template"](2,G,7,10,"div",47),p["\u0275\u0275pipe"](3,"columnCustomization"),p["\u0275\u0275elementStart"](4,"div",48),p["\u0275\u0275template"](5,W,8,21,"div",49),p["\u0275\u0275pipe"](6,"columnCustomization"),p["\u0275\u0275template"](7,X,8,21,"div",49),p["\u0275\u0275pipe"](8,"columnCustomization"),p["\u0275\u0275template"](9,ne,8,21,"div",49),p["\u0275\u0275pipe"](10,"columnCustomization"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](11,"mat-divider",50),p["\u0275\u0275template"](12,se,11,21,"div",47),p["\u0275\u0275pipe"](13,"columnCustomization"),p["\u0275\u0275template"](14,Fe,9,20,"div",51),p["\u0275\u0275element"](15,"mat-divider",50),p["\u0275\u0275template"](16,ye,11,17,"div",47),p["\u0275\u0275pipe"](17,"columnCustomization"),p["\u0275\u0275template"](18,Re,9,20,"ng-container",20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("formGroup",e.scheduleInterviewForm),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](3,9,e.scheduleInterviewFormFields,"hiringStageText","isActive")),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](6,13,e.scheduleInterviewFormFields,"hiringStage","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](8,17,e.scheduleInterviewFormFields,"template","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](10,21,e.scheduleInterviewFormFields,"interviewMode","isActive")),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](13,25,e.scheduleInterviewFormFields,"schedulingMethod","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",1==e.scheduleInterviewForm.get("schedulingMethod").value),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind3"](17,29,e.scheduleInterviewFormFields,"interviewTeam","isActive")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",null!=e.scheduleInterviewForm.get("interviewTeam").value)}}const Ue=function(){return["timelineDay"]},Ge=function(){return["aid"]};function Je(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",88),p["\u0275\u0275elementStart"](1,"div",89),p["\u0275\u0275text"](2," Use The Calendar To Define When You Are Available To Conduct Interviews For This Stage "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](3,"mat-divider",50),p["\u0275\u0275elementStart"](4,"div",90),p["\u0275\u0275elementStart"](5,"div",91),p["\u0275\u0275text"](6),p["\u0275\u0275pipe"](7,"dateFormat"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",91),p["\u0275\u0275text"](9),p["\u0275\u0275pipe"](10,"masterData"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"dx-scheduler",92),p["\u0275\u0275listener"]("onCellClick",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).onCellClick(t)}))("onAppointmentClick",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).onAppointmentClick(t)})),p["\u0275\u0275element"](12,"dxo-scrolling",93),p["\u0275\u0275element"](13,"dxo-editing",94),p["\u0275\u0275element"](14,"dxi-resource",95),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](6),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](7,16,e.scheduleInterviewForm.get("scheduledDate").value,"DD MMMM YYYY")," "),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](10,19,e.scheduleInterviewForm.get("scheduledTimeZone").value,e.scheduledTimeZoneMasterData,"name")," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("dataSource",e.appointmentsData)("views",p["\u0275\u0275pureFunction0"](23,Ue))("startDayHour",0)("endDayHour",24)("cellDuration",60)("groups",p["\u0275\u0275pureFunction0"](24,Ge))("currentDate",e.currentSelectedDate),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("allowAdding",!1)("allowDeleting",!1)("allowUpdating",!1)("allowResizing",!1)("allowDragging",!1),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("allowMultiple",!1)("dataSource",e.employeesData)}}function Ze(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",96),p["\u0275\u0275elementStart"](2,"app-mail-preview",97),p["\u0275\u0275listener"]("attachmentChanges",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).onAttachmentChanges(t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("mailFormGroup",e.mailFormGroup)("mailFormConfig",e.mailFormFields)("templateMasterData",e.templateMasterData)("fromMailPlaceHolderMasterData",e.fromMailPlaceHolderMasterData)("mailPlaceHolderMasterData",e.mailPlaceHolderMasterData)("placeholdersMasterData",e.placeholdersMasterData)("attachmentCount",e.attachmentCount)("attachmentConfig",e.attachmentConfig)("attachmentPath",e.attachmentPath)("filesUploaded",e.filesUploaded)("previewImg",e.data.previewImg)("companyLogo",e.data.companyLogo)}}function Ne(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,Ye,19,33,"div",21),p["\u0275\u0275template"](2,Je,15,25,"div",44),p["\u0275\u0275template"](3,Ze,3,12,"div",20),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==e.currentSection),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(1!=e.currentSection||null!=e.data&&e.data.isCampusJob||null!=e.data&&e.data.isCampusInterviewLogic)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",1==e.currentSection&&(null==e.data?null:e.data.isCampusJob)&&(null==e.data?null:e.data.isCampusInterviewLogic))}}function We(e,t){if(1&e&&p["\u0275\u0275element"](0,"div",100),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275property"]("innerHTML",e.previewContent,p["\u0275\u0275sanitizeHtml"])}}function qe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",101),p["\u0275\u0275elementStart"](1,"div"),p["\u0275\u0275element"](2,"img",102),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",103),p["\u0275\u0275text"](4,"Unable to generate preview"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",e.data.previewImg,p["\u0275\u0275sanitizeUrl"])}}function $e(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",45),p["\u0275\u0275template"](1,We,1,1,"div",98),p["\u0275\u0275template"](2,qe,5,1,"div",99),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.previewContent&&""!=e.previewContent&&null!=e.previewContent),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.previewContent||""==e.previewContent||null==e.previewContent)}}function Qe(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",104),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().openPreview()})),p["\u0275\u0275text"](1," Preview "),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](1,L,e.isLoading?"none":""))}}function Xe(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",105),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275nextContext"]();return 0==t.currentSection?t.changeSection(1):t.scheduleInterviewToCandidate()})),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](2,L,e.isLoading?"none":"")),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",0==e.currentSection?"Continue":"Schedule"," ")}}function Ke(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",106),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().moveToNextSection()})),p["\u0275\u0275text"](1," Next "),p["\u0275\u0275elementEnd"]()}}function et(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",107),p["\u0275\u0275text"](1,"*"),p["\u0275\u0275elementEnd"]())}const tt={parse:{dateInput:"DD MMM YYYY"},display:{dateInput:"DD MMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let nt=(()=>{class e{constructor(e,t,n,i,a,r,s){this.data=e,this._dialogRef=t,this._atsMasterService=n,this._fb=i,this._toaster=a,this._atsCandidateService=r,this._atsTemplateSettingsService=s,this._onDestroy=new o.b,this.isLoading=!0,this.isPreviewScreen=!1,this.dynamicCalendarHeight="",this.currentSection=0,this.previewContent="",this.currentDate=m()(),this.scheduleInterviewFormFields=[],this.hiringStageMasterData=[],this.templateMasterData=[],this.scheduledTimeZoneMasterData=[],this.hostMasterData=[],this.teamMembersMasterData=[],this.schedulingMethodMasterData=[],this.conferenceProviderMasterData=[],this.interviewModeMasterData=[],this.interviewTeamMasterData=[{id:1,name:"All Members"},{id:2,name:"Select The Number Of Interviewers Required"}],this.sections=[],this.interviewDetails={},this.interviewEmailDetails={},this.appointmentsData=[],this.employeesData=[],this.mailFormFields=[],this.fromMailPlaceHolderMasterData=[],this.mailPlaceHolderMasterData=[],this.placeholdersMasterData=[],this.attachmentCount=0,this.attachmentConfig={},this.filesUploaded=[],this.attachmentPath=null,this.scheduleInterviewForm=this._fb.group({}),this.mailFormGroup=this._fb.group({})}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),yield this.determineSections(),yield this.getAtsFormsConfig("scheduleInterview"),"C"==this.data.mode&&(yield this.createInterviewScheduleForms()),yield this.initializeMasterData(),"C"==this.data.mode&&this.onFormValueChanges()}))}onResize(){this.calculateDynamicContentHeight()}determineSections(){var e,t;return Object(i.c)(this,void 0,void 0,(function*(){(null===(e=this.data)||void 0===e?void 0:e.isCampusJob)&&(null===(t=this.data)||void 0===t?void 0:t.isCampusInterviewLogic)?(this.sections.push({name:"Interview Invitation",isSelected:!0,formGroup:"interview",isVisible:!0}),this.sections.push({name:"Email Invitation",isSelected:!1,formGroup:"email",isVisible:!0})):(this.sections.push({name:"Interview Invitation",isSelected:!0,formGroup:"interview",isVisible:!0}),this.sections.push({name:"Interview Slots",isSelected:!1,formGroup:"slots",isVisible:!0}))}))}initializeFormMaster(){Promise.all([this.getPlaceHolders(),this.getReceiverEmail(),this.getSenderEmail(),this.fetchAllEmailTemplates()]).then(e=>{this.isLoading=!1}).catch(e=>{this.isLoading=!1,this.onClose(!1)})}getPlaceHolders(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getPlaceHolders().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.placeholdersMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getAttachmentConfig(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAttachmentConfig(1).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?t.data&&t.data.length>0&&(this.attachmentConfig=t.data[0]):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getReceiverEmail(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getReceiverEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.mailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getSenderEmail(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getSenderEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.fromMailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}createMailForm(){return Object(i.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.mailFormFields.length;e++)this.mailFormGroup.addControl(this.mailFormFields[e].key,this._fb.control(null,[this.mailFormFields[e].isMandatory?a.H.required:null].filter(e=>null!=e))),"signature"==this.mailFormFields[e].key&&this.mailFormGroup.addControl("isSignatureOn",this._fb.control(!1)),this.mailFormGroup.addControl("header",this._fb.control(null))}))}calculateDynamicContentHeight(){this.dynamicCalendarHeight=window.innerHeight-57-63-175+"px",document.documentElement.style.setProperty("--dynamicCalendarHeight",this.dynamicCalendarHeight)}onCellClick(e){e.cancel=!0}onAppointmentClick(e){e.cancel=!0}onAttachmentChanges(e){this.attachmentCount=e.attachmentCount,this.filesUploaded=e.filesUploaded,this.attachmentPath=e.attachmentPath}initializeMasterData(){return Object(i.c)(this,void 0,void 0,(function*(){Promise.all([this.fetchAllEmailTemplates(),this.getAtsEmployee(),this.getAssociateDetailsInSystem(),this.getInterviewPlatform(),this.getJobHiringStageBasedOnCategory(),this.getScheduleType(),this.getTimezone(),this.getInterviewMode()]).then(e=>Object(i.c)(this,void 0,void 0,(function*(){var e,t,n,i;if(null===(e=this.scheduleInterviewForm.get("schedulingMethod"))||void 0===e||e.setValue(this.schedulingMethodMasterData[0].id),"C"!=this.data.mode&&((null===(t=this.data)||void 0===t?void 0:t.isCampusJob)&&(null===(n=this.data)||void 0===n?void 0:n.isCampusInterviewLogic)&&(yield this.patchMailTemplateForm()),yield this.getScheduledInterviewDetails(this.data.interviewId),this.patchInterviewScheduleForms()),"C"==this.data.mode){let e=this.scheduledTimeZoneMasterData.filter(e=>1==e.is_default);e&&e.length>0&&(null===(i=this.scheduleInterviewForm.get("scheduledTimeZone"))||void 0===i||i.setValue(e[0].id))}this.isLoading=!1}))).catch(e=>{this.isLoading=!1,this.onClose(!1)})}))}onFormValueChanges(){var e,t;null===(e=this.scheduleInterviewForm.get("interviewTeam"))||void 0===e||e.valueChanges.subscribe(e=>{var t,n,i,r,o;2==e?null===(t=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===t||t.setValidators([a.H.required,a.H.min(1)]):(null===(n=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===n||n.clearValidators(),null===(i=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===i||i.setErrors(null),null===(r=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===r||r.setValue(null),null===(o=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===o||o.updateValueAndValidity())}),null===(t=this.scheduleInterviewForm.get("scheduledDate"))||void 0===t||t.valueChanges.subscribe(()=>{var e,t;null===(e=this.scheduleInterviewForm.get("scheduledEndTime"))||void 0===e||e.updateValueAndValidity(),null===(t=this.scheduleInterviewForm.get("scheduledStartTime"))||void 0===t||t.updateValueAndValidity()})}onClose(e){this._dialogRef.close(e)}changeSection(e){var t,n,a,r,o,s,d,l,c;return Object(i.c)(this,void 0,void 0,(function*(){if(this.currentSection!=e){if(0==this.currentSection){if(this.scheduleInterviewForm.get("scheduledEndTime").hasError("endTimeInvalid"))return this._toaster.showWarning("Warning \u26a0\ufe0f","End Time must be greater than Start Time!",7e3);if(this.scheduleInterviewForm.get("scheduledStartTime").hasError("startTimeInvalid"))return this._toaster.showWarning("Warning \u26a0\ufe0f","Start Time is less than Current Time!",7e3);let e=null===(t=this.scheduleInterviewForm.get("interviewTeam"))||void 0===t?void 0:t.value;if(2==e&&(!(null===(n=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===n?void 0:n.value)||(null===(a=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===a?void 0:a.value)&&(null===(r=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===r?void 0:r.value)<1))return this._toaster.showWarning("Warning \u26a0\ufe0f","No of interviewers must be greater than zero!",7e3);if(this.scheduleInterviewForm.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly fill all Mandatory Fields!",7e3);if(2==e&&(null===(o=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===o?void 0:o.value)>this.scheduleInterviewForm.get("teamMembers").value.length)return this._toaster.showWarning("Warning \u26a0\ufe0f","No of interviewers selected is lesser than the number of interviewers mentioned!",7e3)}if(1!=e||(null===(s=this.data)||void 0===s?void 0:s.isCampusJob)||(null===(d=this.data)||void 0===d?void 0:d.isCampusInterviewLogic))1==e&&(null===(l=this.data)||void 0===l?void 0:l.isCampusJob)&&(null===(c=this.data)||void 0===c?void 0:c.isCampusInterviewLogic)&&(this.isLoading=!0,yield this.getAttachmentConfig(),yield this.getAtsFormsConfig("sendBulkMailToCandidate"),"E"==this.data.mode?(yield this.getInterviewEmailData(),yield this.patchMailTemplateForm()):yield this.createMailForm(),this.initializeFormMaster(),this.isLoading=!1);else{this.isLoading=!0;let e={associate_ids:this.scheduleInterviewForm.get("teamMembers").value,interviewPlatformId:this.scheduleInterviewForm.get("conferenceProvider").value,timeZone:this.scheduleInterviewForm.get("scheduledTimeZone").value,date:m()(this.scheduleInterviewForm.get("scheduledDate").value).format("YYYY-MM-DD")};this.currentSelectedDate=m()(e.date),yield this.getMeetingsofInterviewers(e),this.isLoading=!1}this.currentSection=e,this.sections.forEach(e=>{e.isSelected=!1}),this.sections[e].isSelected=!0}}))}moveToNextSection(){var e,t;return Object(i.c)(this,void 0,void 0,(function*(){if(this.isLoading=!0,(null===(e=this.data)||void 0===e?void 0:e.isCampusJob)&&(null===(t=this.data)||void 0===t?void 0:t.isCampusInterviewLogic))yield this.getAttachmentConfig(),yield this.getAtsFormsConfig("sendBulkMailToCandidate"),"E"==this.data.mode?(yield this.getInterviewEmailData(),yield this.patchMailTemplateForm()):yield this.createMailForm(),this.initializeFormMaster();else{let e={associate_ids:this.scheduleInterviewForm.get("teamMembers").value,interviewPlatformId:this.scheduleInterviewForm.get("conferenceProvider").value,timeZone:this.scheduleInterviewForm.get("scheduledTimeZone").value,date:m()(this.scheduleInterviewForm.get("scheduledDate").value).format("YYYY-MM-DD")};this.currentSelectedDate=m()(e.date),yield this.getMeetingsofInterviewers(e)}this.isLoading=!1,this.currentSection=1}))}openPreview(){var e;let t=this.templateMasterData.find(e=>e.id==this.scheduleInterviewForm.get("template").value);this.previewContent=(null==t?void 0:t.body)?((null===(e=this.data)||void 0===e?void 0:e.companyLogo)||"")+((null==t?void 0:t.header)||"")+((null==t?void 0:t.body)||"")+((null==t?void 0:t.signature)||""):null,this.isPreviewScreen=!0}goToForms(){this.isPreviewScreen=!1}createInterviewScheduleForms(){return Object(i.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.scheduleInterviewFormFields.length;e++)"display-text"!=this.scheduleInterviewFormFields[e].fieldType&&this.scheduleInterviewForm.addControl(this.scheduleInterviewFormFields[e].key,this._fb.control(null,"scheduledEndTime"==this.scheduleInterviewFormFields[e].key?[this.scheduleInterviewFormFields[e].isMandatory?a.H.required:null,this.endTimeValidator.bind(this),a.H.pattern(/^([01][0-9]|2[0-3]):[0-5][0-9]$/)].filter(e=>null!=e):"scheduledStartTime"==this.scheduleInterviewFormFields[e].key?[this.scheduleInterviewFormFields[e].isMandatory?a.H.required:null,this.startTimeValidator.bind(this),a.H.pattern(/^([01][0-9]|2[0-3]):[0-5][0-9]$/)].filter(e=>null!=e):[this.scheduleInterviewFormFields[e].isMandatory?a.H.required:null,"time"==this.scheduleInterviewFormFields[e].fieldType?a.H.pattern(/^([01][0-9]|2[0-3]):[0-5][0-9]$/):null,"noOfInterviewers"==this.scheduleInterviewFormFields[e].key?a.H.min(1):null].filter(e=>null!=e)))}))}patchInterviewScheduleForms(){var e,t,n,r,o,s,d,l,c;return Object(i.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.scheduleInterviewFormFields.length;e++)"display-text"!=this.scheduleInterviewFormFields[e].fieldType&&this.scheduleInterviewForm.addControl(this.scheduleInterviewFormFields[e].key,"scheduledEndTime"==this.scheduleInterviewFormFields[e].key?this._fb.control(this.interviewDetails[this.scheduleInterviewFormFields[e].key],[this.scheduleInterviewFormFields[e].isMandatory?a.H.required:null,this.endTimeValidator.bind(this),a.H.pattern(/^([01][0-9]|2[0-3]):[0-5][0-9]$/)].filter(e=>null!=e)):"scheduledStartTime"==this.scheduleInterviewFormFields[e].key?this._fb.control(this.interviewDetails[this.scheduleInterviewFormFields[e].key],[this.scheduleInterviewFormFields[e].isMandatory?a.H.required:null,this.startTimeValidator.bind(this),a.H.pattern(/^([01][0-9]|2[0-3]):[0-5][0-9]$/)].filter(e=>null!=e)):this._fb.control("date"!=this.scheduleInterviewFormFields[e].fieldType?this.interviewDetails[this.scheduleInterviewFormFields[e].key]:m()(this.interviewDetails[this.scheduleInterviewFormFields[e].key]),[this.scheduleInterviewFormFields[e].isMandatory?a.H.required:null,"time"==this.scheduleInterviewFormFields[e].fieldType?a.H.pattern(/^([01][0-9]|2[0-3]):[0-5][0-9]$/):null,"noOfInterviewers"==this.scheduleInterviewFormFields[e].key?a.H.min(1):null].filter(e=>null!=e)));2==this.scheduleInterviewForm.get("interviewTeam").value?null===(e=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===e||e.setValidators([a.H.required,a.H.min(1)]):(null===(t=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===t||t.clearValidators(),null===(n=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===n||n.setErrors(null),null===(r=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===r||r.setValue(null),null===(o=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===o||o.updateValueAndValidity()),this.onFormValueChanges(),"V"==this.data.mode&&(null===(s=this.scheduleInterviewForm.get("scheduledStartTime"))||void 0===s||s.disable(),null===(d=this.scheduleInterviewForm.get("scheduledEndTime"))||void 0===d||d.disable(),null===(l=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===l||l.disable(),null===(c=this.scheduleInterviewForm.get("scheduledDate"))||void 0===c||c.disable())}))}patchMailTemplateForm(){var e,t;return Object(i.c)(this,void 0,void 0,(function*(){for(let n=0;n<this.mailFormFields.length;n++)"from"==(null===(e=this.mailFormFields[n])||void 0===e?void 0:e.key)?this.mailFormGroup.addControl(this.mailFormFields[n].key,this._fb.control(this.interviewEmailDetails.from_email?this.interviewEmailDetails.from_email:null,[this.mailFormFields[n].isMandatory?a.H.required:null].filter(e=>null!=e))):"cc"==(null===(t=this.mailFormFields[n])||void 0===t?void 0:t.key)?this.mailFormGroup.addControl(this.mailFormFields[n].key,this._fb.control(this.interviewEmailDetails.cc&&"[]"!=this.interviewEmailDetails.cc?this.interviewEmailDetails.cc:null,[this.mailFormFields[n].isMandatory?a.H.required:null].filter(e=>null!=e))):this.mailFormGroup.addControl(this.mailFormFields[n].key,this._fb.control(this.interviewEmailDetails[this.mailFormFields[n].key],[this.mailFormFields[n].isMandatory?a.H.required:null].filter(e=>null!=e))),"signature"==this.mailFormFields[n].key&&this.mailFormGroup.addControl("isSignatureOn",this._fb.control(!1)),this.mailFormGroup.addControl("header",this._fb.control(null))}))}onCustomSelectValueChange(e){this.scheduleInterviewForm.get(e.data.field).setValue(e.val)}preventPaste(e){e.preventDefault()}preventInvalidValues(e){["0","1","2","3","4","5","6","7","8","9","Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab"].includes(e.key)||e.preventDefault()}endTimeValidator(e){const t=this.scheduleInterviewForm.get("scheduledStartTime").value,n=e.value;if(t&&n){const e=new Date("2000-01-01T"+t);if(new Date("2000-01-01T"+n)<=e)return{endTimeInvalid:!0}}return null}startTimeValidator(e){var t,n,i;let a=null===(t=this.scheduleInterviewForm.get("scheduledDate"))||void 0===t?void 0:t.value,r=e.value;if(!a||!r)return null;a=m()(a).format("YYYY-MM-DD");const[o,s]=r.split(":").map(Number),d=new Date(a);return d.setHours(o),d.setMinutes(s),d.setSeconds(0),d<new Date?(null===(n=this.scheduleInterviewForm.get("scheduledEndTime"))||void 0===n||n.updateValueAndValidity(),{startTimeInvalid:!0}):(null===(i=this.scheduleInterviewForm.get("scheduledEndTime"))||void 0===i||i.updateValueAndValidity(),null)}scheduleInterviewToCandidate(){var e,t,n,a,r,o,s,d;return Object(i.c)(this,void 0,void 0,(function*(){if((null===(e=this.data)||void 0===e?void 0:e.isCampusJob)&&(null===(t=this.data)||void 0===t?void 0:t.isCampusInterviewLogic)){if(this.mailFormGroup.getRawValue(),this.scheduleInterviewForm.getRawValue(),this.mailFormGroup.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Fill All Mandatory Fields!",7e3);this.isLoading=!0;let e=this.mailFormGroup.getRawValue();e.jobId=this.data.jobId,e.candidate_id=this.data.candidateIds,e.attachment=this.attachmentPath,e.logo=this.data.companyLogo,e.isBulkSelectActive=this.data.isBulkSelectActive,e.candidatesExcluded=this.data.candidatesExcluded,e.stageId=this.data.stageId,e.filter=this.data.filter,e.search_params=this.data.search_params;let t=this.scheduleInterviewForm.getRawValue();e.interviewData=t;let n=yield this.createCampusInterviewForcandidates(e);e.interview_id=n&&n.interview_id?n.interview_id:0,e.subject=n&&n.subject?n.subject:0;let i=yield this.sendEmailForCandidates(e);yield this.storeCandidateEmails(e),this.isLoading=!1,i&&this.onClose(!0)}else{let e=null===(n=this.scheduleInterviewForm.get("interviewTeam"))||void 0===n?void 0:n.value;if(2==e&&(!(null===(a=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===a?void 0:a.value)||(null===(r=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===r?void 0:r.value)&&(null===(o=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===o?void 0:o.value)<1))return this._toaster.showWarning("Warning \u26a0\ufe0f","No of interviewers must be greater than zero!",7e3);if(this.scheduleInterviewForm.invalid)return this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly fill all Mandatory Fields!",7e3);if(2==e&&(null===(s=this.scheduleInterviewForm.get("noOfInterviewers"))||void 0===s?void 0:s.value)>this.scheduleInterviewForm.get("teamMembers").value.length)return this._toaster.showWarning("Warning \u26a0\ufe0f","No of interviewers selected is lesser than the number of interviewers mentioned!",7e3);let t=this.scheduleInterviewForm.getRawValue(),i=this.templateMasterData.find(e=>e.id==this.scheduleInterviewForm.get("template").value);t.logo=(null===(d=this.data)||void 0===d?void 0:d.companyLogo)||"",t.header=(null==i?void 0:i.header)||"",t.subject=(null==i?void 0:i.subject)||"",t.body=(null==i?void 0:i.body)||"",t.signature=(null==i?void 0:i.signature)||"",t.candidateId=this.data.candidateIds,t.jobId=this.data.jobId,t.interviewId=this.data.interviewId,t.scheduledDate=m()(t.scheduledDate).format("YYYY-MM-DD"),this.isLoading=!0,"C"==this.data.mode?(yield this.scheduleInterview(t),this.isLoading=!1):"E"==this.data.mode&&(yield this.rescheduleInterview(t),this.isLoading=!1)}}))}getAtsFormsConfig(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{var i,a;if(0==n.err)if("sendBulkMailToCandidate"==e)this.mailFormFields=n.data.form[0].formFields,t(!0);else{if(this.scheduleInterviewFormFields=n.data.form[0].formFields,(null===(i=this.data)||void 0===i?void 0:i.isCampusJob)&&(null===(a=this.data)||void 0===a?void 0:a.isCampusInterviewLogic)){let e=["scheduledStartTime","scheduledTimeZone","scheduledEndTime","interviewTeam","noOfInterviewers","teamMembers","host","conferenceProvider"],t="template",n=this.scheduleInterviewFormFields.map(t=>e.includes(null==t?void 0:t.key)?Object.assign(Object.assign({},t),{isMandatory:!1}):t).filter(e=>(null==e?void 0:e.key)!==t);this.scheduleInterviewFormFields=n}t(!0)}else this._toaster.showError("Error",n.msg,7e3),t(!1)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}sendEmailForCandidates(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsCandidateService.sendEmailForCandidates(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success","Mail(s) sent successfully!",7e3),t(!0)):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),this.isLoading=!1,n()}}))}))}createCampusInterviewForcandidates(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsCandidateService.createCampusInterviewForcandidates(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?t({interview_id:e.interview_id,subject:e.subject}):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),this.isLoading=!1,n()}}))}))}storeCandidateEmails(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsCandidateService.storeCandidateEmails(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?t(!0):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),this.isLoading=!1,n()}}))}))}fetchAllEmailTemplates(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsTemplateSettingsService.fetchAllEmailTemplates(null,null,2).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.templateMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getAtsEmployee(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAtsEmployee().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.hostMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Employee Master Data Retrieval Failed!",7e3),t()}}))}))}getAssociateDetailsInSystem(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAssociateDetailsInSystem().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.teamMembersMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Employee Master Data Retrieval Failed!",7e3),t()}}))}))}getTimezone(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getTimezone().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.scheduledTimeZoneMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Timezone Master Data Retrieval Failed!",7e3),t()}}))}))}getScheduleType(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getScheduleType().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.schedulingMethodMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Scheduling Method Master Data Retrieval Failed!",7e3),t()}}))}))}getInterviewPlatform(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getInterviewPlatform().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.conferenceProviderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Interview Platform Master Data Retrieval Failed!",7e3),t()}}))}))}getJobHiringStageBasedOnCategory(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getJobHiringStageBasedOnCategory(this.data.jobId,3).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.hiringStageMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Hiring Stage Master Data Retrieval Failed!",7e3),t()}}))}))}getInterviewMode(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getInterviewMode().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.interviewModeMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Interview Mode Master Data Retrieval Failed!",7e3),t()}}))}))}scheduleInterview(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsCandidateService.scheduleInterview(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success \u2705","Interview Scheduled Successfully!",7e3),this.onClose(!0)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Interview Scheduling has Failed!",7e3),this.isLoading=!1,n()}}))}))}rescheduleInterview(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsCandidateService.rescheduleInterview(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success \u2705","Interview Re-Scheduled Successfully!",7e3),this.onClose(!0)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Interview Scheduling has Failed!",7e3),this.isLoading=!1,n()}}))}))}getScheduledInterviewDetails(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsCandidateService.getScheduledInterviewDetails(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.interviewDetails=e.data&&e.data.length>0?e.data[0]:{},this.data.jobId=e.data&&e.data.length>0?e.data[0].jobId:this.data.jobId):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Interview Scheduling has Failed!",7e3),this.isLoading=!1,n()}}))}))}getInterviewEmailData(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsCandidateService.getInterviewEmailData(this.data.candidateIds,this.data.jobId).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.interviewEmailDetails=t.data&&t.data.length>0?t.data[0]:{}:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Could not retrieve Interview Email Details!",7e3),this.isLoading=!1,t()}}))}))}getMeetingsofInterviewers(e){return Object(i.c)(this,void 0,void 0,(function*(){return this.appointmentsData=[],this.employeesData=[],new Promise((t,n)=>this._atsCandidateService.getMeetingsofInterviewers(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.appointmentsData=e.data.employee_interview_details,this.employeesData=e.data.employee_name_details):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Meeting Details Retrieval has Failed!",7e3),this.isLoading=!1,n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](r.a),p["\u0275\u0275directiveInject"](r.h),p["\u0275\u0275directiveInject"](u.a),p["\u0275\u0275directiveInject"](a.i),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](v.a),p["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-schedule-interview-dialog"]],hostBindings:function(e,t){1&e&&p["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,p["\u0275\u0275resolveWindow"])},features:[p["\u0275\u0275ProvidersFeature"]([{provide:l.c,useClass:d.c,deps:[l.f,d.a]},{provide:l.e,useValue:tt}])],decls:32,vars:9,consts:[[1,"main-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"d-flex","align-items-center"],["class","back-button",3,"matTooltip","ngStyle","click",4,"ngIf"],[1,"title","p-0"],["width","269","height","56","viewBox","0 0 269 56","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M133.603 -22.5168V4.76469C133.603 8.35397 132.087 11.811 129.39 14.4067C125.91 17.7302 120.857 19.1903 115.947 18.2782L115.946 18.2781L93.9778 14.2877L103.74 5.17581L116.468 7.51274L116.468 7.51278C119.364 8.04124 122.088 5.97651 122.088 3.19398V-11.8208L133.603 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.019 14.2879L157.079 18.2781L157.078 18.2782C152.174 19.1903 147.127 17.73 143.651 14.4063L143.649 14.4043C140.909 11.856 139.394 8.40031 139.394 4.81089V-22.5164L150.895 -11.8208V2.96299C150.895 5.88334 153.764 8.04193 156.758 7.46638C156.758 7.46632 156.758 7.46626 156.759 7.46621L169.221 5.17578L179.019 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.603 37.6429V64.9403L122.088 54.256V38.8889C122.088 36.2929 119.563 34.3707 116.817 34.8512L116.816 34.8515L103.74 37.2322L93.9781 28.1306L115.946 24.1447L115.947 24.1445C120.857 23.2335 125.91 24.6921 129.391 28.012C132.087 30.6048 133.603 34.0579 133.603 37.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.394 64.9399V37.6584C139.394 34.0692 140.91 30.6122 143.607 28.0166C147.087 24.693 152.141 23.2328 157.05 24.1448L157.051 24.145L179.018 28.1352L169.258 37.2012L155.933 34.7718C155.933 34.7718 155.933 34.7718 155.933 34.7718C153.335 34.2893 150.91 36.1234 150.91 38.6285V54.2438L139.394 64.9399Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.5926L63.5512 55.5828L63.5504 55.5829C58.6468 56.495 53.5996 55.0347 50.1236 51.711L50.1214 51.709C47.3813 49.1606 45.8669 45.705 45.8669 42.1156V14.7883L57.3676 25.4838V40.2677C57.3676 43.188 60.2364 45.3466 63.2304 44.7711C63.2307 44.771 63.231 44.771 63.2313 44.7709L75.6933 42.4805L85.4914 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.2879L63.5512 55.2781L63.5504 55.2782C58.6468 56.1903 53.5996 54.73 50.1236 51.4063L50.1214 51.4043C47.3813 48.856 45.8669 45.4003 45.8669 41.8109V14.4836L57.3676 25.1792V39.963C57.3676 42.8833 60.2364 45.0419 63.2304 44.4664C63.2307 44.4663 63.231 44.4663 63.2313 44.4662L75.6933 42.1758L85.4914 51.2879Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 14.7878V42.0694C227.13 45.6587 225.614 49.1157 222.918 51.7114C219.437 55.0348 214.384 56.495 209.475 55.5829L209.474 55.5828L187.505 51.5924L197.267 42.4805L209.995 44.8174L209.995 44.8175C212.892 45.3459 215.615 43.2812 215.615 40.4987V25.4839L227.13 14.7878Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.546 51.5926L250.606 55.5828L250.605 55.5829C245.702 56.495 240.654 55.0347 237.178 51.711L237.176 51.709C234.436 49.1606 232.922 45.705 232.922 42.1156V14.7883L244.422 25.4838V40.2677C244.422 43.188 247.291 45.3466 250.285 44.7711C250.285 44.771 250.286 44.771 250.286 44.7709L262.748 42.4805L272.546 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],[1,"icon",3,"click"],[1,"icon"],["class","d-flex align-items-center section",4,"ngIf"],["class","d-flex align-items-center justify-content-center loading-screen",4,"ngIf"],[4,"ngIf"],["class","main-screen",4,"ngIf"],[1,"d-flex","align-items-center","justify-content-end","footer"],["class","preview-btn",3,"ngStyle","click",4,"ngIf"],["class","button",3,"ngStyle","click",4,"ngIf"],["class","button",3,"click",4,"ngIf"],["mandatoryTemplate",""],[1,"back-button",3,"matTooltip","ngStyle","click"],[1,"back-icon"],[1,"d-flex","align-items-center","section"],[4,"ngFor","ngForOf"],["class","d-flex align-items-center space",3,"click",4,"ngIf"],["class","border",4,"ngIf"],[1,"d-flex","align-items-center","space",3,"click"],[3,"class",4,"ngIf"],["width","30","height","30","viewBox","0 0 24 24"],["cx","12","cy","12","r","12","fill","#79BA44"],["id","mask0_11331_113242","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_11331_113242)"],["d","M10.0008 13.5998L15.9008 7.6998C16.0841 7.51647 16.3174 7.4248 16.6008 7.4248C16.8841 7.4248 17.1174 7.51647 17.3008 7.6998C17.4841 7.88314 17.5758 8.11647 17.5758 8.3998C17.5758 8.68314 17.4841 8.91647 17.3008 9.0998L10.7008 15.6998C10.5008 15.8998 10.2674 15.9998 10.0008 15.9998C9.73411 15.9998 9.50078 15.8998 9.30078 15.6998L6.70078 13.0998C6.51745 12.9165 6.42578 12.6831 6.42578 12.3998C6.42578 12.1165 6.51745 11.8831 6.70078 11.6998C6.88411 11.5165 7.11745 11.4248 7.40078 11.4248C7.68411 11.4248 7.91745 11.5165 8.10078 11.6998L10.0008 13.5998Z","fill","white"],[1,"border"],[1,"d-flex","align-items-center","justify-content-center","loading-screen"],["diameter","40",1,"green-spinner"],["class","slots-screen",4,"ngIf"],[1,"main-screen"],[1,"d-flex","flex-column","forms",3,"formGroup"],["class","col-12 p-0",4,"ngIf"],[1,"d-flex","align-items-center","col-12","p-0"],["class","d-flex flex-column col-4 pl-0",4,"ngIf"],[1,"divider"],["class","d-flex align-items-center col-12 p-0",4,"ngIf"],[1,"col-12","p-0"],[1,"label-text"],[1,"description-text"],[1,"d-flex","flex-column","col-4","pl-0"],[1,"form-label"],[3,"masterData","placeholder","selectedValue","displayClose","data","disabled","onValueChange"],[3,"ngTemplateOutlet"],["class","info-content",4,"ngIf"],["formControlName","schedulingMethod",3,"disabled"],[3,"value","matTooltip",4,"ngFor","ngForOf"],[1,"info-content"],["width","16","height","16","viewBox","0 0 16 16","fill","none"],["clip-path","url(#clip0_12525_131831)"],["d","M7.9987 14.6663C4.3167 14.6663 1.33203 11.6817 1.33203 7.99967C1.33203 4.31767 4.3167 1.33301 7.9987 1.33301C11.6807 1.33301 14.6654 4.31767 14.6654 7.99967C14.6654 11.6817 11.6807 14.6663 7.9987 14.6663ZM7.9987 13.333C9.41319 13.333 10.7697 12.7711 11.7699 11.7709C12.7701 10.7707 13.332 9.41416 13.332 7.99967C13.332 6.58519 12.7701 5.22863 11.7699 4.22844C10.7697 3.22824 9.41319 2.66634 7.9987 2.66634C6.58421 2.66634 5.22766 3.22824 4.22746 4.22844C3.22727 5.22863 2.66536 6.58519 2.66536 7.99967C2.66536 9.41416 3.22727 10.7707 4.22746 11.7709C5.22766 12.7711 6.58421 13.333 7.9987 13.333ZM7.33203 4.66634H8.66536V5.99967H7.33203V4.66634ZM7.33203 7.33301H8.66536V11.333H7.33203V7.33301Z","fill","#1890FF"],["id","clip0_12525_131831"],["width","16","height","16","fill","white"],[3,"value","matTooltip"],["class","d-flex flex-column col-3 pl-0",4,"ngIf"],[1,"d-flex","flex-column","col-3","pl-0"],["appearance","outline",1,"form-field-class"],[1,"date-picker"],["matInput","","formControlName","scheduledDate","disabled","",3,"min","placeholder","matDatepicker"],[3,"for"],["matDatepickerToggleIcon","",1,"calendar-icon"],[3,"disabled"],["dp",""],["formControlName","scheduledStartTime",3,"disabled"],["formControlName","scheduledEndTime",3,"disabled"],[1,"description-text",2,"margin-bottom","12px"],["formControlName","interviewTeam",3,"disabled"],["class","d-flex flex-column col-6 p-0",4,"ngIf"],["class","d-flex flex-column col-10 pl-0",4,"ngIf"],[1,"d-flex","flex-column","col-6","p-0"],["type","number","min","1","matInput","","formControlName","noOfInterviewers",3,"placeholder","keydown","paste"],[1,"d-flex","flex-column","col-10","pl-0"],[3,"type","placeholder","masterData","selectedValues","displayClose","data","disabled","onValueChange"],[1,"slots-screen"],[1,"header-text"],[1,"d-flex","align-items-center","justify-content-between"],[1,"sub-text"],["currentView","timelineDay",1,"scheduler",3,"dataSource","views","startDayHour","endDayHour","cellDuration","groups","currentDate","onCellClick","onAppointmentClick"],["mode","virtual"],[3,"allowAdding","allowDeleting","allowUpdating","allowResizing","allowDragging"],["fieldExpr","aid","label","Interviewers",3,"allowMultiple","dataSource"],[1,"d-flex","flex-column","forms-email"],[3,"mailFormGroup","mailFormConfig","templateMasterData","fromMailPlaceHolderMasterData","mailPlaceHolderMasterData","placeholdersMasterData","attachmentCount","attachmentConfig","attachmentPath","filesUploaded","previewImg","companyLogo","attachmentChanges"],[3,"innerHTML",4,"ngIf"],["class","d-flex flex-column align-items-center justify-content-center","style","gap: 16px",4,"ngIf"],[3,"innerHTML"],[1,"d-flex","flex-column","align-items-center","justify-content-center",2,"gap","16px"],[3,"src"],[1,"preview-blank-text"],[1,"preview-btn",3,"ngStyle","click"],[1,"button",3,"ngStyle","click"],[1,"button",3,"click"],[2,"color","#cf0001"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275elementStart"](2,"div",2),p["\u0275\u0275template"](3,j,3,4,"div",3),p["\u0275\u0275elementStart"](4,"div",4),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",2),p["\u0275\u0275elementStart"](7,"div"),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](8,"svg",5),p["\u0275\u0275element"](9,"path",6),p["\u0275\u0275element"](10,"path",7),p["\u0275\u0275element"](11,"path",8),p["\u0275\u0275element"](12,"path",9),p["\u0275\u0275element"](13,"path",10),p["\u0275\u0275element"](14,"path",11),p["\u0275\u0275element"](15,"path",12),p["\u0275\u0275element"](16,"path",13),p["\u0275\u0275element"](17,"path",14),p["\u0275\u0275element"](18,"path",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](19,"div",16),p["\u0275\u0275listener"]("click",(function(){return t.onClose(!1)})),p["\u0275\u0275elementStart"](20,"mat-icon",17),p["\u0275\u0275text"](21,"close"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](22,Y,2,1,"div",18),p["\u0275\u0275template"](23,U,2,0,"div",19),p["\u0275\u0275template"](24,Ne,4,3,"ng-container",20),p["\u0275\u0275template"](25,$e,3,2,"div",21),p["\u0275\u0275elementStart"](26,"div",22),p["\u0275\u0275template"](27,Qe,2,3,"div",23),p["\u0275\u0275template"](28,Xe,2,4,"div",24),p["\u0275\u0275template"](29,Ke,2,0,"div",25),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](30,et,2,0,"ng-template",null,26,p["\u0275\u0275templateRefExtractor"])),2&e&&(p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",t.isPreviewScreen),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.isPreviewScreen?"Preview":"Schedule Interview"," "),p["\u0275\u0275advance"](17),p["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isPreviewScreen&&t.sections&&t.sections.length>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isPreviewScreen),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isPreviewScreen&&!t.isLoading),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",!(t.isPreviewScreen||null!=t.data&&t.data.isCampusJob||null!=t.data&&t.data.isCampusInterviewLogic)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isPreviewScreen&&"V"!=t.data.mode),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isPreviewScreen&&"V"==t.data.mode&&0==t.currentSection))},directives:[f.NgIf,C.a,w.a,f.NgStyle,f.NgForOf,I.c,a.w,a.n,F.a,x.a,f.NgTemplateOutlet,b.b,a.v,a.l,b.a,M.c,y.b,a.e,_.g,_.i,_.j,_.f,S.a,a.A,D.a,E.a,O.Jd,O.Qb,O.C,P.a],pipes:[T.a,k.a,V.a],styles:[".main-container[_ngcontent-%COMP%]{overflow:hidden}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#f4f4f6;position:absolute;z-index:1}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{width:16px;height:16px;margin-right:10px;border:1px solid #526179;border-radius:4px;cursor:pointer}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{font-size:14px;color:#526179}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{width:78%;height:50px;padding:0 24px;background-color:#f7f9fb;position:absolute;z-index:1;top:56px;gap:16px}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]{cursor:pointer;gap:8px}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border:1px solid #515965;border-radius:50%;background-color:#eee;color:#515965}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle-selected[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:700;font-size:12px;width:30px;height:30px;display:flex;align-items:center;justify-content:center}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle-selected[_ngcontent-%COMP%]{border:2px solid var(--atsprimaryColor);border-radius:50%;background-color:var(--atssecondaryColor2);color:var(--atsprimaryColor)}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#515965}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .section-text-selected[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:var(--atsprimaryColor)}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]{width:40px;height:1px;border:1px dashed #515965!important}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]{margin-top:20%}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .forms-email[_ngcontent-%COMP%]{gap:16px;margin:12% 2%}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]{padding:20px 24px;margin-top:106px;margin-bottom:56px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]{width:100%;gap:16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#b9c0ca}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#8b95a5}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#5f6c81}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:12px 8px;margin:10px 0;background-color:#e8f4ff;border-radius:4px;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-input-element[type=time]{padding-top:8px;height:16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-radio-button.mat-accent .mat-radio-inner-circle{background-color:var(--atssecondaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:var(--atssecondaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-radio-outer-circle{height:16px!important;width:16px!important;margin-top:2px;border-color:#dadce2}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-radio-inner-circle{height:16px!important;width:16px!important;margin-top:2px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-radio-label-content{padding-left:0!important;padding-right:16px!important;font-family:var(--atsfontFamily)!important;color:#5f6c81!important;font-size:14px!important;font-weight:400!important;max-width:500px!important;overflow:hidden!important;text-overflow:ellipsis!important;white-space:nowrap!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .date-picker[_ngcontent-%COMP%]{display:flex;align-items:center;height:17px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .calendar-icon[_ngcontent-%COMP%]{font-size:16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .preview-blank-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:400;color:#6e7b8f}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{width:90%;font-size:12px;font-family:var(--atsfontFamily)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{width:90%!important;font-size:12px!important;font-family:var(--atsfontFamily)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.main-container[_ngcontent-%COMP%]   .slots-screen[_ngcontent-%COMP%]{padding:20px 24px;margin-top:106px;margin-bottom:56px}.main-container[_ngcontent-%COMP%]   .slots-screen[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#5f6c81}.main-container[_ngcontent-%COMP%]   .slots-screen[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#b9c0ca;margin:16px 0}.main-container[_ngcontent-%COMP%]   .slots-screen[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#5f6c81;margin-bottom:16px}.main-container[_ngcontent-%COMP%]   .slots-screen[_ngcontent-%COMP%]   .scheduler[_ngcontent-%COMP%]{height:var(--dynamicCalendarHeight)}.main-container[_ngcontent-%COMP%]   .slots-screen[_ngcontent-%COMP%]     .dx-scheduler-navigator, .main-container[_ngcontent-%COMP%]   .slots-screen[_ngcontent-%COMP%]     .dx-scheduler-view-switcher.dx-dropdownmenu.dx-button-has-icon:not(.dx-button-has-text){visibility:hidden!important}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#fff;position:absolute;z-index:1;bottom:0;gap:10px}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:7px 13px;color:#45546e}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   .preview-btn[_ngcontent-%COMP%]{cursor:pointer;font-family:var(--atsfontFamily);font-size:14px;font-weight:700}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%]{background-color:var(--atsprimaryColor);border-radius:8px;padding:8px 14px;color:#fff}"]}),e})()},IEgo:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("wd/R"),a=n("fXoL");let r=(()=>{class e{transform(e,t){return e?i(e).utc().format(t):"-"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"dateFormat",type:e,pure:!0}),e})()}}]);