(window.webpackJsonp=window.webpackJsonp||[]).push([[751],{Imz7:function(t,e,a){"use strict";a.r(e),a.d(e,"GovRepDmPopupComponent",(function(){return Ce}));var n=a("mrSG"),o=a("xG9w"),l=a("1G5W"),r=a("0IaG"),m=a("wd/R"),i=a("XNiG"),p=a("ofXK"),s=a("bTqV"),d=(a("3Pt+"),a("NFeN")),c=(a("kmnG"),a("qFsG"),a("Qu3c")),u=a("lVl8"),D=a("Xa2L"),v=a("dlKe"),x=a("fXoL"),P=a("DIFc"),g=a("2Clw"),f=a("LcQX"),b=a("vxfF");function h(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1,"Total Milestone - "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](2,"div",16),x["\u0275\u0275text"](3," Planned Count \xa0\xa0: \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementStart"](4,"span",17),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"div",16),x["\u0275\u0275text"](7," Actual Count \xa0\xa0\xa0\xa0\xa0: \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementStart"](8,"span",17),x["\u0275\u0275text"](9),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",16),x["\u0275\u0275text"](11," Overdue Count \xa0: \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementStart"](12,"span",17),x["\u0275\u0275text"](13),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](5),x["\u0275\u0275textInterpolate"](t.modalParams.pmoDb.totPlMlCount),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate"](t.modalParams.pmoDb.totAcMlCount),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate"](t.modalParams.pmoDb.totOvMlCount)}}function y(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1,"Total Milestone - "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](2,"div",16),x["\u0275\u0275text"](3," Planned Value \xa0\xa0: \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementStart"](4,"span",17),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"div",16),x["\u0275\u0275text"](7," Actual Value \xa0\xa0\xa0\xa0\xa0: \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementStart"](8,"span",17),x["\u0275\u0275text"](9),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",16),x["\u0275\u0275text"](11," Overdue Value \xa0: \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementStart"](12,"span",17),x["\u0275\u0275text"](13),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](5),x["\u0275\u0275textInterpolate"](t.modalParams.pmoDb.totPlMlValueTooltip),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate"](t.modalParams.pmoDb.totAcMlValueTooltip),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate"](t.modalParams.pmoDb.totOvMlValueTooltip)}}function C(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",21),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).changePmoDbDataMonthInterval(a.monthInterval)})),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,a=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate1"]("col-1 p-1 ml-0 mr-1 mt-1 mb-1 slide-in-left ",a.modalParams.pmoDb.pmoDbDataMonthInterval==t.monthInterval?"btn-active":"btn-not-active",""),x["\u0275\u0275property"]("disabled",a.isModalLoading),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t.monthIntervalText)}}function I(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",18),x["\u0275\u0275elementStart"](1,"div",19),x["\u0275\u0275template"](2,C,2,5,"button",20),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngForOf",t.modalParams.pmoDb.monthIntervals)}}function _(t,e){1&t&&x["\u0275\u0275element"](0,"div",18)}function S(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",21),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit,n=e.index;return x["\u0275\u0275nextContext"](2).changePmoDbDataWeekInterval(a,n)})),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,a=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate1"]("col-1 p-1 ml-0 mr-1 mt-1 mb-1 slide-in-left ",a.findActiveWeeks(t)?"btn-active":"btn-not-active",""),x["\u0275\u0275property"]("disabled",a.isModalLoading),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t.weekInterval)}}function E(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",22),x["\u0275\u0275element"](1,"div",23),x["\u0275\u0275elementStart"](2,"div",24),x["\u0275\u0275elementStart"](3,"div",19),x["\u0275\u0275template"](4,S,2,5,"button",20),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](5,"div",23),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](4),x["\u0275\u0275property"]("ngForOf",t.modalParams.pmoDb.currentWeekIntervals)}}function M(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",38),x["\u0275\u0275text"](1,"maximize"),x["\u0275\u0275elementEnd"]())}function O(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",39),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]())}function w(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",40),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]())}function K(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",44),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](3);x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra)}}function k(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div"),x["\u0275\u0275elementStart"](1,"div",41),x["\u0275\u0275elementStart"](2,"span",42),x["\u0275\u0275text"](3),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](4,K,2,1,"span",43),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate1"]("",t.modalParams.pmoDb.cssClasses.projectsColSpilloverClass," p-0 br-grey"),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate2"]("Sp. - ",t.modalParams.pmoDb.pmoDataKeys[0].visibleTextSpillover," ",t.modalParams.pmoDb.pmoDataKeys[0].visibleText.includes("Value")?t.modalParams.udrfData.appliedConfig.customFields.isFullValue?"":"INR"==t.modalParams.udrfData.appliedConfig.customFields.defaultCurrency?"(Cr)":"(Mn)":"",""),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra)}}function T(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",46),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t.visibleTextExtra)}}function V(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div"),x["\u0275\u0275elementStart"](1,"div",41),x["\u0275\u0275elementStart"](2,"span"),x["\u0275\u0275text"](3),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](4,T,2,1,"span",45),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,a=e.index,n=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate2"]("",n.modalParams.pmoDb.cssClasses.projectsColClass," ",a!=n.modalParams.pmoDb.pmoDataKeys.length-1?"br-grey":"",""),x["\u0275\u0275advance"](2),x["\u0275\u0275classMapInterpolate1"]("",null!=t.visibleTextExtra?"col-8-5":"col-12"," p-0"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate2"]("",t.visibleText," ",t.visibleText.includes("Value")?n.modalParams.udrfData.appliedConfig.customFields.isFullValue?"":"INR"==n.modalParams.udrfData.appliedConfig.customFields.defaultCurrency?"(Cr)":"(Mn)":"",""),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra)}}function W(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](3);x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyText," Ascending Order")}}function A(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](3);x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyText," Descending Order")}}function F(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](3);x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyText," Default Order")}}function $(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"/"),x["\u0275\u0275elementEnd"]())}function B(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](4);x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtraText," Ascending Order")}}function j(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](4);x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtraText," Descending Order")}}function R(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](4);x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtraText," Default Order")}}function Y(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"](3);return e.headerSortBy(e.modalParams.pmoDb.resolvedPmoDbData,e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"+(e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra.includes("_value")?"Original":""))})),x["\u0275\u0275template"](1,B,2,1,"mat-icon",52),x["\u0275\u0275template"](2,j,2,1,"mat-icon",52),x["\u0275\u0275template"](3,R,2,1,"mat-icon",52),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"](3);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"+(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"+(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"+(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra.includes("_value")?"Original":"")))}}function z(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div"),x["\u0275\u0275elementStart"](1,"div",47),x["\u0275\u0275elementStart"](2,"div",48),x["\u0275\u0275elementStart"](3,"span",49),x["\u0275\u0275text"](4,"Overdue"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",50),x["\u0275\u0275elementStart"](6,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"](2);return e.headerSortBy(e.modalParams.pmoDb.resolvedPmoDbData,e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey+"Spillover"+(e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey.includes("_value")?"Original":""))})),x["\u0275\u0275template"](7,W,2,1,"mat-icon",52),x["\u0275\u0275template"](8,A,2,1,"mat-icon",52),x["\u0275\u0275template"](9,F,2,1,"mat-icon",52),x["\u0275\u0275template"](10,$,2,0,"span",53),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](11,Y,4,3,"button",54),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate1"]("",t.modalParams.pmoDb.cssClasses.projectsColSpilloverClass," p-0 br-grey"),x["\u0275\u0275advance"](7),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey+"Spillover"+(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey+"Spillover"+(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey+"Spillover"+(t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra&&null!=t.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra)}}function N(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.plannedDataKeyText," Ascending Order")}}function H(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.plannedDataKeyText," Descending Order")}}function G(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.plannedDataKeyText," Default Order")}}function L(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"/"),x["\u0275\u0275elementEnd"]())}function Q(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.plannedDataKeyExtraText," Ascending Order")}}function X(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.plannedDataKeyExtraText," Descending Order")}}function J(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.plannedDataKeyExtraText," Default Order")}}function U(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"/"),x["\u0275\u0275elementEnd"]())}function Z(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]().$implicit,a=x["\u0275\u0275nextContext"](2);return a.headerSortBy(a.modalParams.pmoDb.resolvedPmoDbData,e.plannedDataKeyExtra+"Pu"+(e.plannedDataKeyExtra.includes("_value")?"Original":""))})),x["\u0275\u0275template"](1,Q,2,1,"mat-icon",52),x["\u0275\u0275template"](2,X,2,1,"mat-icon",52),x["\u0275\u0275template"](3,J,2,1,"mat-icon",52),x["\u0275\u0275template"](4,U,2,0,"span",53),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","N"==e.getSortOrder(t.plannedDataKeyExtra+"Pu"+(t.plannedDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==e.getSortOrder(t.plannedDataKeyExtra+"Pu"+(t.plannedDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==e.getSortOrder(t.plannedDataKeyExtra+"Pu"+(t.plannedDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",e.modalParams.udrfData.appliedConfig.customFields.isProjectsOverdueVisible&&null!=t.visibleTextExtra)}}function q(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.overdueDataKeyText," Ascending Order")}}function tt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.overdueDataKeyText," Descending Order")}}function et(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.overdueDataKeyText," Default Order")}}function at(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"/"),x["\u0275\u0275elementEnd"]())}function nt(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]().$implicit,a=x["\u0275\u0275nextContext"](2);return a.headerSortBy(a.modalParams.pmoDb.resolvedPmoDbData,e.overdueDataKey+"Pu"+(e.overdueDataKey.includes("_value")?"Original":""))})),x["\u0275\u0275template"](1,q,2,1,"mat-icon",52),x["\u0275\u0275template"](2,tt,2,1,"mat-icon",52),x["\u0275\u0275template"](3,et,2,1,"mat-icon",52),x["\u0275\u0275template"](4,at,2,0,"span",53),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","N"==e.getSortOrder(t.overdueDataKey+"Pu"+(t.overdueDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==e.getSortOrder(t.overdueDataKey+"Pu"+(t.overdueDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==e.getSortOrder(t.overdueDataKey+"Pu"+(t.overdueDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra)}}function ot(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.overdueDataKeyExtraText," Ascending Order")}}function lt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.overdueDataKeyExtraText," Descending Order")}}function rt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.overdueDataKeyExtraText," Default Order")}}function mt(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]().$implicit,a=x["\u0275\u0275nextContext"](2);return a.headerSortBy(a.modalParams.pmoDb.resolvedPmoDbData,e.overdueDataKeyExtra+"Pu"+(e.overdueDataKeyExtra.includes("_value")?"Original":""))})),x["\u0275\u0275template"](1,ot,2,1,"mat-icon",52),x["\u0275\u0275template"](2,lt,2,1,"mat-icon",52),x["\u0275\u0275template"](3,rt,2,1,"mat-icon",52),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","N"==e.getSortOrder(t.overdueDataKeyExtra+"Pu"+(t.overdueDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==e.getSortOrder(t.overdueDataKeyExtra+"Pu"+(t.overdueDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==e.getSortOrder(t.overdueDataKeyExtra+"Pu"+(t.overdueDataKeyExtra.includes("_value")?"Original":"")))}}function it(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.actualDataKeyText," Ascending Order")}}function pt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.actualDataKeyText," Descending Order")}}function st(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.actualDataKeyText," Default Order")}}function dt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"/"),x["\u0275\u0275elementEnd"]())}function ct(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," maximize"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.actualDataExtraKeyText," Ascending Order")}}function ut(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.actualDataExtraKeyText," Descending Order")}}function Dt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",55),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275propertyInterpolate1"]("matTooltip","Set ",t.actualDataExtraKeyText," Default Order")}}function vt(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]().$implicit,a=x["\u0275\u0275nextContext"](2);return a.headerSortBy(a.modalParams.pmoDb.resolvedPmoDbData,e.actualDataKeyExtra+"Pu"+(e.actualDataKeyExtra.includes("_value")?"Original":""))})),x["\u0275\u0275template"](1,ct,2,1,"mat-icon",52),x["\u0275\u0275template"](2,ut,2,1,"mat-icon",52),x["\u0275\u0275template"](3,Dt,2,1,"mat-icon",52),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","N"==e.getSortOrder(t.actualDataKeyExtra+"Pu"+(t.actualDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==e.getSortOrder(t.actualDataKeyExtra+"Pu"+(t.actualDataKeyExtra.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==e.getSortOrder(t.actualDataKeyExtra+"Pu"+(t.actualDataKeyExtra.includes("_value")?"Original":"")))}}function xt(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div"),x["\u0275\u0275elementStart"](1,"div",56),x["\u0275\u0275elementStart"](2,"div"),x["\u0275\u0275elementStart"](3,"span",49),x["\u0275\u0275text"](4,"Planned"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",50),x["\u0275\u0275elementStart"](6,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit,n=x["\u0275\u0275nextContext"](2);return n.headerSortBy(n.modalParams.pmoDb.resolvedPmoDbData,a.plannedDataKey+"Pu"+(a.plannedDataKey.includes("_value")?"Original":""))})),x["\u0275\u0275template"](7,N,2,1,"mat-icon",52),x["\u0275\u0275template"](8,H,2,1,"mat-icon",52),x["\u0275\u0275template"](9,G,2,1,"mat-icon",52),x["\u0275\u0275template"](10,L,2,0,"span",53),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](11,Z,5,4,"button",54),x["\u0275\u0275template"](12,nt,5,4,"button",54),x["\u0275\u0275template"](13,mt,4,3,"button",54),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](14,"div"),x["\u0275\u0275elementStart"](15,"span",49),x["\u0275\u0275text"](16,"Actual"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](17,"div",50),x["\u0275\u0275elementStart"](18,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit,n=x["\u0275\u0275nextContext"](2);return n.headerSortBy(n.modalParams.pmoDb.resolvedPmoDbData,a.actualDataKey+"Pu"+(a.actualDataKey.includes("_value")?"Original":""))})),x["\u0275\u0275template"](19,it,2,1,"mat-icon",52),x["\u0275\u0275template"](20,pt,2,1,"mat-icon",52),x["\u0275\u0275template"](21,st,2,1,"mat-icon",52),x["\u0275\u0275template"](22,dt,2,0,"span",53),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](23,vt,4,3,"button",54),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,a=e.index,n=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate2"]("",n.modalParams.pmoDb.cssClasses.projectsColClass," ",a!=n.modalParams.pmoDb.pmoDataKeys.length-1?"br-grey":"",""),x["\u0275\u0275advance"](2),x["\u0275\u0275classMapInterpolate1"]("sort-header d-flex ",n.modalParams.pmoDb.cssClasses.projectsColItemClass+"-header"," p-0 valueGrey12 center-align"),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf","N"==n.getSortOrder(t.plannedDataKey+"Pu"+(t.plannedDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==n.getSortOrder(t.plannedDataKey+"Pu"+(t.plannedDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==n.getSortOrder(t.plannedDataKey+"Pu"+(t.plannedDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra||t.overdueDataKey&&!t.overdueDataKeyExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra&&null!=t.plannedDataKeyExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",n.modalParams.udrfData.appliedConfig.customFields.isProjectsOverdueVisible),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",n.modalParams.udrfData.appliedConfig.customFields.isProjectsOverdueVisible&&null!=t.visibleTextExtra&&null!=t.overdueDataKeyExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275classMapInterpolate1"]("sort-header d-flex ",n.modalParams.pmoDb.cssClasses.projectsColItemClass+"-other"," p-0 valueGrey12 center-align"),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf","N"==n.getSortOrder(t.actualDataKey+"Pu"+(t.actualDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==n.getSortOrder(t.actualDataKey+"Pu"+(t.actualDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==n.getSortOrder(t.actualDataKey+"Pu"+(t.actualDataKey.includes("_value")?"Original":""))),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra&&null!=t.actualDataKeyExtra)}}function Pt(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",69),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]().$implicit;return e.isExpanded=!e.isExpanded})),x["\u0275\u0275elementStart"](1,"mat-icon",70),x["\u0275\u0275text"](2,"keyboard_arrow_right"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}}function gt(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"button",69),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]().$implicit;return e.isExpanded=!e.isExpanded})),x["\u0275\u0275elementStart"](1,"mat-icon",71),x["\u0275\u0275text"](2,"keyboard_arrow_down"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}}function ft(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",79),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](3).$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate1"]("",e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra&&e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra.includes("ml_")&&e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra&&t[e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"]>0?"bubble-text-red":"bubble-text"," col-3-5-1-spillover p-0 ta-r"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t[e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"])}}function bt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",76),x["\u0275\u0275elementStart"](1,"span",77),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](3,ft,2,4,"span",78),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("ngClass",e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra&&e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra.includes("ml_")&&e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra&&t[e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"]>0?"value13RedBold":""),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t[e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey+"Spillover"]),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=e.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra&&t[e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"]>0)}}function ht(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function yt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Ct(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function It(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function _t(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1),x["\u0275\u0275template"](2,Ct,2,0,"span",53),x["\u0275\u0275template"](3,It,2,0,"span",53),x["\u0275\u0275elementStart"](4,"span",17),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](3).$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" ",e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtraText," \xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=e.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==e.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t[e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"+(e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra.includes("_value")?"Tooltip":"")])}}function St(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](2,"div",16),x["\u0275\u0275text"](3),x["\u0275\u0275template"](4,ht,2,0,"span",53),x["\u0275\u0275template"](5,yt,2,0,"span",53),x["\u0275\u0275elementStart"](6,"span",17),x["\u0275\u0275text"](7),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](8,_t,6,4,"div",80)),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit,e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" Spillover ",e.modalParams.pmoDb.pmoDataKeys[0].description," - "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyText," \xa0\xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=e.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==e.modalParams.pmoDb.pmoDataKeys[0].visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t[e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey+"Spillover"+(e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKey.includes("_value")?"Tooltip":"")]),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=e.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra)}}function Et(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",72),x["\u0275\u0275elementStart"](1,"div",73),x["\u0275\u0275template"](2,bt,4,3,"div",74),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](3,St,9,6,"ng-template",null,75,x["\u0275\u0275templateRefExtractor"]),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275reference"](4),e=x["\u0275\u0275nextContext"]().$implicit,a=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate1"]("",a.modalParams.pmoDb.cssClasses.projectsColSpilloverClass," d-flex m-0 p-0 br-grey"),x["\u0275\u0275property"]("tooltip",t),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",e[a.modalParams.pmoDb.pmoDataKeys[0].overdueDataKeyExtra+"Spillover"]>0)}}function Mt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",87),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](e[t.plannedDataKeyExtra+"Pu"])}}function Ot(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",79),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275classMapInterpolate1"]("",t.overdueDataKeyExtra&&t.overdueDataKeyExtra.includes("ml_")&&t.overdueDataKeyExtra&&e[t.overdueDataKeyExtra+"Pu"]>0?"bubble-text-red":"bubble-text"," col-3-5-1 p-0 ta-r"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](e[t.overdueDataKeyExtra+"Pu"])}}function wt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",76),x["\u0275\u0275elementStart"](1,"span",82),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](3,Ot,2,4,"span",78),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275property"]("ngClass",t.overdueDataKeyExtra&&t.overdueDataKeyExtra.includes("ml_")&&t.overdueDataKeyExtra&&e[t.overdueDataKeyExtra+"Pu"]>0?"value13RedBold":""),x["\u0275\u0275advance"](1),x["\u0275\u0275classMapInterpolate1"]("",null!=t.visibleTextExtra?"col-8-5":"col-12"," p-0 ta-r"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](e[t.overdueDataKey+"Pu"]),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra&&e[t.overdueDataKeyExtra+"Pu"]>0)}}function Kt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",88),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275classMapInterpolate1"]("",t.actualDataKeyExtra&&t.actualDataKeyExtra.includes("ml_")&&t.actualDataKeyExtra&&e[t.actualDataKeyExtra+"Pu"]>0?"bubble-text-green":"bubble-text"," col-3-5-1 p-0 ta-r"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](e[t.actualDataKeyExtra+"Pu"])}}function kt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Tt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Vt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Wt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function At(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1),x["\u0275\u0275template"](2,Vt,2,0,"span",53),x["\u0275\u0275template"](3,Wt,2,0,"span",53),x["\u0275\u0275elementStart"](4,"span",17),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" ",t.plannedDataKeyExtraText," \xa0\xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==t.visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](e[t.plannedDataKeyExtra+"Pu"+(t.plannedDataKeyExtra.includes("_value")?"Tooltip":"")])}}function Ft(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function $t(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Bt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function jt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Rt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1),x["\u0275\u0275template"](2,Bt,2,0,"span",53),x["\u0275\u0275template"](3,jt,2,0,"span",53),x["\u0275\u0275elementStart"](4,"span",17),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" ",t.actualDataKeyExtraText," \xa0\xa0\xa0\xa0\xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==t.visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](e[t.actualDataKeyExtra+"Pu"+(t.actualDataKeyExtra.includes("_value")?"Tooltip":"")])}}function Yt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function zt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Nt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1),x["\u0275\u0275template"](2,Yt,2,0,"span",53),x["\u0275\u0275template"](3,zt,2,0,"span",53),x["\u0275\u0275elementStart"](4,"span",17),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" ",t.overdueDataKeyText," \xa0\xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==t.visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](e[t.overdueDataKey+"Pu"+(t.overdueDataKey.includes("_value")?"Tooltip":"")])}}function Ht(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Gt(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0"),x["\u0275\u0275elementEnd"]())}function Lt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1),x["\u0275\u0275template"](2,Ht,2,0,"span",53),x["\u0275\u0275template"](3,Gt,2,0,"span",53),x["\u0275\u0275elementStart"](4,"span",17),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" ",t.overdueDataKeyExtraText," \xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==t.visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](e[t.overdueDataKeyExtra+"Pu"+(t.overdueDataKeyExtra.includes("_value")?"Tooltip":"")])}}function Qt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",16),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](2,"div",16),x["\u0275\u0275text"](3),x["\u0275\u0275template"](4,kt,2,0,"span",53),x["\u0275\u0275template"](5,Tt,2,0,"span",53),x["\u0275\u0275elementStart"](6,"span",17),x["\u0275\u0275text"](7),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](8,At,6,4,"div",80),x["\u0275\u0275elementStart"](9,"div",16),x["\u0275\u0275text"](10),x["\u0275\u0275template"](11,Ft,2,0,"span",53),x["\u0275\u0275template"](12,$t,2,0,"span",53),x["\u0275\u0275elementStart"](13,"span",17),x["\u0275\u0275text"](14),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](15,Rt,6,4,"div",80),x["\u0275\u0275template"](16,Nt,6,4,"div",80),x["\u0275\u0275template"](17,Lt,6,4,"div",80)),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit,a=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" ",t.description," - "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",t.plannedDataKeyText," \xa0\xa0\xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==t.visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](e[t.plannedDataKey+"Pu"+(t.plannedDataKey.includes("_value")?"Tooltip":"")]),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.plannedDataKeyExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",t.actualDataKeyText," \xa0\xa0\xa0\xa0\xa0\xa0: "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null==t.visibleTextExtra),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](e[t.actualDataKey+"Pu"+(t.actualDataKey.includes("_value")?"Tooltip":"")]),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.actualDataKeyExtra),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",a.modalParams.udrfData.appliedConfig.customFields.isProjectsOverdueVisible),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",a.modalParams.udrfData.appliedConfig.customFields.isProjectsOverdueVisible&&null!=t.overdueDataKeyExtra)}}function Xt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",72),x["\u0275\u0275elementStart"](1,"div"),x["\u0275\u0275elementStart"](2,"div",81),x["\u0275\u0275elementStart"](3,"span",82),x["\u0275\u0275text"](4),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](5,Mt,2,1,"span",83),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](6,wt,4,6,"div",74),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",84),x["\u0275\u0275elementStart"](8,"span",82),x["\u0275\u0275text"](9),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](10,Kt,2,4,"span",85),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](11,Qt,18,13,"ng-template",null,86,x["\u0275\u0275templateRefExtractor"]),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,a=e.index,n=x["\u0275\u0275reference"](12),o=x["\u0275\u0275nextContext"]().$implicit,l=x["\u0275\u0275nextContext"](2);x["\u0275\u0275classMapInterpolate2"]("",l.modalParams.pmoDb.cssClasses.projectsColClass," d-flex m-0 p-0 ",a!=l.modalParams.pmoDb.pmoDataKeys.length-1?"br-grey":"",""),x["\u0275\u0275property"]("tooltip",n),x["\u0275\u0275advance"](1),x["\u0275\u0275classMapInterpolate1"]("col-12 ",l.modalParams.pmoDb.cssClasses.projectsColItemClass," p-0"),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngClass",t.overdueDataKeyExtra&&t.overdueDataKeyExtra.includes("ml_")&&t.overdueDataKeyExtra&&o[t.overdueDataKeyExtra+"Pu"]>0?"value13RedBold":""),x["\u0275\u0275advance"](1),x["\u0275\u0275classMapInterpolate1"]("",null!=t.visibleTextExtra?"col-8-5":"col-12"," p-0 ta-r"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](o[t.plannedDataKey+"Pu"]),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra&&o[t.plannedDataKeyExtra+"Pu"]>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",l.modalParams.udrfData.appliedConfig.customFields.isProjectsOverdueVisible&&o[t.overdueDataKeyExtra+"Pu"]>0),x["\u0275\u0275advance"](1),x["\u0275\u0275classMapInterpolate1"]("",l.modalParams.pmoDb.cssClasses.projectsColItemClass," p-0 mt-1 cp col-12 d-flex row m-0 p-0 value13"),x["\u0275\u0275property"]("ngClass",t.actualDataKeyExtra&&t.actualDataKeyExtra.includes("ml_")&&t.actualDataKeyExtra&&o[t.actualDataKeyExtra+"Pu"]>0?"value13GreenBold":""),x["\u0275\u0275advance"](1),x["\u0275\u0275classMapInterpolate1"]("",null!=t.visibleTextExtra?"col-8-5":"col-12"," p-0 ta-r"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](o[t.actualDataKey+"Pu"]),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=t.visibleTextExtra&&o[t.actualDataKeyExtra+"Pu"]>0)}}const Jt=function(t){return{width:t}};function Ut(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",57),x["\u0275\u0275elementStart"](1,"div",58),x["\u0275\u0275elementStart"](2,"div",59),x["\u0275\u0275elementStart"](3,"div",60),x["\u0275\u0275elementStart"](4,"div",61),x["\u0275\u0275template"](5,Pt,3,0,"button",62),x["\u0275\u0275template"](6,gt,3,0,"button",62),x["\u0275\u0275elementStart"](7,"span"),x["\u0275\u0275text"](8),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](9,Et,5,5,"div",63),x["\u0275\u0275template"](10,Xt,13,24,"div",64),x["\u0275\u0275elementStart"](11,"div",65),x["\u0275\u0275elementStart"](12,"button",66),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openCreateQuickCTAModal(a)})),x["\u0275\u0275elementStart"](13,"mat-icon",67),x["\u0275\u0275text"](14,"assignment_late"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](15,"button",68),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openCommentsModal(a)})),x["\u0275\u0275elementStart"](16,"mat-icon",67),x["\u0275\u0275text"](17,"forum"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,a=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](11,Jt,a.mulFactorForCardSizeCalc()+"px")),x["\u0275\u0275advance"](4),x["\u0275\u0275classMapInterpolate1"]("col-3-px row m-0 value13Bold p-1 ",t["color_class_"+a.selectedMonthData.monthInterval],""),x["\u0275\u0275property"]("matTooltip",t.description)("ngClass",2==t.plLevel?"pl-3":1==t.plLevel?"pl-6":0==t.plLevel?"pl-8":""),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.subItems&&t.subItems.length>0&&!t.isExpanded),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.subItems&&t.subItems.length>0&&t.isExpanded),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t.description),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",0==a.modalParams.pmoDb.pmoDbDataMonthIntervalCount),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",a.modalParams.pmoDb.pmoDataKeys)}}function Zt(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",25),x["\u0275\u0275elementStart"](1,"div",26),x["\u0275\u0275elementStart"](2,"div",27),x["\u0275\u0275elementStart"](3,"div",28),x["\u0275\u0275elementStart"](4,"span",29),x["\u0275\u0275text"](5,"Region / Vertical / Project"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"button",30),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]();return e.headerSortBy(e.modalParams.pmoDb.resolvedPmoDbData,"description")})),x["\u0275\u0275template"](7,M,2,0,"mat-icon",31),x["\u0275\u0275template"](8,O,2,0,"mat-icon",32),x["\u0275\u0275template"](9,w,2,0,"mat-icon",33),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](10,k,5,6,"div",34),x["\u0275\u0275template"](11,V,5,10,"div",35),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](12,"div",27),x["\u0275\u0275element"](13,"p",36),x["\u0275\u0275template"](14,z,12,8,"div",34),x["\u0275\u0275template"](15,xt,24,22,"div",35),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](16,Ut,18,13,"div",37),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](7),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder("description")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder("description")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder("description")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",0==t.modalParams.pmoDb.pmoDbDataMonthIntervalCount),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",t.modalParams.pmoDb.pmoDataKeys),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("ngIf",0==t.modalParams.pmoDb.pmoDbDataMonthIntervalCount),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",t.modalParams.pmoDb.pmoDataKeys),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",t.governanceReportService.getResolvedDataItems(t.modalParams.pmoDb.resolvedPmoDbData))}}function qt(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",89),x["\u0275\u0275elementStart"](1,"h3",90),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](3,"div",91),x["\u0275\u0275element"](4,"img",92),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" Oops ! No ",t.modalParams.popupTypeTitle," Data Found ! ")}}function te(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",38),x["\u0275\u0275text"](1,"maximize"),x["\u0275\u0275elementEnd"]())}function ee(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",39),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]())}function ae(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",40),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]())}function ne(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",38),x["\u0275\u0275text"](1,"maximize"),x["\u0275\u0275elementEnd"]())}function oe(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",39),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]())}function le(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",40),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]())}function re(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",38),x["\u0275\u0275text"](1,"maximize"),x["\u0275\u0275elementEnd"]())}function me(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",39),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]())}function ie(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",40),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]())}function pe(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",38),x["\u0275\u0275text"](1,"maximize"),x["\u0275\u0275elementEnd"]())}function se(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",39),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]())}function de(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",40),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]())}function ce(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",38),x["\u0275\u0275text"](1,"maximize"),x["\u0275\u0275elementEnd"]())}function ue(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",39),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]())}function De(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",40),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]())}function ve(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",38),x["\u0275\u0275text"](1,"maximize"),x["\u0275\u0275elementEnd"]())}function xe(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",39),x["\u0275\u0275text"](1," arrow_upward"),x["\u0275\u0275elementEnd"]())}function Pe(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"mat-icon",40),x["\u0275\u0275text"](1," arrow_downward"),x["\u0275\u0275elementEnd"]())}function ge(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",57),x["\u0275\u0275elementStart"](1,"div",58),x["\u0275\u0275elementStart"](2,"div",102),x["\u0275\u0275elementStart"](3,"div",60),x["\u0275\u0275elementStart"](4,"div",103),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openProject(a)})),x["\u0275\u0275elementStart"](5,"span"),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",104),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openProject(a)})),x["\u0275\u0275elementStart"](8,"span"),x["\u0275\u0275text"](9),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",105),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openProject(a)})),x["\u0275\u0275elementStart"](11,"span"),x["\u0275\u0275text"](12),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](13,"div",106),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openProject(a)})),x["\u0275\u0275elementStart"](14,"span"),x["\u0275\u0275text"](15),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](16,"div",107),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openProject(a)})),x["\u0275\u0275elementStart"](17,"span"),x["\u0275\u0275text"](18),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](19,"div",108),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openProject(a)})),x["\u0275\u0275elementStart"](20,"span"),x["\u0275\u0275text"](21),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](22,"div",65),x["\u0275\u0275elementStart"](23,"button",66),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openCreateQuickCTAModal(a)})),x["\u0275\u0275elementStart"](24,"mat-icon",67),x["\u0275\u0275text"](25,"assignment_late"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](26,"button",68),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const a=e.$implicit;return x["\u0275\u0275nextContext"](2).openCommentsModal(a)})),x["\u0275\u0275elementStart"](27,"mat-icon",67),x["\u0275\u0275text"](28,"forum"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,a=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](13,Jt,a.mulFactorForCardSizeCalc()+"px")),x["\u0275\u0275advance"](4),x["\u0275\u0275property"]("matTooltip",t.project_name),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t.project_name),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("matTooltip",t.l2_activity_name),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t.l2_activity_name),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("matTooltip",t.value_tooltip),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t.value),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("matTooltip",t.l2_activity_status),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t.l2_activity_status),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("matTooltip",t.pl_name),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t.pl_name),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("matTooltip",t.planned_end_date),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](t.planned_end_date)}}function fe(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",109),x["\u0275\u0275elementStart"](1,"div",110),x["\u0275\u0275element"](2,"mat-spinner",111),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](2),x["\u0275\u0275propertyInterpolate1"]("matTooltip","Loading More ",t.modalParams.popupTypeTitle," Data ...")}}function be(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",93),x["\u0275\u0275elementStart"](1,"div",94),x["\u0275\u0275elementStart"](2,"div",95),x["\u0275\u0275elementStart"](3,"span",29),x["\u0275\u0275text"](4,"Project Name"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"button",30),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]();return e.headerSortBy(e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData,"project_name")})),x["\u0275\u0275template"](6,te,2,0,"mat-icon",31),x["\u0275\u0275template"](7,ee,2,0,"mat-icon",32),x["\u0275\u0275template"](8,ae,2,0,"mat-icon",33),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"div",95),x["\u0275\u0275elementStart"](10,"span",29),x["\u0275\u0275text"](11,"Milestone Name"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](12,"button",30),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]();return e.headerSortBy(e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData,"l2_activity_name")})),x["\u0275\u0275template"](13,ne,2,0,"mat-icon",31),x["\u0275\u0275template"](14,oe,2,0,"mat-icon",32),x["\u0275\u0275template"](15,le,2,0,"mat-icon",33),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](16,"div",96),x["\u0275\u0275elementStart"](17,"span",29),x["\u0275\u0275text"](18,"Value"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](19,"button",30),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]();return e.headerSortBy(e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData,"value_original")})),x["\u0275\u0275template"](20,re,2,0,"mat-icon",31),x["\u0275\u0275template"](21,me,2,0,"mat-icon",32),x["\u0275\u0275template"](22,ie,2,0,"mat-icon",33),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](23,"div",97),x["\u0275\u0275elementStart"](24,"span",29),x["\u0275\u0275text"](25,"Milestone Status"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](26,"button",30),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]();return e.headerSortBy(e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData,"l2_activity_status")})),x["\u0275\u0275template"](27,pe,2,0,"mat-icon",31),x["\u0275\u0275template"](28,se,2,0,"mat-icon",32),x["\u0275\u0275template"](29,de,2,0,"mat-icon",33),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](30,"div",98),x["\u0275\u0275elementStart"](31,"span",29),x["\u0275\u0275text"](32,"BU"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](33,"button",30),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]();return e.headerSortBy(e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData,"pl_name")})),x["\u0275\u0275template"](34,ce,2,0,"mat-icon",31),x["\u0275\u0275template"](35,ue,2,0,"mat-icon",32),x["\u0275\u0275template"](36,De,2,0,"mat-icon",33),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](37,"div",99),x["\u0275\u0275elementStart"](38,"span",29),x["\u0275\u0275text"](39,"Closure Date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](40,"button",30),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const e=x["\u0275\u0275nextContext"]();return e.headerSortBy(e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData,"planned_end_date")})),x["\u0275\u0275template"](41,ve,2,0,"mat-icon",31),x["\u0275\u0275template"](42,xe,2,0,"mat-icon",32),x["\u0275\u0275template"](43,Pe,2,0,"mat-icon",33),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](44,"div",100),x["\u0275\u0275listener"]("scrolled",(function(){return x["\u0275\u0275restoreView"](t),x["\u0275\u0275nextContext"]().retrieveProjectMilestonesData(!0)})),x["\u0275\u0275template"](45,ge,29,15,"div",37),x["\u0275\u0275template"](46,fe,3,1,"div",101),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](6),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder("project_name")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder("project_name")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder("project_name")),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder("l2_activity_name")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder("l2_activity_name")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder("l2_activity_name")),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder("value_original")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder("value_original")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder("value_original")),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder("l2_activity_status")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder("l2_activity_status")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder("l2_activity_status")),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder("pl_name")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder("pl_name")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder("pl_name")),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf","N"==t.getSortOrder("planned_end_date")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","A"==t.getSortOrder("planned_end_date")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","D"==t.getSortOrder("planned_end_date")),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("infiniteScrollDistance",4)("scrollWindow",!1),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",t.governanceReportService.getResolvedDataItems(t.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData)),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.modalParams.udrfData.isItemDataLoading)}}function he(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",89),x["\u0275\u0275elementStart"](1,"h3",90),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](3,"div",91),x["\u0275\u0275element"](4,"img",92),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" Oops ! No ",t.modalParams.popupTypeTitle," Data Found ! ")}}function ye(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",112),x["\u0275\u0275element"](1,"mat-spinner",113),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275propertyInterpolate1"]("matTooltip","Loading ",t.modalParams.popupTypeTitle," Data ...")}}let Ce=(()=>{class t{constructor(t,e,a,n,o){this.governanceReportService=t,this.dialogRef=e,this.inData=a,this.detailedViewService=n,this.utilityService=o,this.isModalLoading=!1,this._onDestroy=new i.b,this.spacingKeys={},this.selectedWeekData={startWeek:"",endWeek:""}}ngOnInit(){this.spacingKeys={},this.modalParams=this.inData.modalParams,this.modalParams.doesStartWithLoadingScreen?"Projects"==this.modalParams.popupType&&(this.calculatePmoHeaderDates(),this.retrieveProjectMilestonesData(!1)):(this.calculatePmoDbHeaderData(),this.calculatePmoItemValues())}retrieveProjectMilestonesData(t){if((!t||t&&!this.modalParams.udrfData.isItemDataLoading)&&(t?(this.modalParams.udrfData.isItemDataLoading=!0,this.modalParams.udrfData.start_index+=this.modalParams.udrfData.appliedConfig.customFields.projectMilestonesLimit):(this.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData=[],this.modalParams.udrfData.start_index=0,this.modalParams.udrfData.isFetchingOver=!1,this.isModalLoading=!0),!this.modalParams.udrfData.isFetchingOver)){if(null!=this.modalParams.pmoDbMilestoneData&&"Spillover"!=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval){let t=[];for(let n of this.modalParams.pmoDb.pmoDbDataWeekIntervals)t.push(parseInt(n.weekInterval.replace("W","")));let e=o.default.min(t),a=o.default.max(t);for(let n of this.modalParams.pmoDb.monthIntervals)if(n.monthInterval==this.modalParams.pmoDb.pmoDbDataMonthInterval)for(let t of n.weekIntervals)t.weekInterval=="W"+e?this.modalParams.udrfData.mainApiDateRangeStart=m(t.startDate,"DD-MM-YYYY").format("YYYY-MM-DD"):t.weekInterval=="W"+a&&(this.modalParams.udrfData.mainApiDateRangeEnd=m(t.endDate,"DD-MM-YYYY").format("YYYY-MM-DD"))}else{let t=this.modalParams.pmoDb.monthIntervals[0].weekIntervals[0];this.modalParams.udrfData.mainApiDateRangeStart=m(t.startDate,"DD-MM-YYYY").format("YYYY-MM-DD"),this.modalParams.udrfData.mainApiDateRangeEnd=m(t.endDate,"DD-MM-YYYY").format("YYYY-MM-DD")}null==this.modalParams.udrfData.mainFilterArray&&(this.modalParams.udrfData.mainFilterArray=[]),this.modalParams.udrfData.mainFilterArray=o.default.filter(this.modalParams.udrfData.mainFilterArray,(function(t){if(2!=t.filterId||2==t.filterId&&"SBU"==t.filterName)return t}),this);let t=o.default.where(this.modalParams.udrfData.mainFilterArray,{filterId:2});null!=t&&t.length>0?t[0].multiOptionSelectSearchValues=this.modalParams.multiOptionSelectSearchValuesForPmoDbPlFilter:this.modalParams.udrfData.mainFilterArray.push({filterColumnAlias:"TPI",filterColumnName:"p_and_l_description",filterId:2,multiOptionSelectSearchValues:this.modalParams.multiOptionSelectSearchValuesForPmoDbPlFilter,isFilterRange:!1,isMainDateRangeFilter:!1,shouldMasterTableBeJoined:!1}),this.modalParams.udrfData.limit=this.modalParams.udrfData.appliedConfig.customFields.projectMilestonesLimit,this.detailedViewService.getGanttTypeDetailedView({description:this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem.description,gantt_type_id:this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.visiblePmoDataKey.ganttTypeId,project_ids:this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem.project_ids,status:this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.clickedPmoDbItemDataType,type:"Spillover"!=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval?"business-units":"NGROverdue"},this.modalParams.udrfData,"").pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&"S"==t.messType&&t.data&&t.data.length>0){for(let e of t.data)e.value_original=parseFloat(null!=e.value?e.value:0),e.value=this.getValueWithComma(e.value_original),e.value_tooltip=this.getValueTooltipWithComma(e.value_original),e.planned_end_date=m(e.planned_end_date).format("DD-MMM-YYYY"),e.l2_activity_status=e.status;this.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData=this.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData.concat(t.data)}else this.modalParams.udrfData.isFetchingOver=!0;this.calculatePmoDbHeaderData(),this.modalParams.udrfData.isItemDataLoading=!1,this.isModalLoading=!1})),t=>{this.showErrorMessage(t)})}}showErrorMessage(t){this.utilityService.showErrorMessage(t,"KEBS")}openCreateQuickCTAModal(t){this.governanceReportService.openGovernanceReportCreateQuickCTAModal(t,this.modalParams.applicationId,this.modalParams.dialog)}openCommentsModal(t){this.governanceReportService.openGovernanceReportCommentsModal(t,this.modalParams.popupType,this.modalParams.applicationId,this.modalParams.dialog).then(e=>{e&&(t.hasEnteredComment=e)})}changePmoDbDataMonthInterval(t){this.spacingKeys={},this.modalParams.pmoDb.pmoDbDataMonthInterval=t,this.modalParams.pmoDb.pmoDbDataMonthIntervalCount=parseInt(t.replace("M",""));let e=o.default.where(this.modalParams.pmoDb.monthIntervals,{monthInterval:this.modalParams.pmoDb.pmoDbDataMonthInterval}),a=e&&e.length>0?e[0]:this.modalParams.pmoDb.monthIntervals[0];this.modalParams.pmoDb.pmoDbDataMonthCount=e&&e.length>0?e[0].monthCount:this.modalParams.pmoDb.monthIntervals[0].monthCount,this.modalParams.pmoDb.currentWeekIntervals=a.weekIntervals,this.modalParams.pmoDb.pmoDbDataWeekIntervals=[this.modalParams.pmoDb.currentWeekIntervals[0]],this.callChangeApis()}determinePmoWidgetItemColorClasses(){let t=this.selectedMonthData.monthInterval;for(let e of this.modalParams.pmoDb.resolvedPmoDbData){e["color_class_"+t]="";for(let a of e.subItems){a["color_class_"+t]="";for(let e of a.subItems){e["color_class_"+t]="";for(let a of e.subItems)a["color_class_"+t]=""}}}for(let e of this.modalParams.pmoDb.pmoDbDataWeekIntervals)for(let a of this.modalParams.pmoDb.resolvedPmoDbData){"value13RedBold"!=a["color_class_"+t]&&(a["color_class_"+t]=a["ml_overdue_count"+e.weekInterval+e.monthCount]>0?"value13RedBold":a["ml_actual_count"+e.weekInterval+e.monthCount]>0?"value13GreenBold":"value13GreenBold"!=a["color_class_"+t]?"":"value13GreenBold");for(let n of a.subItems){"value13RedBold"!=n["color_class_"+t]&&(n["color_class_"+t]=n["ml_overdue_count"+e.weekInterval+e.monthCount]>0?"value13RedBold":n["ml_actual_count"+e.weekInterval+e.monthCount]>0?"value13GreenBold":"value13GreenBold"!=n["color_class_"+t]?"":"value13GreenBold");for(let a of n.subItems){"value13RedBold"!=a["color_class_"+t]&&(a["color_class_"+t]=a["ml_overdue_count"+e.weekInterval+e.monthCount]>0?"value13RedBold":a["ml_actual_count"+e.weekInterval+e.monthCount]>0?"value13GreenBold":"value13GreenBold"!=a["color_class_"+t]?"":"value13GreenBold");for(let n of a.subItems)"value13RedBold"!=n["color_class_"+t]&&(n["color_class_"+t]=n["ml_overdue_count"+e.weekInterval+e.monthCount]>0?"value13RedBold":n["ml_actual_count"+e.weekInterval+e.monthCount]>0?"value13GreenBold":"value13GreenBold"!=n["color_class_"+t]?"":"value13GreenBold")}}}}calculatePmoDbHeaderData(){return Object(n.c)(this,void 0,void 0,(function*(){if(this.modalParams.isFromHeader&&this.calculatePmoItemValues(),this.resetPmoHeaderData(),this.modalParams.isFromHeader&&this.calculatePmoHeaderDates(),this.determinePmoWidgetItemColorClasses(),this.modalParams.isFromHeader)for(let t of this.modalParams.pmoDb.milestonePmoDbData)(0==this.modalParams.pmoDb.pmoDbDataMonthIntervalCount&&1==t.is_spill_over||0==t.is_spill_over&&t.week>=this.selectedWeekData.startWeek&&t.week<=this.selectedWeekData.endWeek&&t.month==this.modalParams.pmoDb.pmoDbDataMonthCount+1)&&(yield this.calculatePmoHeaderValuesFromHeader(t));else yield this.calculatePmoHeaderValuesFromNonHeader();this.modalParams.pmoDb.totAcMlValue=this.getValueWithComma(this.modalParams.pmoDb.totAcMlValueOriginal),this.modalParams.pmoDb.totPlMlValue=this.getValueWithComma(this.modalParams.pmoDb.totPlMlValueOriginal),this.modalParams.pmoDb.totOvMlValue=this.getValueWithComma(this.modalParams.pmoDb.totOvMlValueOriginal),this.modalParams.pmoDb.totAcMlValueTooltip=this.getValueTooltipWithComma(this.modalParams.pmoDb.totAcMlValueOriginal),this.modalParams.pmoDb.totPlMlValueTooltip=this.getValueTooltipWithComma(this.modalParams.pmoDb.totPlMlValueOriginal),this.modalParams.pmoDb.totOvMlValueTooltip=this.getValueTooltipWithComma(this.modalParams.pmoDb.totOvMlValueOriginal)}))}calculatePmoItemValues(){for(let t of this.modalParams.pmoDb.resolvedPmoDbData)for(let e of this.modalParams.pmoDb.pmoDataKeys){t=this.calculateValueAndTooltipData(t,e);for(let a of t.subItems){a=this.calculateValueAndTooltipData(a,e);for(let t of a.subItems){t=this.calculateValueAndTooltipData(t,e);for(let a of t.subItems)a=this.calculateValueAndTooltipData(a,e)}}}}calculateValueAndTooltipData(t,e){t[e.actualDataKey+"Pu"+(e.actualDataKey.includes("_value")?"Original":"")]=0,t[e.plannedDataKey+"Pu"+(e.plannedDataKey.includes("_value")?"Original":"")]=0,t[e.overdueDataKey+"Pu"+(e.overdueDataKey.includes("_value")?"Original":"")]=0,null!=e.actualDataKeyExtra&&(t[e.actualDataKeyExtra+"Pu"]=0,t[e.plannedDataKeyExtra+"Pu"]=0,t[e.overdueDataKeyExtra+"Pu"]=0);for(let a of this.modalParams.pmoDb.pmoDbDataWeekIntervals)t[e.actualDataKey+"Pu"+(e.actualDataKey.includes("_value")?"Original":"")]+=t[e.actualDataKey+a.weekInterval+a.monthCount+(e.actualDataKey.includes("_value")?"Original":"")],t[e.plannedDataKey+"Pu"+(e.plannedDataKey.includes("_value")?"Original":"")]+=t[e.plannedDataKey+a.weekInterval+a.monthCount+(e.plannedDataKey.includes("_value")?"Original":"")],t[e.overdueDataKey+"Pu"+(e.overdueDataKey.includes("_value")?"Original":"")]+=t[e.overdueDataKey+a.weekInterval+a.monthCount+(e.overdueDataKey.includes("_value")?"Original":"")],null!=e.actualDataKeyExtra&&(t[e.actualDataKeyExtra+"Pu"]+=t[e.actualDataKeyExtra+a.weekInterval+a.monthCount],t[e.plannedDataKeyExtra+"Pu"]+=t[e.plannedDataKeyExtra+a.weekInterval+a.monthCount],t[e.overdueDataKeyExtra+"Pu"]+=t[e.overdueDataKeyExtra+a.weekInterval+a.monthCount]);return e.actualDataKey.includes("_value")&&(t[e.actualDataKey+"Pu"]=this.getValueWithComma(t[e.actualDataKey+"PuOriginal"]),t[e.actualDataKey+"PuTooltip"]=this.getValueTooltipWithComma(t[e.actualDataKey+"PuOriginal"])),e.plannedDataKey.includes("_value")&&(t[e.plannedDataKey+"Pu"]=this.getValueWithComma(t[e.plannedDataKey+"PuOriginal"]),t[e.plannedDataKey+"PuTooltip"]=this.getValueTooltipWithComma(t[e.plannedDataKey+"PuOriginal"])),e.overdueDataKey.includes("_value")&&(t[e.overdueDataKey+"Pu"]=this.getValueWithComma(t[e.overdueDataKey+"PuOriginal"]),t[e.overdueDataKey+"PuTooltip"]=this.getValueTooltipWithComma(t[e.overdueDataKey+"PuOriginal"])),t}resetPmoHeaderData(){this.modalParams.pmoDb.totAcMlCount=0,this.modalParams.pmoDb.totPlMlCount=0,this.modalParams.pmoDb.totOvMlCount=0,this.modalParams.pmoDb.totAcAcCount=0,this.modalParams.pmoDb.totPlAcCount=0,this.modalParams.pmoDb.totOvAcCount=0,this.modalParams.pmoDb.totAcSoCount=0,this.modalParams.pmoDb.totPlSoCount=0,this.modalParams.pmoDb.totOvSoCount=0,this.modalParams.pmoDb.totAcPhCount=0,this.modalParams.pmoDb.totPlPhCount=0,this.modalParams.pmoDb.totOvPhCount=0,this.modalParams.pmoDb.totAcWpCount=0,this.modalParams.pmoDb.totPlWpCount=0,this.modalParams.pmoDb.totOvWpCount=0,this.modalParams.pmoDb.totAcMlValueOriginal=0,this.modalParams.pmoDb.totPlMlValueOriginal=0,this.modalParams.pmoDb.totOvMlValueOriginal=0,this.modalParams.pmoDb.totAcMlValue="0",this.modalParams.pmoDb.totPlMlValue="0",this.modalParams.pmoDb.totOvMlValue="0",this.modalParams.pmoDb.totAcMlValueTooltip="0",this.modalParams.pmoDb.totPlMlValueTooltip="0",this.modalParams.pmoDb.totOvMlValueTooltip="0"}getValueWithComma(t){return this.utilityService.getValueWithComma(this.modalParams.udrfData.appliedConfig.customFields.isFullValue,t,this.modalParams.udrfData.appliedConfig.customFields.defaultCurrency,"Ngr",this.modalParams.misFlags)}getValueTooltipWithComma(t){return this.utilityService.getValueTooltipWithComma(t,this.modalParams.udrfData.appliedConfig.customFields.defaultCurrency,"Ngr",this.modalParams.misFlags)}calculatePmoHeaderDates(){let t=[];for(let n of this.modalParams.pmoDb.pmoDbDataWeekIntervals)t.push(parseInt(n.weekInterval.replace("W","")));let e=o.default.min(t),a=o.default.max(t);if(this.modalParams.isFromHeader)this.selectedWeekData.startWeek=e,this.selectedWeekData.endWeek=a;else for(let n of this.modalParams.pmoDb.monthIntervals)if(n.monthInterval==this.modalParams.pmoDb.pmoDbDataMonthInterval)for(let t of n.weekIntervals)t.weekInterval=="W"+e&&(this.modalParams.udrfData.mainApiDateRangeStart=m(t.startDate,"DD-MM-YYYY").format("YYYY-MM-DD")),t.weekInterval=="W"+a&&(this.modalParams.udrfData.mainApiDateRangeEnd=m(t.endDate,"DD-MM-YYYY").format("YYYY-MM-DD"));this.calculatePmoMonthData()}calculatePmoMonthData(){let t=o.default.where(this.modalParams.pmoDb.monthIntervals,{monthInterval:this.modalParams.pmoDb.pmoDbDataMonthInterval});this.selectedMonthData=null!=t&&t.length>0?t[0]:""}calculatePmoHeaderValuesFromHeader(t){o.default.contains(this.modalParams.pmoDb.dmBaseLegalEntityRollDowns,t.vertical_id)&&("Milestone"==t.gantt_type_name?(this.modalParams.pmoDb.totAcMlCount+=t.actual_count,this.modalParams.pmoDb.totPlMlCount+=t.planned_count,this.modalParams.pmoDb.totOvMlCount+=t.overdue_count):"Activity"==t.gantt_type_name?(this.modalParams.pmoDb.totAcAcCount+=t.actual_count,this.modalParams.pmoDb.totPlAcCount+=t.planned_count,this.modalParams.pmoDb.totOvAcCount+=t.overdue_count):"Sign Off"==t.gantt_type_name?(this.modalParams.pmoDb.totAcSoCount+=t.actual_count,this.modalParams.pmoDb.totPlSoCount+=t.planned_count,this.modalParams.pmoDb.totOvSoCount+=t.overdue_count):"Phase"==t.gantt_type_name?(this.modalParams.pmoDb.totAcPhCount+=t.actual_count,this.modalParams.pmoDb.totPlPhCount+=t.planned_count,this.modalParams.pmoDb.totOvPhCount+=t.overdue_count):"Work Package"==t.gantt_type_name&&(this.modalParams.pmoDb.totAcWpCount+=t.actual_count,this.modalParams.pmoDb.totPlWpCount+=t.planned_count,this.modalParams.pmoDb.totOvWpCount+=t.overdue_count),this.modalParams.pmoDb.totAcMlValueOriginal+=t.actual_value,this.modalParams.pmoDb.totPlMlValueOriginal+=t.planned_value,this.modalParams.pmoDb.totOvMlValueOriginal+=t.overdue_value)}calculatePmoHeaderValuesFromNonHeader(){return Object(n.c)(this,void 0,void 0,(function*(){if(null==this.modalParams.pmoDbMilestoneData||"Spillover"==this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval)this.weekWiseTotCalc(this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval);else for(let t of this.modalParams.pmoDb.pmoDbDataWeekIntervals)yield this.weekWiseTotCalc(t)}))}weekWiseTotCalc(t){this.modalParams.pmoDb.totAcMlCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_actual_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totPlMlCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_planned_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totOvMlCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_overdue_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totAcAcCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ac_actual_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totPlAcCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ac_planned_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totOvAcCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ac_overdue_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totAcSoCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["so_actual_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totPlSoCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["so_planned_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totOvSoCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["so_overdue_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totAcPhCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ph_actual_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totPlPhCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ph_planned_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totOvPhCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ph_overdue_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totAcWpCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["wp_actual_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totPlWpCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["wp_planned_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totOvWpCount+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["wp_overdue_count"+t.weekInterval+t.monthCount],this.modalParams.pmoDb.totAcMlValueOriginal+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_actual_value"+t.weekInterval+t.monthCount+"Original"],this.modalParams.pmoDb.totPlMlValueOriginal+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_planned_value"+t.weekInterval+t.monthCount+"Original"],this.modalParams.pmoDb.totOvMlValueOriginal+=this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem["ml_overdue_value"+t.weekInterval+t.monthCount+"Original"]}changePmoDbDataWeekInterval(t,e){if(this.spacingKeys={},this.modalParams.isFromHeader||(this.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval=t),-1==this.selectWeek(t)){let t=null;for(let n=e-1;n>=0&&null==t;n--)this.findActiveWeeks(this.modalParams.pmoDb.currentWeekIntervals[n])&&(t=n);if(null!=t)for(let n=t;n<e;n++)this.findActiveWeeks(this.modalParams.pmoDb.currentWeekIntervals[n])||this.selectWeek(this.modalParams.pmoDb.currentWeekIntervals[n]);let a=null;for(let n=e+1;n<this.modalParams.pmoDb.currentWeekIntervals.length&&null==a;n++)this.findActiveWeeks(this.modalParams.pmoDb.currentWeekIntervals[n])&&(a=n);if(null!=a)for(let n=e+1;n<a;n++)this.findActiveWeeks(this.modalParams.pmoDb.currentWeekIntervals[n])||this.selectWeek(this.modalParams.pmoDb.currentWeekIntervals[n]);this.callChangeApis()}else{let a=null;for(let t=e-1;t>=0&&null==a;t--)this.findActiveWeeks(this.modalParams.pmoDb.currentWeekIntervals[t])&&(a=t);let n=null;for(let t=e+1;t<this.modalParams.pmoDb.currentWeekIntervals.length&&null==n;t++)this.findActiveWeeks(this.modalParams.pmoDb.currentWeekIntervals[t])&&(n=t);null!=a&&null!=n?this.changePmoDbDataWeekInterval(t,e):this.callChangeApis()}}callChangeApis(){this.modalParams.isFromHeader?this.calculatePmoDbHeaderData():"Projects"==this.modalParams.popupType&&(this.calculatePmoHeaderDates(),this.retrieveProjectMilestonesData(!1))}selectWeek(t){let e=o.default.pluck(this.modalParams.pmoDb.pmoDbDataWeekIntervals,"weekInterval"),a=o.default.indexOf(e,t.weekInterval);return-1==a?this.modalParams.pmoDb.pmoDbDataWeekIntervals.push(t):this.modalParams.pmoDb.pmoDbDataWeekIntervals.length>1?this.modalParams.pmoDb.pmoDbDataWeekIntervals.splice(a,1):this.modalParams.pmoDb.pmoDbDataWeekIntervals=[this.modalParams.pmoDb.currentWeekIntervals[0]],a}findActiveWeeks(t){let e=o.default.pluck(this.modalParams.pmoDb.pmoDbDataWeekIntervals,"weekInterval");return o.default.contains(e,t.weekInterval)}openProject(t){window.open("/main/project/"+t.project_id+"/"+this.utilityService.encodeURIComponent(t.project_name)+"/overview","_blank")}mulFactorForCardSizeCalc(){return this.modalParams.isFromHeader?200+(this.modalParams.udrfData.appliedConfig.customFields.isFullValue?220:160)*("Projects"==this.modalParams.popupType?this.modalParams.pmoDb.pmoDataKeys.length:1)+145+250:1470}noOfAdditionsForCardSizeCalc(){let t=0;return"Projects"==this.modalParams.popupType&&(t=this.modalParams.pmoDb.pmoDataKeys.length),t}headerSortBy(t,e){this.governanceReportService.headerSortBy(t,e,"Popup","CBOW",this.modalParams.popupType,this.modalParams.baseLegalEntityLevel)}getSortOrder(t){return this.governanceReportService.getSortOrder(t,"Popup","CBOW",this.modalParams.popupType,this.modalParams.baseLegalEntityLevel)}checkIfCommentsExist(t,e){return!!this.modalParams.misFlags.shouldShowIfNgrCommentsAreCounted&&this.governanceReportService.checkIfCommentsExist(t,e,this.modalParams.allComments,this.modalParams.projectAndOpportunityData).hasEnteredComment}closeModal(){this._onDestroy.next(),this._onDestroy.complete(),this.dialogRef.close({event:"Close"})}}return t.\u0275fac=function(e){return new(e||t)(x["\u0275\u0275directiveInject"](P.a),x["\u0275\u0275directiveInject"](r.h),x["\u0275\u0275directiveInject"](r.a),x["\u0275\u0275directiveInject"](g.a),x["\u0275\u0275directiveInject"](f.a))},t.\u0275cmp=x["\u0275\u0275defineComponent"]({type:t,selectors:[["gov-rep-dm-popup"]],decls:25,vars:21,consts:[[1,"container-fluid","dm-popup-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-7","pl-1","pt-1","pb-1","pr-1","d-flex"],[1,"my-auto","value14Red",3,"matTooltip"],["placement","bottom","content-type","template",1,"my-auto","pl-5","value14Bold",3,"tooltip"],["pmoDbMlCountTooltip",""],["pmoDbMlValueTooltip",""],["class","col-4 p-1 d-flex",4,"ngIf"],[1,"col-1","p-0","d-flex"],["mat-icon-button","","matTooltip","Close",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-icon"],["class","row",4,"ngIf"],["class","col-12 p-0 mt-1 mb-0","style","padding: 0px 0px !important; overflow-x: scroll; height: 82vh; min-width: 100%;",4,"ngIf"],["class","col-12 p-0 mt-3 mb-3 center-vertical-align-20","style","padding: 0px 0px !important; overflow-x: scroll; min-width: 100%;",4,"ngIf"],["class","col-12 p-0 mt-3 mb-0 row","style","padding: 0px 0px !important; overflow-x: scroll; height: 79vh; min-width: 100%;",4,"ngIf"],["class","div-center",4,"ngIf"],[1,"row","tooltip-text",2,"position","relative"],[2,"position","absolute","right","0"],[1,"col-4","p-1","d-flex"],[2,"margin","0 auto"],["mat-raised-button","","style","line-height: 18px !important;",3,"disabled","class","click",4,"ngFor","ngForOf"],["mat-raised-button","",2,"line-height","18px !important",3,"disabled","click"],[1,"row"],[1,"col-2"],[1,"col-8","p-1","d-flex"],[1,"col-12","p-0","mt-1","mb-0",2,"padding","0px 0px !important","overflow-x","scroll","height","82vh","min-width","100%"],[1,"higherZ"],[1,"row","bg-white","stickyScrollHeader",2,"float","left"],[1,"col-3-px-header","smallSubtleText","pl-1","pr-1","pb-1","pt-1","sort-header",2,"background-color","#F5F5F5","position","sticky","left","0","z-index","9999999999"],[1,"hide-on-hover"],["mat-icon-button","",1,"ml-1","sort-button",3,"click"],["class","sort-icons","matTooltip","Set Ascending Order",4,"ngIf"],["class","sort-icons","matTooltip","Set Descending Order",4,"ngIf"],["class","sort-icons","matTooltip","Set Default Order",4,"ngIf"],[3,"class",4,"ngIf"],[3,"class",4,"ngFor","ngForOf"],[1,"pl-1","pr-1","title","col-3-px-header","p-0","m-0","valueGrey12",2,"background-color","#F5F5F5","position","sticky","left","0","z-index","9999999999"],["class","card listcard greyCard",3,"ngStyle",4,"ngFor","ngForOf"],["matTooltip","Set Ascending Order",1,"sort-icons"],["matTooltip","Set Descending Order",1,"sort-icons"],["matTooltip","Set Default Order",1,"sort-icons"],[1,"col-12","ta-c","d-flex","row","m-0","p-0","sort-header","valueGrey12"],[1,"col-8-5-spillover","p-0"],["class","bubble-text col-3-5-1-spillover p-0",4,"ngIf"],[1,"bubble-text","col-3-5-1-spillover","p-0"],["class","bubble-text col-3-5-1-1 p-0",4,"ngIf"],[1,"bubble-text","col-3-5-1-1","p-0"],[1,"col-12","d-flex","ta-r","m-0","p-0"],[1,"sort-header","d-flex","col-12","valueGrey12","center-align"],[1,"hide-on-hover",2,"margin-left","auto","margin-right","auto"],[1,"sort-button-auto"],["mat-icon-button","",1,"mat-icon-btn","ml-1",3,"click"],["class","sort-icons",3,"matTooltip",4,"ngIf"],[4,"ngIf"],["mat-icon-button","","class","mat-icon-btn ml-1",3,"click",4,"ngIf"],[1,"sort-icons",3,"matTooltip"],[1,"d-flex","ta-r","m-0","p-0"],[1,"card","listcard","greyCard",3,"ngStyle"],[1,"card-body","row","itemRow"],[1,"card-body","pl-1","pr-1","pb-0","pt-0"],[1,"row","card-details","p-0","stickyScrollHeader",2,"float","left"],[2,"background-color","#F5F5F5","position","sticky","left","0","z-index","99999",3,"matTooltip","ngClass"],["mat-icon-button","","class","arrow-button-in-card mr-2",3,"click",4,"ngIf"],["content-type","template",3,"class","tooltip",4,"ngIf"],["content-type","template",3,"class","tooltip",4,"ngFor","ngForOf"],[1,"col-1","p-0","m-0","ta-c","row","divCenter"],["mat-icon-button","","matTooltip","Create Quick CTA",1,"icon-tray-button","mr-2",2,"display","block !important",3,"click"],[1,"smallCardIcon"],["mat-icon-button","","matTooltip","Add Comments",1,"icon-tray-button",2,"display","block !important",3,"click"],["mat-icon-button","",1,"arrow-button-in-card","mr-2",3,"click"],["matTooltip","Expand",1,"expand-icons"],["matTooltip","Collapse",1,"expand-icons"],["content-type","template",3,"tooltip"],[1,"col-12"],["class","mt-1 cp col-12 d-flex row m-0 p-0 value13",3,"ngClass",4,"ngIf"],["pmoDbItemSpilloverTooltip",""],[1,"mt-1","cp","col-12","d-flex","row","m-0","p-0","value13",3,"ngClass"],[1,"col-8-5-spillover","p-0","ta-r",2,"padding-right","9px !important"],["style","padding-right: 6px !important; height: fit-content;",3,"class",4,"ngIf"],[2,"padding-right","6px !important","height","fit-content"],["style","position: relative;","class","row tooltip-text",4,"ngIf"],[1,"mt-1","ta-r","cp","d-flex","row","m-0","p-0","value13",3,"ngClass"],[2,"padding-right","9px !important"],["style","padding-right: 6px !important; height: fit-content;","class","bubble-text col-3-5-1 p-0 ta-r",4,"ngIf"],[3,"ngClass"],["style","padding-right: 6px !important; margin-left: -4px; height: fit-content;",3,"class",4,"ngIf"],["pmoDbItemTooltip",""],[1,"bubble-text","col-3-5-1","p-0","ta-r",2,"padding-right","6px !important","height","fit-content"],[2,"padding-right","6px !important","margin-left","-4px","height","fit-content"],[1,"col-12","p-0","mt-3","mb-3","center-vertical-align-20",2,"padding","0px 0px !important","overflow-x","scroll","min-width","100%"],[1,"slide-in-top"],[1,"slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","220","width","250"],[1,"col-12","p-0","mt-3","mb-0","row",2,"padding","0px 0px !important","overflow-x","scroll","height","79vh","min-width","100%"],[1,"row","itemRow","higherZ","bg-white","stickyScrollHeader",2,"float","left"],[1,"col-5-px","smallSubtleText","p-1","sort-header"],[1,"col-1-px","smallSubtleText","p-1","sort-header","ta-r","pr-4"],[1,"col-1-px","smallSubtleText","p-1","sort-header","ta-c","pl-4"],[1,"col-4-px","smallSubtleText","p-1","sort-header","pl-4"],[1,"col-1-px","smallSubtleText","p-1","sort-header"],["infinite-scroll","","cdkScrollable","",2,"height","73vh","overflow-y","scroll",3,"infiniteScrollDistance","scrollWindow","scrolled"],["class","col-12 row container d-flex pt-1 mt-2 pb-1 flex-column",4,"ngIf"],[1,"card-body",2,"padding","2px !important"],[1,"col-5-px","value13","row","m-0","p-1","cp",3,"matTooltip","click"],[1,"col-5-px","row","m-0","value13Bold","p-1","cp",3,"matTooltip","click"],[1,"col-1-px","row","m-0","value13Bold","p-1","ta-r","pr-4","cp",3,"matTooltip","click"],[1,"col-1-px","row","m-0","value13","p-1","cp","ta-c",3,"matTooltip","click"],[1,"col-4-px","value13","row","m-0","p-1","pl-4","cp",3,"matTooltip","click"],[1,"col-1-px","row","m-0","value13Bold","p-1","cp",3,"matTooltip","click"],[1,"col-12","row","container","d-flex","pt-1","mt-2","pb-1","flex-column"],[1,"row","justify-content-center"],["diameter","25",3,"matTooltip"],[1,"div-center"],["diameter","34",1,"slide-from-down","spinner-align",3,"matTooltip"]],template:function(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275elementStart"](1,"div",1),x["\u0275\u0275elementStart"](2,"div",2),x["\u0275\u0275elementStart"](3,"span",3),x["\u0275\u0275text"](4),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"span",4),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](7,h,14,3,"ng-template",null,5,x["\u0275\u0275templateRefExtractor"]),x["\u0275\u0275elementStart"](9,"span",4),x["\u0275\u0275text"](10),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](11,y,14,3,"ng-template",null,6,x["\u0275\u0275templateRefExtractor"]),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](13,I,3,1,"div",7),x["\u0275\u0275template"](14,_,1,0,"div",7),x["\u0275\u0275elementStart"](15,"div",8),x["\u0275\u0275elementStart"](16,"button",9),x["\u0275\u0275listener"]("click",(function(){return e.closeModal()})),x["\u0275\u0275elementStart"](17,"mat-icon",10),x["\u0275\u0275text"](18,"close"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](19,E,6,1,"div",11),x["\u0275\u0275template"](20,Zt,17,8,"div",12),x["\u0275\u0275template"](21,qt,5,1,"div",13),x["\u0275\u0275template"](22,be,47,22,"div",14),x["\u0275\u0275template"](23,he,5,1,"div",13),x["\u0275\u0275template"](24,ye,2,1,"div",15),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275reference"](8),a=x["\u0275\u0275reference"](12);x["\u0275\u0275advance"](3),x["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.modalParams.popupTypeTitle," ",e.modalParams.isFromHeader?"":" - "+e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem.description+"'s "+("Spillover"==e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval?"Spillover ":"")+e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.clickedPmoDbItemDataTypeFormatted+" "+e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.visiblePmoDataKey.ganttTypeName,""),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate2"]("",e.modalParams.popupTypeTitle," ",e.modalParams.isFromHeader?"":" - "+e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.resolvedPmoDbDataItem.description+"'s "+("Spillover"==e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval?"Spillover ":"")+e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.clickedPmoDbItemDataTypeFormatted+" "+e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.visiblePmoDataKey.ganttTypeName,""),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("tooltip",t),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate3"]("Ml. Count: ",e.modalParams.pmoDb.totPlMlCount," / ",e.modalParams.pmoDb.totAcMlCount," / ",e.modalParams.pmoDb.totOvMlCount,""),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("tooltip",a),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate4"]("Ml. Value",e.modalParams.udrfData.appliedConfig.customFields.isFullValue?"":"INR"==e.modalParams.udrfData.appliedConfig.customFields.defaultCurrency?" (Cr)":" (Mn)",": ",e.modalParams.pmoDb.totPlMlValue," / ",e.modalParams.pmoDb.totAcMlValue," / ",e.modalParams.pmoDb.totOvMlValue,""),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("ngIf",e.modalParams.isFromHeader||null!=e.modalParams.pmoDbMilestoneData&&"Spillover"!=e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",null!=e.modalParams.pmoDbMilestoneData&&"Spillover"==e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("ngIf",e.modalParams.isFromHeader||null!=e.modalParams.pmoDbMilestoneData&&"Spillover"!=e.modalParams.pmoDbMilestoneData.clickedPmoDbItemData.pmoDbWeekInterval.weekInterval),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!e.isModalLoading&&e.modalParams.isFromHeader&&e.modalParams.pmoDb.resolvedPmoDbData.length>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!e.isModalLoading&&e.modalParams.isFromHeader&&0==e.modalParams.pmoDb.resolvedPmoDbData.length),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!e.isModalLoading&&!e.modalParams.isFromHeader&&e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData.length>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!e.isModalLoading&&!e.modalParams.isFromHeader&&0==e.modalParams.pmoDbMilestoneData.currentWeekPmoMilestoneDbData.length),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",e.isModalLoading)}},directives:[c.a,u.a,p.NgIf,s.a,d.a,p.NgForOf,p.NgStyle,p.NgClass,v.a,b.b,D.c],styles:[".dm-popup-styles[_ngcontent-%COMP%]   .col-2-px[_ngcontent-%COMP%]{flex:0 0 160px;max-width:200px}.dm-popup-styles[_ngcontent-%COMP%]   .col-2-px-item[_ngcontent-%COMP%]{flex:0 0 80px;max-width:80px}.dm-popup-styles[_ngcontent-%COMP%]   .col-2-px-item-header[_ngcontent-%COMP%]{flex:0 0 100px;max-width:100px}.dm-popup-styles[_ngcontent-%COMP%]   .col-2-px-item-other[_ngcontent-%COMP%]{flex:0 0 60px;max-width:60px}.dm-popup-styles[_ngcontent-%COMP%]   .col-5-px-header[_ngcontent-%COMP%]{flex:0 0 266px;max-width:266px}.dm-popup-styles[_ngcontent-%COMP%]   .col-5-px[_ngcontent-%COMP%]{flex:0 0 260px;max-width:260px}.dm-popup-styles[_ngcontent-%COMP%]   .col-5-px-item[_ngcontent-%COMP%]{flex:0 0 130px;max-width:130px}.dm-popup-styles[_ngcontent-%COMP%]   .col-5-px-item-header[_ngcontent-%COMP%]{flex:0 0 140px;max-width:140px}.dm-popup-styles[_ngcontent-%COMP%]   .col-5-px-item-other[_ngcontent-%COMP%]{flex:0 0 80px;max-width:80px}.dm-popup-styles[_ngcontent-%COMP%]   .col-8-5[_ngcontent-%COMP%]{flex:0 0 70.666667%;max-width:70.666667%}.dm-popup-styles[_ngcontent-%COMP%]   .valueGrey12[_ngcontent-%COMP%]{color:#4a4a4a!important;font-size:12px!important;font-weight:400!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.dm-popup-styles[_ngcontent-%COMP%]   .bubble-text[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.79);font-weight:400;font-size:11px!important;line-height:21px;padding:0 4px;border-radius:5px;height:23px}.dm-popup-styles[_ngcontent-%COMP%]   .bubble-text-red[_ngcontent-%COMP%]{background-color:#df514c}.dm-popup-styles[_ngcontent-%COMP%]   .bubble-text-green[_ngcontent-%COMP%], .dm-popup-styles[_ngcontent-%COMP%]   .bubble-text-red[_ngcontent-%COMP%]{color:#fff;font-weight:500!important;font-size:11px!important;line-height:21px;padding:0 4px;border-radius:5px;height:23px}.dm-popup-styles[_ngcontent-%COMP%]   .bubble-text-green[_ngcontent-%COMP%]{background-color:#4caf50}.dm-popup-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:26px!important;height:26px!important;line-height:25px!important;display:none}.dm-popup-styles[_ngcontent-%COMP%]   .center-vertical-align-20[_ngcontent-%COMP%]{text-align:center;position:relative;top:-20%;transform:translateY(20%)}.dm-popup-styles[_ngcontent-%COMP%]   .divCenter[_ngcontent-%COMP%]{width:100%;margin:0 auto;display:flex;justify-content:center}.dm-popup-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.dm-popup-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#000!important;font-size:13px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.dm-popup-styles[_ngcontent-%COMP%]   .mat-icon-btn[_ngcontent-%COMP%]{padding:0;min-width:0;width:25px;height:25px;flex-shrink:0;line-height:25px;border-radius:50%}.dm-popup-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.dm-popup-styles[_ngcontent-%COMP%]   .br-grey[_ngcontent-%COMP%]{border-right:1px solid hsla(0,0%,50.2%,.5019607843137255)}.dm-popup-styles[_ngcontent-%COMP%]   .col-3-5-1[_ngcontent-%COMP%]{flex:0 0 29.333333%;max-width:29.333333%}.dm-popup-styles[_ngcontent-%COMP%]   .col-3-5-1-1[_ngcontent-%COMP%]{flex:0 0 27.333333%;max-width:27.333333%}.dm-popup-styles[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important;padding-left:0!important;padding-right:0!important}.dm-popup-styles[_ngcontent-%COMP%]   .col-3-px[_ngcontent-%COMP%]{flex:0 0 200px;max-width:200px}.dm-popup-styles[_ngcontent-%COMP%]   .col-3-px-header[_ngcontent-%COMP%]{flex:0 0 206px;max-width:206px}.dm-popup-styles[_ngcontent-%COMP%]   .col-1-px[_ngcontent-%COMP%]{flex:0 0 150px;max-width:150px}.dm-popup-styles[_ngcontent-%COMP%]   .col-4-px[_ngcontent-%COMP%]{flex:0 0 250px;max-width:250px}.dm-popup-styles[_ngcontent-%COMP%]   .col-2-5[_ngcontent-%COMP%]{flex:0 0 150px;max-width:150px}.dm-popup-styles[_ngcontent-%COMP%]   .stickyScrollHeader[_ngcontent-%COMP%]{-ms-overflow-style:none;scrollbar-width:none;flex-wrap:nowrap;overflow:unset;min-width:-webkit-fill-available}.dm-popup-styles[_ngcontent-%COMP%]   .pl-6[_ngcontent-%COMP%]{padding-left:1.5rem!important}.dm-popup-styles[_ngcontent-%COMP%]   .pl-8[_ngcontent-%COMP%]{padding-left:3rem!important}.dm-popup-styles[_ngcontent-%COMP%]   .value13[_ngcontent-%COMP%]{color:#000!important;font-size:13px!important;font-weight:400!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.dm-popup-styles[_ngcontent-%COMP%]   .value13RedBold[_ngcontent-%COMP%]{color:#df514c!important}.dm-popup-styles[_ngcontent-%COMP%]   .value13GreenBold[_ngcontent-%COMP%], .dm-popup-styles[_ngcontent-%COMP%]   .value13RedBold[_ngcontent-%COMP%]{font-size:13px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;margin-bottom:0!important}.dm-popup-styles[_ngcontent-%COMP%]   .value13GreenBold[_ngcontent-%COMP%]{color:#4caf50!important}.dm-popup-styles[_ngcontent-%COMP%]   .sort-button[_ngcontent-%COMP%]{height:20px;width:20px;line-height:20px;display:none!important}.dm-popup-styles[_ngcontent-%COMP%]   .col-3-5-1-spillover[_ngcontent-%COMP%]{flex:0 0 33%;max-width:33%}.dm-popup-styles[_ngcontent-%COMP%]   .col-5-px-spillover[_ngcontent-%COMP%]{flex:0 0 150px;max-width:150px}.dm-popup-styles[_ngcontent-%COMP%]   .col-2-px-spillover[_ngcontent-%COMP%]{flex:0 0 145px;max-width:145px}.dm-popup-styles[_ngcontent-%COMP%]   .col-8-5-spillover[_ngcontent-%COMP%]{flex:0 0 65%;max-width:65%}.dm-popup-styles[_ngcontent-%COMP%]   .sort-button-auto[_ngcontent-%COMP%]{height:20px;width:auto;line-height:20px;display:none!important}.dm-popup-styles[_ngcontent-%COMP%]   .center-align[_ngcontent-%COMP%]{justify-content:center;align-items:center}.dm-popup-styles[_ngcontent-%COMP%]   .sort-icons[_ngcontent-%COMP%]{font-size:14px}.dm-popup-styles[_ngcontent-%COMP%]   .sort-header[_ngcontent-%COMP%]:hover   .sort-button[_ngcontent-%COMP%], .dm-popup-styles[_ngcontent-%COMP%]   .sort-header[_ngcontent-%COMP%]:hover   .sort-button-auto[_ngcontent-%COMP%]{display:inline-block!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.dm-popup-styles[_ngcontent-%COMP%]   .sort-header[_ngcontent-%COMP%]:hover   .hide-on-hover[_ngcontent-%COMP%]{display:none!important;animation:slide-bottom .3s cubic-bezier(.25,.46,.45,.94) both}.dm-popup-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.dm-popup-styles[_ngcontent-%COMP%]   .itemRow[_ngcontent-%COMP%]{padding:1px!important}.dm-popup-styles[_ngcontent-%COMP%]   .higherZ[_ngcontent-%COMP%]{z-index:9999999;position:sticky;top:0}.dm-popup-styles[_ngcontent-%COMP%]   .bg-white[_ngcontent-%COMP%]{background-color:#fff}.dm-popup-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000!important;font-weight:500!important}.dm-popup-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%], .dm-popup-styles[_ngcontent-%COMP%]   .value14Red[_ngcontent-%COMP%]{font-size:14px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.dm-popup-styles[_ngcontent-%COMP%]   .value14Red[_ngcontent-%COMP%]{color:#cf0001!important;font-weight:400!important}.dm-popup-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.dm-popup-styles[_ngcontent-%COMP%]   .smallCardIconActive[_ngcontent-%COMP%]{font-size:18px!important;color:#cf0001!important}.dm-popup-styles[_ngcontent-%COMP%]   .arrow-button-in-card[_ngcontent-%COMP%]{height:24px;width:24px;line-height:24px}.dm-popup-styles[_ngcontent-%COMP%]   .arrow-button-in-card[_ngcontent-%COMP%]   .expand-icons[_ngcontent-%COMP%]{font-size:18px}.dm-popup-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.dm-popup-styles[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.dm-popup-styles[_ngcontent-%COMP%]   .slide-in-left[_ngcontent-%COMP%]{animation:slide-in-left .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-left{0%{transform:translateX(-15px);opacity:0}to{transform:translateX(0);opacity:1}}.dm-popup-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#df514c;color:#fff}.dm-popup-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .dm-popup-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:11px!important;min-width:60px;line-height:31px;padding:0 4px;border-radius:30px;margin-right:.5rem!important;margin-bottom:3px;width:19%}.dm-popup-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.dm-popup-styles[_ngcontent-%COMP%]   .div-center[_ngcontent-%COMP%]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)}"]}),t})()}}]);