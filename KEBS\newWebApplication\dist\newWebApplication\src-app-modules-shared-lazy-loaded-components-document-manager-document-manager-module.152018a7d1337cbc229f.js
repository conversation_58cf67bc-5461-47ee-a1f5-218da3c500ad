(window.webpackJsonp=window.webpackJsonp||[]).push([[985],{"A+V/":function(t,e,o){"use strict";o.d(e,"a",(function(){return l}));var i=o("mrSG"),r=o("XNiG"),s=o("xG9w"),a=o("fXoL"),n=o("tk/3"),p=o("flaP");let l=(()=>{class t{constructor(t,e){this._http=t,this.roleService=e,this.tabLoadingSubject=new r.b,this.getTabLoadingObservable=this.tabLoadingSubject.asObservable(),this.isActivityApproval=t=>new Promise((e,o)=>{this._http.post("/api/opportunity/isActivityAppStarted",{oppId:t}).subscribe(t=>e(t),t=>o(t))}),this.colorConfigUrl="/api/opportunity/tabs",this.tabColor=null}setTabLoadingObservable(t){this.tabLoadingSubject.next(t)}getOpportunityTeamDetails(t){return new Promise((e,o)=>{this._http.post("/api/opportunity/getOpportunityTeamDetails",{opportunityId:t}).subscribe(t=>e(t),t=>o(t))})}updateInternalStakeholderAsPrimary(t,e,o,i,r){return new Promise((s,a)=>{this._http.post("/api/opportunity/updateInternalStakeholderAsPrimary",{stakeholderId:t,ownerType:e,opportunityId:o,practice_id:i,member:r}).subscribe(t=>s(t),t=>a(t))})}addTeamMember(t,e){return new Promise((o,i)=>{this._http.post("/api/opportunity/addOpportunityTeamMember",{opportunityId:t,member:e}).subscribe(t=>o(t),t=>i(t))})}removeMember(t){return new Promise((e,o)=>{this._http.post("/api/opportunity/removeMember",{stakeholderId:t}).subscribe(t=>e(t),t=>o(t))})}getPastStakeholders(t){return new Promise((e,o)=>{this._http.post("/api/opportunity/getPastMembers",{opportunityId:t}).subscribe(t=>e(t),t=>o(t))})}getOpportunityStakeholderMaster(t){return new Promise((e,o)=>{this._http.post("api/opportunity/getOpportunityStakeholderMaster",{stakeholderType:t}).subscribe(t=>e(t),t=>o(t))})}getNotifyFormFieldCollection(t){return this._http.post("/api/activity/getNotifyFormFieldCollection",{application_id:t})}updateNotificationStakeholeder(t){return new Promise((e,o)=>{this._http.post("api/opportunity/updateNotificationStakeholeder",{updateNotificationParams:t}).subscribe(t=>e(t),t=>o(t))})}checkEditAcessISH(){s.where(this.roleService.roles,{application_id:36,role_id:1});let t=s.where(this.roleService.roles,{application_id:36,object_id:6,operation:"Read"});return console.log("accessList ISH",t),!(t.length>0)}getTheme(){return Object(i.c)(this,void 0,void 0,(function*(){if(this.tabColor&&null!=this.loaderConfig)return Promise.resolve({tabColor:this.tabColor,loaderConfig:this.loaderConfig});try{const t=yield this._http.get(this.colorConfigUrl).toPromise();return this.tabColor=t.tabColor||"#defaultColor",this.loaderConfig=null==t?void 0:t.loaderConfig,{tabColor:this.tabColor,loaderConfig:t.loaderConfig||!1}}catch(t){return console.error(t),{tabColor:"#79ba44"}}}))}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275inject"](n.c),a["\u0275\u0275inject"](p.a))},t.\u0275prov=a["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);