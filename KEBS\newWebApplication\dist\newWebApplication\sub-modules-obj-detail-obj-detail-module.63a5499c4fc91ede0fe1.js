(window.webpackJsonp=window.webpackJsonp||[]).push([[1010],{tKhT:function(e,t,n){"use strict";n.r(t),n.d(t,"ObjDetailModule",(function(){return On}));var i=n("ofXK"),o=n("tyNb"),r=n("fXoL"),l=n("wZkO");function a(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"a",3,4),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=r["\u0275\u0275reference"](1);r["\u0275\u0275property"]("routerLink",e.path)("active",n.isActive),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.label," ")}}let s=(()=>{class e{constructor(){this.objectivedetailTabLinks=[{label:"Overview",path:"overview"},{label:"Attachments",path:"attachment"},{label:"History",path:"history"}]}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-obj-detailpage"]],decls:4,vars:1,consts:[["mat-tab-nav-bar",""],["mat-tab-link","","routerLinkActive","",3,"routerLink","active",4,"ngFor","ngForOf"],[1,"objective-tab-content"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active"],["rla","routerLinkActive"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"nav",0),r["\u0275\u0275template"](1,a,3,3,"a",1),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275element"](3,"router-outlet"),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",t.objectivedetailTabLinks))},directives:[l.f,i.NgForOf,o.l,o.j,l.e,o.i],styles:["a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}nav[_ngcontent-%COMP%]{position:fixed;width:100%;z-index:14;background:#f8f9fa}.objective-tab-content[_ngcontent-%COMP%]{padding-top:48px!important}"]}),e})();var c=n("mrSG"),d=n("33Jv"),p=n("wd/R"),u=n("25DO"),m=n("0IaG"),g=n("to83"),v=n("LcQX"),b=n("BVzC"),h=n("WGBV"),f=n("XXEo"),x=n("flaP"),_=n("Qu3c"),j=n("FKDz"),w=n("A5z7"),y=n("bTqV"),C=n("NFeN"),E=n("Xa2L"),S=n("CLW7"),O=n("M7nE"),k=n("me71"),I=n("mS9j"),A=n("jmr5"),M=n("Qcpi"),P=n("STbY"),V=n("sfX4"),T=n("f0Cb"),F=n("5RNC"),D=n("4hN+");function L(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",44),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](null==e.objectivedetails||null==e.objectivedetails.goal_details?null:e.objectivedetails.goal_details.goal_name)}}function U(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",45),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).getObjUpdatedValue(t,"name")})),r["\u0275\u0275pipe"](2,"titlecase"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",r["\u0275\u0275pipeBind1"](2,2,null==e.objectivedetails||null==e.objectivedetails.goal_details?null:e.objectivedetails.goal_details.goal_name))("cssData",e.cssClass)}}function K(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",48),r["\u0275\u0275text"](1,"cancel"),r["\u0275\u0275elementEnd"]())}function B(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-chip",46),r["\u0275\u0275listener"]("removed",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).removetag(n)})),r["\u0275\u0275text"](1),r["\u0275\u0275template"](2,K,2,0,"mat-icon",47),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("removable",n.removable),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("",e," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.removable)}}function $(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span",49),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("+",e.objtags.length-2,"")}}function R(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](null==e.objectivedetails?null:e.objectivedetails.desc)}}const N=function(){return{color:"black"}};function W(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"kebs-text-inline-edit-d1",50),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).getObjUpdatedValue(t,"desc")})),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("inputText",e.objectivedetails.desc)("cssData",r["\u0275\u0275pureFunction0"](2,N))}}function H(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275text"](1),r["\u0275\u0275pipe"](2,"date"),r["\u0275\u0275pipe"](3,"date"),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate3"]("",null==e.objectivedetails?null:e.objectivedetails.date_type," (",r["\u0275\u0275pipeBind2"](2,3,null==e.objectivedetails?null:e.objectivedetails.start_date,"dd-MMM-yyyy")," - ",r["\u0275\u0275pipeBind2"](3,6,null==e.objectivedetails?null:e.objectivedetails.end_date,"dd-MMM-yyyy"),")")}}function z(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"kebs-date-inline-edit-d1",51),r["\u0275\u0275listener"]("uptPeriod",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).getUptPeriod(t,"obj","")})),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("selectedPeriod",e.selectedPeriod)("periods",e.periods)("viewPortMargin",90)}}function Y(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",52),r["\u0275\u0275elementStart"](1,"span",53),r["\u0275\u0275element"](2,"app-user-image",54),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"span",53),r["\u0275\u0275element"](4,"app-user-profile",55),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("id",null==e.objectivedetails?null:e.objectivedetails.owner_id),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("type","name")("oid",null==e.objectivedetails?null:e.objectivedetails.owner_id)}}function q(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"app-owner-update",56),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).getObjOwnerUpdatedValue(t)})),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("id",null==e.objectivedetails?null:e.objectivedetails.owner_id)}}function X(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275elementStart"](1,"span",53),r["\u0275\u0275element"](2,"app-user-image",54),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"span",58),r["\u0275\u0275element"](4,"app-user-profile",55),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("id",null==e.objectivedetails?null:e.objectivedetails.co_owner[0].id),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("type","name")("oid",null==e.objectivedetails?null:e.objectivedetails.co_owner[0].id)}}function G(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,X,5,3,"span",57),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.coOwnerList.length>0&&null!=e.coOwnerList&&""!=e.coOwnerList)}}function J(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275elementStart"](1,"span",53),r["\u0275\u0275element"](2,"app-user-image",54),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"app-coowner-update",59),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).getObjCoOwnerUpdatedValue(t)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("id",e.coOwnerList[0].id),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("id",null==e.objectivedetails?null:e.objectivedetails.co_owner)("coOwnerList",e.coOwnerList)("name",e.coOwnerList[0].displayName)}}function Q(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275elementStart"](1,"app-coowner-update",59),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).getObjCoOwnerUpdatedValue(t)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("name",e.noCoOwner)}}function Z(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,J,4,4,"span",57),r["\u0275\u0275template"](2,Q,2,1,"span",57),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.coOwnerList.length>0&&null!=e.coOwnerList&&""!=e.coOwnerList),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==e.coOwnerList.length||null==e.coOwnerList||""==e.coOwnerList)}}function ee(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",78),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](3).removeEmp(t)})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2,"cancel"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}const te=function(e){return{objId:"143002",entityOwner:e,type:"update"}};function ne(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",69),r["\u0275\u0275elementStart"](1,"div",70),r["\u0275\u0275element"](2,"app-user-image",71),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",72),r["\u0275\u0275elementStart"](4,"div",36),r["\u0275\u0275elementStart"](5,"div",73),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",36),r["\u0275\u0275elementStart"](8,"div",74),r["\u0275\u0275element"](9,"app-user-profile",75),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",76),r["\u0275\u0275template"](11,ee,3,0,"button",77),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("id",e.id),r["\u0275\u0275advance"](3),r["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.displayName," "),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("oid",e.id)("type","designation"),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](6,te,null==n.objectivedetails?null:n.objectivedetails.owner_id))}}function ie(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",60),r["\u0275\u0275elementStart"](1,"button",61),r["\u0275\u0275elementStart"](2,"mat-icon",62),r["\u0275\u0275text"](3,"supervisor_account"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"strong",62),r["\u0275\u0275text"](5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"mat-menu",63,64),r["\u0275\u0275elementStart"](8,"div",65),r["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),r["\u0275\u0275elementStart"](9,"div",66),r["\u0275\u0275elementStart"](10,"div",67),r["\u0275\u0275text"](11," Selected Co-Owners "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](12,ne,12,8,"div",68),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](7),t=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matMenuTriggerFor",e),r["\u0275\u0275advance"](4),r["\u0275\u0275textInterpolate1"]("+",t.coOwnerList.length,""),r["\u0275\u0275advance"](7),r["\u0275\u0275property"]("ngForOf",t.coOwnerList)}}function oe(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",79),r["\u0275\u0275pipe"](2,"titlecase"),r["\u0275\u0275pipe"](3,"titlecase"),r["\u0275\u0275elementStart"](4,"strong",80),r["\u0275\u0275text"](5),r["\u0275\u0275pipe"](6,"titlecase"),r["\u0275\u0275pipe"](7,"titlecase"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275propertyInterpolate2"]("matTooltip","",r["\u0275\u0275pipeBind1"](2,4,e.objectivedetails.visibility_name)," - ",r["\u0275\u0275pipeBind1"](3,6,null==e.objectivedetails?null:e.objectivedetails.org_name),""),r["\u0275\u0275advance"](4),r["\u0275\u0275textInterpolate2"]("",r["\u0275\u0275pipeBind1"](6,8,e.objectivedetails.visibility_name)," - ",r["\u0275\u0275pipeBind1"](7,10,null==e.objectivedetails?null:e.objectivedetails.org_name),"")}}function re(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",79),r["\u0275\u0275elementStart"](1,"strong",80),r["\u0275\u0275text"](2),r["\u0275\u0275pipe"](3,"titlecase"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275propertyInterpolate"]("matTooltip",e.objectivedetails.visibility_name),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind1"](3,2,e.objectivedetails.visibility_name))}}function le(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",79),r["\u0275\u0275pipe"](1,"titlecase"),r["\u0275\u0275pipe"](2,"titlecase"),r["\u0275\u0275elementStart"](3,"app-visibility-update",82),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).getObjVisibilityUpdatedValue(t)})),r["\u0275\u0275pipe"](4,"titlecase"),r["\u0275\u0275pipe"](5,"titlecase"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275propertyInterpolate2"]("matTooltip","",r["\u0275\u0275pipeBind1"](1,4,e.objectivedetails.visibility_name)," - ",r["\u0275\u0275pipeBind1"](2,6,null==e.objectivedetails?null:e.objectivedetails.org_name),""),r["\u0275\u0275advance"](3),r["\u0275\u0275propertyInterpolate2"]("inputName","",r["\u0275\u0275pipeBind1"](4,8,e.objectivedetails.visibility_name)," - ",r["\u0275\u0275pipeBind1"](5,10,null==e.objectivedetails?null:e.objectivedetails.org_name),"")}}function ae(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,re,4,4,"div",81),r["\u0275\u0275template"](2,le,6,12,"div",81),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","across org"!=e.objectivedetails.visibility_name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","across org"==e.objectivedetails.visibility_name)}}function se(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",86),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275propertyInterpolate1"]("matTooltip","Weightage - ",e.objectivedetails.weightage,""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.objectivedetails.weightage," ")}}function ce(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",87),r["\u0275\u0275text"](1," - "),r["\u0275\u0275elementEnd"]())}function de(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",83),r["\u0275\u0275text"](2,"Weightage (%)"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,se,2,2,"div",84),r["\u0275\u0275template"](4,ce,2,0,"div",85),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=e.objectivedetails.weightage),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==e.objectivedetails.weightage||!e.objectivedetails.weightage)}}const pe=function(){return{color:"black",font_size:"16px",font_weight:500}};function ue(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",79),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",88),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).getWeightageUpdatedValue(t,"obj","")})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275propertyInterpolate1"]("matTooltip","Weightage - ",e.objectivedetails.weightage,""),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",e.objectivedetails.weightage)("viewPort",70)("cssData",r["\u0275\u0275pureFunction0"](4,pe))}}function me(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",87),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",88),r["\u0275\u0275listener"]("uptValChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).getWeightageUpdatedValue(t,"obj","")})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",0)("viewPort",70)("cssData",r["\u0275\u0275pureFunction0"](3,pe)))}function ge(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",83),r["\u0275\u0275text"](2,"Weightage (%)"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,ue,2,5,"div",81),r["\u0275\u0275template"](4,me,2,4,"div",85),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=e.objectivedetails.weightage),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==e.objectivedetails.weightage||!e.objectivedetails.weightage)}}function ve(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",86),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275propertyInterpolate1"]("matTooltip","Planned Score - ",e.objectivedetails.plnd_score,""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.objectivedetails.plnd_score," ")}}function be(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",91),r["\u0275\u0275text"](1," - "),r["\u0275\u0275elementEnd"]())}function he(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",89),r["\u0275\u0275elementStart"](1,"span",83),r["\u0275\u0275text"](2,"Planned Score"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,ve,2,2,"div",84),r["\u0275\u0275template"](4,be,2,0,"div",90),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=e.objectivedetails.plnd_score),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==e.objectivedetails.plnd_score||!e.objectivedetails.plnd_score)}}function fe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",86),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275propertyInterpolate1"]("matTooltip","Actual Score - ",e.objectivedetails.act_score,""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.objectivedetails.act_score," ")}}function xe(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",94),r["\u0275\u0275text"](1," - "),r["\u0275\u0275elementEnd"]())}function _e(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",92),r["\u0275\u0275elementStart"](1,"span",83),r["\u0275\u0275text"](2,"Actual Score"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,fe,2,2,"div",84),r["\u0275\u0275template"](4,xe,2,0,"div",93),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=e.objectivedetails.act_score),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==e.objectivedetails.act_score||!e.objectivedetails.act_score)}}function je(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",86),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275propertyInterpolate1"]("matTooltip","Total Score - ",e.objectivedetails.tot_score,""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.objectivedetails.tot_score," ")}}function we(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",96),r["\u0275\u0275text"](1," - "),r["\u0275\u0275elementEnd"]())}function ye(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",92),r["\u0275\u0275elementStart"](1,"span",83),r["\u0275\u0275text"](2,"Total Score"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,je,2,2,"div",84),r["\u0275\u0275template"](4,we,2,0,"div",95),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=e.objectivedetails.tot_score),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==e.objectivedetails.tot_score||!e.objectivedetails.tot_score)}}const Ce=function(e){return{objId:"143005",entityOwner:e,type:"view"}},Ee=function(e){return{objId:"143005",entityOwner:e,type:"update"}},Se=function(e){return{objId:"143006",entityOwner:e,type:"view"}},Oe=function(e){return{objId:"143006",entityOwner:e,type:"update"}},ke=function(e){return{objId:"143040",entityOwner:e,type:"view"}},Ie=function(e){return{objId:"143040",entityOwner:e,type:"update"}},Ae=function(e){return{objId:"143001",entityOwner:e,type:"view"}},Me=function(e){return{objId:"143001",entityOwner:e,type:"update"}},Pe=function(e){return{objId:"143002",entityOwner:e,type:"view"}},Ve=function(e){return{objId:"143003",entityOwner:e,type:"view"}},Te=function(e){return{objId:"143003",entityOwner:e,type:"update"}},Fe=function(e){return{objId:"143004",entityOwner:e,type:"view"}},De=function(e){return{objId:"143004",entityOwner:e,type:"update"}},Le=function(e){return{objId:"143029",entityOwner:e,type:"view"}},Ue=function(e){return{objId:"143030",entityOwner:e,type:"view"}},Ke=function(e){return{objId:"143031",entityOwner:e,type:"view"}};function Be(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",5),r["\u0275\u0275elementStart"](1,"div",6),r["\u0275\u0275elementStart"](2,"div",7),r["\u0275\u0275elementStart"](3,"div",8),r["\u0275\u0275template"](4,L,3,1,"ng-container",9),r["\u0275\u0275template"](5,U,3,4,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",10),r["\u0275\u0275elementStart"](7,"mat-chip-list",11),r["\u0275\u0275template"](8,B,3,3,"mat-chip",12),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](9,$,2,1,"span",13),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",14),r["\u0275\u0275elementStart"](11,"button",15),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]();return t.openCommentBox("obj",t.objectivedetails,"")})),r["\u0275\u0275elementStart"](12,"mat-icon"),r["\u0275\u0275text"](13,"question_answer"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",16),r["\u0275\u0275elementStart"](15,"div",17),r["\u0275\u0275elementStart"](16,"div",18),r["\u0275\u0275template"](17,R,2,1,"span",9),r["\u0275\u0275template"](18,W,1,3,"kebs-text-inline-edit-d1",19),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div",20),r["\u0275\u0275elementStart"](20,"div",21),r["\u0275\u0275elementStart"](21,"span",22),r["\u0275\u0275text"](22,"Target"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](23,H,4,9,"span",9),r["\u0275\u0275template"](24,z,1,3,"kebs-date-inline-edit-d1",23),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](25,"div",24),r["\u0275\u0275elementStart"](26,"span",25),r["\u0275\u0275text"](27,"Owner"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](28,Y,5,3,"div",26),r["\u0275\u0275template"](29,q,1,1,"app-owner-update",27),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](30,"div",28),r["\u0275\u0275elementStart"](31,"div",29),r["\u0275\u0275elementStart"](32,"span",25),r["\u0275\u0275text"](33,"Co-Owner"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](34,G,2,1,"ng-container",9),r["\u0275\u0275template"](35,Z,3,2,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](36,ie,13,3,"div",30),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](37,"div",31),r["\u0275\u0275elementStart"](38,"div",32),r["\u0275\u0275elementStart"](39,"span",25),r["\u0275\u0275text"](40,"Visibility"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](41,oe,8,12,"ng-container",9),r["\u0275\u0275template"](42,ae,3,2,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](43,"div",24),r["\u0275\u0275template"](44,de,5,2,"ng-container",9),r["\u0275\u0275template"](45,ge,5,2,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](46,he,5,2,"div",33),r["\u0275\u0275template"](47,_e,5,2,"div",34),r["\u0275\u0275template"](48,ye,5,2,"div",34),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](49,"div",35),r["\u0275\u0275elementStart"](50,"div",36),r["\u0275\u0275elementStart"](51,"div",37),r["\u0275\u0275elementStart"](52,"div",38),r["\u0275\u0275element"](53,"mat-progress-spinner",39),r["\u0275\u0275elementStart"](54,"div",40),r["\u0275\u0275text"](55),r["\u0275\u0275pipe"](56,"number"),r["\u0275\u0275elementStart"](57,"span",41),r["\u0275\u0275text"](58,"%"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](59,"div",42),r["\u0275\u0275element"](60,"mat-progress-spinner",43),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275propertyInterpolate"]("matTooltip",null==e.objectivedetails||null==e.objectivedetails.goal_details?null:e.objectivedetails.goal_details.goal_name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](30,Ce,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](32,Ee,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngForOf",e.tags),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.objtags.length>2),r["\u0275\u0275advance"](7),r["\u0275\u0275propertyInterpolate"]("matTooltip",e.objectivedetails.desc),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](34,Se,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](36,Oe,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](38,ke,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](40,Ie,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](42,Ae,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](44,Me,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](46,Pe,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](48,te,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.coOwnerList.length>0&&null!=e.coOwnerList&&""!=e.coOwnerList),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](50,Ve,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](52,Te,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](54,Fe,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](56,De,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](58,Le,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](60,Ue,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](62,Ke,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("diameter",80)("value",null==e.objectivedetails?null:e.objectivedetails.progress_val),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",r["\u0275\u0275pipeBind2"](56,27,null==e.objectivedetails?null:e.objectivedetails.progress_val,"1.0-0"),""),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("diameter",80)("value",100)}}function $e(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",66),r["\u0275\u0275elementStart"](1,"div",103),r["\u0275\u0275elementStart"](2,"span"),r["\u0275\u0275elementStart"](3,"strong"),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"span",104),r["\u0275\u0275text"](6,"Obj Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"span",105),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",106),r["\u0275\u0275elementStart"](10,"span",107),r["\u0275\u0275text"](11,"ORG Name "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"span",108),r["\u0275\u0275text"](13),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;r["\u0275\u0275advance"](4),r["\u0275\u0275textInterpolate1"]("",n+1,"."),r["\u0275\u0275advance"](3),r["\u0275\u0275propertyInterpolate"]("matTooltip",null==e?null:e.goal_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",null==e?null:e.goal_name," "),r["\u0275\u0275advance"](4),r["\u0275\u0275propertyInterpolate"]("matTooltip",null==e?null:e.Org_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",null==e?null:e.Org_name,"")}}function Re(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",97),r["\u0275\u0275elementStart"](1,"div",98),r["\u0275\u0275elementStart"](2,"div",99),r["\u0275\u0275elementStart"](3,"mat-icon"),r["\u0275\u0275text"](4,"group"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"span",100),r["\u0275\u0275text"](6,"This Objective is "),r["\u0275\u0275elementStart"](7,"strong",101),r["\u0275\u0275text"](8,"Collabrated"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275text"](9," with these Objectives : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](10,$e,14,5,"div",102),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](10),r["\u0275\u0275property"]("ngForOf",null==e.objectivedetails?null:e.objectivedetails.collaborativeObjectiveDetails)}}function Ne(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("Key Results (",e.keyresultsLength,")")}}function We(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("KPI (",e.keyresultsLength,")")}}function He(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",116),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).krclick()})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2,"add_circle_outline"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function ze(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275template"](2,Ne,2,1,"strong",113),r["\u0275\u0275template"](3,We,2,1,"strong",113),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](4,He,3,0,"button",115),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",0==e.is_ikr),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.is_ikr),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==e.is_ikr)}}function Ye(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",116),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).kpiclick()})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2,"add_circle_outline"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function qe(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,Ye,3,0,"button",115),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.is_ikr)}}function Xe(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",141),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",null==e||null==e.KR_goal_details?null:e.KR_goal_details.goal_name," ")}}const Ge=function(){return{font_size:"16px",font_weight:500}};function Je(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",142),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](2).getKrUpdatedValue(t,"name",n)})),r["\u0275\u0275pipe"](2,"titlecase"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",r["\u0275\u0275pipeBind1"](2,2,null==e||null==e.KR_goal_details?null:e.KR_goal_details.goal_name))("cssData",r["\u0275\u0275pureFunction0"](4,Ge))}}function Qe(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",144),r["\u0275\u0275elementStart"](2,"strong",101),r["\u0275\u0275text"](3),r["\u0275\u0275pipe"](4,"number"),r["\u0275\u0275elementStart"](5,"span",41),r["\u0275\u0275text"](6,"%"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind2"](4,1,e.progress_val,"1.0-2"))}}function Ze(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"strong",101),r["\u0275\u0275text"](2),r["\u0275\u0275pipe"](3,"number"),r["\u0275\u0275elementStart"](4,"span",41),r["\u0275\u0275text"](5,"%"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind2"](3,1,e.metric_details.milestone,"1.0-2"))}}function et(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275elementStart"](1,"div",145),r["\u0275\u0275element"](2,"div",146),r["\u0275\u0275elementStart"](3,"strong",58),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275styleProp"]("background",null==e||null==e.status?null:e.status.color),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"]("",null==e||null==e.status?null:e.status.name," ")}}function tt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",143),r["\u0275\u0275template"](2,Qe,7,4,"ng-container",57),r["\u0275\u0275template"](3,Ze,6,4,"ng-container",57),r["\u0275\u0275template"](4,et,5,3,"span",57),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",1==e.metric_id||2==e.metric_id),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",4==e.metric_id),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",5==e.metric_id)}}function nt(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"mat-slider",147),r["\u0275\u0275listener"]("change",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"](2).$implicit;return r["\u0275\u0275nextContext"](2).sliderChange(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",144),r["\u0275\u0275elementStart"](4,"strong",101),r["\u0275\u0275text"](5),r["\u0275\u0275pipe"](6,"number"),r["\u0275\u0275elementStart"](7,"span",41),r["\u0275\u0275text"](8,"%"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("value",e.progress_val),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind2"](6,2,e.progress_val,"1.0-2"))}}function it(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"kebs-milestone-slider-d1",148),r["\u0275\u0275listener"]("selectedVal",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"](2).$implicit;return r["\u0275\u0275nextContext"](2).sliderChange(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2).$implicit,t=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("token",t.token)("data",e.metric_details.milestone)}}function ot(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275elementStart"](1,"div",149),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).$implicit;return r["\u0275\u0275nextContext"](2).sliderChange(0,t)})),r["\u0275\u0275element"](2,"div",146),r["\u0275\u0275elementStart"](3,"strong",58),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275styleProp"]("background",null==e||null==e.status?null:e.status.color),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"]("",null==e||null==e.status?null:e.status.name," ")}}function rt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",143),r["\u0275\u0275template"](2,nt,9,5,"ng-container",57),r["\u0275\u0275template"](3,it,2,2,"ng-container",57),r["\u0275\u0275template"](4,ot,5,3,"span",57),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",1==e.metric_id||2==e.metric_id),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",4==e.metric_id),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",5==e.metric_id)}}function lt(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",null==e?null:e.date_type," ")}}function at(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275pipe"](2,"date"),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" C - ",r["\u0275\u0275pipeBind2"](2,1,null==e?null:e.start_date,"y")," ")}}function st(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",53),r["\u0275\u0275element"](2,"app-user-image",54),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"span",53),r["\u0275\u0275element"](4,"app-user-profile",55),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("id",null==e?null:e.owner_id),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("type","name")("oid",null==e?null:e.owner_id)}}function ct(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"app-owner-update",56),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](2).getKrOwnerUpdatedValue(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("id",null==e?null:e.owner_id)}}function dt(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",86),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275propertyInterpolate1"]("matTooltip","Weightage - ",null==e?null:e.weightage,""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",null==e?null:e.weightage," ")}}function pt(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",87),r["\u0275\u0275text"](1," - "),r["\u0275\u0275elementEnd"]())}function ut(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",150),r["\u0275\u0275text"](2,"Weightage (%)"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,dt,2,2,"div",84),r["\u0275\u0275template"](4,pt,2,0,"div",85),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=(null==e?null:e.weightage)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==(null==e?null:e.weightage)||!(null!=e&&e.weightage))}}const mt=function(){return{color:"black",font_size:"14px",font_weight:500}};function gt(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",79),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",88),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"](2).index;return r["\u0275\u0275nextContext"](2).getWeightageUpdatedValue(t,"kr",n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275propertyInterpolate1"]("matTooltip","Weightage - ",null==e?null:e.weightage,""),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",null==e?null:e.weightage)("viewPort",70)("cssData",r["\u0275\u0275pureFunction0"](4,mt))}}function vt(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",87),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",88),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"](2).index;return r["\u0275\u0275nextContext"](2).getWeightageUpdatedValue(t,"kr",n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",0)("viewPort",70)("cssData",r["\u0275\u0275pureFunction0"](3,mt)))}function bt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",150),r["\u0275\u0275text"](2,"Weightage (%)"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,gt,2,5,"div",81),r["\u0275\u0275template"](4,vt,2,4,"div",85),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=(null==e?null:e.weightage)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==(null==e?null:e.weightage)||!(null!=e&&e.weightage))}}function ht(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span",79),r["\u0275\u0275pipe"](1,"date"),r["\u0275\u0275text"](2),r["\u0275\u0275pipe"](3,"date"),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275propertyInterpolate"]("matTooltip",r["\u0275\u0275pipeBind2"](1,2,null==e?null:e.last_updated,"MMM d,YYYY,hh:mm a")),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind2"](3,5,null==e?null:e.last_updated,"MMM d,YYYY"))}}function ft(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275text"](1,"No updates"),r["\u0275\u0275elementEnd"]())}const xt=function(e){return{objId:"143013",entityOwner:e,type:"view"}},_t=function(e){return{objId:"143013",entityOwner:e,type:"update"}},jt=function(e){return{objId:"143014",entityOwner:e,type:"view"}},wt=function(e){return{objId:"143014",entityOwner:e,type:"update"}},yt=function(e){return{objId:"143008",entityOwner:e,type:"view"}},Ct=function(e){return{objId:"143008",entityOwner:e,type:"update"}},Et=function(e){return{objId:"143010",entityOwner:e,type:"view"}},St=function(e){return{objId:"143010",entityOwner:e,type:"update"}};function Ot(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",117),r["\u0275\u0275elementStart"](1,"div",118),r["\u0275\u0275element"](2,"mat-divider",119),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",120),r["\u0275\u0275elementStart"](4,"span"),r["\u0275\u0275text"](5,"___"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",121),r["\u0275\u0275elementStart"](7,"div",122),r["\u0275\u0275template"](8,Xe,3,1,"ng-container",9),r["\u0275\u0275template"](9,Je,3,5,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",123),r["\u0275\u0275elementStart"](11,"button",124),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit,i=r["\u0275\u0275nextContext"](2);return i.krselect(null==n?null:n._id,null==n||null==n.KR_goal_details?null:n.KR_goal_details.goal_name,i.isOpenNewTab)})),r["\u0275\u0275elementStart"](12,"mat-icon"),r["\u0275\u0275text"](13,"launch"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"button",125),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).krHistoryPopup(null==n?null:n._id,!0,!1)})),r["\u0275\u0275elementStart"](15,"mat-icon"),r["\u0275\u0275text"](16,"history"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](17,tt,5,3,"ng-container",9),r["\u0275\u0275template"](18,rt,5,3,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div",126),r["\u0275\u0275elementStart"](20,"div",127),r["\u0275\u0275elementStart"](21,"button",128),r["\u0275\u0275elementStart"](22,"mat-icon"),r["\u0275\u0275text"](23),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](24,"div",129),r["\u0275\u0275elementStart"](25,"div",36),r["\u0275\u0275elementStart"](26,"div",130),r["\u0275\u0275text"](27,"Target"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](28,"div",129),r["\u0275\u0275template"](29,lt,2,1,"strong",113),r["\u0275\u0275template"](30,at,3,4,"strong",113),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](31,"div",131),r["\u0275\u0275elementStart"](32,"span",132),r["\u0275\u0275text"](33),r["\u0275\u0275pipe"](34,"date"),r["\u0275\u0275pipe"](35,"date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](36,"div",133),r["\u0275\u0275elementStart"](37,"div",134),r["\u0275\u0275elementStart"](38,"span",25),r["\u0275\u0275text"](39,"Owner"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](40,st,5,3,"ng-container",9),r["\u0275\u0275template"](41,ct,2,1,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](42,"div",135),r["\u0275\u0275template"](43,ut,5,2,"ng-container",9),r["\u0275\u0275template"](44,bt,5,2,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](45,"div",136),r["\u0275\u0275elementStart"](46,"span",137),r["\u0275\u0275text"](47,"Last updated- "),r["\u0275\u0275template"](48,ht,4,8,"span",138),r["\u0275\u0275template"](49,ft,2,0,"ng-template",139,140,r["\u0275\u0275templateRefExtractor"]),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275reference"](50),i=r["\u0275\u0275nextContext"](2);r["\u0275\u0275styleProp"]("border-left-color",null==e||null==e.status?null:e.status.color),r["\u0275\u0275advance"](7),r["\u0275\u0275propertyInterpolate"]("matTooltip",null==e||null==e.KR_goal_details?null:e.KR_goal_details.goal_name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](26,xt,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](28,_t,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](8),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](30,jt,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](32,wt,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](2),r["\u0275\u0275styleProp"]("background",null==e||null==e.metric_details?null:e.metric_details.icon_bg_colour),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](null==e||null==e.metric_details?null:e.metric_details.icon_name),r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("ngIf","custom"!=(null==e?null:e.date_type)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","custom"==(null==e?null:e.date_type)),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate2"]("",r["\u0275\u0275pipeBind2"](34,20,null==e?null:e.start_date,"MMM d")," - ",r["\u0275\u0275pipeBind2"](35,23,null==e?null:e.end_date,"MMM d"),""),r["\u0275\u0275advance"](7),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](34,yt,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](36,Ct,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](38,Et,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](40,St,null==i.objectivedetails?null:i.objectivedetails.owner_id)),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("ngIf",null==e?null:e.last_updated)("ngIfElse",n)}}function kt(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("Initiative Results (",null==e.objectivedetails||null==e.objectivedetails.objective_initiative_details?null:e.objectivedetails.objective_initiative_details.length,")")}}function It(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("BAU (",null==e.objectivedetails||null==e.objectivedetails.objective_initiative_details?null:e.objectivedetails.objective_initiative_details.length,")")}}function At(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",152),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).createInitiative("","obj","")})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2,"add_circle_outline"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function Mt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,At,3,0,"button",151),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==e.is_ikr)}}function Pt(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",152),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).createBAU("","obj","")})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2,"add_circle_outline"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function Vt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,Pt,3,0,"button",151),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.is_ikr)}}function Tt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",141),r["\u0275\u0275text"](2),r["\u0275\u0275pipe"](3,"titlecase"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",r["\u0275\u0275pipeBind1"](3,1,null==e?null:e.name)," ")}}function Ft(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",160),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](2).getInitUpdatedValue(t,"name",n)})),r["\u0275\u0275pipe"](2,"titlecase"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",r["\u0275\u0275pipeBind1"](2,2,null==e?null:e.name))("cssData",r["\u0275\u0275pureFunction0"](4,Ge))}}function Dt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",144),r["\u0275\u0275elementStart"](2,"strong",101),r["\u0275\u0275text"](3),r["\u0275\u0275pipe"](4,"number"),r["\u0275\u0275elementStart"](5,"span",41),r["\u0275\u0275text"](6,"%"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind2"](4,1,null==e?null:e.progress_val,"1.0-2"))}}function Lt(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"mat-slider",147),r["\u0275\u0275listener"]("change",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).openInitMetricPopUp(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",144),r["\u0275\u0275elementStart"](4,"strong",101),r["\u0275\u0275text"](5),r["\u0275\u0275pipe"](6,"number"),r["\u0275\u0275elementStart"](7,"span",41),r["\u0275\u0275text"](8,"%"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("value",null==e?null:e.progress_val),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind2"](6,2,null==e?null:e.progress_val,"1.0-2"))}}function Ut(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",null==e?null:e.date_type," ")}}function Kt(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"strong",101),r["\u0275\u0275text"](1),r["\u0275\u0275pipe"](2,"date"),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" C - ",r["\u0275\u0275pipeBind2"](2,1,null==e?null:e.start_date,"y")," ")}}function Bt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",53),r["\u0275\u0275element"](2,"app-user-image",54),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"span",53),r["\u0275\u0275element"](4,"app-user-profile",55),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("id",null==e?null:e.owner_id),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("type","name")("oid",null==e?null:e.owner_id)}}function $t(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"app-owner-update",56),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](2).getInitOwnerUpdatedValue(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("id",null==e?null:e.owner_id)}}function Rt(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",86),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275propertyInterpolate1"]("matTooltip","Weightage - ",null==e?null:e.weightage,""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",null==e?null:e.weightage," ")}}function Nt(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",87),r["\u0275\u0275text"](1," - "),r["\u0275\u0275elementEnd"]())}function Wt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",150),r["\u0275\u0275text"](2,"Weightage (%)"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,Rt,2,2,"div",84),r["\u0275\u0275template"](4,Nt,2,0,"div",85),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=(null==e?null:e.weightage)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==(null==e?null:e.weightage)||!(null!=e&&e.weightage))}}function Ht(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",79),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",88),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"](2).index;return r["\u0275\u0275nextContext"](2).getWeightageUpdatedValue(t,"init",n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275propertyInterpolate1"]("matTooltip","Weightage - ",null==e?null:e.weightage,""),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",null==e?null:e.weightage)("viewPort",70)("cssData",r["\u0275\u0275pureFunction0"](4,mt))}}function zt(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",87),r["\u0275\u0275elementStart"](1,"kebs-text-inline-edit-d1",88),r["\u0275\u0275listener"]("uptValChange",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"](2).index;return r["\u0275\u0275nextContext"](2).getWeightageUpdatedValue(t,"init",n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("inputText",0)("viewPort",70)("cssData",r["\u0275\u0275pureFunction0"](3,mt)))}function Yt(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",150),r["\u0275\u0275text"](2,"Weightage (%)"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,Ht,2,5,"div",81),r["\u0275\u0275template"](4,zt,2,4,"div",85),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",""!=(null==e?null:e.weightage)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""==(null==e?null:e.weightage)||!(null!=e&&e.weightage))}}function qt(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275text"](1),r["\u0275\u0275pipe"](2,"date"),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind2"](2,1,null==e?null:e.last_updated,"MMM d,YYYY"))}}function Xt(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"span"),r["\u0275\u0275text"](1,"No updates"),r["\u0275\u0275elementEnd"]())}const Gt=function(e){return{objId:"143021",entityOwner:e,type:"view"}},Jt=function(e){return{objId:"143021",entityOwner:e,type:"update"}},Qt=function(e){return{objId:"143022",entityOwner:e,type:"view"}},Zt=function(e){return{objId:"143022",entityOwner:e,type:"update"}},en=function(e){return{objId:"143026",entityOwner:e,type:"view"}},tn=function(e){return{objId:"143026",entityOwner:e,type:"update"}},nn=function(e){return{objId:"143018",entityOwner:e,type:"view"}},on=function(e){return{objId:"143018",entityOwner:e,type:"update"}};function rn(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",153),r["\u0275\u0275elementStart"](1,"div",118),r["\u0275\u0275element"](2,"mat-divider",154),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",120),r["\u0275\u0275elementStart"](4,"span"),r["\u0275\u0275text"](5,"___"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",121),r["\u0275\u0275elementStart"](7,"div",122),r["\u0275\u0275template"](8,Tt,4,3,"ng-container",9),r["\u0275\u0275template"](9,Ft,3,5,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",123),r["\u0275\u0275elementStart"](11,"button",155),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit,i=r["\u0275\u0275nextContext"](2);return-i.initiativePageRoute(null==n?null:n._id,null==n?null:n.name,i.isOpenNewTab)})),r["\u0275\u0275elementStart"](12,"mat-icon"),r["\u0275\u0275text"](13,"launch"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"button",125),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).openKrHistoryPopup(null==n?null:n._id,!1,!0)})),r["\u0275\u0275elementStart"](15,"mat-icon"),r["\u0275\u0275text"](16,"history"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",143),r["\u0275\u0275template"](18,Dt,7,4,"ng-container",9),r["\u0275\u0275template"](19,Lt,9,5,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](20,"div",156),r["\u0275\u0275elementStart"](21,"div",157),r["\u0275\u0275elementStart"](22,"button",128),r["\u0275\u0275elementStart"](23,"mat-icon"),r["\u0275\u0275text"](24,"bolt"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](25,"div",129),r["\u0275\u0275elementStart"](26,"div",36),r["\u0275\u0275elementStart"](27,"div",130),r["\u0275\u0275text"](28,"Target"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](29,"div",129),r["\u0275\u0275template"](30,Ut,2,1,"strong",113),r["\u0275\u0275template"](31,Kt,3,4,"strong",113),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](32,"div",158),r["\u0275\u0275elementStart"](33,"span"),r["\u0275\u0275text"](34),r["\u0275\u0275pipe"](35,"date"),r["\u0275\u0275pipe"](36,"date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](37,"div",133),r["\u0275\u0275elementStart"](38,"div",134),r["\u0275\u0275elementStart"](39,"span",25),r["\u0275\u0275text"](40,"Owner"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](41,Bt,5,3,"ng-container",9),r["\u0275\u0275template"](42,$t,2,1,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](43,"div",135),r["\u0275\u0275template"](44,Wt,5,2,"ng-container",9),r["\u0275\u0275template"](45,Yt,5,2,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](46,"div",136),r["\u0275\u0275elementStart"](47,"span",137),r["\u0275\u0275text"](48,"Last updated- "),r["\u0275\u0275template"](49,qt,3,4,"span",159),r["\u0275\u0275template"](50,Xt,2,0,"ng-template",null,140,r["\u0275\u0275templateRefExtractor"]),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=r["\u0275\u0275reference"](51),o=r["\u0275\u0275nextContext"](2);r["\u0275\u0275styleProp"]("border-left-color",null==o.objectivedetails.objective_initiative_status_details[n]?null:o.objectivedetails.objective_initiative_status_details[n].color),r["\u0275\u0275advance"](7),r["\u0275\u0275propertyInterpolate"]("matTooltip",null==e?null:e.name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](23,Gt,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](25,Jt,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](9),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](27,Qt,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](29,Zt,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](11),r["\u0275\u0275property"]("ngIf","custom"!=(null==e?null:e.date_type)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","custom"==(null==e?null:e.date_type)),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate2"](" ",r["\u0275\u0275pipeBind2"](35,17,null==e?null:e.start_date,"dd-MMM-yyyy")," - ",r["\u0275\u0275pipeBind2"](36,20,null==e?null:e.end_date,"dd-MMM-yyyy")," "),r["\u0275\u0275advance"](7),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](31,en,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](33,tn,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](35,nn,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](37,on,null==o.objectivedetails?null:o.objectivedetails.owner_id)),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("ngIf",null==e?null:e.last_updated)("ngIfElse",i)}}const ln=function(e){return{objId:"143016",entityOwner:e,type:"create"}},an=function(e){return{objId:"143038",entityOwner:e,type:"create"}},sn=function(e){return{objId:"143023",entityOwner:e,type:"create"}},cn=function(e){return{objId:"143039",entityOwner:e,type:"create"}};function dn(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",109),r["\u0275\u0275elementStart"](1,"div",110),r["\u0275\u0275elementStart"](2,"div",111),r["\u0275\u0275template"](3,ze,5,3,"ng-container",9),r["\u0275\u0275template"](4,qe,2,1,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](5,Ot,51,42,"div",112),r["\u0275\u0275elementStart"](6,"div",111),r["\u0275\u0275elementStart"](7,"span"),r["\u0275\u0275template"](8,kt,2,1,"strong",113),r["\u0275\u0275template"](9,It,2,1,"strong",113),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](10,Mt,2,1,"ng-container",9),r["\u0275\u0275template"](11,Vt,2,1,"ng-container",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](12,rn,52,39,"div",114),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](8,ln,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](10,an,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",e.objectivedetails.key_results),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",0==e.is_ikr),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.is_ikr),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](12,sn,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("okrAuth",r["\u0275\u0275pureFunction1"](14,cn,null==e.objectivedetails?null:e.objectivedetails.owner_id)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",e.objectivedetails.objective_initiative_details)}}let pn=(()=>{class e{constructor(e,t,i,o,r,l,a,s,p,u){this._okr=e,this.router=t,this.activeroute=i,this._dialog=o,this._initStatus=r,this._util=l,this._ErrorService=a,this.accountService=s,this._loginService=p,this._rolesService=u,this.subs=new d.a,this.searchAlignText="",this.isObjectiveLoading=!0,this.alignedObjArr=[],this.alignedKrArr=[],this.isAlignObjective=!1,this.isAlignedObjLoading=!0,this.objectivedetails=null,this.removable=!0,this.progressvalue=0,this.sum=0,this.searchAlignedArr=[],this.isSearchingAlign=!1,this.coOwnerList=[],this.noCoOwner="-",this.periods=[],this.selectedPeriodCustom={id:"CCCCCC",type:"custom",startDate:"2021-04-01",endDate:"2022-03-31"},this.is_ikr=!1,this.krMetrics={increase:{icon:"keyboard_arrow_up",background:"#019334"},decrease:{icon:"keyboard_arrow_down",background:"#cf0000"},milestone:{icon:"golf_course",background:"#fda400"}},this.objtags=[],this.tags=[],this.allTags=[],this.cssClass={font_weight:500,font_size:"18px",color:"black"},this.addTag=!1,this.addTagButton=!0,this.userProfile=null,this.roleMatrix=null,this.initObjectiveData=()=>Object(c.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{this.activeroute.parent.params.subscribe(t=>Object(c.c)(this,void 0,void 0,(function*(){return this.objectiveid=t.id,yield this.getObjectiveDetails(),this.getAlignedObj(),this.getAlignObj(),this.getAllTags(),this.getPeriods(),e(this.objectivedetails)})),e=>(console.log(e),t(e)))})})),this.getAllTags=()=>{this.subs.sink=this._okr.getTagsMaster().subscribe(e=>{this.allTags=e.data},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.getObjectiveDetails=()=>new Promise((e,t)=>{this.isObjectiveLoading=!0,this.subs.sink=this._okr.getObjectiveDetailsMaster({objective_id:this.objectiveid}).subscribe(t=>{var n,i,o;return this.objectivedetails=t.data[0],this.selectedPeriod={type:null===(n=this.objectivedetails)||void 0===n?void 0:n.date_type,startDate:null===(i=this.objectivedetails)||void 0===i?void 0:i.start_date,endDate:null===(o=this.objectivedetails)||void 0===o?void 0:o.end_date},this.keyresultsLength=this.objectivedetails.key_results.length,this.is_ikr=this.objectivedetails.is_ikr,console.log(this.objectivedetails),this.getCoOwner(),this.tagsdisplay(),this.isObjectiveLoading=!1,e(this.objectivedetails)},e=>{this.isObjectiveLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),t(e)})}),this.nameCatch=e=>{console.log(e)},this.getOpenNewTabConfig=()=>{this.subs.sink=this._okr.getOpenNewTabConfig().subscribe(e=>{this.isOpenNewTab=e.data[0].configuration_data},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.openValMetricPopUp=(e,t,i,o)=>Object(c.c)(this,void 0,void 0,(function*(){const{MetricUptValComponent:r}=yield n.e(184).then(n.bind(null,"VEKw"));this._dialog.open(r,{width:"46%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{updatedValue:e,krDetail:t,objectiveId:this.objectiveid,isInitiative:i,showProgressBar:o}}).afterClosed().subscribe(e=>{this.activeroute.parent.params.subscribe(e=>{this.objectiveid=e.id,this.getObjectiveDetails()})})})),this.openMilMetricPopUp=e=>Object(c.c)(this,void 0,void 0,(function*(){const{MetricUptMileComponent:t}=yield n.e(0).then(n.bind(null,"eEz1"));this._dialog.open(t,{width:"46%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{krDetail:e,objectiveId:this.objectiveid}}).afterClosed().subscribe(e=>{this.activeroute.parent.params.subscribe(e=>{this.objectiveid=e.id,this.getObjectiveDetails()})})})),this.openKrHistoryPopup=(e,t,i)=>Object(c.c)(this,void 0,void 0,(function*(){const{HistoryPopupComponent:o}=yield n.e(0).then(n.bind(null,"kXFY"));this._dialog.open(o,{width:"55%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{id:e,isKeyResult:t,isInitiative:i,hideSecondRow:!1}})})),this.deleteAlignedObjOrKr=(e,t)=>{this._util.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this will be deleted !").then(n=>Object(c.c)(this,void 0,void 0,(function*(){n&&(this.isAlignedObjLoading=!0,this.subs.sink=this._okr.deleteAlignedObjOrKr({parent_id:this.objectiveid,child_type:t,child_id:e}).subscribe(e=>{console.log(e),this.isAlignedObjLoading=!1,this.getAlignedObj()},e=>{console.error(e),this.isAlignedObjLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}))})))},this.openCommentBox=(e,t,i)=>Object(c.c)(this,void 0,void 0,(function*(){let o;"obj"==e?o={inputData:{application_id:143,application_name:"OKR",title:t.goal_details.goal_name,unique_id_1:t._id,unique_id_2:""},context:{Objective:t.goal_details.goal_name,Year:t.date_type},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"}:"kr"==e?o={inputData:{application_id:143,application_name:"OKR",title:i.goal_name,unique_id_1:t._id,unique_id_2:""},context:{"Key Result":i.goal_name,Year:t.date_type,Metric:t.metric_name,progress:t.progress_val},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"}:"init"==e&&(o={inputData:{application_id:143,application_name:"OKR",title:i.name,unique_id_1:i._id,unique_id_2:""},context:{Initiative:i.name,Objective:t.objective_goal_details.goal_name,"Start Date":i.start_date,"End Date":i.end_date},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"});const{ChatCommentContextModalComponent:r}=yield Promise.all([n.e(4),n.e(63),n.e(75),n.e(983)]).then(n.bind(null,"vg2w"));this._dialog.open(r,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:o}})}))}ngOnInit(){return Object(c.c)(this,void 0,void 0,(function*(){this._okr._userProfile.subscribe(e=>Object(c.c)(this,void 0,void 0,(function*(){console.log("User Profile"),console.log(e),this.userProfile=e}))),yield this.initObjectiveData(),this.getOpenNewTabConfig()}))}addNewTag(){this.addTagButton=!1,this.addTag=!0}select(e){let t=!0;console.log(e);for(let n=0;n<this.objtags.length;n++)if(e==this.objtags[n]){t=!1;break}t&&(this.objtags.push(e),this.updateTag(e)),this.addTag=!1,this.addTagButton=!0,this.objectivetags()}addTags(e){console.log(this.allTags);let t=!0;for(let n=0;n<this.objtags.length;n++)if(e.target.value==this.objtags[n]){t=!1;break}t&&(this.objtags.push(e.target.value),this.updateTag(e.target.value)),this.addTag=!1,this.addTagButton=!0,this.objectivetags()}updateTag(e){this.subs.sink=this._okr.updateTag({goal_id:this.objectivedetails.goal_details._id,tag:e}).subscribe(e=>{console.log("Tag Updated")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}tagsdisplay(){this.objtags=[];for(let e=0;e<this.objectivedetails.goal_details.goal_tag.length;e++)this.objtags.push(this.objectivedetails.goal_details.goal_tag[e]);this.objectivetags()}objectivetags(){this.tags=[],this.objtags.length<3?this.tags=this.objtags:(this.tags.push(this.objtags[0]),this.tags.push(this.objtags[1]))}removetag(e){const t=this.objtags.indexOf(e);t>=0&&(this.objtags.splice(t,1),this.objectivetags()),this.subs.sink=this._okr.removetags({goal_id:this.objectivedetails.goal_details._id,tag:e}).subscribe(e=>{console.log("Tag removed")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}krHistoryPopup(e,t){this.openKrHistoryPopup(e,!0,!1)}krselect(e,t,n){console.log(n),1==n?this.router.navigate(["/main/okr/kr",e,"KeyResult"]):window.location.href="/main/okr/kr/"+e+"/KeyResult"}initiativePageRoute(e,t,n){console.log(n),1==n?this.router.navigate(["/main/okr/initiative",e,"Initiative"]):window.open("/main/okr/initiative/"+e+"/Initiative","_blank")}kpiclick(){return Object(c.c)(this,void 0,void 0,(function*(){let e={goalResult:{data:{goal_name:this.objectivedetails.goal_details.goal_name}},objectiveResult:{data:{desc:this.objectivedetails.desc,_id:this.objectivedetails._id,start_date:this.objectivedetails.start_date,end_date:this.objectivedetails.end_date,owner_id:this.objectivedetails.owner_id}}};const{KrCreationComponent:t}=yield n.e(115).then(n.bind(null,"bIF9"));this._dialog.open(t,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":"withObj",data:e,name:"KPI"}}).afterClosed().subscribe(e=>{this.activeroute.parent.params.subscribe(e=>{this.objectiveid=e.id,this.getObjectiveDetails()})})}))}krclick(){return Object(c.c)(this,void 0,void 0,(function*(){let e={goalResult:{data:{goal_name:this.objectivedetails.goal_details.goal_name}},objectiveResult:{data:{desc:this.objectivedetails.desc,_id:this.objectivedetails._id,start_date:this.objectivedetails.start_date,end_date:this.objectivedetails.end_date,owner_id:this.objectivedetails.owner_id}}};const{KrCreationComponent:t}=yield n.e(115).then(n.bind(null,"bIF9"));this._dialog.open(t,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":"withObj",data:e,name:"Key Result"}}).afterClosed().subscribe(e=>{this.activeroute.parent.params.subscribe(e=>{this.objectiveid=e.id,this.getObjectiveDetails()})})}))}createInitiative(e,t,i){return Object(c.c)(this,void 0,void 0,(function*(){let e,i=this.objectivedetails;console.log(i),"obj"==t&&(e={name:i.goal_details.goal_name,desc:i.desc,_id:i._id,start_date:i.start_date,end_date:i.end_date,owner_id:i.owner_id}),console.log(i);const{InitiativeCreationComponent:o}=yield Promise.all([n.e(8),n.e(905)]).then(n.bind(null,"e+FK"));this._dialog.open(o,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":t,data:e,name:"Initiative"}}).afterClosed().subscribe(e=>{this.getObjectiveDetails()})}))}createBAU(e,t,i){return Object(c.c)(this,void 0,void 0,(function*(){let e,i=this.objectivedetails;console.log(i),"obj"==t&&(e={name:i.goal_details.goal_name,desc:i.desc,_id:i._id,start_date:i.start_date,end_date:i.end_date,owner_id:i.owner_id}),console.log(i);const{InitiativeCreationComponent:o}=yield Promise.all([n.e(8),n.e(905)]).then(n.bind(null,"e+FK"));this._dialog.open(o,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":t,data:e,name:"BAU",withoutObj:!1}}).afterClosed().subscribe(e=>{this.getObjectiveDetails()})}))}sliderChange(e,t){console.log(e,t),1==t.metric_id||2==t.metric_id?this.openValMetricPopUp(null==t?void 0:t.upd_val,t,!1,!0):4==t.metric_id?this.openMilMetricPopUp(t):5==t.metric_id&&this.openValMetricPopUp(0,t,!1,!1)}openInitMetricPopUp(e,t){this._initStatus.transform(t.status).then(n=>{t.status=n[0],this.openValMetricPopUp(e.value,t,!0,!0)})}getAlignObj(){this.subs.sink=this._okr.getAllObjForAlignment({parent_id:this.objectiveid}).subscribe(e=>{this.alignObjArr=[...e.ObjResult,...e.KrResult],console.log(e)},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}onAlignObjSelect(e){this.selectedAlignObj=e}alignObj(){if(this.isAlignedObjLoading=!0,this.selectedAlignObj){let e={parent_id:this.objectiveid,child_id:this.selectedAlignObj,parent_type:"objective",child_type:"objective"};console.log(e),this.subs.sink=this._okr.alignOKRUpd(e).subscribe(e=>{console.log(e),this.getAlignedObj(),this.isAlignedObjLoading=!1},e=>{console.log(e),this.isAlignedObjLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}),this.isAlignObjective=!1}}getAlignedObj(){this.isAlignedObjLoading=!0,this.subs.sink=this._okr.getAllAlignedObjAndKr({parent_id:this.objectiveid}).subscribe(e=>{this.alignedObjArr=e.ObjResult,this.alignedKrArr=e.KrResult,this.isAlignedObjLoading=!1,console.log(this.alignedObjArr)},e=>{console.log(e),this.isAlignedObjLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}searchAlignObj(){if(this.searchAlignedArr=[],this.searchAlignText&&""!=this.searchAlignText){this.isSearchingAlign=!0;for(let e of this.alignObjArr)e.goal_name.toLowerCase().includes(this.searchAlignText.toLowerCase())&&this.searchAlignedArr.push(e)}else this.isSearchingAlign=!1}ngOnDestroy(){this.subs.unsubscribe()}getObjUpdatedValue(e,t){"desc"==t?this.subs.sink=this._okr.updateObjDesc({objective_id:this.objectiveid,okr_name:this.objectivedetails.goal_details.goal_name,desc:e}).subscribe(e=>{console.log("Description successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):"name"==t&&(this.subs.sink=this._okr.updateObjName({objective_id:this.objectiveid,okr_name:this.objectivedetails.goal_details.goal_name,title:e}).subscribe(e=>{console.log("Name successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.code,"Some Error Happened in completing the Activity",e.errMessage)}))}getKrUpdatedValue(e,t,n){"desc"==t?this.subs.sink=this._okr.updateKrDesc({key_result_id:this.objectivedetails.key_results[n]._id,okr_name:this.objectivedetails.key_results[n].desc,desc:e}).subscribe(e=>{console.log("Description successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):"name"==t&&(this.subs.sink=this._okr.updateKrName({key_result_id:this.objectivedetails.key_results[n]._id,okr_name:this.objectivedetails.key_results[n].KR_goal_details.goal_name,title:e}).subscribe(e=>{console.log("Name successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.code,"Some Error Happened in completing the Activity",e.errMessage)}))}getInitUpdatedValue(e,t,n){"desc"==t?this.subs.sink=this._okr.updateInitDesc({initiative_id:this.objectivedetails.objective_initiative_details[n]._id,okr_name:this.objectivedetails.objective_initiative_details[n].name,desc:e}).subscribe(e=>{console.log("Description successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):"name"==t&&(this.subs.sink=this._okr.updateInitName({initiative_id:this.objectivedetails.objective_initiative_details[n]._id,okr_name:this.objectivedetails.objective_initiative_details[n].name,title:e}).subscribe(e=>{console.log("Name successfuly updated"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}))}getObjOwnerUpdatedValue(e){console.log(e),this.subs.sink=this._okr.updateObjOwner({objective_id:this.objectiveid,owner_id:e,okr_name:this.objectivedetails.goal_details.goal_name}).subscribe(e=>{console.log("owner updated successfully"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getKrOwnerUpdatedValue(e,t){console.log(e),this.subs.sink=this._okr.updateKrOwner({key_result_id:this.objectivedetails.key_results[t]._id,okr_name:this.objectivedetails.key_results[t].KR_goal_details.goal_name,owner_id:e}).subscribe(e=>{console.log("owner updated successfully"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getInitOwnerUpdatedValue(e,t){console.log(e),this.subs.sink=this._okr.updateInitOwner({initiative_id:this.objectivedetails.objective_initiative_details[t]._id,okr_name:this.objectivedetails.objective_initiative_details[t].name,owner_id:e}).subscribe(e=>{console.log("owner updated successfully"),this.ngOnInit()},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getWeightageUpdatedValue(e,t,n){"obj"==t?this.subs.sink=this._okr.updateObjWeightage({okr_name:this.objectivedetails.goal_details.goal_name,objective_id:this.objectiveid,weightage:e}).subscribe(e=>{console.log("Weightage Updated Successfully"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):"kr"==t?this.subs.sink=this._okr.updateKrWeightage({key_result_id:this.objectivedetails.key_results[n]._id,weightage:e}).subscribe(e=>{console.log("Weightage Updated Successfully"),this.ngOnInit()},e=>{console.log("Error")}):"init"==t&&(this.subs.sink=this._okr.updateInitWeightage({initiative_id:this.objectivedetails.objective_initiative_details[n]._id,weightage:e}).subscribe(e=>{console.log("Weightage Updated Successfully"),this.ngOnInit()},e=>{console.log("Error")}))}getObjVisibilityUpdatedValue(e){this.subs.sink=this._okr.updateObjVisibility({objective_id:this.objectiveid,visibility_id:this.objectivedetails.visibility_id,Org_id:e.code,Org_name:e.name}).subscribe(e=>{console.log("Visibility Updated Successfully"),this.ngOnInit()},e=>{console.log("error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getPeriods(){this.subs.sink=this._okr.getDurationTypesMaster().subscribe(e=>{console.log(e),this.periods=e.data})}getUptPeriod(e,t,n){"custom"==e.type?(this.startDate=p(e.startDate).format("YYYY-MM-DD"),this.endDate=p(e.endDate).format("YYYY-MM-DD")):(this.startDate=e.startDate,this.endDate=e.endDate),console.log(e),"obj"==t?this.subs.sink=this._okr.updateObjPeriod({objective_id:this.objectiveid,period_value:e.type,startDate:this.startDate,endDate:this.endDate}).subscribe(e=>{e&&e.data&&this.ngOnInit()},e=>{this._ErrorService.userErrorAlert("Error while updating Period Value",e)}):"kr"==t?this.subs.sink=this._okr.updateKRdate({kr_id:this.objectivedetails.key_results[n]._id,type:e.type,startDate:this.startDate,endDate:this.endDate}).subscribe(e=>{console.log("date successfully updated"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):"init"==t&&(this.subs.sink=this._okr.updateInitdate({init_id:this.objectivedetails.objective_initiative_details[n]._id,type:e.type,startDate:this.startDate,endDate:this.endDate}).subscribe(e=>{console.log("date successfully updated"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}))}getCoOwner(){return Object(c.c)(this,void 0,void 0,(function*(){let e=this.objectivedetails.co_owner;if(console.log(e),this.coOwnerList=[],null!=e)for(let t=0;t<e.length;t++){let n=yield this.accountService.getUserProfileFromDB(e[t]);this.coOwnerList.push(n[0])}console.log(this.coOwnerList)}))}getObjCoOwnerUpdatedValue(e){return Object(c.c)(this,void 0,void 0,(function*(){console.log(e);let t=[...new Set([...this.objectivedetails.co_owner,...e])];console.log(t),this.coOwnerList=[],this.subs.sink=this._okr.updateCoOwnerObj({obj_id:this.objectiveid,co_owner:t}).subscribe(e=>{this._util.showMessage("Objective CoOwner Updated Successfully","Dismiss"),this.ngOnInit()},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}removeEmp(e){console.log(e);let t=this.coOwnerList[e].id;this.coOwnerList.splice(e,1),console.log(t),this.subs.sink=this._okr.removeCoOwnerObj({obj_id:this.objectiveid,co_owner:t}).subscribe(e=>{this._util.showMessage("Objective CoOwner removed Successfully","Dismiss")},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}get token(){return this._loginService.getToken()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](u.a),r["\u0275\u0275directiveInject"](o.g),r["\u0275\u0275directiveInject"](o.a),r["\u0275\u0275directiveInject"](m.b),r["\u0275\u0275directiveInject"](g.a),r["\u0275\u0275directiveInject"](v.a),r["\u0275\u0275directiveInject"](b.a),r["\u0275\u0275directiveInject"](h.a),r["\u0275\u0275directiveInject"](f.a),r["\u0275\u0275directiveInject"](x.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-obj-detail-overview"]],decls:5,vars:3,consts:[[1,"mt-3","mx-4","position-relative"],[1,"bg-overlay"],["class","row p-3 obj-card",4,"ngIf"],["class","row my-2 py-3 px-4 obj-card",4,"ngIf"],["class","row pt-1",4,"ngIf"],[1,"row","p-3","obj-card"],[1,"row","w-100","d-flex","align-items-center"],[1,"col-7","pl-4"],[1,"overflow-ctrl",3,"matTooltip"],[4,"okrAuth"],[1,"col-4","d-flex","align-items-center"],[1,"chips","d-flex"],[3,"removable","removed",4,"ngFor","ngForOf"],["class","pl-3 pt-1","style","font-weight: 500; color: grey; font-size: 14px",4,"ngIf"],[1,"col-1"],["mat-icon-button","","matTooltip","comments",1,"float-right","small-icons-obj",3,"click"],[1,"row","w-100","mt-2"],[1,"col-9","pl-4"],[1,"row","overflow-ctrl",3,"matTooltip"],["label","Objective Description","type","textArea",3,"inputText","cssData","uptValChange",4,"okrAuth"],[1,"row","mt-4","d-flex","w-100","align-items-center"],[1,"col-3","p-0","overflow-ctrl"],[1,"d-inline-block","sub-title",2,"width","50px"],["label","Target","marginInbetween","3rem",3,"selectedPeriod","periods","viewPortMargin","uptPeriod",4,"okrAuth"],[1,"col-3","d-flex","overflow-ctrl"],[1,"sub-title"],["class","p-0",4,"okrAuth"],[3,"id","uptValChange",4,"okrAuth"],[1,"col-6","pl-4","pr-0","d-flex","align-items-center"],[1,"col-8","p-0","overflow-ctrl"],["class","col-3 p-0",4,"ngIf"],[1,"row","mb-2","mt-3","d-flex","w-100","align-items-center"],[1,"col-3","pl-0","d-flex","overflow-ctrl"],["class","col-2 pl-4 d-flex",4,"okrAuth"],["class","col-2 d-flex overflow-ctrl",4,"okrAuth"],[1,"col-3","align-items-center"],[1,"row"],[1,"mx-auto","my-spinner"],[1,"position-relative","org-spinner"],["mode","determinate","strokeWidth","7",1,"align-content-center",3,"diameter","value"],[2,"position","absolute","color","#ff7200","top","50%","left","50%","transform","translate(-50%, -50%)","font-size","20px","font-weight","900"],[2,"padding-left","2px"],[1,"dummy-spinner"],["mode","determinate","strokeWidth","7",1,"align-content-center",2,"top","5rem",3,"diameter","value"],[1,"obj-name"],["label","Objective Name","type","text",3,"inputText","cssData","uptValChange"],[3,"removable","removed"],["matChipRemove","",4,"ngIf"],["matChipRemove",""],[1,"pl-3","pt-1",2,"font-weight","500","color","grey","font-size","14px"],["label","Objective Description","type","textArea",3,"inputText","cssData","uptValChange"],["label","Target","marginInbetween","3rem",3,"selectedPeriod","periods","viewPortMargin","uptPeriod"],[1,"p-0"],[1,"pl-2"],["imgWidth","25px","imgHeight","25px",3,"id"],[3,"type","oid"],[3,"id","uptValChange"],[4,"ngIf"],[1,"pl-2",2,"font-weight","500"],[3,"id","coOwnerList","name","uptValChange"],[1,"col-3","p-0"],["mat-icon-button","",3,"matMenuTriggerFor"],[2,"vertical-align","text-bottom"],["yPosition","below"],["menu","matMenu"],[1,"row",2,"height","20rem",3,"click"],[1,"col-12","p-0"],[1,"d-flex","justify-content-center","mt-3","mb-3","menu-title"],["class","row mt-2 mb-2 pb-2 border-bottom solid",4,"ngFor","ngForOf"],[1,"row","mt-2","mb-2","pb-2","border-bottom","solid"],[1,"col-3","pr-0"],[3,"id"],[1,"col-7","pl-3"],[1,"col-12","pl-0","overflow-ctrl",3,"matTooltip"],[1,"col-12","pl-0","overflow-ctrl"],[3,"oid","type"],[1,"col-2","p-0"],["mat-icon-button","","style","color: gray","matTooltip","remove",3,"click",4,"okrAuth"],["mat-icon-button","","matTooltip","remove",2,"color","gray",3,"click"],[3,"matTooltip"],[1,"ml-3",2,"font-weight","500"],[3,"matTooltip",4,"ngIf"],[3,"inputName","uptValChange"],[1,"sub-title","pr-3"],["style","font-weight: 500",3,"matTooltip",4,"ngIf"],["matTooltip","Weightage - 0",4,"ngIf"],[2,"font-weight","500",3,"matTooltip"],["matTooltip","Weightage - 0"],["label"," Weightage","type","weightage",3,"inputText","viewPort","cssData","uptValChange"],[1,"col-2","pl-4","d-flex"],["matTooltip","Planned Score - 0",4,"ngIf"],["matTooltip","Planned Score - 0"],[1,"col-2","d-flex","overflow-ctrl"],["matTooltip","Actual Score - 0",4,"ngIf"],["matTooltip","Actual Score - 0"],["matTooltip","Total Score - 0",4,"ngIf"],["matTooltip","Total Score - 0"],[1,"row","my-2","py-3","px-4","obj-card"],[1,"row","w-100","p-0"],[1,"abs-icon","abs-colab"],[1,"overflow-ctrl"],[2,"font-weight","500"],["class","col-12 p-0",4,"ngFor","ngForOf"],[1,"row","w-100","p-0","pt-1","d-flex"],[1,"pl-1",2,"color","gray","font-weight","500"],[1,"overflow-ctrl","pl-2",2,"font-weight","500",3,"matTooltip"],[1,"row","w-100","pt-1","d-flex"],[1,"pl-3",2,"color","gray","font-weight","500"],[1,"pl-2",2,"font-weight","500",3,"matTooltip"],[1,"row","pt-1"],[1,"col-12","pl-4","kr-list"],[1,"ml-3","pb-2","mt-2"],["class","row d-flex align-items-center-2 py-3 mb-3 kr-card",3,"border-left-color",4,"ngFor","ngForOf"],["style","font-weight: 500",4,"ngIf"],["class","row d-flex px-2 py-3 mb-3 kr-card",3,"border-left-color",4,"ngFor","ngForOf"],["mat-icon-button","","class","ml-3 small-icons-kr","matTooltip","Add New KeyResult",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Add New KeyResult",1,"ml-3","small-icons-kr",3,"click"],[1,"row","d-flex","align-items-center-2","py-3","mb-3","kr-card"],[1,"position-absolute",2,"left","-21px","bottom","37px"],["vertical","",2,"height","135px","border-right-width","2px"],[1,"position-absolute",2,"color","#ddd8ce","left","-20px","bottom","33px"],[1,"row","w-100","d-flex","align-items-center",2,"height","48px"],[1,"col-7","pl-2","overflow-ctrl",3,"matTooltip"],[1,"col-2"],["mat-icon-button","","matTooltip","Go to KeyResult",1,"ml-3","small-icons-kr",3,"click"],["mat-icon-button","","matTooltip","History",1,"ml-3","small-icons-kr",3,"click"],[1,"row","w-100","mt-2","d-flex","align-items-center"],[1,"col-1","ml-2","pl-0","metrics-icon"],["mat-icon-button","",1,"metric-status"],[1,"col-4","pl-2","pr-0"],[1,"col-2","p-0","sub-title"],[1,"col-6","pl-0"],[1,"ml-2"],[1,"col-5","d-flex","p-0","m-0"],[1,"col-8","p-0","m-0","overflow-ctrl"],[1,"col-4","p-0","m-0","d-flex","overflow-ctrl"],[1,"col-2","p-0","overflow-ctrl"],[2,"font-weight","100"],[3,"matTooltip",4,"ngIf","ngIfElse"],["matTooltip","No updates"],["temp",""],[1,"overflow-ctrl",2,"font-size","16px","font-weight","500"],["label","Key Result Name","type","text",3,"inputText","cssData","uptValChange"],[1,"col-3","d-flex","align-items-center","my-slider"],[1,"pl-4"],[1,"d-flex","align-items-center"],[1,"init-status-icon"],[3,"value","change"],[3,"token","data","selectedVal"],[1,"d-flex","align-items-center",2,"cursor","pointer",3,"click"],[1,"sub-title","pr-2"],["mat-icon-button","","class","ml-3 small-icons-kr","matTooltip","Add New Initiative",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Add New Initiative",1,"ml-3","small-icons-kr",3,"click"],[1,"row","d-flex","px-2","py-3","mb-3","kr-card"],["vertical","",2,"height","155px","border-right-width","2px"],["mat-icon-button","","matTooltip","Go to Initiative",1,"ml-3","small-icons-kr",3,"click"],[1,"row","w-100","mt-2","d-flex"],[1,"col-1","ml-2","pl-0","metrics-icon",2,"background","gray"],[1,"col-6","pl-3"],[4,"ngIf","ngIfElse"],["label","Initiative Name","type","text",3,"inputText","cssData","uptValChange"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275element"](1,"div",1),r["\u0275\u0275template"](2,Be,61,64,"div",2),r["\u0275\u0275template"](3,Re,11,1,"div",3),r["\u0275\u0275template"](4,dn,13,16,"div",4),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.objectivedetails),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.objectivedetails&&(null==t.objectivedetails?null:t.objectivedetails.is_collaborative)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.objectivedetails))},directives:[i.NgIf,_.a,j.a,w.c,i.NgForOf,y.a,C.a,E.a,S.a,w.a,w.d,O.a,k.a,I.a,A.a,M.a,P.f,P.g,V.a,T.a,F.a,D.a],pipes:[i.DecimalPipe,i.TitleCasePipe,i.DatePipe],styles:['.kr-card[_ngcontent-%COMP%], .obj-card[_ngcontent-%COMP%]{position:relative;background-color:#fff;border-radius:3px;box-shadow:0 1px 2px 0 #ccc}.kr-card[_ngcontent-%COMP%]{border-left:5px solid}.bg-overlay[_ngcontent-%COMP%]{position:absolute;width:calc(100% + 3.5rem);height:37%;background:#fcf6ea;left:-2rem;top:-2rem}.sub-title[_ngcontent-%COMP%]{color:#777;font-weight:500}.small-icons-obj[_ngcontent-%COMP%]{height:20px;width:20px;line-height:10px}.small-icons-obj[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:grey;font-size:16px;line-height:20px}.small-icons-kr[_ngcontent-%COMP%]{height:20px;width:20px;line-height:10px}.small-icons-kr[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:grey;font-size:18px;line-height:20px}.small-icons-tags[_ngcontent-%COMP%]{height:20px;width:20px}.small-icons-tags[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:grey;font-size:18px;line-height:15px}.metric-status[_ngcontent-%COMP%]{color:#f5f5f5;height:25px;width:25px;line-height:10px}.init-status-icon[_ngcontent-%COMP%], .metric-status[_ngcontent-%COMP%]{box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.init-status-icon[_ngcontent-%COMP%]{height:14px;width:14px;border-radius:50%}.my-spinner[_ngcontent-%COMP%]   .org-spinner[_ngcontent-%COMP%]{z-index:2}.my-spinner[_ngcontent-%COMP%]   .dummy-spinner[_ngcontent-%COMP%]{top:-5rem;position:absolute;z-index:1}.mat-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%], .org-spinner[_ngcontent-%COMP%]     .mat-progress-spinner circle{stroke:#ffa202}.dummy-spinner[_ngcontent-%COMP%]     .mat-progress-spinner circle, .mat-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%]{stroke:#f8d492}.my-slider[_ngcontent-%COMP%]   mat-slider[_ngcontent-%COMP%]{width:20%;max-width:40px}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-track-wrapper{border-radius:2px!important;height:5px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-track-background{height:5px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-wrapper{height:12px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-thumb{height:21px;width:20px;bottom:-8px!important;border:none!important;transform:none!important;transition:none!important}.my-slider[_ngcontent-%COMP%]     .mat-slider-horizontal .mat-slider-track-fill{height:8px!important}.my-slider[_ngcontent-%COMP%]     .mat-slider.mat-slider-horizontal .mat-slider-track-fill{background-color:#ffa202}.my-slider[_ngcontent-%COMP%]     .mat-slider-track-background{background-color:#f8d492}.my-slider[_ngcontent-%COMP%]     .mat-accent .mat-slider-thumb{background-color:#ffa202}.alignment-card[_ngcontent-%COMP%]{height:340px;background-color:#fff;position:relative;border-radius:3px;box-shadow:0 1px 2px 0 #ccc}.alignment-card[_ngcontent-%COMP%]     .mat-stroked-button{border-color:red;color:red;font-weight:400;font-size:12px}.chips[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%]{background-color:#000!important}.chips[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%], .chips[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff!important}.kr-list[_ngcontent-%COMP%]{height:calc(100vh - 285px);overflow:auto}.kr-list[_ngcontent-%COMP%]::-webkit-scroll{display:none}.metrics-icon[_ngcontent-%COMP%]{height:25px;max-width:25px;border-radius:50%}.overflow-ctrl[_ngcontent-%COMP%]{width:85%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.alignobj-wrapper[_ngcontent-%COMP%]{z-index:10;position:absolute;top:0;left:-18.1rem;box-shadow:0 0 3px 0 #ccc;width:360px;background-color:#fff}.alignobj-wrapper[_ngcontent-%COMP%]:before{border:13px solid transparent;border-left-color:#ccc}.alignobj-wrapper[_ngcontent-%COMP%]:after, .alignobj-wrapper[_ngcontent-%COMP%]:before{content:"";display:block;position:absolute;left:100%;top:50%;width:0;height:0}.alignobj-wrapper[_ngcontent-%COMP%]:after{border:12px solid transparent;border-left-color:#fff}.alignobj-wrapper[_ngcontent-%COMP%]   .obj-wrapper[_ngcontent-%COMP%]{max-height:180px;overflow:auto}.alignobj-wrapper[_ngcontent-%COMP%]   .obj-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.alignobj-wrapper[_ngcontent-%COMP%]   .obj-wrapper[_ngcontent-%COMP%]   .obj[_ngcontent-%COMP%]{border-bottom:1px solid #ccc}.alignobj-wrapper[_ngcontent-%COMP%]   .obj-wrapper[_ngcontent-%COMP%]   .obj[_ngcontent-%COMP%]:last-child{border-bottom:0}.red[_ngcontent-%COMP%]{color:#cf0000}.background-gray[_ngcontent-%COMP%]{background:#efefef}.aligned-obj-wrapper[_ngcontent-%COMP%]{max-height:300px;overflow:auto;background-color:#fff}.aligned-obj-wrapper[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.aligned-obj-wrapper[_ngcontent-%COMP%]   .obj[_ngcontent-%COMP%]{border-bottom:1px solid #ccc}.aligned-obj-wrapper[_ngcontent-%COMP%]   .obj[_ngcontent-%COMP%]:last-child{border-bottom:0}.tags[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:25px;border-radius:4px!important;background-color:#babfc4!important}.abs-icon[_ngcontent-%COMP%]{position:absolute;left:-10px;bottom:5%;height:25px;width:25px;border-radius:50%;text-align:center}.abs-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#f5f5f5;line-height:25px;font-size:18px}.abs-colab[_ngcontent-%COMP%]{height:23px;width:23px;background:#0077b8;bottom:50%;transform:translateY(50%)}.abs-colab[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{line-height:22px!important;color:#f5f5f5;font-size:18px!important}.abs-aggre[_ngcontent-%COMP%]{height:23px;width:23px;background:#9370db;bottom:50%;transform:translateY(50%)}.abs-aggre[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{line-height:23px!important;color:#f5f5f5;font-size:16px!important}.obj-name[_ngcontent-%COMP%]{font-weight:500;font-size:18px;color:"black"}']}),e})();var un=n("u0xZ");let mn=(()=>{class e{constructor(e,t,n){this.UtilityService=e,this.route=t,this._okr=n,this.fileArray=[],this.urls={uploadUrl:"/api/cta/uploadFile",downloadUrl:"api/cta/downloadFile"}}ngOnInit(){this.route.parent.params.subscribe(e=>Object(c.c)(this,void 0,void 0,(function*(){console.log(e),this.objectiveId=e.id}))),this._okr.getObjAttachment({id:this.objectiveId}).subscribe(e=>{null!=e.data.attachments&&(console.log(e.data.attachments),this.fileArray=e.data.attachments);for(let t=0;t<this.fileArray.length;t++){let e=this.fileArray[t].fileName.split("(");this.fileArray[t].name=e[0],this.fileArray[t].deleteInProgress=!1,this.fileArray[t].size=(this.fileArray[t].size/1024).toFixed(2)+"KB"}},e=>{console.error(e)})}updateObjAttachment(e){this._okr.addObjAttachment({id:this.objectiveId,file:e}).subscribe(e=>{if(console.log(e),null!=e.data.attachments){this.fileArray=[];let t=e.data.attachments;for(let e=0;e<t.length;e++){t[e].deleteInProgress=!1;let n=t[e].fileName.split("(");t[e].name=n[0],t[e].size=(t[e].size/1024).toFixed(2)+"KB",this.fileArray.push(t[e])}this.UtilityService.showMessage("Uploaded Successfully","Dismiss",3e3)}},e=>{console.error(e)})}deleteObjAttachment(e){this._okr.deleteObjAttachment({id:this.objectiveId,file:e}).subscribe(e=>{if(console.log(e),null!=e.data.attachments){this.fileArray=[];let t=e.data.attachments;for(let e=0;e<t.length;e++){t[e].deleteInProgress=!1;let n=t[e].fileName.split("(");t[e].name=n[0],t[e].size=(t[e].size/1024).toFixed(2)+"KB",this.fileArray.push(t[e])}}else this.fileArray=[];this.UtilityService.showMessage("Deleted Successfully","Dismiss",3e3)},e=>{console.error(e)})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](v.a),r["\u0275\u0275directiveInject"](o.a),r["\u0275\u0275directiveInject"](u.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-obj-detail-attachments"]],decls:1,vars:2,consts:[[3,"fileArray","urls","updateFileInApp","deleteFileInApp"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"app-s3-attachment",0),r["\u0275\u0275listener"]("updateFileInApp",(function(e){return t.updateObjAttachment(e)}))("deleteFileInApp",(function(e){return t.deleteObjAttachment(e)})),r["\u0275\u0275elementEnd"]()),2&e&&r["\u0275\u0275property"]("fileArray",t.fileArray)("urls",t.urls)},directives:[un.a],styles:[""]}),e})();var gn=n("a1z7");const vn=[{path:"",component:s,children:[{path:"",redirectTo:"overview",pathMatch:"full"},{path:"overview",component:pn,data:{breadcrumb:"Overview"}},{path:"attachment",component:mn,data:{breadcrumb:"Attachments"}},{path:"history",component:(()=>{class e{constructor(e){this._activatedRoute=e}ngOnInit(){this._activatedRoute.parent.params.subscribe(e=>{this.objectiveId=e.id})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-obj-detail-history"]],decls:2,vars:1,consts:[[1,"ml-3"],[3,"objectiveId"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275element"](1,"app-okr-history",1),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("objectiveId",t.objectiveId))},directives:[gn.a],styles:[""]}),e})(),data:{breadcrumb:"History"}}]}];let bn=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(vn)],o.k]}),e})();var hn=n("3Pt+"),fn=n("3PA3"),xn=n("Xi0T"),_n=n("kmnG"),jn=n("qFsG"),wn=n("d3UM"),yn=n("N+00"),Cn=n("Zg/p"),En=n("rDax"),Sn=n("vVoB");let On=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,bn,l.g,m.g,C.b,E.b,D.b,w.e,y.b,F.b,T.b,hn.p,fn.a,yn.a,xn.a,_n.e,jn.c,_.b,Cn.a,wn.d,S.b,En.h,hn.E,O.b,P.e,Sn.a]]}),e})()}}]);