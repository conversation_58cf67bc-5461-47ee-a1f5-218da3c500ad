(window.webpackJsonp=window.webpackJsonp||[]).push([[1020,634,858,987,990,991],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},SSbP:function(e,t,n){"use strict";n.r(t),n.d(t,"WfhModule",(function(){return _t}));var i=n("ofXK"),r=n("NFeN"),o=n("Wp6s"),a=n("XhcP"),l=n("xHqg"),s=n("kmnG"),c=n("qFsG"),d=n("0IaG"),m=n("bTqV"),p=n("/1cH"),h=n("d3UM"),u=n("bSwM"),f=n("f0Cb"),g=n("dNgK"),v=n("lVl8"),x=n("Xa2L"),w=n("Qu3c"),y=n("tyNb"),S=n("mrSG"),b=n("fXoL");function C(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",24),b["\u0275\u0275elementStart"](1,"div",25),b["\u0275\u0275element"](2,"div",26),b["\u0275\u0275elementStart"](3,"span",27),b["\u0275\u0275text"](4),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275advance"](4),b["\u0275\u0275textInterpolate"](e.name)}}let E=(()=>{class e{constructor(e){this.dialogRef=e,this.checklist=[{name:"Office set up at home "},{name:"Stable and reliable internet connection"},{name:"Noise free background"},{name:"Office Laptop in good condition"},{name:"Sufficient power and network  back up"},{name:"Laptop Camera Working for Video Calls"}],this.closeDialog=()=>{this.dialogRef.close()}}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](d.h))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-info"]],decls:61,vars:1,consts:[[1,"container-fluid","wfhInfoContainer"],[1,"row","pt-2","mt-1","pb-2"],[1,"col-11",2,"font-weight","500","font-size","16px","line-height","1.8"],[1,"col-1"],["mat-icon-button","",1,"ml-auto",3,"click"],[1,"iconButton"],[1,"row","pt-2"],[1,"col-8","p-0"],["class","row",4,"ngFor","ngForOf"],[1,"row","pt-3"],[1,"ml-3",2,"font-weight","500","font-size","16px"],[1,"ml-1",2,"color","#cf0001","font-weight","500","font-size","16px"],[1,"ml-3",2,"font-size","14px","line-height","1.8"],[1,"row","pt-3","pl-3",2,"font-weight","500","font-size","16px"],[1,"row","pt-2","pl-3"],[1,"wfh-flow-text"],[1,"row","pt-3","pl-3"],[2,"display","flex","font-size","14px","font-weight","500"],[1,"status-circular-approved","mr-2"],[1,"pl-1"],[1,"status-circular-wfh","mr-2"],[1,"status-circular-rejected","mr-2"],[1,"status-circular-cancelled","mr-2"],[1,"status-circular-recalled","mr-2"],[1,"row"],[1,"col-6","pb-3","d-flex"],[1,"status-circular"],[1,"ml-3",2,"font-weight","500","font-size","14px"]],template:function(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",2),b["\u0275\u0275elementStart"](3,"span"),b["\u0275\u0275text"](4," Work from home describes work being done remotely , instead of at an office. Remote WorX is used as a nickname for the concept . To implement a successful Remote WorX culture employees are requested to ensure the following checklists: "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"div",3),b["\u0275\u0275elementStart"](6,"span"),b["\u0275\u0275elementStart"](7,"button",4),b["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),b["\u0275\u0275elementStart"](8,"mat-icon",5),b["\u0275\u0275text"](9,"clear"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"div",6),b["\u0275\u0275elementStart"](11,"div",7),b["\u0275\u0275template"](12,C,5,1,"div",8),b["\u0275\u0275elementStart"](13,"div",9),b["\u0275\u0275elementStart"](14,"span",10),b["\u0275\u0275text"](15,"Maximum Work from Home duration is "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](16,"span",11),b["\u0275\u0275text"](17,"1 Year "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](18,"div",6),b["\u0275\u0275elementStart"](19,"span",12),b["\u0275\u0275text"](20,"Approver has full control on cancelling Active requests. Employee cannot edit or delete Remote WorX request once it is approved , but they can cancel the active Remote WorX request anytime."),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](21,"div",13),b["\u0275\u0275text"](22," Remote WorX flow "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](23,"div",14),b["\u0275\u0275elementStart"](24,"span",15),b["\u0275\u0275text"](25,'Once you apply Work from Home, depending on your manager\'s decision the status will be changed to "Approved / Rejected" '),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](26,"div",16),b["\u0275\u0275elementStart"](27,"div",17),b["\u0275\u0275element"](28,"div",18),b["\u0275\u0275text"](29," Approved "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](30,"div",19),b["\u0275\u0275elementStart"](31,"span",15),b["\u0275\u0275text"](32," - Your Remote WorX request is approved ! \ud83d\ude00 "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](33,"div",16),b["\u0275\u0275elementStart"](34,"div",17),b["\u0275\u0275element"](35,"div",20),b["\u0275\u0275text"](36," Working from home "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](37,"div",19),b["\u0275\u0275elementStart"](38,"span",15),b["\u0275\u0275text"](39,' - If approved, the request status will be automatically moved to "Working from home" depending on requests start date '),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](40,"div",16),b["\u0275\u0275elementStart"](41,"div",17),b["\u0275\u0275element"](42,"div",21),b["\u0275\u0275text"](43," Rejected "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](44,"div",19),b["\u0275\u0275elementStart"](45,"span",15),b["\u0275\u0275text"](46,' - If rejected, the request status will be moved to "Rejected" and you cannot perform any operations on it '),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](47,"div",16),b["\u0275\u0275elementStart"](48,"div",17),b["\u0275\u0275element"](49,"div",22),b["\u0275\u0275text"](50," Cancelled "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](51,"div",19),b["\u0275\u0275elementStart"](52,"span",15),b["\u0275\u0275text"](53,' - If the initiator / Manager cancels the active request, request status will be moved to "Cancelled". Time to report to office !! \ud83d\udcbc '),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](54,"div",16),b["\u0275\u0275elementStart"](55,"div",17),b["\u0275\u0275element"](56,"div",23),b["\u0275\u0275text"](57," Recalled "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](58,"div",19),b["\u0275\u0275elementStart"](59,"span",15),b["\u0275\u0275text"](60," - If you want to edit the submitted request, the request should be recalled. "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e&&(b["\u0275\u0275advance"](12),b["\u0275\u0275property"]("ngForOf",t.checklist))},directives:[m.a,r.a,i.NgForOf],styles:[".wfhInfoContainer[_ngcontent-%COMP%]{background-image:url(WFH-info-bg.2c842583837b50278473.png);background-size:383px 272px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:90% 21%;min-height:68vh}.wfhInfoContainer[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{height:35px;width:35px;line-height:35px}.wfhInfoContainer[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:17px}.wfhInfoContainer[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{background-color:#cf0001}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%], .wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-approved[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-approved[_ngcontent-%COMP%]{background-color:#009432}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-rejected[_ngcontent-%COMP%]{background-color:#cf0001}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-rejected[_ngcontent-%COMP%], .wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-wfh[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-wfh[_ngcontent-%COMP%]{background-color:#9980fa}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-cancelled[_ngcontent-%COMP%]{background-color:#9f2825}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-cancelled[_ngcontent-%COMP%], .wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-recalled[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.wfhInfoContainer[_ngcontent-%COMP%]   .status-circular-recalled[_ngcontent-%COMP%]{background-color:#adadad}.wfhInfoContainer[_ngcontent-%COMP%]   .wfh-flow-text[_ngcontent-%COMP%]{vertical-align:text-bottom;font-size:14px;font-weight:400;color:#000}"]}),e})();var _=n("3Pt+"),D=n("xG9w"),k=n("wd/R"),O=n("7pIB"),I=n("iadO"),P=n("Xi0T"),M=n("JqCM"),F=n("F97M");let W=(()=>{class e{constructor(e){this.graphApi=e,this.imgSrc="assets/images/User.png",this.imageList={},this.imgWidth="37px",this.imgHeight="37px",this.borderWidth="0px",this.borderStyle="solid",this.borderColor="#000000"}ngOnInit(){}ngOnChanges(){return Object(S.c)(this,void 0,void 0,(function*(){try{this.id?this.imageList[this.id]?this.imgSrc=this.imageList[this.id]:(this.imgSrc=yield this.graphApi.getUserImage(this.id),this.imageList[this.id]=this.imgSrc):this.imgSrc="assets/images/User.png"}catch(e){this.imgSrc="assets/images/User.png",this.imageList[this.id]=this.imgSrc}}))}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](F.a))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-approver-image"]],inputs:{id:"id",imgWidth:"imgWidth",imgHeight:"imgHeight",borderWidth:"borderWidth",borderStyle:"borderStyle",borderColor:"borderColor"},features:[b["\u0275\u0275NgOnChangesFeature"]],decls:1,vars:11,consts:[[3,"src"]],template:function(e,t){1&e&&b["\u0275\u0275element"](0,"img",0),2&e&&(b["\u0275\u0275styleProp"]("height",t.imgHeight)("width",t.imgWidth)("border-width",t.borderWidth)("border-style",t.borderStyle)("border-color",t.borderColor),b["\u0275\u0275property"]("src",t.imgSrc,b["\u0275\u0275sanitizeUrl"]))},styles:["img[_ngcontent-%COMP%]{border-radius:50%!important}"]}),e})();var L=n("XXEo"),R=n("2Vo4"),A=n("tk/3"),N=n("flaP"),T=n("ucYs"),q=n("LcQX"),j=n("BVzC");let H=(()=>{class e{constructor(e,t,n,i,r){this.http=e,this.roleService=t,this.workflowService=n,this.utilityService=i,this.errorService=r,this.wfhStatusList=[],this._createWfhMode$=new R.a(""),this.createWfhMode=this._createWfhMode$.asObservable(),this._editWfhId$=new R.a(""),this.editWfhId=this._editWfhId$.asObservable(),this.getEditWfhId=()=>Object(S.c)(this,void 0,void 0,(function*(){return this._editWfhId$.value})),this.setEditWfhId=e=>Object(S.c)(this,void 0,void 0,(function*(){this._editWfhId$.next(e)})),this._wfhList$=new R.a([]),this.wfhList=this._wfhList$.asObservable(),this.getWfhList=()=>this._wfhList$.value,this.setWfhList=e=>{this._wfhList$.next(e)},this.refreshWfhList=()=>Object(S.c)(this,void 0,void 0,(function*(){let e=this.roleService.userProfile.oid,t=yield this.getWfhListByAssociateOid(e);t="string"==typeof t?JSON.parse(t):t,this.setWfhList(t)})),this._wfProperties$=new R.a([]),this.wfProperties=this._wfProperties$.asObservable(),this.getWfProperties=()=>this._wfProperties$.value,this.saveWfhRequest=(e,t)=>this.http.post("/api/ts_wfh/saveWfhRequest",{formDetails:e,mode:t}),this.updateWfhRequest=e=>this.http.post("/api/ts_wfh/updateWfhRequest",{formDetails:e}),this.getWfhApproversHierarchy=e=>this.http.post("/api/wfPrimary/getApproversHierarchy",e),this.getWfhCheckList=()=>this.http.post("/api/ts_wfh/getWfhChecklist",{}),this.recallWfh=e=>Object(S.c)(this,void 0,void 0,(function*(){let t=k(e.end_date).diff(k(e.start_date),"days"),n=yield this.filterWfPropertiesByValue(t);if(n){let t={userOId:this.roleService.userProfile.oid,workflowHeaderId:e.workflow_header_id,aggregationFlag:n.aggregation_allowed,status:"RL",comments:[]},i=yield Promise.all([this.workflowService.updateWorkflowItems(t),this.http.post("/api/ts_wfh/recallWfhRequest",{id:e.id}).toPromise()]);return this.refreshWfhList(),i}return this.utilityService.showMessage("No Workflow property found. Contact KEBS Team","Dismiss"),""})),this.cancelWfh=e=>Object(S.c)(this,void 0,void 0,(function*(){let t=yield this.http.post("/api/ts_wfh/cancelWfhRequest",{id:e.id}).toPromise();return this.refreshWfhList(),t})),this.completeWfh=e=>Object(S.c)(this,void 0,void 0,(function*(){let t=yield this.http.post("/api/ts_wfh/completeWfhRequest",{id:e.id,new_end_date:k().utc().add(k().utcOffset(),"minutes")}).toPromise();return this.refreshWfhList(),t})),this.searchWfhList=e=>Object(S.c)(this,void 0,void 0,(function*(){let t=yield this.http.post("/api/ts_wfh/getWfhSearchList",{associate_oid:this.roleService.userProfile.oid,search_text:e}).toPromise();return"S"==t.messType&&this.setWfhList(t.messData),t.messData})),this.getWfhStatus=()=>Object(S.c)(this,void 0,void 0,(function*(){if(this.wfhStatusList&&this.wfhStatusList.length>0)return this.wfhStatusList;{let e=yield this.http.post("/api/ts_wfh/getWfhStatusList",{}).toPromise();return this.wfhStatusList=e,e}})),this.getImageFromS3=e=>new Promise((t,n)=>{this.http.post("/api/ts_wfh/getImageFromS3",{keyName:e}).subscribe(e=>t(e),e=>n(e))}),this.deleteImageFromS3=e=>this.http.post("/api/ts_wfh/deleteImageFromS3",{files:e}),this.getWorkflowPropertiesByAppId()}getCreateWfhMode(){return this._createWfhMode$.value}setCreateWfhMode(e){this._createWfhMode$.next(e)}setWfProperties(e){this._wfProperties$.next(e)}filterWfPropertiesByValue(e){let t=this.getWfProperties();return t&&t.length>0?D.find(t,t=>{let n=Number(t.max_value)-Number(t.min_value);return e<=n}):""}getWfhListByAssociateOid(e){return Object(S.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>{this.http.post("/api/ts_wfh/getWfhList",{associate_oid:e}).subscribe(e=>e&&"S"===e.messType?t(e.messData):(console.log(e),t([])),e=>(console.log(e),n(e)))})}))}getWorkflowPropertiesByWorkflowId(){return this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:56})}getWfhDetailById(e){return Object(S.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>{this.http.post("/api/ts_wfh/getWfhDetail",{id:e}).subscribe(e=>e&&"S"===e.messType&&e.messData.length>0?t(e.messData[0]):(console.log(e),t({})),e=>(console.log(e),n(e)))})}))}isWFHRequestPresent(e){return Object(S.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>{this.http.post("/api/ts_wfh/isWFHRequestPresent",{associate_oid:e}).subscribe(e=>e&&"S"===e.messType?t(e.result):(console.log(e),t([])),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while checking WFH Request is Present",e&&e.params?e.params:e&&e.error?e.error.params:{}),n(e)))})}))}getWorkflowPropertiesByAppId(){return Object(S.c)(this,void 0,void 0,(function*(){let e=yield this.workflowService.getWorkflowProperties(83);return e&&"S"==e.messType?(this.setWfProperties(e.data),e.data):[]}))}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275inject"](A.c),b["\u0275\u0275inject"](N.a),b["\u0275\u0275inject"](T.a),b["\u0275\u0275inject"](q.a),b["\u0275\u0275inject"](j.a))},e.\u0275prov=b["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var B=n("rq16"),V=n("qFYv");const z=["stepper"];function J(e,t){1&e&&b["\u0275\u0275text"](0,"General")}function Y(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Pincode"))}}function X(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"]("",e.label," is required. Please select from the drop down search.")}}function G(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",102),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](2,"div",102),b["\u0275\u0275text"](3),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",102),b["\u0275\u0275text"](5),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]().$implicit;b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.name," "),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"](" ",e.role," "),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function U(e,t){if(1&e&&(b["\u0275\u0275elementContainerStart"](0),b["\u0275\u0275element"](1,"app-approver-image",100),b["\u0275\u0275template"](2,G,6,3,"ng-template",null,101,b["\u0275\u0275templateRefExtractor"]),b["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=b["\u0275\u0275reference"](3);b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function $(e,t){if(1&e&&(b["\u0275\u0275elementContainerStart"](0),b["\u0275\u0275template"](1,U,4,2,"ng-container",99),b["\u0275\u0275elementContainerEnd"]()),2&e){const e=b["\u0275\u0275nextContext"](2);b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.approvers)}}function K(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",102),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](2,"div",102),b["\u0275\u0275text"](3),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",102),b["\u0275\u0275text"](5),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"](3);b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.approvers[0].name," "),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"](" ",e.approvers[0].role," "),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"](" Level ",e.approvers[0].level," ")}}function Z(e,t){if(1&e&&(b["\u0275\u0275elementContainerStart"](0),b["\u0275\u0275elementStart"](1,"div",3),b["\u0275\u0275elementStart"](2,"div",78),b["\u0275\u0275element"](3,"app-approver-image",103),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",104),b["\u0275\u0275elementStart"](5,"div",3),b["\u0275\u0275elementStart"](6,"span",105),b["\u0275\u0275text"](7),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](8,"div",3),b["\u0275\u0275elementStart"](9,"span",106),b["\u0275\u0275text"](10),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](11,K,6,3,"ng-template",null,107,b["\u0275\u0275templateRefExtractor"]),b["\u0275\u0275elementContainerEnd"]()),2&e){const e=b["\u0275\u0275reference"](12),t=b["\u0275\u0275nextContext"](2);b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("tooltip",e)("id",t.approvers[0].oid),b["\u0275\u0275advance"](4),b["\u0275\u0275textInterpolate1"](" ",t.approvers[0].name," "),b["\u0275\u0275advance"](3),b["\u0275\u0275textInterpolate1"](" ",t.approvers[0].role," ")}}function Q(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",96),b["\u0275\u0275template"](1,$,2,1,"ng-container",97),b["\u0275\u0275template"](2,Z,13,4,"ng-template",null,98,b["\u0275\u0275templateRefExtractor"]),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275reference"](3),t=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.approvers.length>1)("ngIfElse",e)}}function ee(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Address Line 1"))}}function te(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Address Line 2"))}}function ne(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Primary Contact Number"))}}function ie(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Emergency Contact Name"))}}function re(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Emergency Contact Number"))}}function oe(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Relation"))}}function ae(e,t){1&e&&b["\u0275\u0275text"](0,"Terms and Conditions")}function le(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",108),b["\u0275\u0275elementStart"](1,"div",109),b["\u0275\u0275elementStart"](2,"span",110),b["\u0275\u0275elementStart"](3,"button",111),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"]().selectCheckList(n)})),b["\u0275\u0275elementStart"](4,"mat-icon"),b["\u0275\u0275text"](5,"check_circle"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"span",112),b["\u0275\u0275text"](7),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",e.isSelected?"tick-active":"tick-not-active"),b["\u0275\u0275advance"](4),b["\u0275\u0275textInterpolate1"](" ",e.item," ")}}function se(e,t){1&e&&b["\u0275\u0275text"](0,"My desk")}function ce(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",113),b["\u0275\u0275elementStart"](1,"button",114),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](5).click()})),b["\u0275\u0275elementStart"](2,"mat-icon",115),b["\u0275\u0275text"](3,"cloud_upload"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](4,"input",116,117),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType)}}function de(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",118),b["\u0275\u0275listener"]("fileOver",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().fileOverDropZone(t)}))("onFileDrop",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().filedrop(t)})),b["\u0275\u0275elementStart"](1,"div",119),b["\u0275\u0275elementStart"](2,"span",120),b["\u0275\u0275text"](3," Drop your office desk image and internet connectivity speed test screenshot here"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",121),b["\u0275\u0275elementStart"](5,"mat-icon",122),b["\u0275\u0275text"](6,"cloud_upload"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"div",123),b["\u0275\u0275elementStart"](8,"button",124),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](11).click()})),b["\u0275\u0275text"](9," Upload "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](10,"input",116,125),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275property"]("ngClass",e.isFileOverDropZone?"file-present":"file-empty")("uploader",e.uploader),b["\u0275\u0275advance"](10),b["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType)}}function me(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",129),b["\u0275\u0275elementStart"](1,"div",130),b["\u0275\u0275elementStart"](2,"div",131),b["\u0275\u0275elementStart"](3,"span",132),b["\u0275\u0275element"](4,"img",133),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"span",113),b["\u0275\u0275elementStart"](6,"button",134),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"](2).removeFile(n)})),b["\u0275\u0275elementStart"](7,"mat-icon",135),b["\u0275\u0275text"](8,"close"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("src",e,b["\u0275\u0275sanitizeUrl"])}}function pe(e,t){1&e&&b["\u0275\u0275element"](0,"mat-spinner",136),2&e&&b["\u0275\u0275property"]("diameter",30)}function he(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",126),b["\u0275\u0275elementStart"](1,"div",62),b["\u0275\u0275template"](2,me,9,1,"div",127),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](3,pe,1,1,"mat-spinner",128),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngForOf",e.fileList),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",e.isImgLoading)}}function ue(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",137),b["\u0275\u0275listener"]("fileOver",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().fileOverDropZone(t)}))("onFileDrop",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().filedrop(t)})),b["\u0275\u0275elementStart"](1,"div",138),b["\u0275\u0275elementStart"](2,"span",139),b["\u0275\u0275text"](3," Drop more images here"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",140),b["\u0275\u0275elementStart"](5,"mat-icon",141),b["\u0275\u0275text"](6,"cloud_upload"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275property"]("ngClass",e.isFileOverDropZone?"file-present":"file-empty")("uploader",e.uploader)}}function fe(e,t){1&e&&b["\u0275\u0275text"](0,"Duration")}function ge(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"span",142),b["\u0275\u0275text"](1," You can choose the period according to your preference, it can be long period request, short period request or reporting to office in any particular day in a week / particular week in a month. "),b["\u0275\u0275elementStart"](2,"span"),b["\u0275\u0275text"](3,"Maximum Remote WorX duration is "),b["\u0275\u0275elementStart"](4,"span",143),b["\u0275\u0275text"](5,"1 year"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275text"](6,", also this is integrated with your timesheet."),b["\u0275\u0275elementEnd"](),b["\u0275\u0275text"](7," You can edit it in timesheet, if you report to office while you are on Remote WorX, don't worry it has no effect on Payroll ! "),b["\u0275\u0275elementEnd"]())}function ve(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"span",142),b["\u0275\u0275text"](1," You can take WFH according to your preference, it can be long WFH, short WFH or reporting to office in any particular day in a week / particular week in a month."),b["\u0275\u0275elementStart"](2,"span"),b["\u0275\u0275text"](3,"Maximum WFH duration is - "),b["\u0275\u0275elementStart"](4,"span",143),b["\u0275\u0275text"](5," 1 Year"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275text"](6,", WFH is integrated with timesheet, Once your request is approved, it will show "),b["\u0275\u0275elementStart"](7,"span",143),b["\u0275\u0275text"](8,'"WH"'),b["\u0275\u0275elementEnd"](),b["\u0275\u0275text"](9," for the days you applied."),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"span"),b["\u0275\u0275text"](11," You can edit in timesheet, if you report to office while you are on WFH, Don't worry it has no effect on Payroll !"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]())}function xe(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("Start Date"))}}function we(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-error",95),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.getErrorMessage("End Date"))}}function ye(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"span",144),b["\u0275\u0275text"](1,' ( Choose either "Long Duration" or "Short Duration" or "Custom Start Date and End Date" ) '),b["\u0275\u0275elementEnd"]())}function Se(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"mat-icon"),b["\u0275\u0275text"](1,"done_all"),b["\u0275\u0275elementEnd"]())}function be(e,t){1&e&&b["\u0275\u0275element"](0,"mat-spinner",145)}function Ce(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"mat-icon"),b["\u0275\u0275text"](1,"save"),b["\u0275\u0275elementEnd"]())}function Ee(e,t){1&e&&b["\u0275\u0275element"](0,"mat-spinner",145)}const _e=function(e){return{"background-color":e}};function De(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"button",146),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().saveFiles()})),b["\u0275\u0275template"](1,Ce,2,0,"mat-icon",92),b["\u0275\u0275template"](2,Ee,1,0,"mat-spinner",93),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](4,_e,e.isSubmitting||e.isSaving?"#cbcbcb":"#cf0001"))("disabled",e.isSubmitting||e.isSaving),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",!e.isSaving),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",e.isSaving)}}const ke=function(){return["name"]},Oe=function(){return{"background-color":"#cf0001",color:"#ffffff"}},Ie=function(){return{"background-color":"#cbcbcb",color:"#000000"}},Pe=function(e,t){return{"btn-active":e,"btn-not-active":t}};let Me=(()=>{class e{constructor(e,t,n,i,r,o,a,l){this.fb=e,this._auth=t,this._wfhService=n,this.wfPrimaryService=i,this._snack=r,this.dialog_data=o,this.dialogRef=a,this.errorService=l,this.notifyParent=new b.EventEmitter,this.selectedCountry={countryId:null},this.selectedState={stateId:null},this.reportingDays=new Map,this.reportingWeek=new Map,this.wfhCreationForm=this.fb.group({name:["",_.H.required],oid:["",_.H.required],pincode:["",_.H.required],country:["",_.H.required],state:["",_.H.required],addressLine1:["",_.H.required],addressLine2:["",_.H.required],city:["",_.H.required],primaryContactNumber:["",_.H.required],emergencyName:["",_.H.required],emergencyCtNumber:["",_.H.required],relation:["",_.H.required],checkList:["",_.H.required],termsAndConditions:[!1,_.H.required],startDate:["",_.H.required],endDate:["",_.H.required],reportingDays:["[]",_.H.required],reportingWeeks:["[]",_.H.required],wfhType:[""],wfhDuration:[""],wfhDurationUnit:[""],approvers:[""],workflowId:[""],isAggregation:[""],attachments:[""]}),this.checked=!1,this.editDetails="",this.mode="create",this.tomorrow=k().add(1,"days").toDate(),this.weekVisibility={week1:!1,week2:!1,week3:!1,week4:!1},this.isSaving=!1,this.isSubmitting=!1,this.ORGANIZATION="KAARTECH",this.listenToCountryChanges=()=>{this.wfhCreationForm.get("country").valueChanges.subscribe(e=>{if(e){let t=JSON.parse(e);this.selectedCountry.countryId=t.id}})},this.listenToStateChanges=()=>{this.wfhCreationForm.get("state").valueChanges.subscribe(e=>{if(e){let t=JSON.parse(e);this.selectedState.stateId=t.id}})},this.moveToNextStepper=e=>{this.goForward(e)},this.defaultFormGroup=()=>{this.wfhCreationForm.patchValue({name:this.profile.name,oid:this.profile.oid,pincode:"",country:"",state:"",addressLine1:"",addressLine2:"",city:"",primaryContactNumber:"",emergencyName:"",emergencyCtNumber:"",relation:"",checkList:"",termsAndConditions:!1,startDate:"",endDate:"",reportingDays:[],reportingWeeks:[],wfhType:"",wfhDuration:"",wfhDurationUnit:"",approvers:"",workflowId:"",isAggregation:"",attachments:null}),this.fileList=[],this.uploader.clearQueue(),this.getCheckList(),this.getExpenseApproversHierarchy(),this.reportingDays.clear(),this.reportingWeek.clear()},this.initEditMode=()=>Object(S.c)(this,void 0,void 0,(function*(){if(this.editDetails&&(this.editDetails.checklist="string"==typeof this.editDetails.checklist?JSON.parse(this.editDetails.checklist):this.editDetails.checklist,this.editDetails.country="string"==typeof this.editDetails.country?JSON.parse(this.editDetails.country):this.editDetails.country,this.editDetails.state="string"==typeof this.editDetails.state?JSON.parse(this.editDetails.state):this.editDetails.state,this.editDetails.city="string"==typeof this.editDetails.city?JSON.parse(this.editDetails.city):this.editDetails.city,this.editDetails.duration_selection="string"==typeof this.editDetails.duration_selection?JSON.parse(this.editDetails.duration_selection):this.editDetails.duration_selection,this.editDetails.duration_selection.reporting_days="string"==typeof this.editDetails.duration_selection.reporting_days?JSON.parse(this.editDetails.duration_selection.reporting_days):this.editDetails.duration_selection.reporting_days,this.editDetails.duration_selection.reporting_weeks="string"==typeof this.editDetails.duration_selection.reporting_weeks?JSON.parse(this.editDetails.duration_selection.reporting_weeks):this.editDetails.duration_selection.reporting_weeks,this.checkList=this.editDetails.checklist,this.wfhCreationForm.patchValue({pincode:this.editDetails.pin_code,country:JSON.stringify(this.editDetails.country),state:JSON.stringify(this.editDetails.state),city:JSON.stringify(this.editDetails.city),addressLine1:this.editDetails.address_line1,addressLine2:this.editDetails.address_line2,primaryContactNumber:this.editDetails.primary_ct_no,emergencyName:this.editDetails.emergency_ct_name,emergencyCtNumber:this.editDetails.emergency_ct_no,relation:this.editDetails.emergency_relation,checkList:this.editDetails.checklist,termsAndConditions:!0,startDate:this.editDetails.start_date,endDate:this.editDetails.end_date,reportingDays:this.editDetails.duration_selection&&this.editDetails.duration_selection.reporting_days?JSON.stringify(this.editDetails.duration_selection.reporting_days):[],reportingWeeks:this.editDetails.duration_selection&&this.editDetails.duration_selection.reporting_weeks?JSON.stringify(this.editDetails.duration_selection.reporting_weeks):[],wfhType:this.editDetails.duration_selection&&this.editDetails.duration_selection.wfh_type?this.editDetails.duration_selection.wfh_type:[],wfhDuration:this.editDetails.duration_selection&&this.editDetails.duration_selection.wfh_duration?this.editDetails.duration_selection.wfh_duration:[],wfhDurationUnit:this.editDetails.duration_selection&&this.editDetails.duration_selection.wfh_duration_unit?this.editDetails.duration_selection.wfh_duration_unit:[],attachments:this.editDetails.attachments?"string"==typeof this.editDetails.attachments?JSON.parse(this.editDetails.attachments):this.editDetails.attachments:null}),this.editDetails.duration_selection&&this.editDetails.duration_selection.reporting_days&&this.editDetails.duration_selection.reporting_days.length>0&&this.editDetails.duration_selection.reporting_days.forEach(e=>{this.addToReportingDays(e)}),this.editDetails.duration_selection&&this.editDetails.duration_selection.reporting_weeks&&this.editDetails.duration_selection.reporting_weeks.length>0&&this.editDetails.duration_selection.reporting_weeks.forEach(e=>{this.addToReportingWeek(e)}),this.fileList=[],this.uploader.clearQueue(),this.wfhCreationForm.get("attachments").value)){let e=this.wfhCreationForm.get("attachments").value;for(let t of e)this.isImgLoading=!0,t.isNewFile=!1,yield this._wfhService.getImageFromS3(t.key).then(e=>{this.isImgLoading=!1,this.fileList.push("data:"+t.type+";base64,"+e.data.fileData)},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","File Not found while retrieving from S3",e&&e.params?e.params:e&&e.error?e.error.params:{})});this.filesJsonConcat=e}})),this.getCheckList=()=>{this.checkListSubscription&&this.checkListSubscription.unsubscribe(),this.checkListSubscription=this._wfhService.getWfhCheckList().subscribe(e=>{D.map(e,e=>{e.isSelected=!1}),this.checkList=e},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while retrieving WFH Check List",e&&e.params?e.params:e&&e.error?e.error.params:{})})},this.selectCheckList=e=>{this.checkList[e].isSelected=!this.checkList[e].isSelected},this.getWorkFlowProperty=()=>new Promise((e,t)=>{this._wfhService.getWorkflowPropertiesByWorkflowId().subscribe(t=>{e(t.data[0])},e=>{console.error(e),t(e)})}),this.getExpenseApproversHierarchy=()=>Object(S.c)(this,void 0,void 0,(function*(){let e=yield this.getWorkFlowProperty();this.wfhCreationForm.get("isAggregation").patchValue(e.aggregation_allowed),this.wfhCreationForm.get("workflowId").patchValue(e.id),this.wfPrimaryService.getApproversHierarchy({workflowId:e.id,sortByDesignation:e.sort_by_desgn,initiatorOId:this.profile.oid,approvalType:e.approval_type,approvalParams:e.approval_params,costCentresAndTypes:[],costCentreList:[],shouldFormatApproversHierarchy:!0}).subscribe(e=>{this.approvers="S"==e.messType&&e.data?e.data:[],this.wfhCreationForm.get("approvers").patchValue(JSON.stringify(this.approvers))},e=>{console.error(e)})})),this.selectDuration=(e,t,n)=>{let i,r;i=this.wfhCreationForm.get("startDate").value?this.wfhCreationForm.get("startDate").value:k().add(1,"days"),r=k(i).add(t,n),this.wfhCreationForm.patchValue({wfhType:e,wfhDuration:t,wfhDurationUnit:n,endDate:new Date(r),startDate:new Date(i)})},this.isBtnActive=(e,t,n)=>{let i=this.wfhCreationForm.get("wfhType").value,r=this.wfhCreationForm.get("wfhDuration").value,o=this.wfhCreationForm.get("wfhDurationUnit").value;return e==i&&t==r&&n==o},this.addToReportingDays=e=>{this.reportingDays.has(e)?this.reportingDays.delete(e):this.reportingDays.set(e,e);let t=[];this.reportingDays.forEach((e,n)=>{t.push(e)}),this.wfhCreationForm.get("reportingDays").patchValue(JSON.stringify(t))},this.addToReportingWeek=e=>{this.reportingWeek.has(e)?this.reportingWeek.delete(e):this.reportingWeek.set(e,e);let t=[];this.reportingWeek.forEach((e,n)=>{t.push(e)}),this.wfhCreationForm.get("reportingWeeks").patchValue(JSON.stringify(t))},this.saveRequest=()=>Object(S.c)(this,void 0,void 0,(function*(){if(this.isSubmitting=!0,this.wfhCreationForm.get("checkList").patchValue(JSON.stringify(this.checkList)),yield this.preSubmitChecker()){let e=JSON.parse(JSON.stringify(this.wfhCreationForm.value));e=yield this.dateOffsetter(e),this.requestSub&&this.requestSub.unsubscribe(),"edit"==this.dialog_data.mode&&(e.id=this.editDetails.id,e.status=this.editDetails.status),this.requestSub=this._wfhService.saveWfhRequest(e,this.dialog_data.mode).subscribe(e=>{this.dropFilesFromS3(),this.closeCreationDialog("creation Success"),this.showAlert("Remote WorX request created successfully!"),this._wfhService.refreshWfhList(),this.formStepper.reset(),this.defaultFormGroup(),this.isSubmitting=!1},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while creating Remote WorX Request",e&&e.params?e.params:e&&e.error?e.error.params:{}),this.isSubmitting=!1})}else this.isSubmitting=!1})),this.updateWfhRequest=()=>Object(S.c)(this,void 0,void 0,(function*(){if(this.isSaving=!0,this.wfhCreationForm.get("checkList").patchValue(JSON.stringify(this.checkList)),yield this.preSubmitChecker()){let e=JSON.parse(JSON.stringify(this.wfhCreationForm.value));e=yield this.dateOffsetter(e),this.requestSub&&this.requestSub.unsubscribe(),e.workflow_header_id=this.editDetails.workflow_header_id,e.id=this.editDetails.id,e.status=this.editDetails.status,this.requestSub=this._wfhService.updateWfhRequest(e).subscribe(e=>{this.dropFilesFromS3(),this.closeCreationDialog("save Success"),this.showAlert("Remote WorX Request Updated successfully!"),this.isSaving=!1,this._wfhService.refreshWfhList()},e=>{this.isSaving=!1,this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while updating Remote WorX request",e&&e.params?e.params:e&&e.error?e.error.params:{})})}})),this.showAlert=e=>{this._snack.open(e,"Dismiss",{duration:3e3})},this.detectChanges=()=>{this.startDateSubscription&&this.startDateSubscription.unsubscribe(),this.startDateSubscription=this.wfhCreationForm.get("startDate").valueChanges.subscribe(e=>{this.startDate=e,this.minEndDate=k(e).add(1,"d"),this.maxEndDate=k(e).add(1,"y")}),this.endDateSubscription=this.wfhCreationForm.get("endDate").valueChanges.subscribe(e=>{this.endDate=e})},this.differenceBetweenTwoDates=(e,t)=>{const n=Math.abs(t-e);return Math.ceil(n/864e5)},this.dateOffsetter=e=>Object(S.c)(this,void 0,void 0,(function*(){let t=k().utcOffset();return e.startDate=k(e.startDate).utc().add(t,"minutes"),e.endDate=k(e.endDate).utc().add(t,"minutes"),e})),this.preSubmitChecker=()=>Object(S.c)(this,void 0,void 0,(function*(){let e=this.wfhCreationForm.value;return!!(e.name&&e.oid&&e.pincode&&e.country&&e.state&&e.city&&e.primaryContactNumber&&e.emergencyName&&e.emergencyCtNumber&&e.relation&&e.startDate&&e.endDate&&e.termsAndConditions&&e.addressLine1&&e.addressLine2)||(this.formStepper.previous(),this.formStepper.previous(),e.country&&(e.country="string"==typeof e.country?JSON.parse(e.country):e.country),e.state&&(e.state="string"==typeof e.state?JSON.parse(e.state):e.state),e.city&&(e.city="string"==typeof e.city?JSON.parse(e.city):e.city),e.country&&e.country.id?e.state&&e.state.id?e.city&&e.city.id||(this.showAlert("City is required. Please use the drop down to select the city"),this.wfhCreationForm.get("city").markAsTouched()):(this.showAlert("State is required. Please use the drop down to select the state"),this.wfhCreationForm.get("state").markAsTouched()):(this.showAlert("Country is required. Please use the drop down to select the country"),this.wfhCreationForm.get("country").markAsTouched()),!1)})),this.isReportingWeeksDisabled=()=>{let e=this.wfhCreationForm.get("startDate").value,t=this.wfhCreationForm.get("endDate").value;return k(t).diff(k(e),"days")<30},this.isReportingDaysDisabled=()=>{let e=this.wfhCreationForm.get("startDate").value,t=this.wfhCreationForm.get("endDate").value;return k(t).diff(k(e),"days")<7},this.getErrorMessage=e=>e+" is required",this.allowedMimeType=["image/png","image/jpg","image/jpeg"],this.maxFileSize=10485760,this.fileList=[],this.filesToBeRemoved=[],this.filesJsonConcat=[],this.isFileOverDropZone=!1,this.isFileUploadedFromEditMode=!1,this.isImgLoading=!1,this.uploader=new O.d({url:"/api/ts_wfh/uploadWfhAttachment",authToken:"Bearer "+this._auth.getToken(),allowedMimeType:this.allowedMimeType,disableMultipart:!1,maxFileSize:this.maxFileSize,headers:[{name:"user",value:this._auth.getProfile().profile.name}]}),this.closeCreationDialog=e=>{"creation Success"!=e&&"close"!=e||this.dialogRef.close(e)},this.fileUploadChanges=()=>{this.uploader.onAfterAddingFile=e=>{console.log(e),this.isFileOverDropZone=!1;var t=new FileReader;t.readAsDataURL(e._file),t.onload=e=>{this.fileList.push(t.result)}},this.uploader.onWhenAddingFileFailed=e=>{this.isFileOverDropZone=!1,this.showAlert("Only Images are allowed to upload")},this.uploader.onCompleteItem=(e,t,n,i)=>{if(200==n&&t&&t.length>0){let e=JSON.parse(t);this.filesJsonConcat=this.filesJsonConcat.concat(e.files_json);let n=D.where(this.filesJsonConcat,{isNewFile:!0});this.uploader.queue.length==n.length?(this.wfhCreationForm.get("attachments").patchValue(JSON.stringify(this.filesJsonConcat)),this.isFileUploadedFromEditMode?this.updateWfhRequest():this.saveRequest()):this.showAlert("File upload not completed. Uploader queue not matching new files length")}else this.showAlert("Unable to upload")}},this.uploadFiles=()=>{this.isFileUploadedFromEditMode=!1,this.uploader.queue.length>0?this.uploader.uploadAll():null==this.wfhCreationForm.get("attachments").value?this.showAlert("Images not attached"):this.saveRequest()},this.profile=this._auth.getProfile().profile,this.minEndDate=k().add(1,"d"),this.wfhCreationForm.patchValue({name:this.profile.name,oid:this.profile.oid}),this.listenToCountryChanges(),this.listenToStateChanges()}goBack(e){e.previous()}goForward(e){e.next()}ngOnInit(){"edit"!=this.dialog_data.mode&&this.getCheckList(),this.getExpenseApproversHierarchy(),this.detectChanges(),this.fileUploadChanges(),this.dialog_data&&(this.mode=this.dialog_data.mode,this.editDetails=this.dialog_data.editDetails),this.formStepper&&this.formStepper.steps&&this.formStepper.reset(),"edit"==this.dialog_data.mode?this.initEditMode():this.defaultFormGroup()}isReportingWeeksEnabled(e){let t=[];return this.wfhCreationForm&&""!=this.wfhCreationForm.get("reportingWeeks").value&&(t=JSON.parse(this.wfhCreationForm.get("reportingWeeks").value)),!!t.includes(e)}isReportingDaysEnabled(e){let t=[];if(this.wfhCreationForm&&""!=this.wfhCreationForm.get("reportingDays").value&&(t=JSON.parse(this.wfhCreationForm.get("reportingDays").value)),t.includes(e))return!0}numberOnly(e){const t=e.which?e.which:e.keyCode;return t>=48&&t<=57}fileOverDropZone(e){this.isFileOverDropZone=!0}filedrop(e){this.isFileOverDropZone=!1}removeFile(e){if(this.fileList.splice(e,1),"edit"==this.mode){let t=this.wfhCreationForm.get("attachments").value?"string"==typeof this.wfhCreationForm.get("attachments").value?JSON.parse(this.wfhCreationForm.get("attachments").value):this.wfhCreationForm.get("attachments").value:null;if(t[e]&&!t[e].isNewFile)this.filesToBeRemoved.push(t[e]),t.splice(e,1);else{let n=D.where(t,{isNewFile:!1});this.uploader.queue[e-n.length]&&this.uploader.queue.splice(e-n.length,1)}this.wfhCreationForm.get("attachments").patchValue(t&&t.length>0?JSON.stringify(t):null)}else this.uploader.queue.splice(e,1)}saveFiles(){this.isFileUploadedFromEditMode=!0,this.uploader.queue.length>0?this.uploader.uploadAll():null==this.wfhCreationForm.get("attachments").value?this.showAlert("Images not attached"):this.updateWfhRequest()}dropFilesFromS3(){this.filesToBeRemoved.length>0&&this._wfhService.deleteImageFromS3(this.filesToBeRemoved).subscribe(e=>{console.log("files deleted")},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internl server error while deleting files",e&&e.params?e.params:e&&e.error?e.error.params:{})})}ngOnDestroy(){this.checkListSubscription&&this.checkListSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](_.i),b["\u0275\u0275directiveInject"](L.a),b["\u0275\u0275directiveInject"](H),b["\u0275\u0275directiveInject"](B.a),b["\u0275\u0275directiveInject"](g.a),b["\u0275\u0275directiveInject"](d.a),b["\u0275\u0275directiveInject"](d.h),b["\u0275\u0275directiveInject"](j.a))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-creation"]],viewQuery:function(e,t){if(1&e&&b["\u0275\u0275viewQuery"](z,!0),2&e){let e;b["\u0275\u0275queryRefresh"](e=b["\u0275\u0275loadQuery"]())&&(t.formStepper=e.first)}},inputs:{editDetails:"editDetails",mode:"mode",min:"min",max:"max"},outputs:{notifyParent:"notifyParent"},decls:280,vars:166,consts:[[1,"row","mt-2","col-12","d-flex","mb-2"],["mat-icon-button","",1,"ml-auto","icon-btn",3,"click"],[1,"close-icon"],[1,"row"],["autocomplete","new-password",3,"formGroup"],[1,"container-fluid"],["linear",""],["stepper",""],["matStepLabel",""],[1,"row","p-0","pt-3","pb-2"],[1,"ml-2",2,"color","#bf360c","font-weight","500","font-size","14px"],[1,"ml-1",2,"font-weight","500","font-size","14px"],[1,"row","pt-2"],[1,"col-6","p-0"],["appearance","outline",1,"create-account-field"],["matInput","","formControlName","name","readonly","","required",""],[1,"row","pt-1"],["matInput","","autocomplete","new-password","placeholder","Eg.600094","formControlName","pincode","maxlength","10","required",""],["class","mt-2",4,"ngIf"],[1,"ml-2"],["formControlName","country",3,"label","optionLabel","apiUri","isRequired"],[1,"row","d-flex","justify-content-end",2,"min-height","7rem"],[1,"col-6","p-2",2,"background-color","#f5f5f5"],[1,"p-1","ml-2",2,"font-size","13px","font-weight","500"],["class","col-12 pt-3 pl-0 d-flex align-items-center",4,"ngIf"],[1,"col-3","p-0"],["matInput","","autocomplete","new-password","placeholder","Eg: #3, IOB colony","formControlName","addressLine1","required",""],[1,"col-3","ml-2","p-0"],["matInput","","autocomplete","new-password","placeholder","Eg: Anna Nagar","formControlName","addressLine2","required",""],[1,"row","pt-1","pb-1"],["formControlName","state",3,"label","optionLabel","bodyParams","apiUri","isRequired"],[1,"col-3","p-0","ml-2"],["formControlName","city",3,"label","optionLabel","bodyParams","apiUri","isRequired"],[1,"row","pt-2","pb-2"],[2,"font-weight","500","font-size","14px"],[1,"row","pt-1","pb-2"],["matInput","","autocomplete","new-password","placeholder","Eg.9835468589","formControlName","primaryContactNumber","maxlength","15","required","",3,"keypress"],["matInput","","autocomplete","new-password","formControlName","emergencyName","required",""],["matInput","","autocomplete","new-password","formControlName","emergencyCtNumber","maxlength","15","required","",3,"keypress"],["matInput","","autocomplete","new-password","formControlName","relation","required",""],[1,"ml-2","col-3","pt-2"],[1,"ml-2","col-2","pt-2"],["mat-icon-button","",1,"mat-mini-fab",2,"background-color","#cf0001","color","white",3,"click"],[2,"color","#bf360c","font-weight","500","font-size","14px"],[1,"col-7","pl-0"],[1,"card","slide-in-top"],["class","row d-flex pt-2 pb-1 pl-3","style","height: 59px",4,"ngFor","ngForOf"],[1,"col-5"],[1,"row",2,"padding-top","9rem","padding-left","7rem"],["src","https://assets.kebs.app/images/WFH-Checklist.png","height","250px","width","270px"],[1,"row","pt-3"],[1,"col-7","pt-2","pb-0","border","solid"],["formControlName","termsAndConditions",1,"ml-2","pt-2"],[1,"ml-2",2,"font-weight","500","font-size","14px"],[1,"col-2"],["mat-icon-button","",1,"mat-mini-fab",2,"background-color","#cf0001","color","white",3,"ngStyle","disabled","click"],[1,"col-7","pl-0",2,"color","#bf360c","font-weight","500","font-size","14px"],["class","col-1",4,"ngIf"],[1,"row","pt-2","pb-2","d-flex"],["ng2FileDrop","","style","min-height: 50vh","class","col-8 p-5",3,"ngClass","uploader","fileOver","onFileDrop",4,"ngIf"],["class","col-8 pl-0",4,"ngIf"],[1,"col-4"],[1,"row","d-flex","flex-column"],["ng2FileDrop","","class","col-8","style","min-height: 5rem",3,"ngClass","uploader","fileOver","onFileDrop",4,"ngIf"],[1,"row","d-flex","flex-column",2,"min-height","50vh"],[1,"col-12","my-auto"],["src","https://assets.kebs.app/images/wfh_attachment.png","height","200px","width","300px"],[1,"col-12","d-flex"],["mat-icon-button","",1,"mat-mini-fab","ml-auto","mr-4",2,"background-color","#cf0001","color","white",3,"ngStyle","disabled","click"],[1,"col-12","p-1"],[1,"material-icons","info-icon","pt-1"],["style","font-size: 14px; font-weight: 500; line-height: 1.8",4,"ngIf"],[1,"col-3","pl-1"],[1,"row","p-0"],["appearance","outline",1,"create-account-field",2,"height","34px"],["matInput","","formControlName","startDate",3,"matDatepicker","min"],["matSuffix","",3,"for"],["picker1",""],[1,"col-3"],["matInput","","formControlName","endDate",3,"matDatepicker","min","max"],["picker2",""],[1,"col-6","d-flex"],["class","ml-3 my-auto","style","font-size: 12px; color: #646464; font-weight: 500",4,"ngIf"],[1,"col-10","p-0"],[1,"col-6","highlighted-text"],[1,"col-6"],["mat-raised-button","",3,"ngClass","click"],[1,"col-6",2,"padding-right","4.5rem !important"],[1,"row","pt-4"],["mat-raised-button","",3,"ngClass","disabled","click"],[1,"row",2,"padding-top","24rem"],["mat-icon-button","","matTooltip","Create Remote WorX Request",1,"mat-mini-fab",2,"color","white",3,"ngStyle","disabled","click"],[4,"ngIf"],["class","spinner-color","diameter","19",4,"ngIf"],["mat-icon-button","","class","mat-mini-fab ml-4","style","color: white","matTooltip","Save",3,"ngStyle","disabled","click",4,"ngIf"],[1,"mt-2"],[1,"col-12","pt-3","pl-0","d-flex","align-items-center"],[4,"ngIf","ngIfElse"],["singleApprover",""],[4,"ngFor","ngForOf"],["content-type","template","placement","top","imgHeight","38px","imgWidth","38px","borderStyle","solid","borderWidth","2px",2,"margin","2px",3,"tooltip","id"],["approverTooltip",""],[1,"row","tooltip-text"],["content-type","template","placement","top","imgHeight","42px","imgWidth","42px","borderStyle","solid","borderWidth","2px",2,"margin","2px",3,"tooltip","id"],[1,"col-9","pl-4"],[2,"font-size","15px","font-weight","500"],[2,"font-size","14px"],["singleApproverTooltip",""],[1,"row","d-flex","pt-2","pb-1","pl-3",2,"height","59px"],[1,"col-12","p-0","border-bottom","solid"],[1,"p-0"],["mat-icon-button","",2,"width","34px","height","34px","line-height","16px !important",3,"ngClass","click"],[1,"ml-2","pt-2",2,"font-weight","500","font-size","14px","color","black"],[1,"col-1"],["mat-icon-button","","matTooltip","Add more files",1,"view-button-inactive",3,"click"],[1,"iconButton"],["hidden","","type","file","ng2FileSelect","","required","",3,"uploader","accept"],["moreFileInput",""],["ng2FileDrop","",1,"col-8","p-5",2,"min-height","50vh",3,"ngClass","uploader","fileOver","onFileDrop"],[1,"row","justify-content-center"],[2,"font-size","16px","font-weight","600"],[1,"row","d-flex","justify-content-center",2,"min-height","10rem"],[1,"my-auto","cloud-icon"],[1,"row","pt-2","justify-content-center"],["mat-raised-button","",1,"upload-btn",3,"click"],["fileInput",""],[1,"col-8","pl-0"],["class","col-12",4,"ngFor","ngForOf"],["class","mx-auto mt-3",3,"diameter",4,"ngIf"],[1,"col-12"],[2,"border","solid 2px #cacaca"],[1,"row","p-4"],[1,"col-11","d-flex","justify-content-center"],["height","200",3,"src"],["mat-icon-button","","matTooltip","Remove Image",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[1,"mx-auto","mt-3",3,"diameter"],["ng2FileDrop","",1,"col-8",2,"min-height","5rem",3,"ngClass","uploader","fileOver","onFileDrop"],[1,"row","pt-1","justify-content-center"],[2,"font-size","14px","font-weight","500"],[1,"row","d-flex","pt-3","justify-content-center"],[1,"my-auto","cloud-icon",2,"font-size","3rem"],[2,"font-size","14px","font-weight","500","line-height","1.8"],[2,"color","#cf0001"],[1,"ml-3","my-auto",2,"font-size","12px","color","#646464","font-weight","500"],["diameter","19",1,"spinner-color"],["mat-icon-button","","matTooltip","Save",1,"mat-mini-fab","ml-4",2,"color","white",3,"ngStyle","disabled","click"]],template:function(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"button",1),b["\u0275\u0275listener"]("click",(function(){return t.closeCreationDialog("close")})),b["\u0275\u0275elementStart"](2,"mat-icon",2),b["\u0275\u0275text"](3,"clear"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",3),b["\u0275\u0275elementStart"](5,"form",4),b["\u0275\u0275elementStart"](6,"div",5),b["\u0275\u0275elementStart"](7,"mat-horizontal-stepper",6,7),b["\u0275\u0275elementStart"](9,"mat-step"),b["\u0275\u0275template"](10,J,1,0,"ng-template",8),b["\u0275\u0275elementStart"](11,"div",9),b["\u0275\u0275elementStart"](12,"span",10),b["\u0275\u0275text"](13),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](14,"span",11),b["\u0275\u0275text"](15,"Kindly let us know from where you are working,"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](16,"div",12),b["\u0275\u0275elementStart"](17,"div",13),b["\u0275\u0275elementStart"](18,"div",3),b["\u0275\u0275elementStart"](19,"mat-form-field",14),b["\u0275\u0275elementStart"](20,"mat-label"),b["\u0275\u0275text"](21,"Name"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](22,"input",15),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](23,"div",16),b["\u0275\u0275elementStart"](24,"div",13),b["\u0275\u0275elementStart"](25,"mat-form-field",14),b["\u0275\u0275elementStart"](26,"mat-label"),b["\u0275\u0275text"](27,"Pin Code"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](28,"input",17),b["\u0275\u0275template"](29,Y,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](30,"div",13),b["\u0275\u0275elementStart"](31,"span",19),b["\u0275\u0275elementStart"](32,"app-input-search-huge-input",20),b["\u0275\u0275template"](33,X,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](34,"div",13),b["\u0275\u0275elementStart"](35,"div",21),b["\u0275\u0275elementStart"](36,"div",22),b["\u0275\u0275elementStart"](37,"span",23),b["\u0275\u0275text"](38,"Your Remote WorX approver"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](39,Q,4,2,"div",24),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](40,"div",16),b["\u0275\u0275elementStart"](41,"div",25),b["\u0275\u0275elementStart"](42,"mat-form-field",14),b["\u0275\u0275elementStart"](43,"mat-label"),b["\u0275\u0275text"](44,"Address Line 1"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](45,"input",26),b["\u0275\u0275template"](46,ee,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](47,"div",27),b["\u0275\u0275elementStart"](48,"mat-form-field",14),b["\u0275\u0275elementStart"](49,"mat-label"),b["\u0275\u0275text"](50,"Address Line 2"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](51,"input",28),b["\u0275\u0275template"](52,te,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](53,"div",29),b["\u0275\u0275elementStart"](54,"div",25),b["\u0275\u0275element"](55,"app-input-search-huge-input",30),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](56,"div",31),b["\u0275\u0275element"](57,"app-input-search-huge-input",32),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](58,"div",33),b["\u0275\u0275elementStart"](59,"span",34),b["\u0275\u0275text"](60,"Kindly let us know your contact number"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](61,"div",35),b["\u0275\u0275elementStart"](62,"div",13),b["\u0275\u0275elementStart"](63,"mat-form-field",14),b["\u0275\u0275elementStart"](64,"mat-label"),b["\u0275\u0275text"](65,"Primary Contact Number"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](66,"input",36),b["\u0275\u0275listener"]("keypress",(function(e){return t.numberOnly(e)})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](67,ne,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](68,"div",33),b["\u0275\u0275elementStart"](69,"span",34),b["\u0275\u0275text"](70,"Who should we contact, in case of emergency"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](71,"div",16),b["\u0275\u0275elementStart"](72,"div",13),b["\u0275\u0275elementStart"](73,"mat-form-field",14),b["\u0275\u0275elementStart"](74,"mat-label"),b["\u0275\u0275text"](75,"Name"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](76,"input",37),b["\u0275\u0275template"](77,ie,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](78,"div",16),b["\u0275\u0275elementStart"](79,"div",25),b["\u0275\u0275elementStart"](80,"mat-form-field",14),b["\u0275\u0275elementStart"](81,"mat-label"),b["\u0275\u0275text"](82,"Emergency Contact Number"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](83,"input",38),b["\u0275\u0275listener"]("keypress",(function(e){return t.numberOnly(e)})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](84,re,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](85,"div",27),b["\u0275\u0275elementStart"](86,"mat-form-field",14),b["\u0275\u0275elementStart"](87,"mat-label"),b["\u0275\u0275text"](88,"Relation"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](89,"input",39),b["\u0275\u0275template"](90,oe,2,1,"mat-error",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](91,"div",40),b["\u0275\u0275elementStart"](92,"div",41),b["\u0275\u0275elementStart"](93,"button",42),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=b["\u0275\u0275reference"](8);return t.moveToNextStepper(n)})),b["\u0275\u0275elementStart"](94,"mat-icon"),b["\u0275\u0275text"](95,"keyboard_arrow_right"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](96,"mat-step"),b["\u0275\u0275template"](97,ae,1,0,"ng-template",8),b["\u0275\u0275elementStart"](98,"div",33),b["\u0275\u0275elementStart"](99,"span",43),b["\u0275\u0275text"](100,"Here are few T&C, Kindly check the lists !"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](101,"div",33),b["\u0275\u0275elementStart"](102,"div",44),b["\u0275\u0275elementStart"](103,"div",45),b["\u0275\u0275template"](104,le,8,2,"div",46),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](105,"div",47),b["\u0275\u0275elementStart"](106,"div",48),b["\u0275\u0275element"](107,"img",49),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](108,"div",50),b["\u0275\u0275elementStart"](109,"div",51),b["\u0275\u0275elementStart"](110,"span"),b["\u0275\u0275elementStart"](111,"mat-checkbox",52),b["\u0275\u0275elementStart"](112,"span",53),b["\u0275\u0275text"](113," I hereby declare , I have gone through all the checklists mentioned above "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](114,"div",54),b["\u0275\u0275elementStart"](115,"div",54),b["\u0275\u0275elementStart"](116,"button",55),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=b["\u0275\u0275reference"](8);return t.moveToNextStepper(n)})),b["\u0275\u0275elementStart"](117,"mat-icon"),b["\u0275\u0275text"](118,"keyboard_arrow_right"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](119,"mat-step"),b["\u0275\u0275template"](120,se,1,0,"ng-template",8),b["\u0275\u0275elementStart"](121,"div",33),b["\u0275\u0275elementStart"](122,"span",56),b["\u0275\u0275text"](123,"Kindly upload image of your office desk in home and screenshot of your internet connectivity speed test"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](124,ce,6,2,"div",57),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](125,"div",58),b["\u0275\u0275template"](126,de,12,4,"div",59),b["\u0275\u0275template"](127,he,4,2,"div",60),b["\u0275\u0275elementStart"](128,"div",61),b["\u0275\u0275elementStart"](129,"div",62),b["\u0275\u0275template"](130,ue,7,2,"div",63),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](131,"div",64),b["\u0275\u0275elementStart"](132,"div",65),b["\u0275\u0275element"](133,"img",66),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](134,"div",3),b["\u0275\u0275elementStart"](135,"div",67),b["\u0275\u0275elementStart"](136,"button",68),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=b["\u0275\u0275reference"](8);return t.moveToNextStepper(n)})),b["\u0275\u0275elementStart"](137,"mat-icon"),b["\u0275\u0275text"](138,"keyboard_arrow_right"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](139,"mat-step"),b["\u0275\u0275template"](140,fe,1,0,"ng-template",8),b["\u0275\u0275elementStart"](141,"div",3),b["\u0275\u0275elementStart"](142,"div",69),b["\u0275\u0275elementStart"](143,"span",70),b["\u0275\u0275text"](144," info "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](145,ge,8,0,"span",71),b["\u0275\u0275template"](146,ve,12,0,"span",71),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](147,"div",35),b["\u0275\u0275elementStart"](148,"div",72),b["\u0275\u0275elementStart"](149,"div",73),b["\u0275\u0275elementStart"](150,"mat-form-field",74),b["\u0275\u0275elementStart"](151,"mat-label"),b["\u0275\u0275text"](152,"Start Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](153,"input",75),b["\u0275\u0275template"](154,xe,2,1,"mat-error",18),b["\u0275\u0275element"](155,"mat-datepicker-toggle",76),b["\u0275\u0275element"](156,"mat-datepicker",null,77),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](158,"div",78),b["\u0275\u0275elementStart"](159,"mat-form-field",74),b["\u0275\u0275elementStart"](160,"mat-label"),b["\u0275\u0275text"](161,"End Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](162,"input",79),b["\u0275\u0275template"](163,we,2,1,"mat-error",18),b["\u0275\u0275element"](164,"mat-datepicker-toggle",76),b["\u0275\u0275element"](165,"mat-datepicker",null,80),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](167,"div",81),b["\u0275\u0275template"](168,ye,2,0,"span",82),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](169,"div",12),b["\u0275\u0275elementStart"](170,"div",83),b["\u0275\u0275elementStart"](171,"div",3),b["\u0275\u0275elementStart"](172,"div",84),b["\u0275\u0275text"](173,"Long Duration"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](174,"div",84),b["\u0275\u0275text"](175,"Short Duration"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](176,"div",12),b["\u0275\u0275elementStart"](177,"div",13),b["\u0275\u0275elementStart"](178,"div",12),b["\u0275\u0275elementStart"](179,"div",85),b["\u0275\u0275elementStart"](180,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("long",1,"M")})),b["\u0275\u0275text"](181," 1 Month "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](182,"div",85),b["\u0275\u0275elementStart"](183,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("long",5,"M")})),b["\u0275\u0275text"](184," 5 Months "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](185,"div",12),b["\u0275\u0275elementStart"](186,"div",85),b["\u0275\u0275elementStart"](187,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("long",2,"M")})),b["\u0275\u0275text"](188," 2 Months "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](189,"div",85),b["\u0275\u0275elementStart"](190,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("long",6,"M")})),b["\u0275\u0275text"](191," 6 Months "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](192,"div",12),b["\u0275\u0275elementStart"](193,"div",85),b["\u0275\u0275elementStart"](194,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("long",3,"M")})),b["\u0275\u0275text"](195," 3 Months "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](196,"div",85),b["\u0275\u0275elementStart"](197,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("long",1,"y")})),b["\u0275\u0275text"](198," 1 Year "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](199,"div",12),b["\u0275\u0275elementStart"](200,"div",85),b["\u0275\u0275elementStart"](201,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("long",4,"M")})),b["\u0275\u0275text"](202," 4 Months "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](203,"div",87),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](204,"div",13),b["\u0275\u0275elementStart"](205,"div",12),b["\u0275\u0275elementStart"](206,"div",85),b["\u0275\u0275elementStart"](207,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("short",1,"d")})),b["\u0275\u0275text"](208," 1 Day "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](209,"div",85),b["\u0275\u0275elementStart"](210,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("short",1,"w")})),b["\u0275\u0275text"](211," 1 Week "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](212,"div",12),b["\u0275\u0275elementStart"](213,"div",85),b["\u0275\u0275elementStart"](214,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("short",2,"d")})),b["\u0275\u0275text"](215," 2 Days "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](216,"div",85),b["\u0275\u0275elementStart"](217,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("short",2,"w")})),b["\u0275\u0275text"](218," 2 Weeks "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](219,"div",12),b["\u0275\u0275elementStart"](220,"div",85),b["\u0275\u0275elementStart"](221,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("short",5,"d")})),b["\u0275\u0275text"](222," 5 Days "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](223,"div",85),b["\u0275\u0275elementStart"](224,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("short",3,"w")})),b["\u0275\u0275text"](225," 3 Weeks "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](226,"div",12),b["\u0275\u0275elementStart"](227,"div",85),b["\u0275\u0275elementStart"](228,"button",86),b["\u0275\u0275listener"]("click",(function(){return t.selectDuration("short",10,"d")})),b["\u0275\u0275text"](229," 10 Days "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](230,"div",88),b["\u0275\u0275elementStart"](231,"div",84),b["\u0275\u0275text"](232," Reporting to office in following days in a week "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](233,"div",84),b["\u0275\u0275text"](234," Reporting to office in following weeks in a month "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](235,"div",12),b["\u0275\u0275elementStart"](236,"div",13),b["\u0275\u0275elementStart"](237,"div",12),b["\u0275\u0275elementStart"](238,"div",85),b["\u0275\u0275elementStart"](239,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingDays("Monday")})),b["\u0275\u0275text"](240," Monday "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](241,"div",85),b["\u0275\u0275elementStart"](242,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingDays("Thursday")})),b["\u0275\u0275text"](243," Thursday "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](244,"div",12),b["\u0275\u0275elementStart"](245,"div",85),b["\u0275\u0275elementStart"](246,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingDays("Tuesday")})),b["\u0275\u0275text"](247," Tuesday "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](248,"div",85),b["\u0275\u0275elementStart"](249,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingDays("Friday")})),b["\u0275\u0275text"](250," Friday "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](251,"div",12),b["\u0275\u0275elementStart"](252,"div",85),b["\u0275\u0275elementStart"](253,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingDays("Wednesday")})),b["\u0275\u0275text"](254," Wednesday "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](255,"div",85),b["\u0275\u0275elementStart"](256,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingDays("Sunday")})),b["\u0275\u0275text"](257," Sunday "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](258,"div",13),b["\u0275\u0275elementStart"](259,"div",12),b["\u0275\u0275elementStart"](260,"div",85),b["\u0275\u0275elementStart"](261,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingWeek("Week 1")})),b["\u0275\u0275text"](262," Week 1 "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](263,"div",85),b["\u0275\u0275elementStart"](264,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingWeek("Week 3")})),b["\u0275\u0275text"](265," Week 3 "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](266,"div",12),b["\u0275\u0275elementStart"](267,"div",85),b["\u0275\u0275elementStart"](268,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingWeek("Week 2")})),b["\u0275\u0275text"](269," Week 2 "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](270,"div",85),b["\u0275\u0275elementStart"](271,"button",89),b["\u0275\u0275listener"]("click",(function(){return t.addToReportingWeek("Week 4")})),b["\u0275\u0275text"](272," Week 4 "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](273,"div",54),b["\u0275\u0275elementStart"](274,"div",90),b["\u0275\u0275elementStart"](275,"div",67),b["\u0275\u0275elementStart"](276,"button",91),b["\u0275\u0275listener"]("click",(function(){return t.uploadFiles()})),b["\u0275\u0275template"](277,Se,2,0,"mat-icon",92),b["\u0275\u0275template"](278,be,1,0,"mat-spinner",93),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](279,De,3,6,"button",94),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275reference"](157),n=b["\u0275\u0275reference"](166);let i=null,r=null,o=null,a=null,l=null,s=null,c=null,d=null,m=null;b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",t.wfhCreationForm),b["\u0275\u0275advance"](8),b["\u0275\u0275textInterpolate1"]("",t.profile?"Hi "+t.profile.name:"",","),b["\u0275\u0275advance"](16),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(i=t.wfhCreationForm.get("pincode"))?null:i.invalid),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("label","Country")("optionLabel",b["\u0275\u0275pureFunction0"](85,ke))("apiUri","/api/ts_wfh/getCountries")("isRequired",!0),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",!0),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngIf",t.approvers),b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(r=t.wfhCreationForm.get("addressLine1"))?null:r.invalid),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(o=t.wfhCreationForm.get("addressLine2"))?null:o.invalid),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("label","State")("optionLabel",b["\u0275\u0275pureFunction0"](86,ke))("bodyParams",t.selectedCountry)("apiUri","/api/ts_wfh/getStates")("isRequired",!0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("label","City")("optionLabel",b["\u0275\u0275pureFunction0"](87,ke))("bodyParams",t.selectedState)("apiUri","/api/ts_wfh/getCity")("isRequired",!0),b["\u0275\u0275advance"](10),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(a=t.wfhCreationForm.get("primaryContactNumber"))?null:a.invalid),b["\u0275\u0275advance"](10),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(l=t.wfhCreationForm.get("emergencyName"))?null:l.invalid),b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(s=t.wfhCreationForm.get("emergencyCtNumber"))?null:s.invalid),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(c=t.wfhCreationForm.get("relation"))?null:c.invalid),b["\u0275\u0275advance"](14),b["\u0275\u0275property"]("ngForOf",t.checkList),b["\u0275\u0275advance"](12),b["\u0275\u0275property"]("ngStyle",t.wfhCreationForm.get("termsAndConditions").value?b["\u0275\u0275pureFunction0"](88,Oe):b["\u0275\u0275pureFunction0"](89,Ie))("disabled",!t.wfhCreationForm.get("termsAndConditions").value),b["\u0275\u0275advance"](8),b["\u0275\u0275property"]("ngIf",t.fileList.length>0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",0==t.fileList.length),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.fileList.length>0),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngIf",t.fileList.length>0),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngStyle",t.fileList.length>0?b["\u0275\u0275pureFunction0"](90,Oe):b["\u0275\u0275pureFunction0"](91,Ie))("disabled",0==t.fileList.length),b["\u0275\u0275advance"](9),b["\u0275\u0275property"]("ngIf","KAARTECH"==t.ORGANIZATION),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","KAARTECH"!=t.ORGANIZATION),b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("matDatepicker",e)("min",t.tomorrow),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(d=t.wfhCreationForm.get("startDate"))?null:d.invalid),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",e),b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("matDatepicker",n)("min",t.minEndDate)("max",t.maxEndDate),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",null==t.wfhCreationForm||null==(m=t.wfhCreationForm.get("endDate"))?null:m.invalid),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",n),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngIf",!t.wfhCreationForm.value.startDate),b["\u0275\u0275advance"](12),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](92,Pe,t.isBtnActive("long",1,"M"),!t.isBtnActive("long",1,"M"))),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](95,Pe,t.isBtnActive("long",5,"M"),!t.isBtnActive("long",5,"M"))),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](98,Pe,t.isBtnActive("long",2,"M"),!t.isBtnActive("long",2,"M"))),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](101,Pe,t.isBtnActive("long",6,"M"),!t.isBtnActive("long",6,"M"))),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](104,Pe,t.isBtnActive("long",3,"M"),!t.isBtnActive("long",3,"M"))),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](107,Pe,t.isBtnActive("long",1,"y"),!t.isBtnActive("long",1,"y"))),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](110,Pe,t.isBtnActive("long",4,"M"),!t.isBtnActive("long",4,"M"))),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](113,Pe,t.isBtnActive("short",1,"d"),!t.isBtnActive("short",1,"d"))),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](116,Pe,t.isBtnActive("short",1,"w"),!t.isBtnActive("short",1,"w"))),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](119,Pe,t.isBtnActive("short",2,"d"),!t.isBtnActive("short",2,"d"))),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](122,Pe,t.isBtnActive("short",2,"w"),!t.isBtnActive("short",2,"w"))),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](125,Pe,t.isBtnActive("short",5,"d"),!t.isBtnActive("short",5,"d"))),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](128,Pe,t.isBtnActive("short",3,"w"),!t.isBtnActive("short",3,"w"))),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](131,Pe,t.isBtnActive("short",10,"d"),!t.isBtnActive("short",10,"d"))),b["\u0275\u0275advance"](11),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](134,Pe,t.isReportingDaysEnabled("Monday"),!t.isReportingDaysEnabled("Monday")))("disabled",t.isReportingDaysDisabled()),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](137,Pe,t.isReportingDaysEnabled("Thursday"),!t.isReportingDaysEnabled("Thursday")))("disabled",t.isReportingDaysDisabled()),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](140,Pe,t.isReportingDaysEnabled("Tuesday"),!t.isReportingDaysEnabled("Tuesday")))("disabled",t.isReportingDaysDisabled()),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](143,Pe,t.isReportingDaysEnabled("Friday"),!t.isReportingDaysEnabled("Friday")))("disabled",t.isReportingDaysDisabled()),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](146,Pe,t.isReportingDaysEnabled("Wednesday"),!t.isReportingDaysEnabled("Wednesday")))("disabled",t.isReportingDaysDisabled()),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](149,Pe,t.isReportingDaysEnabled("Sunday"),!t.isReportingDaysEnabled("Sunday")))("disabled",t.isReportingDaysDisabled()),b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](152,Pe,t.isReportingWeeksEnabled("Week 1"),!t.isReportingWeeksEnabled("Week 1")))("disabled",t.isReportingWeeksDisabled()),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](155,Pe,t.isReportingWeeksEnabled("Week 3"),!t.isReportingWeeksEnabled("Week 3")))("disabled",t.isReportingWeeksDisabled()),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](158,Pe,t.isReportingWeeksEnabled("Week 2"),!t.isReportingWeeksEnabled("Week 2")))("disabled",t.isReportingWeeksDisabled()),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction2"](161,Pe,t.isReportingWeeksEnabled("Week 4"),!t.isReportingWeeksEnabled("Week 4")))("disabled",t.isReportingWeeksDisabled()),b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](164,_e,t.isSubmitting||t.isSaving?"#cbcbcb":"#cf0001"))("disabled",t.isSubmitting||t.isSaving),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",!t.isSubmitting),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.isSubmitting),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf","edit"==t.mode)}},directives:function(){return[m.a,r.a,_.J,_.w,_.n,l.a,l.b,l.c,s.c,s.g,c.b,_.e,_.v,_.l,_.F,_.q,i.NgIf,V.a,i.NgForOf,u.a,i.NgStyle,I.g,I.i,s.i,I.f,i.NgClass,w.a,s.b,W,v.a,O.b,O.a,x.c]},styles:['.create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}  .mat-horizontal-stepper-header-container{white-space:nowrap;height:45px;display:flex;align-items:center}.tooltip-text[_ngcontent-%COMP%]{font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.highlighted-text[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:13px}.info-icon[_ngcontent-%COMP%]{font-size:20px;color:#9f2825;padding-right:4px;vertical-align:sub}.btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.btn-active[_ngcontent-%COMP%], .btn-not-active[_ngcontent-%COMP%]{font-weight:400;margin-bottom:1%;width:9rem;text-align:center;padding:-1px;overflow:hidden;height:35px;text-overflow:ellipsis;white-space:nowrap}.btn-not-active[_ngcontent-%COMP%]{background-color:#f3f3f3;color:#1a1a1a}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.tick-active[_ngcontent-%COMP%]{color:#009432}.tick-not-active[_ngcontent-%COMP%]{color:#c9c8c8}.cloud-icon[_ngcontent-%COMP%]{font-size:7rem;color:#b8b7b5;display:flex;align-items:center;justify-content:center}.upload-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;margin-bottom:1%;width:8rem;text-align:center;padding:-1px;overflow:hidden;height:33px;font-size:14px;text-overflow:ellipsis;white-space:nowrap}.file-present[_ngcontent-%COMP%]{border:3px dotted #e44a4a;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.file-present[_ngcontent-%COMP%]   .cloud-icon[_ngcontent-%COMP%]{color:#e44a4a}.file-empty[_ngcontent-%COMP%]{border:2px solid #cacaca}.view-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;right:60px;position:absolute;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.icon-btn[_ngcontent-%COMP%]{height:25px;width:25px;line-height:25px}.spinner-color[_ngcontent-%COMP%]{margin:auto;display:inline-block}.close-icon[_ngcontent-%COMP%]{font-size:17px}.mat-horizontal-content-container[_ngcontent-%COMP%]{overflow:hidden;padding:0 24px 22px}']}),e})();var Fe=n("me71");const We=function(e){return{color:e}};function Le(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",1),b["\u0275\u0275elementStart"](1,"div",4),b["\u0275\u0275elementStart"](2,"mat-icon",5),b["\u0275\u0275text"](3,"fiber_manual_record "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"span",6),b["\u0275\u0275text"](5),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"div",7),b["\u0275\u0275text"](7),b["\u0275\u0275pipe"](8,"date"),b["\u0275\u0275pipe"](9,"date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](9,We,n.statusBasedColorSelector(null==e?null:e.new_status_id))),b["\u0275\u0275advance"](3),b["\u0275\u0275textInterpolate"](null==e?null:e.status_desc),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null!=e&&e.created_on?b["\u0275\u0275pipeBind2"](8,3,null==e?null:e.created_on,"dd-MMM-yy"):null!=e&&e.changed_on?b["\u0275\u0275pipeBind2"](9,6,null==e?null:e.changed_on,"dd-MMM-yy"):"-")}}let Re=(()=>{class e{constructor(e){this.wfhService=e,this.statusHistory=[],this.statusList=[],this.initHistoryPage=()=>Object(S.c)(this,void 0,void 0,(function*(){this.statusList=yield this.wfhService.getWfhStatus(),this.statusHistory&&this.statusHistory.length>0&&(this.statusHistory=D.map(this.statusHistory,e=>{console.log(e);let t=D.find(this.statusList,t=>t.id==e.new_status_id);return e.status_desc=t?t.description:"",e.changed_on=k(e.changed_on).toDate(),e}))}))}ngOnInit(){}ngOnChanges(){this.initHistoryPage()}statusBasedColorSelector(e){if(e=Number(e))switch(e){case 1:return"#FF7200";case 2:return"#9980FA";case 3:return"#009432";case 4:return"#9F2825";case 5:return"#209606";case 6:return"#e81022";case 7:case 8:return"#9F2825";case 9:return"#c92020";case 10:return"#adadad"}}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](H))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-history"]],inputs:{statusHistory:"statusHistory"},features:[b["\u0275\u0275NgOnChangesFeature"]],decls:5,vars:1,consts:[[1,"main-ctnr","container-fluid"],[1,"row","mt-2"],[1,"title-txt","colored-title"],["class","row mt-2",4,"ngFor","ngForOf"],[1,"col-4","d-flex"],[1,"status-icon","content-center",3,"ngStyle"],[1,"content-center"],[1,"col-8"]],template:function(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"span",2),b["\u0275\u0275text"](3,"History"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](4,Le,10,11,"div",3),b["\u0275\u0275elementEnd"]()),2&e&&(b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngForOf",t.statusHistory))},directives:[i.NgForOf,r.a,i.NgStyle],pipes:[i.DatePipe],styles:[".main-ctnr[_ngcontent-%COMP%]{font-size:14px}.main-ctnr[_ngcontent-%COMP%]   .content-center[_ngcontent-%COMP%]{align-items:center;display:flex}.main-ctnr[_ngcontent-%COMP%]   .colored-title[_ngcontent-%COMP%]{color:#c92020}.main-ctnr[_ngcontent-%COMP%]   .title-txt[_ngcontent-%COMP%]{font-size:15px;font-weight:500}.main-ctnr[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{color:#6f6f6f}.main-ctnr[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:17px}"]}),e})();const Ae=function(e){return{color:e}};function Ne(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",40),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](2,"div",40),b["\u0275\u0275text"](3),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",40),b["\u0275\u0275text"](5),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"div",41),b["\u0275\u0275elementStart"](7,"div",42),b["\u0275\u0275text"](8),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"mat-icon",43),b["\u0275\u0275text"](10,"fiber_manual_record"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]().$implicit,t=b["\u0275\u0275nextContext"](3);b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.employee_name),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](e.appr_desgn),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"](" Level ",e.appr_level," "),b["\u0275\u0275advance"](3),b["\u0275\u0275textInterpolate1"](" ",t.getStatusDescriptionWorkflow(e.status)," "),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](5,Ae,t.statusColorApprovers(e.status)))}}function Te(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",36),b["\u0275\u0275element"](1,"app-user-image",37),b["\u0275\u0275template"](2,Ne,11,7,"ng-template",38,39,b["\u0275\u0275templateRefExtractor"]),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=b["\u0275\u0275reference"](3),i=b["\u0275\u0275nextContext"](3);b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("tooltip",n)("id",e.actual_appr_oid)("borderColor",e.is_curr_appr?"#ffa502":i.statusColorApprovers(e.status))}}function qe(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",34),b["\u0275\u0275template"](1,Te,4,3,"div",35),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"](2);b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.workflowDetails)}}function je(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",18),b["\u0275\u0275text"](1,"Approved On"),b["\u0275\u0275elementEnd"]())}function He(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",18),b["\u0275\u0275text"](1,"Rejected On"),b["\u0275\u0275elementEnd"]())}function Be(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",19),b["\u0275\u0275text"](1),b["\u0275\u0275pipe"](2,"date"),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"](2);b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](b["\u0275\u0275pipeBind2"](2,1,null==e.genDetails?null:e.genDetails.cc_action_on,"dd-MMM-yy"))}}const Ve=function(e){return{"background-color":e}},ze=function(e){return{"btn-selected":e}},Je=function(e){return{color:e}};function Ye(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",5),b["\u0275\u0275elementStart"](1,"div",6),b["\u0275\u0275elementStart"](2,"div",7),b["\u0275\u0275text"](3),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",8),b["\u0275\u0275elementStart"](5,"button",9),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().closeDialog()})),b["\u0275\u0275elementStart"](6,"mat-icon",10),b["\u0275\u0275text"](7,"clear"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](8,"div",11),b["\u0275\u0275elementStart"](9,"div",12),b["\u0275\u0275elementStart"](10,"div",13),b["\u0275\u0275elementStart"](11,"div",14),b["\u0275\u0275text"](12,"Start Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](13,"div",15),b["\u0275\u0275text"](14),b["\u0275\u0275pipe"](15,"date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](16,"div",16),b["\u0275\u0275text"](17,"Approvers"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](18,qe,2,1,"div",17),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](19,"div",11),b["\u0275\u0275elementStart"](20,"div",18),b["\u0275\u0275text"](21,"End Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"div",19),b["\u0275\u0275text"](23),b["\u0275\u0275pipe"](24,"date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](25,je,2,0,"div",20),b["\u0275\u0275template"](26,He,2,0,"div",20),b["\u0275\u0275template"](27,Be,3,4,"div",21),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](28,"div",22),b["\u0275\u0275elementStart"](29,"div",18),b["\u0275\u0275text"](30,"Submitted On"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](31,"div",19),b["\u0275\u0275text"](32),b["\u0275\u0275pipe"](33,"date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](34,"div",18),b["\u0275\u0275text"](35,"Status"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](36,"div",23),b["\u0275\u0275element"](37,"div",24),b["\u0275\u0275elementStart"](38,"span",25),b["\u0275\u0275text"](39),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](40,"div",26),b["\u0275\u0275elementStart"](41,"div",13),b["\u0275\u0275elementStart"](42,"mat-card",27),b["\u0275\u0275elementStart"](43,"span",28),b["\u0275\u0275text"](44),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](45,"span",29),b["\u0275\u0275text"](46,"Days Left"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](47,"div",30),b["\u0275\u0275elementStart"](48,"button",31),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().openHistory()})),b["\u0275\u0275elementStart"](49,"mat-icon",32),b["\u0275\u0275text"](50,"history"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](51,"button",33),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().openCommentsContext()})),b["\u0275\u0275elementStart"](52,"mat-icon",32),b["\u0275\u0275text"](53,"question_answer"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](3),b["\u0275\u0275textInterpolate1"](" ",null==e.genDetails?null:e.genDetails.duration_selection_text," "),b["\u0275\u0275advance"](11),b["\u0275\u0275textInterpolate1"](" ",b["\u0275\u0275pipeBind2"](15,15,null==e.genDetails?null:e.genDetails.start_date,"dd-MMM-yy")," "),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngIf",e.workflowDetails&&e.workflowDetails.length>0),b["\u0275\u0275advance"](5),b["\u0275\u0275textInterpolate1"](" ",b["\u0275\u0275pipeBind2"](24,18,null==e.genDetails?null:e.genDetails.end_date,"dd-MMM-yy")," "),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",6!=(null==e.genDetails?null:e.genDetails.status)),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",6==(null==e.genDetails?null:e.genDetails.status)),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",1!=(null==e.genDetails?null:e.genDetails.status)&&9!=(null==e.genDetails?null:e.genDetails.status)&&10!=(null==e.genDetails?null:e.genDetails.status)),b["\u0275\u0275advance"](5),b["\u0275\u0275textInterpolate1"](" ",b["\u0275\u0275pipeBind2"](33,21,null==e.genDetails?null:e.genDetails.created_on,"dd-MMM-yy")," "),b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](24,Ve,e.statusBasedColorSelector())),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null==e.genDetails?null:e.genDetails.status_description),b["\u0275\u0275advance"](5),b["\u0275\u0275textInterpolate1"]("",e.genDetails.days_left?e.genDetails.days_left:" - "," "),b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](26,ze,e.isHistoryClicked)),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](28,Je,e.isHistoryClicked?"#ffffff":"")),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngClass",b["\u0275\u0275pureFunction1"](30,ze,e.isCommentsClicked)),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](32,Je,e.isCommentsClicked?"#ffffff":""))}}function Xe(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",49),b["\u0275\u0275text"](1,"Attachments"),b["\u0275\u0275elementEnd"]())}function Ge(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"div",58),b["\u0275\u0275elementStart"](2,"span",59),b["\u0275\u0275element"](3,"img",60),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("src",e,b["\u0275\u0275sanitizeUrl"])}}function Ue(e,t){1&e&&b["\u0275\u0275element"](0,"mat-spinner",61),2&e&&b["\u0275\u0275property"]("diameter",30)}function $e(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",66),b["\u0275\u0275elementStart"](1,"div",67),b["\u0275\u0275elementStart"](2,"span",68),b["\u0275\u0275elementStart"](3,"button",69),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"](3).selectCheckList(n)})),b["\u0275\u0275elementStart"](4,"mat-icon",70),b["\u0275\u0275text"](5,"check_circle"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"span",71),b["\u0275\u0275text"](7),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngClass",e.isSelected?"tick-active":"tick-not-active"),b["\u0275\u0275advance"](4),b["\u0275\u0275textInterpolate1"](" ",e.item," ")}}function Ke(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",62),b["\u0275\u0275elementStart"](1,"span",63),b["\u0275\u0275text"](2,"Checklist"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](3,"div",64),b["\u0275\u0275template"](4,$e,8,2,"div",65),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"](2);b["\u0275\u0275advance"](4),b["\u0275\u0275property"]("ngForOf",null==e.genDetails?null:e.genDetails.checklist)}}function Ze(e,t){if(1&e&&b["\u0275\u0275element"](0,"app-wfh-history",72),2&e){const e=b["\u0275\u0275nextContext"](2);b["\u0275\u0275property"]("statusHistory",e.genDetails.status_change_history)}}function Qe(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",44),b["\u0275\u0275elementStart"](1,"div",45),b["\u0275\u0275elementStart"](2,"div",46),b["\u0275\u0275text"](3,"Working from"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",47),b["\u0275\u0275elementStart"](5,"span"),b["\u0275\u0275text"](6),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"span"),b["\u0275\u0275text"](8),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"span",48),b["\u0275\u0275text"](10),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](11,"span"),b["\u0275\u0275text"](12),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](13,"div",49),b["\u0275\u0275text"](14,"Contact No"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](15,"div",11),b["\u0275\u0275text"](16),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"div",49),b["\u0275\u0275text"](18,"Emergency Contact"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](19,"div",47),b["\u0275\u0275elementStart"](20,"span"),b["\u0275\u0275text"](21),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"span"),b["\u0275\u0275text"](23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](24,"span"),b["\u0275\u0275text"](25),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](26,Xe,2,0,"div",50),b["\u0275\u0275elementStart"](27,"div",44),b["\u0275\u0275template"](28,Ge,4,1,"div",51),b["\u0275\u0275template"](29,Ue,1,1,"mat-spinner",52),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](30,"div",53),b["\u0275\u0275element"](31,"mat-divider",54),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](32,"div",55),b["\u0275\u0275template"](33,Ke,5,1,"div",56),b["\u0275\u0275template"](34,Ze,1,1,"app-wfh-history",57),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](6),b["\u0275\u0275textInterpolate"](null==e.genDetails?null:e.genDetails.address_line1),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null==e.genDetails?null:e.genDetails.address_line2),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate3"]("",null==e.genDetails||null==e.genDetails.city?null:e.genDetails.city.name," - ",null==e.genDetails?null:e.genDetails.pin_code,", ",null==e.genDetails||null==e.genDetails.state?null:e.genDetails.state.name,""),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null==e.genDetails||null==e.genDetails.country?null:e.genDetails.country.name),b["\u0275\u0275advance"](4),b["\u0275\u0275textInterpolate"](null==e.genDetails?null:e.genDetails.primary_ct_no),b["\u0275\u0275advance"](5),b["\u0275\u0275textInterpolate"](null==e.genDetails?null:e.genDetails.emergency_ct_name),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null==e.genDetails?null:e.genDetails.emergency_relation),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null==e.genDetails?null:e.genDetails.emergency_ct_no),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",e.fileList.length>0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngForOf",e.fileList),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",e.isImgLoading),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("vertical",!0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",!e.isHistoryClicked),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",e.genDetails&&e.isHistoryClicked)}}function et(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"app-chat-comment-context",73),b["\u0275\u0275listener"]("closeCommentsContext",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().closeCommentsContext()})),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275property"]("commentBoxHeight","70vh")("commentBoxScrollHeight","80%")("inputData",e.commentsData)("context",e.commentsContext)("contextWidth","95%")}}function tt(e,t){1&e&&b["\u0275\u0275element"](0,"mat-spinner",74),2&e&&b["\u0275\u0275property"]("diameter",60)}let nt=(()=>{class e{constructor(e,t,i,r,o,a,l,s,c){this.dialogRef=e,this.data=t,this.dialog=i,this.workflowService=r,this.utilityService=o,this.wfhService=a,this.fb=l,this.router=s,this.errorService=c,this.mode="view",this.genDetails="",this.workflowDetails={},this.isCommentsClicked=!1,this.commentsContext={},this.commentsData={},this.isHistoryClicked=!1,this.isImgLoading=!1,this.fileList=[],this.initPage=()=>Object(S.c)(this,void 0,void 0,(function*(){if(console.log(this.data),this.genDetails=yield this.wfhService.getWfhDetailById(this.data),this.genDetails.checklist="string"==typeof this.genDetails.checklist?JSON.parse(this.genDetails.checklist):this.genDetails.checklist,this.genDetails.country="string"==typeof this.genDetails.country?JSON.parse(this.genDetails.country):this.genDetails.country,this.genDetails.state="string"==typeof this.genDetails.state?JSON.parse(this.genDetails.state):this.genDetails.state,this.genDetails.city="string"==typeof this.genDetails.city?JSON.parse(this.genDetails.city):this.genDetails.city,this.genDetails.attachments){this.isImgLoading=!0,this.genDetails.attachments="string"==typeof this.genDetails.attachments?JSON.parse(this.genDetails.attachments):this.genDetails.attachments;for(let e of this.genDetails.attachments)yield this.wfhService.getImageFromS3(e.key).then(t=>{this.isImgLoading=!1,this.fileList.push("data:"+e.type+";base64,"+t.data.fileData)},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","File Not found while retrieving from S3",e&&e.params?e.params:e&&e.error?e.error.params:{})})}if(this.genDetails&&this.genDetails.workflow_header_id){let e=yield this.workflowService.getWorkflowDetails(this.genDetails.workflow_header_id);console.log(e),e&&"S"==e.messType?this.workflowDetails=e.data:this.utilityService.showMessage("Error while retrieving Workflow details","Dismiss")}else this.utilityService.showMessage("Error while retrieving Remote WorX Request Details","Dismiss")})),this.openCommentsContext=()=>Object(S.c)(this,void 0,void 0,(function*(){let e={context:{Details:this.genDetails.duration_selection_text,"Start Date":this.genDetails.start_date?k(this.genDetails.start_date).format("DD-MMM-YY"):"","End Date":this.genDetails.end_date?k(this.genDetails.end_date).format("DD-MMM-YY"):"",Status:this.genDetails.status_description},inputData:{application_id:83,unique_id_1:this.genDetails.id,unique_id_2:this.genDetails.workflow_header_id,application_name:"Remote WorX",title:"Remote WorX #"+this.genDetails.id,link:window.location.origin+""+this.router.url},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};const{ChatCommentContextModalComponent:t}=yield Promise.all([n.e(4),n.e(63),n.e(75),n.e(983)]).then(n.bind(null,"vg2w"));this.dialog.open(t,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:e}}),this.isHistoryClicked=!1})),this.closeDialog=()=>{this.dialogRef.close()}}ngOnInit(){this.initPage()}statusBasedColorSelector(){if(this.genDetails&&this.genDetails.status)switch(this.genDetails.status){case 1:return"#FF7200";case 2:return"#9980FA";case 3:return"#009432";case 4:return"#9F2825";case 5:return"#209606";case 6:return"#e81022";case 7:case 8:return"#9F2825";case 9:return"#c92020";case 10:return"#adadad"}}statusColorApprovers(e){return"S"==e?"#b7b7b7":"A"==e?"#009432":"R"==e?"#af0505":"RL"==e?"#ff7200":"white"}getStatusDescriptionWorkflow(e){let t=D.findWhere(this.workflowService.workflowStatusList,{code:e});return t?t.status:""}closeCommentsContext(){this.isCommentsClicked=!1}openHistory(){this.isCommentsClicked=!1,this.isHistoryClicked=!this.isHistoryClicked,this.genDetails.status_change_history=this.genDetails.status_change_history&&"string"==typeof this.genDetails.status_change_history?JSON.parse(this.genDetails.status_change_history):this.genDetails.status_change_history}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](d.h),b["\u0275\u0275directiveInject"](d.a),b["\u0275\u0275directiveInject"](d.b),b["\u0275\u0275directiveInject"](T.a),b["\u0275\u0275directiveInject"](q.a),b["\u0275\u0275directiveInject"](H),b["\u0275\u0275directiveInject"](_.i),b["\u0275\u0275directiveInject"](y.g),b["\u0275\u0275directiveInject"](j.a))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-details-overview"]],inputs:{mode:"mode"},decls:5,vars:4,consts:[[1,"main-ctnr","container-fluid","h-100","pt-2","d-flex","flex-column"],["class","py-2 background-emphasis slide-top",4,"ngIf"],["class","row mt-2 d-flex",4,"ngIf"],[3,"commentBoxHeight","commentBoxScrollHeight","inputData","context","contextWidth","closeCommentsContext",4,"ngIf"],["class","mx-auto my-auto",3,"diameter",4,"ngIf"],[1,"py-2","background-emphasis","slide-top"],[1,"row","d-flex"],[1,"col-11","title-txt","colored-title"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","icon-btn",3,"click"],[1,"close-icon"],[1,"row","mt-2"],[1,"col-9","pl-0"],[1,"row"],[1,"col-3","d-flex","align-items-center","content-title"],[1,"col-3","d-flex","align-items-center","title-txt"],[1,"col-3","content-title","d-flex","align-items-center"],["class","col-3 align-items-center title-txt d-flex",4,"ngIf"],[1,"col-3","content-title"],[1,"col-3","title-txt"],["class","col-3 content-title",4,"ngIf"],["class","col-3 title-txt",4,"ngIf"],[1,"row","mt-3"],[1,"col-3","d-flex"],[1,"status-circular","mr-2","mb-1",3,"ngStyle"],[1,"content-center","title-txt"],[1,"col-3"],[1,"w-100","p-1","d-flex","flex-column"],[1,"mx-auto","pt-1","pb-1","content-center","title-txt","colored-title"],[1,"mx-auto","content-center","mt-1","content-title","title-txt"],[1,"row","mt-3","col-12","d-flex"],["mat-icon-button","",1,"ml-auto","my-auto","mr-2","icon-btn",3,"ngClass","click"],[1,"icon",3,"ngStyle"],["mat-icon-button","",1,"mr-2","my-auto","icon-btn",3,"ngClass","click"],[1,"col-3","align-items-center","title-txt","d-flex"],["class","p-0 my-auto",4,"ngFor","ngForOf"],[1,"p-0","my-auto"],["content-type","template","placement","top","imgHeight","35px","imgWidth","35px","borderStyle","solid","borderWidth","3px",2,"margin","2px",3,"tooltip","id","borderColor"],["placement","top"],["approverTooltip",""],[1,"row","tooltip-text"],[1,"d-flex","flex-row","mx-auto"],[1,"pb-1","row","tooltip-text"],[1,"tooltip-status-indicator","p-0","mt-0","mb-0","ml-2",3,"ngStyle"],[1,"row","mt-2","d-flex"],[1,"col-4","slide-in-top"],[1,"row","mt-2","title-txt","colored-title"],[1,"row","mt-2","d-flex","flex-column"],[1,"d-flex"],[1,"row","mt-4","title-txt","colored-title"],["class","row mt-4 title-txt colored-title",4,"ngIf"],[4,"ngFor","ngForOf"],["class","mx-auto mt-3",3,"diameter",4,"ngIf"],[1,"col-1","row","d-flex"],[1,"mx-auto",3,"vertical"],[1,"col-7"],["class","row d-flex flex-column",4,"ngIf"],[3,"statusHistory",4,"ngIf"],[1,"mt-1","mb-1",2,"border","solid 2px #cacaca"],[1,"d-flex","justify-content-center"],["height","200","width","300",3,"src"],[1,"mx-auto","mt-3",3,"diameter"],[1,"row","d-flex","flex-column"],[1,"title-txt","colored-title"],[1,"mt-2","card","slide-in-top"],["class","row d-flex pt-2 pb-1 pl-3","style","height: 59px",4,"ngFor","ngForOf"],[1,"row","d-flex","pt-2","pb-1","pl-3",2,"height","59px"],[1,"col-12","p-0","border-bottom","solid"],[1,"p-0"],["mat-icon-button","","disabled","",2,"width","34px","height","34px",3,"ngClass","click"],[2,"font-size","22px"],[1,"ml-2","pt-2",2,"font-weight","500","font-size","14px"],[3,"statusHistory"],[3,"commentBoxHeight","commentBoxScrollHeight","inputData","context","contextWidth","closeCommentsContext"],[1,"mx-auto","my-auto",3,"diameter"]],template:function(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275template"](1,Ye,54,34,"div",1),b["\u0275\u0275template"](2,Qe,35,16,"div",2),b["\u0275\u0275template"](3,et,1,5,"app-chat-comment-context",3),b["\u0275\u0275template"](4,tt,1,1,"mat-spinner",4),b["\u0275\u0275elementEnd"]()),2&e&&(b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.genDetails),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.genDetails&&!t.isCommentsClicked),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.genDetails&&t.isCommentsClicked),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",!t.genDetails))},directives:[i.NgIf,m.a,r.a,i.NgStyle,o.a,i.NgClass,i.NgForOf,Fe.a,v.a,f.a,x.c,Re],pipes:[i.DatePipe],styles:['.main-ctnr[_ngcontent-%COMP%]{font-size:14px}.main-ctnr[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:13px}.main-ctnr[_ngcontent-%COMP%]   .content-center[_ngcontent-%COMP%]{align-items:center;display:flex}.main-ctnr[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{height:27px;width:27px;line-height:27px}.main-ctnr[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:17px}.main-ctnr[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;color:#7d7d79}.main-ctnr[_ngcontent-%COMP%]   .colored-title[_ngcontent-%COMP%]{color:#cf0001}.main-ctnr[_ngcontent-%COMP%]   .title-txt[_ngcontent-%COMP%]{font-weight:500}.main-ctnr[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{color:#6f6f6f}.main-ctnr[_ngcontent-%COMP%]   .background-emphasis[_ngcontent-%COMP%]{background-color:#f5f5f5}.main-ctnr[_ngcontent-%COMP%]   .slide-top[_ngcontent-%COMP%]{animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-top{0%{transform:translateY(8px)}to{transform:translateY(0)}}.main-ctnr[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.main-ctnr[_ngcontent-%COMP%]   .tick-active[_ngcontent-%COMP%]{color:#009432}.main-ctnr[_ngcontent-%COMP%]   .tick-not-active[_ngcontent-%COMP%]{color:#c9c8c8}.main-ctnr[_ngcontent-%COMP%]   .btn-selected[_ngcontent-%COMP%]{background-color:#c92020!important;color:#fff!important}.main-ctnr[_ngcontent-%COMP%]   .tooltip-text[_ngcontent-%COMP%]{font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.main-ctnr[_ngcontent-%COMP%]   .tooltip-status-indicator[_ngcontent-%COMP%]{font-size:12px;margin-top:9px}.main-ctnr[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}']}),e})();var it=n("PSD3"),rt=n.n(it);function ot(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",19),b["\u0275\u0275elementStart"](1,"span"),b["\u0275\u0275elementStart"](2,"b"),b["\u0275\u0275text"](3,"Duration"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"span",20),b["\u0275\u0275text"](5," :"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"span",21),b["\u0275\u0275text"](7),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](8,"div",19),b["\u0275\u0275elementStart"](9,"span"),b["\u0275\u0275elementStart"](10,"b"),b["\u0275\u0275text"](11,"Reporting Days"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](12,"span",22),b["\u0275\u0275text"](13," : "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](14,"span",21),b["\u0275\u0275text"](15),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](16,"div",19),b["\u0275\u0275elementStart"](17,"span"),b["\u0275\u0275elementStart"](18,"b"),b["\u0275\u0275text"](19,"Reporting Weeks"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](20,"span",23),b["\u0275\u0275text"](21,":"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"span",21),b["\u0275\u0275text"](23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](7),b["\u0275\u0275textInterpolate1"]("",e.wfhDetail.wfhDuration," "),b["\u0275\u0275advance"](8),b["\u0275\u0275textInterpolate1"](" ",e.wfhDetail.reportingDays,""),b["\u0275\u0275advance"](8),b["\u0275\u0275textInterpolate"](e.wfhDetail.reportingWeeks)}}function at(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"button",24),b["\u0275\u0275listener"]("click",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().editWfh(),t.stopPropagation()})),b["\u0275\u0275elementStart"](1,"mat-icon",18),b["\u0275\u0275text"](2,"edit"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}}function lt(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"button",25),b["\u0275\u0275listener"]("click",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().recall(),t.stopPropagation()})),b["\u0275\u0275elementStart"](1,"mat-icon",18),b["\u0275\u0275text"](2,"undo"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}}function st(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"button",26),b["\u0275\u0275listener"]("click",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().cancel(),t.stopPropagation()})),b["\u0275\u0275elementStart"](1,"mat-icon",18),b["\u0275\u0275text"](2,"cancel"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}}function ct(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"button",27),b["\u0275\u0275listener"]("click",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().complete(),t.stopPropagation()})),b["\u0275\u0275elementStart"](1,"mat-icon",18),b["\u0275\u0275text"](2,"offline_pin"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}}const dt=function(e){return{"border-color":e}},mt=function(e){return{"background-color":e}};let pt=(()=>{class e{constructor(e,t,i,r){this.wfhService=e,this.dialog=t,this.utilityService=i,this.router=r,this.wfhDetail={},this.editWfh=()=>{this.wfhService.setEditWfhId(this.wfhDetail.id),this.wfhService.setCreateWfhMode("edit")},this.recall=()=>Object(S.c)(this,void 0,void 0,(function*(){(yield this.confirmSweetAlert("Are you sure ?","This will recall the Remote WorX Request. Once recalled, you can edit and resubmit")).value&&(yield this.wfhService.recallWfh(this.wfhDetail),this.utilityService.showMessage("Remote WorX Request Recalled Successfully","Dismiss"))})),this.cancel=()=>Object(S.c)(this,void 0,void 0,(function*(){(yield this.confirmSweetAlert("Are you sure ?","By cancelling, it means you have not worked from home during the duration of this request.")).value&&(yield this.wfhService.cancelWfh(this.wfhDetail),this.utilityService.showMessage("Remote WorX Request Cancelled Successfully","Dismiss"))})),this.complete=()=>Object(S.c)(this,void 0,void 0,(function*(){(yield this.confirmSweetAlert("Are you sure ?","By completing, it means you have worked from home in accordance with duration of the request till today and you wish to resume office from tomorrow.")).value&&(yield this.wfhService.completeWfh(this.wfhDetail),this.utilityService.showMessage("Remote WorX Request Completed Successfully","Dismiss"))})),this.getWfhSweetAlertTemplate=(e,t)=>({customClass:{title:"title-class",confirmButton:"confirm-button-class",cancelButton:"confirm-button-class"},title:e,text:t,type:"warning",showConfirmButton:!0,showCancelButton:!0}),this.openComments=()=>Object(S.c)(this,void 0,void 0,(function*(){let e={context:{Details:this.wfhDetail.duration_text,"Start Date":this.wfhDetail.start_date?k(this.wfhDetail.start_date).format("DD-MMM-YY"):"","End Date":this.wfhDetail.end_date?k(this.wfhDetail.end_date).format("DD-MMM-YY"):"",Status:this.wfhDetail.status_description},inputData:{application_id:83,unique_id_1:this.wfhDetail.id,unique_id_2:this.wfhDetail.workflow_header_id,application_name:"Remote WorX",title:"Remote WorX #"+this.wfhDetail.id,link:window.location.origin+""+this.router.url},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};const{ChatCommentContextModalComponent:t}=yield Promise.all([n.e(4),n.e(63),n.e(75),n.e(983)]).then(n.bind(null,"vg2w"));this.dialog.open(t,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:e}})}))}ngOnInit(){}ngOnChanges(){}statusBasedColorSelector(){if(this.wfhDetail&&this.wfhDetail.status_id)switch(this.wfhDetail.status_id){case 1:return"#FF7200";case 2:return"#9980FA";case 3:return"#009432";case 4:return"#9F2825";case 5:return"#209606";case 6:return"#e81022";case 7:case 8:return"#9F2825";case 9:return"#c92020";case 10:return"#adadad"}}openWFHOverview(){return Object(S.c)(this,void 0,void 0,(function*(){this.dialog.open(nt,{height:"99%",width:"86%",maxWidth:"86%",data:this.wfhDetail.id})}))}confirmSweetAlert(e,t){let n=this.getWfhSweetAlertTemplate(e,t);return rt.a.fire(n)}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](H),b["\u0275\u0275directiveInject"](d.b),b["\u0275\u0275directiveInject"](q.a),b["\u0275\u0275directiveInject"](y.g))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-details-card"]],inputs:{wfhDetail:"wfhDetail"},features:[b["\u0275\u0275NgOnChangesFeature"]],decls:30,vars:23,consts:[[1,"main-ctnr","container-fluid"],[1,"d-flex","p-0","detail-card",3,"ngStyle","click"],[1,"col-1","text-bold","content-center","py-2"],["content-type","template","max-width","300","placement","top",1,"col-3","py-2","content-center",3,"tooltip"],[1,"text-bold","emphasis-text","wrapped-text"],["wfhTooltip",""],[1,"col-2","text-bold","content-center","py-2"],[1,"ml-1"],[1,"col-2","py-2","d-flex"],[1,"status-circular","mr-2","mb-1",3,"ngStyle"],[1,"content-center"],[1,"col-1","content-center","py-2"],[1,"col-1","d-flex","card-btn-grp"],["mat-icon-button","","matTooltip","Edit","class","my-auto mr-2 icon-btn",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Recall","class","my-auto mr-2 icon-btn",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Cancel","class","my-auto mr-2 icon-btn",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Pre-emptive Complete","class","my-auto mr-2 icon-btn",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Comments",1,"mr-2","my-auto","icon-btn",3,"click"],[1,"icon"],[1,"row",2,"font-size","12px"],[2,"padding-left","1.5rem !important"],[1,"ml-2"],[2,"padding-left","1.1rem !important"],[2,"padding-left","0.5rem !important"],["mat-icon-button","","matTooltip","Edit",1,"my-auto","mr-2","icon-btn",3,"click"],["mat-icon-button","","matTooltip","Recall",1,"my-auto","mr-2","icon-btn",3,"click"],["mat-icon-button","","matTooltip","Cancel",1,"my-auto","mr-2","icon-btn",3,"click"],["mat-icon-button","","matTooltip","Pre-emptive Complete",1,"my-auto","mr-2","icon-btn",3,"click"]],template:function(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"mat-card",1),b["\u0275\u0275listener"]("click",(function(){return t.openWFHOverview()})),b["\u0275\u0275elementStart"](2,"div",2),b["\u0275\u0275text"](3),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",3),b["\u0275\u0275elementStart"](5,"span",4),b["\u0275\u0275text"](6),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](7,ot,24,3,"ng-template",null,5,b["\u0275\u0275templateRefExtractor"]),b["\u0275\u0275elementStart"](9,"div",6),b["\u0275\u0275elementStart"](10,"span",7),b["\u0275\u0275text"](11),b["\u0275\u0275pipe"](12,"date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](13,"div",6),b["\u0275\u0275text"](14),b["\u0275\u0275pipe"](15,"date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](16,"div",8),b["\u0275\u0275element"](17,"div",9),b["\u0275\u0275elementStart"](18,"span",10),b["\u0275\u0275text"](19),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](20,"div",11),b["\u0275\u0275text"](21),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"div",12),b["\u0275\u0275template"](23,at,3,0,"button",13),b["\u0275\u0275template"](24,lt,3,0,"button",14),b["\u0275\u0275template"](25,st,3,0,"button",15),b["\u0275\u0275template"](26,ct,3,0,"button",16),b["\u0275\u0275elementStart"](27,"button",17),b["\u0275\u0275listener"]("click",(function(e){return t.openComments(),e.stopPropagation()})),b["\u0275\u0275elementStart"](28,"mat-icon",18),b["\u0275\u0275text"](29,"question_answer"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275reference"](8);b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](19,dt,t.statusBasedColorSelector())),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"]("#",null==t.wfhDetail?null:t.wfhDetail.id,""),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("tooltip",e),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"](" ",null==t.wfhDetail?null:t.wfhDetail.duration_text,""),b["\u0275\u0275advance"](5),b["\u0275\u0275textInterpolate1"]("",b["\u0275\u0275pipeBind2"](12,13,null==t.wfhDetail?null:t.wfhDetail.start_date,"dd-MMM-yy")," "),b["\u0275\u0275advance"](3),b["\u0275\u0275textInterpolate1"](" ",b["\u0275\u0275pipeBind2"](15,16,null==t.wfhDetail?null:t.wfhDetail.end_date,"dd-MMM-yy")," "),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](21,mt,t.statusBasedColorSelector())),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate"](null==t.wfhDetail?null:t.wfhDetail.status_description),b["\u0275\u0275advance"](2),b["\u0275\u0275textInterpolate1"](" ",t.wfhDetail.days_left?t.wfhDetail.days_left+" Days":" - "," "),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngIf",10==(null==t.wfhDetail?null:t.wfhDetail.status_id)),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",1==(null==t.wfhDetail?null:t.wfhDetail.status_id)||6==(null==t.wfhDetail?null:t.wfhDetail.status_id)),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",5==(null==t.wfhDetail?null:t.wfhDetail.status_id)||2==(null==t.wfhDetail?null:t.wfhDetail.status_id)),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",2==(null==t.wfhDetail?null:t.wfhDetail.status_id))}},directives:[o.a,i.NgStyle,v.a,i.NgIf,m.a,w.a,r.a],pipes:[i.DatePipe],styles:['.main-ctnr[_ngcontent-%COMP%]{font-size:13px}.main-ctnr[_ngcontent-%COMP%]   .detail-card[_ngcontent-%COMP%]{border-left:3px solid #3b883b;border-radius:2px}.main-ctnr[_ngcontent-%COMP%]   .text-bold[_ngcontent-%COMP%]{font-weight:500}.main-ctnr[_ngcontent-%COMP%]   .emphasis-text[_ngcontent-%COMP%]{color:#cf0001}.main-ctnr[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{height:28px;width:27px;line-height:28px}.main-ctnr[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;color:#868683}.main-ctnr[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:17px;color:#5c5c5c}.main-ctnr[_ngcontent-%COMP%]   .detail-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);cursor:pointer}.main-ctnr[_ngcontent-%COMP%]   .detail-card[_ngcontent-%COMP%]:hover   .card-btn-grp[_ngcontent-%COMP%]{visibility:visible!important;justify-content:flex-end;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.main-ctnr[_ngcontent-%COMP%]   .card-btn-grp[_ngcontent-%COMP%]{visibility:hidden}.main-ctnr[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:17px}.main-ctnr[_ngcontent-%COMP%]   .content-center[_ngcontent-%COMP%]{align-items:center;display:flex}.main-ctnr[_ngcontent-%COMP%]   .title-class[_ngcontent-%COMP%]{font-size:18px!important}.main-ctnr[_ngcontent-%COMP%]   .confirm-button-class[_ngcontent-%COMP%]{margin:10px!important;font-size:15px!important;padding:11px 35px!important;box-shadow:none}.main-ctnr[_ngcontent-%COMP%]   .wrapped-text[_ngcontent-%COMP%]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.main-ctnr[_ngcontent-%COMP%]   .tooltip-text[_ngcontent-%COMP%]{font-size:5px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.main-ctnr[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}']}),e})();function ht(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",20),b["\u0275\u0275elementStart"](1,"div",21),b["\u0275\u0275elementStart"](2,"span",22),b["\u0275\u0275text"](3,"ID"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",23),b["\u0275\u0275elementStart"](5,"span",24),b["\u0275\u0275text"](6,"Details"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"div",25),b["\u0275\u0275elementStart"](8,"span",26),b["\u0275\u0275text"](9,"Start Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"div",27),b["\u0275\u0275elementStart"](11,"span",28),b["\u0275\u0275text"](12,"End Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](13,"div",27),b["\u0275\u0275elementStart"](14,"span",28),b["\u0275\u0275text"](15,"Status"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](16,"div",29),b["\u0275\u0275elementStart"](17,"span"),b["\u0275\u0275text"](18,"Days Left"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](19,"div",30),b["\u0275\u0275elementEnd"]())}function ut(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",32),b["\u0275\u0275elementStart"](1,"div",33),b["\u0275\u0275element"](2,"app-wfh-details-card",34),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("wfhDetail",e)}}function ft(e,t){if(1&e&&(b["\u0275\u0275elementContainerStart"](0),b["\u0275\u0275template"](1,ut,3,1,"div",31),b["\u0275\u0275elementContainerEnd"]()),2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngForOf",e.wfhList)("ngForOf",e.index)}}function gt(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",35),b["\u0275\u0275element"](1,"div",36),b["\u0275\u0275elementStart"](2,"div",37),b["\u0275\u0275element"](3,"mat-spinner",38),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](4,"div",36),b["\u0275\u0275elementEnd"]())}let vt=(()=>{class e{constructor(e,t){this.wfhService=e,this.dialog=t,this.wfhList=[],this.searchText=new _.j,this.isLoading=!1,this.openWfhCreateNav=()=>Object(S.c)(this,void 0,void 0,(function*(){this.wfhService.setCreateWfhMode("create")})),this.searchChanges=()=>{this.searchTextSubscription=this.searchText.valueChanges.subscribe(e=>Object(S.c)(this,void 0,void 0,(function*(){e?(this.isLoading=!0,yield this.wfhService.searchWfhList(e),this.isLoading=!1):this.wfhService.refreshWfhList()})))},this.clearSearch=()=>{this.searchText.patchValue("")},this.searchChanges()}ngOnInit(){this.initWFHList()}initWFHList(){return Object(S.c)(this,void 0,void 0,(function*(){yield this.wfhService.refreshWfhList(),this.wfhListSubscription=this.wfhService.wfhList.subscribe(e=>{this.wfhList=e,this.wfhList=D.sortBy(this.wfhList,e=>e.id),this.wfhList.reverse()}),console.log("this.wfhList"),console.log(this.wfhList)}))}openWfhInfo(){return Object(S.c)(this,void 0,void 0,(function*(){this.dialog.open(E,{height:"94%",width:"83%",minWidth:"83%",minHeight:"88%"})}))}ngOnDestroy(){this.wfhListSubscription.unsubscribe(),this.searchTextSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](H),b["\u0275\u0275directiveInject"](d.b))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-details-list"]],decls:27,vars:5,consts:[[1,"main-ctnr","pt-1","mb-3","pl-2","pr-2","container-fuid"],[1,"row"],[1,"col-2","ml-3","d-flex"],[1,"my-auto","mr-2",2,"font-weight","500"],[1,"my-auto"],[1,"search-bar","d-flex","col-7","pt-1"],["appearance","outline",1,"ml-auto","mr-auto","my-auto"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["matInput","","placeholder","Search Remote WorX Requests",3,"formControl"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear Search",2,"font-size","18px !important","color","#66615b !important"],[1,"col-2","pt-2","d-flex"],["mat-icon-button","","matTooltip","Info",1,"mr-2","trend-button-inactive",3,"click"],[1,"iconButton"],["mat-icon-button","","matTooltip","Create Remote WorX Request",1,"mr-2","trend-button-inactive",3,"click"],["class","row p-1 pt-2",4,"ngIf"],[4,"ngIf"],["class","container d-flex h-100 flex-column",4,"ngIf"],[1,"row","p-1","pt-2"],[1,"col-1","title-txt"],[1,"ml-3"],[1,"col-3","title-txt"],[1,"ml-2"],[1,"col-2","title-txt"],[1,"ml-1"],[1,"col-2","pl-1","title-txt"],[1,"ml-0"],[1,"col-1","pl-0","title-txt"],[1,"col-1"],["class","row","style","padding-bottom: 1px;",4,"ngFor","ngForOf"],[1,"row",2,"padding-bottom","1px"],[1,"col-12","pl-0","pr-3"],[1,"w-100",3,"wfhDetail"],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","justify-content-center"],["diameter","30"]],template:function(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",2),b["\u0275\u0275elementStart"](3,"span",3),b["\u0275\u0275text"](4,"Total :"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"span",4),b["\u0275\u0275text"](6),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"div",5),b["\u0275\u0275elementStart"](8,"mat-form-field",6),b["\u0275\u0275elementStart"](9,"span",7),b["\u0275\u0275elementStart"](10,"mat-icon",8),b["\u0275\u0275text"](11,"search"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](12,"input",9),b["\u0275\u0275elementStart"](13,"mat-icon",10),b["\u0275\u0275elementStart"](14,"button",11),b["\u0275\u0275listener"]("click",(function(){return t.clearSearch()})),b["\u0275\u0275elementStart"](15,"mat-icon",12),b["\u0275\u0275text"](16,"close "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"div",13),b["\u0275\u0275elementStart"](18,"button",14),b["\u0275\u0275listener"]("click",(function(){return t.openWfhInfo()})),b["\u0275\u0275elementStart"](19,"mat-icon",15),b["\u0275\u0275text"](20,"info"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](21,"button",16),b["\u0275\u0275listener"]("click",(function(){return t.openWfhCreateNav()})),b["\u0275\u0275elementStart"](22,"mat-icon",15),b["\u0275\u0275text"](23,"add_circle_outline"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275template"](24,ht,20,0,"div",17),b["\u0275\u0275template"](25,ft,2,2,"ng-container",18),b["\u0275\u0275template"](26,gt,5,0,"div",19),b["\u0275\u0275elementEnd"]()),2&e&&(b["\u0275\u0275advance"](6),b["\u0275\u0275textInterpolate1"](" ",null==t.wfhList?null:t.wfhList.length,""),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("formControl",t.searchText),b["\u0275\u0275advance"](12),b["\u0275\u0275property"]("ngIf",!t.isLoading),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",!t.isLoading),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[s.c,s.h,r.a,c.b,_.e,_.v,_.k,s.i,m.a,w.a,i.NgIf,i.NgForOf,pt,x.c],styles:[".main-ctnr[_ngcontent-%COMP%]{font-size:14px}.main-ctnr[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{height:28px;width:28px;line-height:28px;background-color:#f2f2f2;box-shadow:0 4px 5px 0 rgba(0,0,0,.2),0 1px 7px 0 rgba(0,0,0,.19)}.main-ctnr[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;color:#000}.main-ctnr[_ngcontent-%COMP%]   .title-txt[_ngcontent-%COMP%]{font-size:11px;color:#66615b}.main-ctnr[_ngcontent-%COMP%]   .bold-txt[_ngcontent-%COMP%]{font-weight:500}.main-ctnr[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw}.main-ctnr[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.main-ctnr[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.main-ctnr[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:17px}"]}),e})(),xt=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-details-page"]],decls:1,vars:0,template:function(e,t){1&e&&b["\u0275\u0275element"](0,"app-wfh-details-list")},directives:[vt],styles:[""]}),e})();function wt(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",10),b["\u0275\u0275elementStart"](1,"div",11),b["\u0275\u0275elementStart"](2,"div",12),b["\u0275\u0275elementStart"](3,"button",13),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().openWfhInfo()})),b["\u0275\u0275elementStart"](4,"mat-icon",14),b["\u0275\u0275text"](5,"info"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"div",15),b["\u0275\u0275elementStart"](7,"span",16),b["\u0275\u0275text"](8,"No Remote WorX requests found !"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"div",17),b["\u0275\u0275element"](10,"img",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](11,"div",19),b["\u0275\u0275elementStart"](12,"div",20),b["\u0275\u0275elementStart"](13,"button",21),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().openWFHCreate()})),b["\u0275\u0275text"](14," New Remote WorX Request "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}}function yt(e,t){1&e&&b["\u0275\u0275element"](0,"app-wfh-details-page")}const St=[{path:"",component:(()=>{class e{constructor(e,t,n,i){this.wfhService=e,this.roleService=t,this.spinner=n,this.dialog=i,this.isDataPresent=!1,this.newRequestVisibility=!1,this.wfhCreateMode="init_create",this.editDetails={},this.editWfh=e=>Object(S.c)(this,void 0,void 0,(function*(){let t=yield this.wfhService.getWfhDetailById(e);t.checklist="string"==typeof t.checklist?JSON.parse(t.checklist):t.checklist,t.country="string"==typeof t.country?JSON.parse(t.country):t.country,t.state="string"==typeof t.state?JSON.parse(t.state):t.state,t.city="string"==typeof t.city?JSON.parse(t.city):t.city,this.wfhCreateMode="edit",this.editDetails=t,this.openWFHCreate()})),this.openWFHCreate=()=>{this.dialog.open(Me,{height:"100%",width:"89%",minWidth:"89%",data:{mode:this.wfhCreateMode,editDetails:this.editDetails},position:{right:"0px"}}).afterClosed().subscribe(e=>{this.closeDrawerAndRefresh(e)})},this.closeDrawerAndRefresh=e=>{"creation Success"==e&&this.initPage()}}ngOnInit(){this.initPage(),this.modeSubscription=this.wfhService.createWfhMode.subscribe(e=>Object(S.c)(this,void 0,void 0,(function*(){if("edit"==e){let e=yield this.wfhService.getEditWfhId();this.editWfh(e)}else"create"==e&&(this.wfhCreateMode="create",this.openWFHCreate())})))}initPage(){return Object(S.c)(this,void 0,void 0,(function*(){this.newRequestVisibility=!1,this.spinner.show(),console.log(this.roleService.userProfile),this.isDataPresent=Boolean(yield this.wfhService.isWFHRequestPresent(this.roleService.userProfile?this.roleService.userProfile.oid:"")),this.spinner.hide(),0==this.isDataPresent&&(this.newRequestVisibility=!0)}))}openWfhInfo(){return Object(S.c)(this,void 0,void 0,(function*(){this.dialog.open(E,{height:"86%",width:"86%",minWidth:"86%",minHeight:"86%"})}))}ngOnDestroy(){this.wfhService.setCreateWfhMode(""),this.modeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](H),b["\u0275\u0275directiveInject"](N.a),b["\u0275\u0275directiveInject"](M.c),b["\u0275\u0275directiveInject"](d.b))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-landing-page"]],decls:13,vars:2,consts:[["hasBackdrop","false",1,"drawer"],["mode","over","position","end",1,"side-drawer"],["createDrawer",""],[1,"row","mt-2","mb-1","col-12","d-flex"],["mat-icon-button","",1,"ml-auto","icon-btn",3,"click"],[1,"close-icon"],["class","pt-4 mb-2 container-fluid",4,"ngIf"],[4,"ngIf"],["bdColor","rgba(230, 230, 231,0.7)","size","medium","color","#cf0001","type","ball-clip-rotate","fullScreen","true"],[2,"color","#cf0001","margin-top","15px !important","font-weight","400"],[1,"pt-4","mb-2","container-fluid"],[1,"row","justify-content-end","mb-5"],[1,"col-4"],["mat-icon-button","",1,"mr-2","trend-button-inactive",3,"click"],[1,"iconButton"],[1,"row","justify-content-center","slide-in-top"],[1,"mr-2","not-found"],[1,"row","d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/WFH.png","height","240","width","430",1,"mt-4","mb-3"],[1,"row","justify-content-center","slide-from-down"],[1,"col-md-6","offset-md-4"],["mat-raised-button","",1,"mt-4","btn-active","slide-from-down",2,"text-align","center",3,"click"]],template:function(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"mat-drawer-container",0),b["\u0275\u0275elementStart"](1,"mat-drawer",1,2),b["\u0275\u0275elementStart"](3,"div",3),b["\u0275\u0275elementStart"](4,"button",4),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](2).close()})),b["\u0275\u0275elementStart"](5,"mat-icon",5),b["\u0275\u0275text"](6,"clear"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](7,"mat-drawer-content"),b["\u0275\u0275template"](8,wt,15,0,"div",6),b["\u0275\u0275template"](9,yt,1,0,"app-wfh-details-page",7),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"ngx-spinner",8),b["\u0275\u0275elementStart"](11,"p",9),b["\u0275\u0275text"](12," Please wait.. "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}2&e&&(b["\u0275\u0275advance"](8),b["\u0275\u0275property"]("ngIf",t.newRequestVisibility),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.isDataPresent))},directives:[a.b,a.a,m.a,r.a,a.c,i.NgIf,M.a,xt],styles:[".btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;width:13rem;line-height:30px;padding:0 25px;border-radius:1!important}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}.drawer[_ngcontent-%COMP%]{width:100%}.side-drawer[_ngcontent-%COMP%]{width:80rem!important;background-size:202px 182px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:97% 81%}.icon-btn[_ngcontent-%COMP%]{height:25px;width:25px;line-height:25px}.close-icon[_ngcontent-%COMP%]{font-size:17px}.not-found[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px;text-align:center}.trend-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}];let bt=(()=>{class e{}return e.\u0275mod=b["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=b["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[y.k.forChild(St)],y.k]}),e})();var Ct=n("FKr1"),Et=n("1yaQ");let _t=(()=>{class e{}return e.\u0275mod=b["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=b["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,bt,Ct.n,Et.b,I.h,m.b,p.c,g.b,h.d,r.b,o.d,a.g,l.f,s.e,_.E,c.c,d.g,u.b,_.p,f.b,M.b,x.b,P.a,v.b,w.b,O.c]]}),e})()},qFYv:function(e,t,n){"use strict";n.d(t,"a",(function(){return C}));var i=n("fXoL"),r=n("tk/3"),o=n("XNiG"),a=n("3Pt+"),l=n("NJ67"),s=n("1G5W"),c=n("Kj3r"),d=n("XXEo"),m=n("kmnG"),p=n("ofXK"),h=n("qFsG"),u=n("/1cH"),f=n("bTqV"),g=n("NFeN"),v=n("Qu3c"),x=n("FKr1");function w(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",7),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().checkAndClearInput(!0)})),i["\u0275\u0275elementStart"](1,"mat-icon",8),i["\u0275\u0275text"](2," close "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function y(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",9),i["\u0275\u0275elementStart"](1,"div",10),i["\u0275\u0275element"](2,"div"),i["\u0275\u0275element"](3,"div"),i["\u0275\u0275element"](4,"div"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function S(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",12),i["\u0275\u0275listener"]("onSelectionChange",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"](2).resultClicked(n)})),i["\u0275\u0275elementStart"](1,"span"),i["\u0275\u0275elementStart"](2,"small",13),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275classMap"](n.ngClasses),i["\u0275\u0275propertyInterpolate3"]("matTooltip","",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"",""),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate3"]("",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"","")}}function b(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,S,4,9,"mat-option",11),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.searchResult)}}let C=(()=>{class e extends l.a{constructor(e,t){super(),this.http=e,this._login=t,this.label="",this.placeholder="",this.isRequired=!1,this.isReadOnly=!1,this.emitVal=new i.EventEmitter,this.optClicked=!1,this.searchTextControl=new a.j,this.searchResult=[],this.isLoading=!1,this.ifPlaceholderExists=!1,this._onDestroy=new o.b,this.resultClicked=e=>{setTimeout(()=>{this.optClicked=!0,this.outputParam?(this.onChange(e[this.outputParam]),this.emitVal.emit(e[this.outputParam])):(this.onChange(JSON.stringify(e)),this.emitVal.emit(JSON.stringify(e))),setTimeout(()=>{document.getElementById("searchTextInput").blur()},100),this.searchTextControl.patchValue(e[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+e[this.optionLabel[1]]:""))},100)}}ngOnInit(){""!=this.placeholder&&""==this.label&&(this.label=this.placeholder,this.ifPlaceholderExists=!0),this.searchTxtControlSubscription=this.searchTextControl.valueChanges.pipe(Object(s.a)(this._onDestroy),Object(c.a)(700)).subscribe(e=>{e&&e.length>0&&this.callApiFn(e)})}callInitApi(){null==this.searchTextControl.value&&0==this.searchResult.length&&this.callApiFn("")}callApiFn(e){this.isLoading=!0;let t={searchParameter:e,options:this.bodyParams},n={headers:new r.f({Authorization:"Bearer "+this._login.getToken()})};this.http.post(this.apiUri,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isLoading=!1,this.searchResult=e},e=>{console.error(e)})}checkAndClearInput(e){this.optClicked&&!e||0!=this.isReadOnly||(this.searchTextControl.reset(),this.onChange(null),this.emitVal.emit(null)),e&&setTimeout(()=>{document.getElementById("searchTextInput").blur(),setTimeout(()=>{document.getElementById("searchTextInput").focus()},100)},100)}writeValue(e){if(e){let t=JSON.parse(e);this.searchTextControl.patchValue(t[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+t[this.optionLabel[1]]:""))}else this.searchTextControl.patchValue("")}resetSuggestion(){this.searchTextControl.patchValue("")}ngOnDestroy(){this.searchTxtControlSubscription&&this.searchTxtControlSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](r.c),i["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-huge-input"]],inputs:{apiUri:"apiUri",bodyParams:"bodyParams",outputParam:"outputParam",optionLabel:"optionLabel",optionValue:"optionValue",label:"label",placeholder:"placeholder",ngClasses:"ngClasses",isRequired:"isRequired",isReadOnly:"isReadOnly"},outputs:{emitVal:"emitVal"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:11,consts:[["appearance","outline",3,"ngClass"],["id","searchTextInput","matInput","","autocomplete","off",3,"placeholder","matAutocomplete","formControl","required","readonly","focusout","focusin","keyup"],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[3,"displayWith"],["auto","matAutocomplete"],["class","is-loading",4,"ngIf"],[4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],[1,"is-loading"],[1,"lds-facebook"],[3,"onSelectionChange",4,"ngFor","ngForOf"],[3,"onSelectionChange"],[2,"padding-left","12px",3,"matTooltip"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"input",1),i["\u0275\u0275listener"]("focusout",(function(){return t.checkAndClearInput(!1)}))("focusin",(function(){return t.callInitApi()}))("keyup",(function(){return t.optClicked=!1})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,w,3,0,"button",2),i["\u0275\u0275elementStart"](5,"mat-autocomplete",3,4),i["\u0275\u0275template"](7,y,5,0,"mat-option",5),i["\u0275\u0275template"](8,b,2,1,"ng-container",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](6);i["\u0275\u0275property"]("ngClass",t.ifPlaceholderExists?"label-no-float":""),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.label),i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("placeholder",t.label),i["\u0275\u0275property"]("matAutocomplete",e)("formControl",t.searchTextControl)("required",t.isRequired)("readonly",t.isReadOnly),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=t.searchTextControl.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("displayWith",t.displayFn),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.isLoading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isLoading)}},directives:[m.c,p.NgClass,m.g,h.b,u.d,a.e,a.v,a.k,a.F,p.NgIf,u.b,f.a,m.i,g.a,v.a,x.p,p.NgForOf],styles:[".label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-label{display:none}  .label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:initial}.mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})()},rq16:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("wd/R"),r=n("fXoL"),o=n("tk/3"),a=n("LcQX");let l=(()=>{class e{constructor(e,t){this.http=e,this.utilityService=t}getWorkflowProperties(e){return this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e.applicationId})}getApproversHierarchy(e){return this.http.post("/api/wfPrimary/getApproversHierarchy",e)}getApproversHierarchyForISA(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>{console.log("getApproversHierarchyForISA res"),console.log(e),t(e)},e=>{t([]),console.log("getApproversHierarchyForISA err"),console.log(e)})})}getOrgData(){return this.http.post("/api/wfPrimary/getOrgData",{})}getOrgTypes(){return this.http.post("/api/wfPrimary/getOrgTypes",{})}addOrgData(e){let t={org_code:"",org_name:"",org_category:"",org_type:"",cost_centre:"",p_and_l_name:"",roll_up_code:"",start_date:"",end_date:""};return t.org_code=e.org_code,t.org_name=e.org_name,t.org_category=e.category,t.org_type=e.org_type,t.cost_centre=e.cost_centre,t.roll_up_code=e.roll_up_code,t.start_date=i(this.utilityService.convertToLocalTime(e.start_date)).format("YYYY-MM-DD"),t.end_date=i(this.utilityService.convertToLocalTime(e.end_date)).format("YYYY-MM-DD"),this.http.post("/api/wfPrimary/addOrgData",{org_data:t})}updateOrgData(e,t){let n={org_code:"",org_name:"",org_category:"",org_type:"",cost_centre:"",p_and_l_id:"",roll_up_code:"",start_date:"",end_date:"",is_active:""};return n.org_code=e.org_code?e.org_code:null,n.org_name=t.org_name?t.org_name:e.org_name,n.org_category=t.category?t.category:e.category,n.org_type=t.org_type?t.org_type:e.org_type,n.cost_centre=t.cost_centre||""==t.cost_centre?t.cost_centre:e.cost_centre,n.p_and_l_id=e.p_and_l_id?e.p_and_l_id:null,n.roll_up_code=t.roll_up_code?t.roll_up_code:e.roll_up_code,n.start_date=t.start_date?i(this.utilityService.convertToLocalTime(t.start_date)).format("YYYY-MM-DD"):i(this.utilityService.convertToLocalTime(e.start_date)).format("YYYY-MM-DD"),n.end_date=t.end_date?i(this.utilityService.convertToLocalTime(t.end_date)).format("YYYY-MM-DD"):i(this.utilityService.convertToLocalTime(e.end_date)).format("YYYY-MM-DD"),n.is_active=t.is_active||0==t.is_active?t.is_active:e.is_active,this.http.post("/api/wfPrimary/updateOrgData",{org_data:n})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](o.c),r["\u0275\u0275inject"](a.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("mrSG"),r=n("xG9w"),o=n("fXoL"),a=n("tk/3"),l=n("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],o=r.keys(t["cc"+n]);for(let r=0;r<o.length;r++)for(let a=0;a<t["cc"+n][o[r]].length;a++){let l={name:t["cc"+n][o[r]][a].DELEGATE_NAME,oid:t["cc"+n][o[r]][a].DELEGATE_OID,level:r+1,designation:t["cc"+n][o[r]][a].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][o[r]][a].IS_DELEGATED,role:t["cc"+n][o[r]][a].DELEGATE_ROLE_NAME};if(1==t["cc"+n][o[r]][a].IS_DELEGATED&&(l.delegated_by={name:t["cc"+n][o[r]][a].APPROVER_NAME,oid:t["cc"+n][o[r]][a].APPROVER_OID,level:r+1,designation:t["cc"+n][o[r]][a].APPROVER_DESIGNATION_NAME}),i.push(l),n==e.length-1&&r==o.length-1&&a==t["cc"+n][o[r]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=r.keys(e["cc"+t]);for(let r=0;r<i.length;r++)for(let o=0;o<e["cc"+t][i[r]].length;o++){let a={name:e["cc"+t][i[r]][o].DELEGATE_NAME,oid:e["cc"+t][i[r]][o].DELEGATE_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[r]][o].IS_DELEGATED};if(1==e["cc"+t][i[r]][o].IS_DELEGATED&&(a.delegated_by={name:e["cc"+t][i[r]][o].APPROVER_NAME,oid:e["cc"+t][i[r]][o].APPROVER_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].APPROVER_DESIGNATION_NAME}),n.push(a),r==i.length-1&&o==e["cc"+t][i[r]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.c),o["\u0275\u0275inject"](l.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);