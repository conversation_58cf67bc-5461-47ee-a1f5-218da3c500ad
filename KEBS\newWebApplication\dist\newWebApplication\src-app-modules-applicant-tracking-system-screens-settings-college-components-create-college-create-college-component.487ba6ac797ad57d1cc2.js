(window.webpackJsonp=window.webpackJsonp||[]).push([[926],{inxZ:function(e,t,o){"use strict";o.r(t),o.d(t,"CreateCollegeComponent",(function(){return f}));var l=o("mrSG"),n=o("3Pt+"),r=o("0IaG"),i=o("XNiG"),a=o("1G5W"),s=o("fXoL"),c=o("XNFG"),m=o("tyNb"),d=o("XQl4"),g=o("kmnG"),u=o("qFsG"),p=o("TmG/");let f=(()=>{class e{constructor(e,t,o,l,r,a){this.dialogRef=e,this.formbuilder=t,this._toaster=o,this.router=l,this._settingsService=r,this.data=a,this._onDestroy=new i.b,this.createCollegeStatus=[],this.form=this.formbuilder.group({collegeName:["",[n.H.required,n.H.pattern("[a-zA-Z ]*")]],collegeStatus:[1,n.H.required],collegeUrl:["",[n.H.required,n.H.pattern("^(http[s]?:\\/\\/){0,1}(www\\.){0,1}[a-zA-Z0-9\\.\\-]+\\.[a-zA-Z]{2,5}[\\.]{0,1}(\\/)?$")]]}),this.formValue={collegeName:"",collegeStatus:"",collegeUrl:""}}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){this.createCollegeStatus=this.data}))}onNoClick(){this.dialogRef.close()}getFormValue(){return Object(l.c)(this,void 0,void 0,(function*(){this.formValue.collegeName=this.form.controls.collegeName.value,this.formValue.collegeStatus=this.form.controls.collegeStatus.value,this.formValue.collegeUrl=this.form.controls.collegeUrl.value}))}createCollege(e){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((t,o)=>this._settingsService.createCollege(e).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.currentCollegeId=e.data,this._toaster.showSuccess("Success",e.msg,3e3)):this._toaster.showError("Error",e.msg,3e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS College Creation Failed!",3e3),o()}}))}))}onSubmit(){var e,t,o,n,r;return Object(l.c)(this,void 0,void 0,(function*(){this.form.valid?(yield this.getFormValue(),yield this.createCollege(this.formValue),this.onNoClick(),this.router.navigate(["/main/ats/settings/college/",this.currentCollegeId])):(null===(e=this.form.controls.collegeName.errors)||void 0===e?void 0:e.required)?this._toaster.showError("Warning","College Name is Mandatory",7e3):(null===(t=this.form.controls.collegeUrl.errors)||void 0===t?void 0:t.required)?this._toaster.showError("Warning","College Link is Mandatory",7e3):(null===(o=this.form.controls.collegeName.errors)||void 0===o?void 0:o.pattern)?this._toaster.showError("Warning","Only alphabets and spaces are allowed for College Name",7e3):(null===(n=this.form.controls.collegeUrl.errors)||void 0===n?void 0:n.pattern)?this._toaster.showError("Warning","Please enter a valid URL",7e3):(null===(r=this.form.controls.collegeStatus.errors)||void 0===r?void 0:r.required)?this._toaster.showError("Warning","College Status is Mandatory",7e3):this._toaster.showError("Warning","Please Fill all the Mandatory Fields",7e3)}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](r.h),s["\u0275\u0275directiveInject"](n.i),s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](m.g),s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-college"]],decls:33,vars:4,consts:[[1,"container-mat-dialog-create-college"],[1,"create-colege-title"],[3,"formGroup"],[1,"form-group"],[1,"form-field"],[1,"label"],[1,"required-field"],["appearance","outline","color","warn",1,"mat-form-field"],["matInput","","type","text","formControlName","collegeName","placeholder","College Name",1,"collegeName"],["placeholder","Select College Status","formControlName","collegeStatus",1,"collegeStatus",3,"list","disableNone","hideMatLabel"],["matInput","","type","url","formControlName","collegeUrl","placeholder","College Website",1,"collegeUrl"],[1,"buttons"],["type","button",1,"cancel-button",3,"click"],["type","submit",1,"save-button",3,"click"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275text"](2,"Create College"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"form",2),s["\u0275\u0275elementStart"](4,"div",3),s["\u0275\u0275elementStart"](5,"div",4),s["\u0275\u0275elementStart"](6,"div",5),s["\u0275\u0275text"](7," College Name "),s["\u0275\u0275elementStart"](8,"span"),s["\u0275\u0275elementStart"](9,"sup",6),s["\u0275\u0275text"](10,"*"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"mat-form-field",7),s["\u0275\u0275element"](12,"input",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"div",4),s["\u0275\u0275elementStart"](14,"div",5),s["\u0275\u0275text"](15," College Status "),s["\u0275\u0275elementStart"](16,"span"),s["\u0275\u0275elementStart"](17,"sup",6),s["\u0275\u0275text"](18,"*"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](19,"app-input-search",9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](20,"div",4),s["\u0275\u0275elementStart"](21,"div",5),s["\u0275\u0275text"](22," College Website "),s["\u0275\u0275elementStart"](23,"span"),s["\u0275\u0275elementStart"](24,"sup",6),s["\u0275\u0275text"](25,"*"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](26,"mat-form-field",7),s["\u0275\u0275element"](27,"input",10),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](28,"div",11),s["\u0275\u0275elementStart"](29,"div",12),s["\u0275\u0275listener"]("click",(function(){return t.onNoClick()})),s["\u0275\u0275text"](30," Cancel "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](31,"div",13),s["\u0275\u0275listener"]("click",(function(){return t.onSubmit()})),s["\u0275\u0275text"](32,"Save"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](16),s["\u0275\u0275property"]("list",t.createCollegeStatus)("disableNone",!0)("hideMatLabel",!0))},directives:[n.J,n.w,n.n,g.c,u.b,n.e,n.v,n.l,p.a],styles:[".container-mat-dialog-create-college[_ngcontent-%COMP%]{padding:35px;overflow:hidden}.form-group[_ngcontent-%COMP%]{margin-bottom:20px;overflow:hidden}.create-colege-title[_ngcontent-%COMP%]{color:#111434;font-size:18px;font-weight:700}.create-colege-title[_ngcontent-%COMP%], .label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);line-height:24px;letter-spacing:.02em;text-align:left}.label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f;margin-top:10px}.mat-form-field[_ngcontent-%COMP%]{width:100%}.buttons[_ngcontent-%COMP%]{display:flex;gap:20px;position:absolute;margin-left:481px;margin-top:25px}.cancel-button[_ngcontent-%COMP%]{text-align:left;color:#45546e;border-radius:5px;border:1px solid #45546e}.cancel-button[_ngcontent-%COMP%], .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:16px;letter-spacing:-.02em;height:40px;padding:10px;justify-content:center;align-items:center;display:flex;width:64px;cursor:pointer}.save-button[_ngcontent-%COMP%]{border-radius:5px;color:#fff;background-color:#79ba44}input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;line-height:24px;letter-spacing:.02em;text-align:left;color:#5f6c81}.required-field[_ngcontent-%COMP%]{color:red;font-size:15px;top:0}"]}),e})()}}]);